package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 사용자쿠폰기본(ez_pm.pm_usr_cpn_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PmUsrCpnBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 사용자쿠폰번호(usr_cpn_no) not null
     */
    private Long usrCpnNo;

    /**
     * 쿠폰번호(cpn_no) not null
     */
    private String cpnNo;

    /**
     * 고객사코드(clnt_cd)
     */
    private String clntCd;

    /**
     * 고객사명(clnt_nm)
     */
    private String clntNm;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;
    /**
     * 가족회원번호(fam_mem_no)
     */
    private String famMemNo;

    /**
     * 사용자명(usr_nm)
     */
    private String usrNm;

    /**
     * 유효기간시작일시(vlid_term_strt_dtm) not null
     */
    private String vlidTermStrtDtm;

    /**
     * 유효기간종료일시(vlid_term_end_dtm) not null
     */
    private String vlidTermEndDtm;

    /**
     * 쿠폰사용상태코드(cpn_use_st_cd) not null
     */
    private String cpnUseStCd;

    /**
     * 테스트쿠폰발급여부(test_cpn_issu_yn) not null
     */
    private String testCpnIssuYn;

    /**
     * 채널코드(ch_cd)
     */
    private String chCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
