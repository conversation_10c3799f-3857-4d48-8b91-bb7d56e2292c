package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.tasklet.BsicInfOrdLoanDdBatPointTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatPointJobConfig {
    private final CommonJobListener commonJobListener;
    private final BsicInfOrdLoanDdBatPointTasklet bsicInfOrdLoanDdBatPointTasklet;

    /**
     * 온라인 정산 데이터 처리(포인트직결제) 일배치
     * 주기 : TODO 매일 오전 3시 0분 0초 (AS-IS 기준)
     * 설명 : 온라인 정산 데이터 처리(포인트직결제) 일배치
     * 
     * @param jobRepository
     * @param bsicInfOrdLoanDdBatPointTaskletStep
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatPointJob")
    Job bsicInfOrdLoanDdBatPointJob(
            JobRepository jobRepository,
            Step bsicInfOrdLoanDdBatPointTaskletStep
    ) {
        return new JobBuilder("bsicInfOrdLoanDdBatPointJob", jobRepository)
                .listener(commonJobListener)
                .start(bsicInfOrdLoanDdBatPointTaskletStep) // 온라인 정산 데이터 처리(포인트직결제) 프로시저 호출 Step TODO 개발필요
                .build();
    }

    /**
     * 온라인 정산 데이터 처리(포인트직결제) 프로시저 호출
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatPointTaskletStep")
    Step bsicInfOrdLoanDdBatPointTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("bsicInfOrdLoanDdBatPointTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(bsicInfOrdLoanDdBatPointTasklet, transactionManager)
                .build();
    }
}