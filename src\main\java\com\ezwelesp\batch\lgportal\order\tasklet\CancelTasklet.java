package com.ezwelesp.batch.lgportal.order.tasklet;

import com.ezwelesp.batch.lgportal.order.service.ClaimService;
import com.ezwelesp.framework.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
  * LG포털 취소목록 조회 및 클레임 생성 처리
  *
  * <AUTHOR>
  * @since 2025.03.31
  * @see com.ezwelesp.batch.lgportal.order.tasklet
  */
@Slf4j
@Component
@RequiredArgsConstructor
public class CancelTasklet implements Tasklet {

    private final ClaimService claimService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) {
        try {
            JobParameters jobParameters = contribution.getStepExecution().getJobParameters();
            Map<String, String> parameters = this.setParameters(jobParameters);

            this.claimService.regOrderCancel(parameters.get("beforeTime"), parameters.get("nowTime"));
        } catch (Exception e) {
            log.error("CancelTasklet Failed: {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        return RepeatStatus.FINISHED;
    }

    private Map<String, String> setParameters(JobParameters jobParameters) {
        Map<String, String> paramterMap = new HashMap<>();

        String beforeTime = jobParameters.getString("beforeTime");
        String nowTime = jobParameters.getString("nowTime");

        if (StringUtils.isEmpty(nowTime) && StringUtils.isEmpty(beforeTime)) {
            String mode = jobParameters.getString("mode");

            Calendar cal = Calendar.getInstance();

            if (StringUtils.equals("all", mode)) {
                cal.add(Calendar.DATE, -1);

                paramterMap.put("nowTime", DateUtils.dateTime(cal.getTime()) + " 23:59:59");
                paramterMap.put("beforeTime", DateUtils.dateTime(cal.getTime()) + " 00:00:00");
            }
            else {
                cal.add(Calendar.HOUR, 1);
                paramterMap.put("nowTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, cal.getTime()));

                cal.add(Calendar.HOUR, -12);
                paramterMap.put("beforeTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, cal.getTime()));
            }
        }
        else {
            paramterMap.put("nowTime", nowTime);
            paramterMap.put("beforeTime", beforeTime);
        }

        return paramterMap;
    }
}
