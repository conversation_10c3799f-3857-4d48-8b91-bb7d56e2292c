package com.ezwelesp.batch.lgportal.product.job;


import com.ezwelesp.batch.lgportal.product.tasklet.LgAddProductTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;



/**
 * LG_API 상품등록
 *
 * Scheduled (cron = "00 15 10 * * *") 매일 10시 15분에 1회 실행
 */
// remark 상품 건별로 옵션 및 고시정보 가지고 오려면 너무 db연결이 많아져서 최소화 하기 위해서 tasklet으로 개발
@Configuration
public class LgPortalAddProductJobConfig {
    private final LgAddProductTasklet lgAddProductTasklet;

    public LgPortalAddProductJobConfig(
            @Qualifier("lgAddProductTasklet") LgAddProductTasklet lgAddProductTasklet
    ) {
        this.lgAddProductTasklet = lgAddProductTasklet;
    }

    @Bean("BA_HIPO00030")
    public Job lgPortalAddProductJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPO00030_STEP") Step lgPortalAddProductStep
    ) {
        return new JobBuilder("BA_HIPO00030", jobRepository)
                .start(lgPortalAddProductStep)
                .build();
    }

    @Bean("BA_HIPO00030_STEP")
    public Step lgPortalAddProductStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
    ) {
        return new StepBuilder("BA_HIPO00030_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(lgAddProductTasklet, transactionManager)
                .build();
    }
}
