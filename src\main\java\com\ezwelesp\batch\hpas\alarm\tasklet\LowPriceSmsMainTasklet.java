package com.ezwelesp.batch.hpas.alarm.tasklet;

import java.util.List;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.hpas.alarm.dto.LowPriceSmsMainDto;
import com.ezwelesp.batch.hpas.alarm.service.LowPriceSmsMainService;
import com.ezwelesp.batch.hpas.util.HpasUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class LowPriceSmsMainTasklet implements Tasklet {
    private final LowPriceSmsMainService lowPriceSmsMainService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) {
        int dataCnt = 0; // 데이타 건
        int sucessCnt = 0; // 성공 건
        int errorCnt = 0; // 실패 건

        try {
            log.info("#############################################");
            log.info("### [협력사에 상품최저가 관리 안내SMS 발송] 시작 일시 : " + HpasUtils.getCurrentDtm() + " ###");
            log.info("#############################################");

            List<LowPriceSmsMainDto> lowPriceSmsMainList = lowPriceSmsMainService.getLowPriceSmsMainList();

            for (LowPriceSmsMainDto lowPriceSmsMain : lowPriceSmsMainList) {
                String frstRegDtm = lowPriceSmsMain.getFrstRegDtm();
                frstRegDtm = frstRegDtm.substring(0, 4) + "/" + frstRegDtm.substring(4, 6) + "/"
                        + frstRegDtm.substring(6, 8);

                String smsMsg = "";
                smsMsg = smsMsg + "[이지웰 최저가 관리안내]\n";
                smsMsg = smsMsg + frstRegDtm + " 기준\n";
                smsMsg = smsMsg + " 협력사명 : " + lowPriceSmsMain.getCspNm();
                smsMsg = smsMsg + "- 최저가 상품수 : " + lowPriceSmsMain.getLwprCnftGdsCnt() + "\n";
                smsMsg = smsMsg + "- 조정필요 상품수 : " + lowPriceSmsMain.getPrcAdjNeedGdsCnt() + "\n";
                smsMsg = smsMsg + "- 모니터링불가 상품수 : " + lowPriceSmsMain.getLwprCnftNdmtGdsCnt() + "\n";
                smsMsg = smsMsg + "- 단독판매 상품수 : " + lowPriceSmsMain.getIpdSellGdsCnt() + "\n";

                // 최저가 목록 SMS 발송
                // sendSmsBean.setCallFrom("0232820579");
                // sendSmsBean.setSmsTxt(smsMsg);
                // sendSmsBean.setSvcType("1014");
                log.info("메일발송 : " + smsMsg);

                // 메일발송 오류 시
                //if (메일발송 오류 시) {
                //lowPriceSmsMainService.

                //}

            }

            log.info("#############################################");
            log.info("### 데이터 총 건수 : " + dataCnt + " 건 / 성공 건수 : " + sucessCnt + " 건 / 실패 건수 : " + errorCnt);
            log.info("### [협력사에 상품최저가 관리 안내SMS 발송] 종료 일시 : " + HpasUtils.getCurrentDtm() + " ###");
            log.info("#############################################");

        } catch (Exception e) {
            log.error("처리중 에러가 발생했습니다.", e);
        }

        return RepeatStatus.FINISHED;
    }
}
