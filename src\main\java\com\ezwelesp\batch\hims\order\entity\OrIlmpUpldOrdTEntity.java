package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 일괄업로드주문임시(ez_or.or_ilmp_upld_ord_t)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrIlmpUpldOrdTEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 일괄업로드주문차수(ilmp_upld_ord_nos) not null
     */
    private Long ilmpUpldOrdNos;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 사용자ID(usr_id)
     */
    private String usrId;

    /**
     * 고객사공통부서코드(cc_dept_cd)
     */
    private String ccDeptCd;

    /**
     * 부서명(dept_nm)
     */
    private String deptNm;

    /**
     * 비고(rmrk)
     */
    private String rmrk;

    /**
     * 일괄업로드처리완료여부(ilmp_upld_prcs_cmpt_yn) not null
     */
    private String ilmpUpldPrcsCmptYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
