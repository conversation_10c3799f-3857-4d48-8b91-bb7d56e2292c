package com.ezwelesp.batch.hpas.alarm.dto;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * 입점 승인 후 3일간 시스템 미접속 / 7일간 상품 미등록 안내메일 발송 DTO
 * 
 * <AUTHOR>
 * @since 2025.05.27
 */
@Data
public class EntryCspMainDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String cspCd;              // 협력사코드
    private String cspNm;              // 협력사명
    private String mgrLginId;          // 관리자로그인ID

    private String chrgrNm;            // 협력사 담당자명
    private String chrgrTelno;         // 협력사 담당자전화번호
    private String chrgrEmlAdr;        // 협력사 담당자이메일주소

    private String chrgrNmOper;        // 운영담당 MD명
    private String chrgrMblTelnoOper;  // 운영담당 MD모바일전화번호
    private String chrgrEmlAdrOper;    // 운영담당 MD이메일주소

    private String chrgrNmBein;        // 입점담당 MD명
    private String chrgrMblTelnoBein;  // 입점담당 MD모바일전화번호
    private String chrgrEmlAdrBein;    // 입점담당 MD이메일주소

    private String beinApvDtm;         // 입점승인일시
    private String satDlvPossYn;       // 토요일배송가능여부
    private String lginDtm;            // 로그인일시
}

