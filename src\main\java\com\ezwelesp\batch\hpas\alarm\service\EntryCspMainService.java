package com.ezwelesp.batch.hpas.alarm.service;

import java.util.List;
import com.ezwelesp.batch.hpas.alarm.dto.EntryCspMainDto;

public interface EntryCspMainService {

    int getEntryCspMainLoginCount();

    List<EntryCspMainDto> getEntryCspMainLoginList(int pageInt);

    void sendEmailEntryCspMainLogin(EntryCspMainDto entryCspMain);


    int getEntryCspMainGoodsCount();

    List<EntryCspMainDto> getEntryCspMainGoodsList(int pageInt);

    void sendEmailEntryCspMainGoods(EntryCspMainDto entryCspMain);
}
