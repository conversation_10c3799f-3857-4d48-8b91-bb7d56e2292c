<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgcompare.pinpay.mapper.command.PinpayCommandMapper">

	<insert id="insertPinpayOrderLog">
		insert into ez_if.ez_ec_order_pay_log 
		(
			TID
			,STATUS_CD
			,ORDER_NUM
			,EZWEL_ORDER_NUM
			,STORE_ID
			,DEAL_TYPE
			,DEVICE_TYPE
			,PAY_TYPE
			,DEAL_STATUS
			,CONFIRM_DD
			,CONFIRM_TM
			,DEAL_AMT
			,CANCEL_TID
			<if test="cancelDd != null and cancelDd != ''">
			,CANCEL_DD
			</if>
			<if test="cancelTm != null and cancelTm != ''">
			,CANCEL_TM
			</if>
			<if test="cancelAmt != null and cancelAmt != ''">
			,CANCEL_AMT
			</if>
			<if test="cardAmt != null and cardAmt != ''">
			,CARD_AMT
			</if>
			<if test="instMm != null and instMm != ''">
			,INST_MM
			</if>
			<if test="confirmNum != null and confirmNum != ''">
			,CONFIRM_NUM
			</if>
			,PAY_DT
			,PG_TYPE
		)values(
			#{tid}
			, (SELECT 
				CASE
					WHEN (SELECT COUNT(STATUS_CD) FROM ez_if.ez_ec_order_pay_log WHERE TID = #{tid} AND STATUS_CD LIKE '%2') > 0 
						THEN (SELECT LPAD((MAX(STATUS_CD) ::numeric + 10) ::varchar, 4, '0') STATUS_CD FROM ez_if.ez_ec_order_pay_log WHERE TID = #{tid} AND STATUS_CD LIKE '%2')
					ELSE #{statusCd}
				END STATUS_CD
			)
			,#{orderNum}
			,#{ezwelOrderNum}
			,#{storeId}
			,#{dealType}
			,#{deviceType}
			,#{payType}
			,#{dealStatus}
			,#{confirmDd}
			,#{confirmTm}
			,#{dealAmt}
			,#{cancelTid}
			<if test="cancelDd != null and cancelDd != ''">
			,#{cancelDd}
			</if>
			<if test="cancelTm != null and cancelTm != ''">
			,#{cancelTm}
			</if>
			<if test="cancelAmt != null and cancelAmt != ''">
			,#{cancelAmt} 
			</if>
			<if test="cardAmt != null and cardAmt != ''">
			,#{cardAmt}
			</if>
			<if test="instMm != null and instMm != ''">
			,#{instMm}
			</if>
			<if test="confirmNum != null and confirmNum != ''">
			,#{confirmNum}
			</if>
			,#{payDt}
			,#{pgType}
		)
	</insert>

</mapper>