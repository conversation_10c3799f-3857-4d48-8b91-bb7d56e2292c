package com.ezwelesp.batch.hims.order.receipt.service.command.impl;


import com.ezwelesp.batch.hims.order.config.CashReceiptConstants;
import com.ezwelesp.batch.hims.order.config.ClaimConstants;
import com.ezwelesp.batch.hims.order.entity.ClClmGdsDEntity;
import com.ezwelesp.batch.hims.order.receipt.domain.*;
import com.ezwelesp.batch.hims.order.receipt.mapper.command.CashReceiptCommandMapper;
import com.ezwelesp.batch.hims.order.receipt.service.command.CashReceiptCommandService;
import com.ezwelesp.batch.hims.order.receipt.service.query.CashReceiptQueryService;
import com.ezwelesp.batch.hims.order.utils.OrderJsonUtils;
import com.ezwelesp.framework.apiresponse.BaseEspHttpStatus;
import com.ezwelesp.framework.exception.ServiceException;
import com.ezwelesp.framework.utils.DateUtils;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.ezwelesp.framework.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CashReceiptCommandServiceImpl implements CashReceiptCommandService {

    private final CashReceiptCommandMapper cashReceiptCommandMapper;
    private final CashReceiptQueryService cashReceiptQueryService;


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public OrderInfoDto createCashReceipt(CashReceiptParamDto cashReceipt) {
        OrderJsonUtils.checkJsonWrap("현금영수증 발행시작", cashReceipt);

        List<OrderInfoDto> orderInfoList = cashReceiptQueryService.getOrderInfo(cashReceipt.getOrdNo());

        OrderJsonUtils.checkJsonWrap(orderInfoList);

        // 해당 주문 클레임 정보를 가져온다
        OrderInfoDto orderInfo =
                orderInfoList.stream()
                        .filter(orderInfoDto -> cashReceipt.getOrdNo().equals(orderInfoDto.getOrdNo()) && (
                                (cashReceipt.getClmNo() == null && (orderInfoDto.getClmNo() == null || "".equals(
                                        orderInfoDto.getClmNo()))) || (
                                        cashReceipt.getClmNo() != null && cashReceipt.getClmNo().equals(
                                                orderInfoDto.getClmNo()))))
                        .findFirst().orElseThrow(() -> new ServiceException(
                                BaseEspHttpStatus.valueOf("%s,%s"), cashReceipt.getOrdNo(), cashReceipt.getClmNo()));

        if (cashReceiptQueryService.getCountCashReceipt(cashReceipt) > 0) {
            throw new ServiceException(BaseEspHttpStatus.valueOf("이미 등록된 현금 영수증 입니다 "+cashReceipt.getOrdNo()));
        }
        //ERROR_CLAIM_NOT_COMPLETE
        if (orderInfo.getClm() != null) {
            if (ClaimConstants.ClaimStatusCodeEnum.COMPLETION.getCode().equals(orderInfo.getClm().getClmStCd())) {
                CashReceiptParamDto prevSearch =
                        CashReceiptParamDto.builder().ordNo(cashReceipt.getOrdNo()).clmNo(cashReceipt.getPrevClmNo()).build();
                // 이전 클레임이 등록됬는지 확인
                if (cashReceiptQueryService.getCountCashReceipt(prevSearch) > 0) {
                    this.repackOrderInfo(orderInfoList,cashReceipt);
                }
                else {
                    throw new ServiceException(BaseEspHttpStatus.valueOf("이전 변경 클레임 항목이 없습니다 이전 클레임 등록후 등록해주세요"+cashReceipt.getOrdNo()) );
                }
            }
            else {
                throw new ServiceException(BaseEspHttpStatus.valueOf("완료된 클레임만 현금영수증 변경이 가능합니다."+cashReceipt.getOrdNo()) );
            }

        }
        else {
            OrderJsonUtils.checkJsonWrap("발행 주문",orderInfo);
            OrderJsonUtils.checkJsonWrap("발행 구성 상품",orderInfo.getOrdGdsList());
            OrderJsonUtils.checkJsonWrap("발행 배송비",orderInfo.getDlvExpList());
            this.registCashReceipt(orderInfo, orderInfo.getOrdGdsList(),orderInfo.getDlvExpList());

        }


        return orderInfo;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public int modifyPublicationReceipt(CashPublicationGatherDto cashPubGatherDto) {
        return cashReceiptCommandMapper.updatePublicationReceipt(cashPubGatherDto);
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public int modifyPublicationReceiptError(CashPublicationGatherDto cashPubGatherDto) {
        return cashReceiptCommandMapper.updatePublicationReceiptError(cashPubGatherDto);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public int modifyPublicationReceiptNumError(CashPublicationGatherDto cashPubGatherDto) {
        return cashReceiptCommandMapper.updatePublicationReceiptNumError(cashPubGatherDto);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public int deletePublicationReceiptError(CashPublicationGatherDto cashPubGatherDto) {
        return cashReceiptCommandMapper.deletePublicationReceiptError(cashPubGatherDto);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public int modifyPublicationReceiptCancel(CashPublicationGatherDto cashPubGatherDto) {
        return cashReceiptCommandMapper.updatePublicationReceiptCancel(cashPubGatherDto);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public int modifyPublicationReceiptCancelError(CashPublicationGatherDto cashPubGatherDto) {
        return cashReceiptCommandMapper.updatePublicationReceiptCancelError(cashPubGatherDto);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public int createPublicationReceiptError(CashPublicationGatherDto cashPubGatherDto) {
        cashPubGatherDto.setJsonStr(OrderJsonUtils.convertToJson(cashPubGatherDto));
        return cashReceiptCommandMapper.insertPublicationReceiptError(cashPubGatherDto);
    }

    private void registCashReceipt(OrderInfoDto orderInfo, List<OrdGdsDto> orderGoodsList,List<DeliveryExpenseDto> deliveryExpenseList) {


        for (OrdGdsDto ordgds : orderGoodsList) {
            
            // 현금영수증 주문상품순번의 최대값을 가져온다
            Long maxPrcsNo = cashReceiptCommandMapper.selectClaimCashReceiptNextNumber(ordgds.getOrdNo(),ordgds.getOrdGdsSeq());
            log.debug("##########maxPrcsNo:"+maxPrcsNo);
            // 현금영수증 금액
            BigDecimal payAmt = ordgds.getGdsSellPrc().multiply(new BigDecimal(ordgds.getOrdGdsQty()));

            String publicationNo = "0100001234";//orderInfo.getOrd().getOrdrMblTelno();
            String csrcPblcUsgCd = "0";

            if("echo".equals(orderInfo.getOrd().getClntCd())){ // 예외처리1번
                // echo 고객사인경우
//                FO에서 현금영수증 발행 정보를 받지 않고, 등록되어 있는 사업자 번호로 사업자 지출증빙용으로 현금영수증을 발행
//
//                AS-IS) ezwel.ez_user_sub 테이블의 bsns_num 번호로 현금영수증 발행
//                TO-BE) 테이블 생성 전
                // 해당정보 아직 미생성 TO DO
                if(true)
                    throw new ServiceException();
                csrcPblcUsgCd = "1";//사업자 지출증빙
            }
            // 예외처리 2번  sgmall, sg30mall 고객사 는 결제테이블 생성시 현금성 금액으로 셋팅

            ContentServiceProviderDto cspInfo = cashReceiptQueryService.getUnitedContentServiceProviderInfo(ordgds.getCspCd());

            CashPublicationBuilderDto receipt = CashPublicationBuilderDto.builder()
                    .ordNo(ordgds.getOrdNo())
                    .ordGdsSeq(ordgds.getOrdGdsSeq())
                    .csrcHndwPrcsNos(maxPrcsNo+1L) //고속버스는 갯수당 번호 도입예정
                    .clmNo(StringUtils.isNotEmpty(orderInfo.getClmNo())?orderInfo.getClmNo():"0")
                    //.aspOrdNo()
                    .allPymtAmt(payAmt)
                    // 현금영수증 발행용도 코드 소비자 소득공제 0  사업자지출증빙 1
                    .csrcPblcUsgCd(csrcPblcUsgCd)
                    // 현금영수증발행대상일자 // TODO
                    .csrcPblcObjDt(DateUtils.dateTimeNow("yyyyMMdd"))
                    // 현금영수증 발행번호 차후 수정해야함
                    .encCsrcPblcNo(publicationNo)
                    // pg가맹점번호? // TODO
                    .pgFrcsNo("00000000")
                    // 현금영수증발행유형코드 A 관리자 B 배치 U 사용자
                    .csrcPblcTypCd("B")
                    .userKey(orderInfo.getOrd().getUserKey())
                    .ordrNm(orderInfo.getOrd().getOrdrNm())
                    .ordrEmlAdr(orderInfo.getOrd().getOrdrEmlAdr())
                    .ordrMblTelno(orderInfo.getOrd().getOrdrMblTelno())
                    // 현금영수증 승인정보는 승인 시점에 저장
                    .gdsCd(ordgds.getGdsCd())
                    .gdsNm(ordgds.getGdsNm())
                    .cspCd(ordgds.getCspCd())
                    // 협력사명은 존재여부 확인  //TODO필요
                    // 상품금액비율 확인  //TODO필요
                    .gdsAmtRt(0d)
                    // 상품 공급가 확인  //TODO필요
                    .gdsSplPrc(ordgds.getGdsPchsPrc())
                    // 현금영수증 상품발행금액
                    .csrcGdsPblcAmt(payAmt)
                    // 현금영수증 공급가격 확인  //TODO
                    .csrcSplPrc(payAmt.subtract(payAmt.multiply(new BigDecimal("0.11"))))
                    // 현금영수증 부가가치세금액 확인  //TODO
                    .csrcVatAmt(payAmt.multiply(new BigDecimal("0.11")))
                    // 상품부가가치세금액
                    .gdsVatAmt(payAmt.multiply(new BigDecimal("0.11")))
                    // 협력사 현금영수증 발행여부 확인  //TODO
                    .cspCsrcPblcYn("N")
                    // 협력사 현금영수증 발행주체코드 확인  //TODO
                    .cspCsrcPblcMagnCd("")
                    // 현금영수증사업자등록번호대상코드
                    .csrcBzrnObjCd("")
                    // 현금영수증발행종류코드 1001 현금영수증카드번호 1002 핸드폰번호 1003 사업자등록번호
                    .csrcPblcKndCd("")
                    // 현금영수증발행대상상품여부
                    .csrcPblcObjGdsYn("Y")
                    // 협력사사업자등록번호
                    .cspBzrn(cspInfo.getBzrn())
                    .cspNm(cspInfo.getCspNm())
                    // 과세종류코드 00 과세 01 면세 03 영수증 04 dudtp
                    .taxnKndCd(ordgds.getTaxnKndCd())
                    // 표준메뉴코드
                    .stdMenuCd(ordgds.getHezoMenuCd())
                    // 제도마감취소여부 확인  //TODO
                    .rgmClsgCnclYn("")
                    // 현금영수증발행여부
                    .csrcPblcYn("N")
                    // 비고
                    .rmrk("")
                    // 선택적복지포인트결제금액 확인  //TODO
                    .wfpCsrcPblcAmt(BigDecimal.ZERO)
                    // 급여차감결제금액 //TODO
                    .wgsSbtrCsrcPblcAmt(BigDecimal.ZERO)
                    // 특별포인트결제금액 //TODO
                    .sppCsrcPblcAmt(BigDecimal.ZERO)
                    // 계좌이체결제금액 //TODO
                    .acntTrnsCsrcPblcAmt(BigDecimal.ZERO)
                    // 가상계좌결제금액 //TODO
                    .vtacCsrcPblcAmt(BigDecimal.ZERO)
                    // 지역화폐결제금액 //TODO
                    .lbvCsrcPblcAmt(BigDecimal.ZERO)
                    // 대한상공회의소포인트 //TODO
                    .kcciPntCsrcPblcAmt(BigDecimal.ZERO)
                    // 복지대장포인트결제금액 //TODO
                    .vndpCsrcPblcAmt(BigDecimal.ZERO)
                    // 대장페이포인트결제금액 //TODO
                    .vnppCsrcPblcAmt(BigDecimal.ZERO)
                    // H포인트현금영수증발행금액 //TODO
                    .hpntCsrcPblcAmt(BigDecimal.ZERO)
                    // 네이버페이현금영수증발행금액 //TODO
                    .nvpayCsrcPblcAmt(BigDecimal.ZERO)
                    // NH포인트현금영수증발행금액 //TODO
                    .nhpntCsrcPblcAmt(BigDecimal.ZERO)
                    // 선택적복지포인트여부 //TODO
                    .wfpYn("N")
                    // 특별포인트여부 //TODO
                    .sppYn("N")
                    // 문화비소득공제대상여부 //TODO
                    .clxpItdObjYn("N")
                    .frstRegPgmId(CashReceiptConstants.CASH_RECEIPT_PROGRAM_ID)
                    .lastModPgmId(CashReceiptConstants.CASH_RECEIPT_PROGRAM_ID)
                    .build();

            cashReceiptCommandMapper.insertCashReceipt(receipt);

        }
        long index = 1000;
        if(deliveryExpenseList!=null&&!deliveryExpenseList.isEmpty()) {
            for (DeliveryExpenseDto dlvDto : deliveryExpenseList) {


                long seq = index++;
                // 현금영수증 주문상품순번의 최대값을 가져온다
                Long maxPrcsNo = cashReceiptCommandMapper.selectClaimCashReceiptNextNumber(dlvDto.getOrdNo(), seq);
                log.debug("##########maxPrcsNo:" + maxPrcsNo);
                // 현금영수증 금액
                BigDecimal payAmt = dlvDto.getDlvExp();

                ContentServiceProviderDto cspDlvInfo = cashReceiptQueryService.getUnitedContentServiceProviderInfo(dlvDto.getCspCd());;

                CashPublicationBuilderDto receipt = CashPublicationBuilderDto.builder()
                        .ordNo(dlvDto.getOrdNo())
                        .ordGdsSeq(seq)
                        .csrcHndwPrcsNos(maxPrcsNo + 1L) //고속버스는 갯수당 번호 도입예정
                        .clmNo(StringUtils.isNotEmpty(orderInfo.getClmNo()) ? orderInfo.getClmNo() : "0")
                        //.aspOrdNo()
                        .allPymtAmt(payAmt)
                        // 현금영수증 발행용도 코드 소비자 소득공제 0  사업자지출증빙 1
                        .csrcPblcUsgCd("0")
                        // 현금영수증발행대상일자 // TODO
                        .csrcPblcObjDt(DateUtils.dateTimeNow("yyyyMMdd"))
                        // 현금영수증 발행번호 차후 수정해야함
                        .encCsrcPblcNo(orderInfo.getOrd().getOrdrMblTelno())
                        // pg가맹점번호? // TODO
                        .pgFrcsNo("00000000")
                        // 현금영수증발행유형코드 A 관리자 B 배치 U 사용자
                        .csrcPblcTypCd("U")
                        .userKey(orderInfo.getOrd().getUserKey())
                        .ordrNm(orderInfo.getOrd().getOrdrNm())
                        .ordrEmlAdr(orderInfo.getOrd().getOrdrEmlAdr())
                        .ordrMblTelno(orderInfo.getOrd().getOrdrMblTelno())
                        // 현금영수증 승인정보는 승인 시점에 저장
                        .gdsCd("")
                        .gdsNm(dlvDto.getCspCd() + "배송비")
                        .cspCd(dlvDto.getCspCd())
                        // 협력사명은 존재여부 확인  //TODO필요
                        // 상품금액비율 확인  //TODO필요
                        .gdsAmtRt(0d)
                        // 상품 공급가 확인  //TODO필요
                        .gdsSplPrc(BigDecimal.ZERO)
                        // 현금영수증 상품발행금액
                        .csrcGdsPblcAmt(payAmt)
                        // 현금영수증 공급가격 확인  //TODO
                        .csrcSplPrc(payAmt.subtract(payAmt.multiply(new BigDecimal("0.11"))))
                        // 현금영수증 부가가치세금액 확인  //TODO
                        .csrcVatAmt(payAmt.multiply(new BigDecimal("0.11")))
                        // 상품부가가치세금액
                        .gdsVatAmt(payAmt.multiply(new BigDecimal("0.11")))
                        // 협력사 현금영수증 발행여부 확인  //TODO
                        .cspCsrcPblcYn("N")
                        // 협력사 현금영수증 발행주체코드 확인  //TODO
                        .cspCsrcPblcMagnCd("")
                        // 현금영수증사업자등록번호대상코드
                        .csrcBzrnObjCd("")
                        // 현금영수증발행종류코드 1001 현금영수증카드번호 1002 핸드폰번호 1003 사업자등록번호
                        .csrcPblcKndCd("")
                        // 현금영수증발행대상상품여부
                        .csrcPblcObjGdsYn("Y")
                        // 협력사사업자등록번호
                        .cspBzrn(cspDlvInfo.getBzrn())
                        .cspNm(cspDlvInfo.getCspNm())
                        // 과세종류코드 00 과세 01 면세 03 영수증 04 dudtp
                        .taxnKndCd("01")
                        // 표준메뉴코드
                        .stdMenuCd("")
                        // 제도마감취소여부 확인  //TODO
                        .rgmClsgCnclYn("")
                        // 현금영수증발행여부
                        .csrcPblcYn("N")

                        // 비고
                        .rmrk("")
                        // 선택적복지포인트결제금액 확인  //TODO
                        .wfpCsrcPblcAmt(BigDecimal.ZERO)
                        // 급여차감결제금액 //TODO
                        .wgsSbtrCsrcPblcAmt(BigDecimal.ZERO)
                        // 특별포인트결제금액 //TODO
                        .sppCsrcPblcAmt(BigDecimal.ZERO)
                        // 계좌이체결제금액 //TODO
                        .acntTrnsCsrcPblcAmt(BigDecimal.ZERO)
                        // 가상계좌결제금액 //TODO
                        .vtacCsrcPblcAmt(BigDecimal.ZERO)
                        // 지역화폐결제금액 //TODO
                        .lbvCsrcPblcAmt(BigDecimal.ZERO)
                        // 대한상공회의소포인트 //TODO
                        .kcciPntCsrcPblcAmt(BigDecimal.ZERO)
                        // 복지대장포인트결제금액 //TODO
                        .vndpCsrcPblcAmt(BigDecimal.ZERO)
                        // 대장페이포인트결제금액 //TODO
                        .vnppCsrcPblcAmt(BigDecimal.ZERO)
                        // H포인트현금영수증발행금액 //TODO
                        .hpntCsrcPblcAmt(BigDecimal.ZERO)
                        // 네이버페이현금영수증발행금액 //TODO
                        .nvpayCsrcPblcAmt(BigDecimal.ZERO)
                        // NH포인트현금영수증발행금액 //TODO
                        .nhpntCsrcPblcAmt(BigDecimal.ZERO)
                        // 선택적복지포인트여부 //TODO
                        .wfpYn("N")
                        // 특별포인트여부 //TODO
                        .sppYn("N")
                        // 문화비소득공제대상여부 //TODO
                        .clxpItdObjYn("N")
                        .frstRegPgmId(CashReceiptConstants.CASH_RECEIPT_PROGRAM_ID)
                        .lastModPgmId(CashReceiptConstants.CASH_RECEIPT_PROGRAM_ID)
                        .build();

                cashReceiptCommandMapper.insertCashReceipt(receipt);

            }
        }


    }

    private List<OrdGdsDto> repackOrderGoods(List<OrderInfoDto> orderInfoList, CashReceiptParamDto cashReceipt) {
// 첫 구성 주문상품을 입력
        List<OrdGdsDto> receiptOrdGdsList = orderInfoList.get(0).getOrdGdsList();


        // 잔여구성을 만든다
        for (OrderInfoDto orderInfo : orderInfoList) {
            if ("".equals(orderInfo.getClmNo()))
                continue; // 첫구성은 스킾
            if(cashReceipt.getClmNo().equals(orderInfo.getPrevClmNo())){
                break;
            }

            //CNCL EXCH RTP
            if (ClaimConstants.ClaimKindCodeEnum.CANCEL.getCode().equals(orderInfo.getClm().getClmKndCd())
                    || ClaimConstants.ClaimKindCodeEnum.RETURN.getCode().equals(
                    orderInfo.getClm().getClmKndCd())) { // 취소 반품
                for (ClClmGdsDEntity clmOrdGds : orderInfo.getClmGdsList()) {
                    OrdGdsDto item = receiptOrdGdsList.stream()
                            .filter(gdsitem -> gdsitem.getOrdGdsSeq().equals(clmOrdGds.getOrdGdsSeq())).findFirst()
                            .orElse(null);
                    if (item != null) {
                        item.setOrdGdsQty(item.getOrdGdsQty().intValue() - clmOrdGds.getClmGdsQty());
                    }
                }
            }
            if (ClaimConstants.ClaimKindCodeEnum.EXCHANGE.getCode().equals(orderInfo.getClm().getClmKndCd())) { // 교환
                // 교환 처리되는 상품 제거
                for (ClClmGdsDEntity clmOrdGds : orderInfo.getClmGdsList()) {
                    OrdGdsDto item = receiptOrdGdsList.stream()
                            .filter(gdsitem -> gdsitem.getOrdGdsSeq().equals(clmOrdGds.getOrdGdsSeq())).findFirst()
                            .orElse(null);
                    if (item != null) {
                        item.setOrdGdsQty(item.getOrdGdsQty().intValue() - clmOrdGds.getClmGdsQty());
                    }
                }
                for (OrdGdsDto exOrdGds : orderInfo.getExOrdGdsList()) {
                    receiptOrdGdsList.add(exOrdGds);
                }
                // 교환 대상이 되는 상품 추가
            }
            // 클레임이 검출되면 리팩 종료
        }

        List<OrdGdsDto> newReceiptOrdGdsList = new ArrayList<>();
        for (OrdGdsDto gdsDto : receiptOrdGdsList) {
            if (gdsDto.getOrdGdsQty() > 0) {
                newReceiptOrdGdsList.add(gdsDto);
            }
        }
        return newReceiptOrdGdsList;
    }
    private OrderPaymentPackDto repackOrderPayment(List<OrderInfoDto> orderInfoList, CashReceiptParamDto cashReceipt) {

        // 주문 결제 목록
        List<OrderPaymentDto> receiptPayList = orderInfoList.get(0).getOrdPayList();


        // 잔여구성을 만든다
        for (OrderInfoDto orderInfo : orderInfoList) {
            if ("".equals(orderInfo.getClmNo()))
                continue; // 첫구성은 스킾
            if(cashReceipt.getClmNo().equals(orderInfo.getPrevClmNo())){
                break;
            }

            for(OrderPaymentDto ordPayDto:orderInfo.getClmPayList()){
                OrderPaymentDto pay = receiptPayList.stream()
                        .filter(payItem -> payItem.equals(ordPayDto.getPymtMnsCd())).findFirst()
                        .orElse(null);
                if (pay != null) {
                    if("PYMT".equals(ordPayDto.getPymtDivCd())){
                        pay.setPymtAmt(pay.getPymtAmt().add(ordPayDto.getPymtAmt()));
                    }else{
                        pay.setPymtAmt(pay.getPymtAmt().subtract(ordPayDto.getPymtAmt()));
                    }
                }else{
                    // 항목이없으면 리스트에 추가
                    if(!"PYMT".equals(ordPayDto.getPymtDivCd())){
                        ordPayDto.setPymtAmt(ordPayDto.getPymtAmt().multiply(new BigDecimal("-1")));
                    }
                    receiptPayList.add(ordPayDto);
                }
            }
           // 클레임이 검출되면 리팩 종료
        }

        return OrderPaymentPackDto.builder().orderPaymentList(receiptPayList).build();
    }
    private void repackOrderInfo(List<OrderInfoDto> orderInfoList, CashReceiptParamDto cashReceipt) {

        OrderInfoDto lastOrderInfo =
                orderInfoList.stream().filter(item -> item.getClmNo().equals(cashReceipt.getClmNo())).findFirst()
                        .orElseThrow(() -> new ServiceException(
                                BaseEspHttpStatus.valueOf("클레임이 없습니다." + cashReceipt.getClmNo())));
        List<OrdGdsDto> newReceiptOrdGdsList = repackOrderGoods(orderInfoList, cashReceipt);
        OrderPaymentPackDto paymentPackDto = repackOrderPayment(orderInfoList, cashReceipt);

        WithDrawalReceiptDto withdrawalReceipt = WithDrawalReceiptDto.builder()
                .ordNo(cashReceipt.getOrdNo())
                .csrcPblcCnclDt(DateUtils.dateTimeNow("yyyyMMdd"))
                .clmNo(cashReceipt.getClmNo())
                .prevClmNo(cashReceipt.getPrevClmNo())
                .build();

        // 현금영수증 취소항목이 있으면 오류 발생
        if (cashReceiptCommandMapper.selectWithdrawalReceipt(withdrawalReceipt) == 0) {
            OrderJsonUtils.checkJsonWrap("발행취소", withdrawalReceipt);
            cashReceiptCommandMapper.insertCashReceiptWithdrawal(withdrawalReceipt);
            if (!newReceiptOrdGdsList.isEmpty()) { // 잔여 수량이 있어야 재발급
                OrderJsonUtils.checkJsonWrap("재발행 마지막 주문", lastOrderInfo);
                OrderJsonUtils.checkJsonWrap("재발행 재구성 상품", newReceiptOrdGdsList);
                OrderJsonUtils.checkJsonWrap("재발행 배송비 상품", null);
                this.registCashReceipt(lastOrderInfo, newReceiptOrdGdsList, null);
            }
            else {
                throw new ServiceException(BaseEspHttpStatus.valueOf(
                        "이미 취소 현금영수증 항목이 있습니다 " + cashReceipt.getOrdNo() + cashReceipt.getClmNo()));
            }

        }
    }





}
