package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 복지몰기본(ez_ct.ct_hezo_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class CtHezoBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 복지몰번호(hezo_no) not null
     */
    private Long hezoNo;

    /**
     * 복지몰서비스도메인명(hezo_srv_dmn_nm)
     */
    private String hezoSrvDmnNm;

    /**
     * 고객사분류코드(clnt_cls_cd)
     */
    private String clntClsCd;

    /**
     * 고객사분류명(clnt_cls_nm)
     */
    private String clntClsNm;

    /**
     * 복지몰명(hezo_nm) not null
     */
    private String hezoNm;

    /**
     * 복지몰유형코드(hezo_typ_cd)
     */
    private String hezoTypCd;

    /**
     * 브라우저제목(brow_ttl)
     */
    private String browTtl;

    /**
     * 로고이미지파일명(logo_img_file_nm)
     */
    private String logoImgFileNm;

    /**
     * 로고이미지경로(logo_img_path)
     */
    private String logoImgPath;

    /**
     * 당년오픈일자(tyr_open_dt)
     */
    private String tyrOpenDt;

    /**
     * 당년마감일자(tyr_clsg_dt)
     */
    private String tyrClsgDt;

    /**
     * 고객센터전화번호적용기준코드(cst_cntr_telno_aply_bsic_cd)
     */
    private String cstCntrTelnoAplyBsicCd;

    /**
     * 복지몰오픈일시(hezo_open_dtm)
     */
    private String hezoOpenDtm;

    /**
     * 콜센터전화번호(clct_telno)
     */
    private String clctTelno;

    /**
     * 문자메시지수신거부안내전화번호(tmsg_rcv_rfs_gd_telno)
     */
    private String tmsgRcvRfsGdTelno;

    /**
     * OB카페사용여부(obcf_use_yn) not null
     */
    private String obcfUseYn;

    /**
     * 복지제도포인트단위코드(wsp_unit_cd)
     */
    private String wspUnitCd;

    /**
     * 복지제도포인트단위명(wsp_unit_nm)
     */
    private String wspUnitNm;

    /**
     * 복지제도포인트지급주기코드(wsp_pay_cycl_cd)
     */
    private String wspPayCyclCd;

    /**
     * 복지제도포인트지급주기문자값(wsp_pay_cycl_cval)
     */
    private String wspPayCyclCval;

    /**
     * 제도운영기준안내URL(rgm_oper_bsic_gd_url)
     */
    private String rgmOperBsicGdUrl;

    /**
     * 선택적복지포인트사용기한코드(wfp_use_dlin_cd)
     */
    private String wfpUseDlinCd;

    /**
     * 고객사선택적복지포인트명(clnt_wfp_nm)
     */
    private String clntWfpNm;

    /**
     * 고객사특별포인트명(clnt_spp_nm)
     */
    private String clntSppNm;

    /**
     * 고객사서비스상태코드(clnt_srv_st_cd)
     */
    private String clntSrvStCd;

    /**
     * 로그인방식코드(lgin_way_cd)
     */
    private String lginWayCd;

    /**
     * 로그인속성유형코드(lgin_att_typ_cd)
     */
    private String lginAttTypCd;

    /**
     * 사용자생성방법코드(usr_crt_mthd_cd)
     */
    private String usrCrtMthdCd;

    /**
     * 회원가입본인인증예외여부(mem_join_self_crtf_excp_yn) not null
     */
    private String memJoinSelfCrtfExcpYn;

    /**
     * 초기비밀번호정책입력내용(init_pwd_plcy_insr_cntn)
     */
    private String initPwdPlcyInsrCntn;

    /**
     * 로그인이후초기정책내용(lgin_aft_init_plcy_cntn)
     */
    private String lginAftInitPlcyCntn;

    /**
     * 복지몰페이지유형코드(hezo_page_typ_cd) not null
     */
    private String hezoPageTypCd;

    /**
     * 가족회원서비스허용여부(fam_mem_srv_allw_yn) not null
     */
    private String famMemSrvAllwYn;

    /**
     * 가족회원포인트사용여부(fam_mem_pnt_use_yn) not null
     */
    private String famMemPntUseYn;

    /**
     * 가족회원서비스오픈일자(fam_mem_srv_open_dt) not null
     */
    private String famMemSrvOpenDt;

    /**
     * 로그인2차인증사용여부(lgin_r2nd_crtf_use_yn) not null
     */
    private String lginR2ndCrtfUseYn;

    /**
     * 개인정보수정본인인증사용여부(pi_mod_self_crtf_use_yn) not null
     */
    private String piModSelfCrtfUseYn;

    /**
     * 복지몰템플릿데이터사용여부(hezo_tmpl_data_use_yn) not null
     */
    private String hezoTmplDataUseYn;

    /**
     * 모바일전용여부(mbl_only_yn) not null
     */
    private String mblOnlyYn;

    /**
     * 고객사어드민모바일접근허용여부(hcas_mbl_aprc_allw_yn) not null
     */
    private String hcasMblAprcAllwYn;

    /**
     * 2단계인증사용여부(stp2_crtf_use_yn) not null
     */
    private String stp2CrtfUseYn;

    /**
     * 포인트노출기준코드(pnt_exps_bsic_cd)
     */
    private String pntExpsBsicCd;

    /**
     * SSO연동사용여부(sso_intl_use_yn) not null
     */
    private String ssoIntlUseYn;

    /**
     * 이지웰프렌즈사용여부(ezfrn_use_yn)
     */
    private String ezfrnUseYn;

    /**
     * 이지웰프렌즈추천지점코드(ezfrn_rcmm_br_cd)
     */
    private String ezfrnRcmmBrCd;

    /**
     * 제로페이선불명(zrpay_prpy_nm)
     */
    private String zrpayPrpyNm;

    /**
     * 제로페이선불환불안내문구내용(zrpay_prpy_rfnd_gd_phrs_cntn)
     */
    private String zrpayPrpyRfndGdPhrsCntn;

    /**
     * 이지웰페이사용여부(ezpay_use_yn) not null
     */
    private String ezpayUseYn;

    /**
     * 이지웰페이연동유형코드(ezpay_intl_typ_cd)
     */
    private String ezpayIntlTypCd;

    /**
     * H포인트사용여부(hpnt_use_yn) not null
     */
    private String hpntUseYn;

    /**
     * NH멤버스포인트사용여부(nhmbrs_pnt_use_yn) not null
     */
    private String nhmbrsPntUseYn;

    /**
     * 지역화폐사용여부(lbv_use_yn) not null
     */
    private String lbvUseYn;

    /**
     * 사용자소속정보변경허용여부(usr_blgt_inf_chg_allw_yn) not null
     */
    private String usrBlgtInfChgAllwYn;

    /**
     * 직거래장터사용유형코드(dmkt_use_typ_cd) not null
     */
    private String dmktUseTypCd;

    /**
     * 개인화추천위젯사용여부(psnz_rcmm_widg_use_yn) not null
     */
    private String psnzRcmmWidgUseYn;

    /**
     * 장바구니사용여부(bskt_use_yn)
     */
    private String bsktUseYn;

    /**
     * 선물하기사용여부(gvgft_use_yn)
     */
    private String gvgftUseYn;

    /**
     * 다중배송가능여부(mti_dlv_poss_yn) not null
     */
    private String mtiDlvPossYn;

    /**
     * 추천상품사용여부(rcmm_gds_use_yn) not null
     */
    private String rcmmGdsUseYn;

    /**
     * 레인보우혜택사용여부(rbbf_use_yn) not null
     */
    private String rbbfUseYn;

    /**
     * EZ라이브사용여부(ezlive_use_yn) not null
     */
    private String ezliveUseYn;

    /**
     * 정기구독사용여부(sto_use_yn) not null
     */
    private String stoUseYn;

    /**
     * 정기구독결제복지제도포인트구분코드(sto_pymt_wsp_div_cd)
     */
    private String stoPymtWspDivCd;

    /**
     * 적립금충전소사용여부(mlg_chst_use_yn) not null
     */
    private String mlgChstUseYn;

    /**
     * 테스트복지몰여부(test_hezo_yn) not null
     */
    private String testHezoYn;

    /**
     * 복지샵헤더사용여부(hews_hdr_use_yn) not null
     */
    private String hewsHdrUseYn;

    /**
     * 복지유형아이콘사용여부(wlfr_typ_icon_use_yn) not null
     */
    private String wlfrTypIconUseYn;

    /**
     * 제휴사헤더유형코드(asp_hdr_typ_cd)
     */
    private String aspHdrTypCd;

    /**
     * CSS적용색상유형명(css_aply_clr_typ_nm)
     */
    private String cssAplyClrTypNm;

    /**
     * 보험약관확인방법코드(insu_tnc_cnft_mthd_cd)
     */
    private String insuTncCnftMthdCd;

    /**
     * PC메인페이지대체URL(pc_main_page_rplc_url)
     */
    private String pcMainPageRplcUrl;

    /**
     * 모바일메인페이지대체URL(mbl_main_page_rplc_url)
     */
    private String mblMainPageRplcUrl;

    /**
     * PC메인페이지대체URL사용여부(pc_main_page_rplc_url_use_yn) not null
     */
    private String pcMainPageRplcUrlUseYn;

    /**
     * 모바일메인페이지대체URL사용여부(mbl_main_page_rplc_url_use_yn) not null
     */
    private String mblMainPageRplcUrlUseYn;

    /**
     * PC랜딩페이지URL(pc_lndg_page_url)
     */
    private String pcLndgPageUrl;

    /**
     * 모바일랜딩페이지URL(mbl_lndg_page_url)
     */
    private String mblLndgPageUrl;

    /**
     * 사원등록조건설정사용여부(emp_reg_cond_stup_use_yn) not null
     */
    private String empRegCondStupUseYn;

    /**
     * 소속필수설정여부1(blgt_mndr_stup_yn1) not null
     */
    private String blgtMndrStupYn1;

    /**
     * 소속필수설정여부(blgt_mndr_stup_yn) not null
     */
    private String blgtMndrStupYn;

    /**
     * 소속필수설정여부2(blgt_mndr_stup_yn2) not null
     */
    private String blgtMndrStupYn2;

    /**
     * 소속필수설정여부3(blgt_mndr_stup_yn3) not null
     */
    private String blgtMndrStupYn3;

    /**
     * 고객사정산주기코드(clnt_stl_cycl_cd)
     */
    private String clntStlCyclCd;

    /**
     * 오프라인계산서대리발행여부(ofln_bill_sbst_pblc_yn) not null
     */
    private String oflnBillSbstPblcYn;

    /**
     * 복지몰구축상태코드(hezo_bld_st_cd)
     */
    private String hezoBldStCd;

    /**
     * 대표관리자지정인원수(rps_mgr_dsnt_psnl_cnt)
     */
    private Integer rpsMgrDsntPsnlCnt;

    /**
     * 메뉴그룹설정사용여부(menu_grp_stup_use_yn)
     */
    private String menuGrpStupUseYn;

    /**
     * 복지플러스사용여부(wfpls_use_yn)
     */
    private String wfplsUseYn;

    /**
     * PC로그인이후이동URL(pc_lgin_aft_mv_url)
     */
    private String pcLginAftMvUrl;

    /**
     * 모바일로그인이후이동URL(mbl_lgin_aft_mv_url)
     */
    private String mblLginAftMvUrl;

    /**
     * PC로그인이후이동URL사용여부(pc_lgin_aft_mv_url_use_yn) not null
     */
    private String pcLginAftMvUrlUseYn;

    /**
     * 모바일로그인이후이동URL사용여부(mbl_lgin_aft_mv_url_use_yn) not null
     */
    private String mblLginAftMvUrlUseYn;

    /**
     * 로그인이전소개페이지URL(lgin_bef_intro_page_url)
     */
    private String lginBefIntroPageUrl;

    /**
     * 로그인이전소개페이지URL사용여부(lgin_bef_intro_page_url_use_yn) not null
     */
    private String lginBefIntroPageUrlUseYn;

    /**
     * 로그인이후소개페이지URL(lgin_aft_intro_page_url)
     */
    private String lginAftIntroPageUrl;

    /**
     * 로그인이후소개페이지URL사용여부(lgin_aft_intro_page_url_use_yn) not null
     */
    private String lginAftIntroPageUrlUseYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
