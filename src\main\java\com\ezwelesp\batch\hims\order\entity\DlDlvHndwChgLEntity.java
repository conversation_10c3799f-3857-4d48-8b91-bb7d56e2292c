package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 배송수기변경내역(ez_or.dl_dlv_hndw_chg_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlDlvHndwChgLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송수기변경내역순번(dlv_hndw_chg_lst_seq) not null
     */
    private Long dlvHndwChgLstSeq;

    /**
     * 배송번호(dlv_no)
     */
    private String dlvNo;

    /**
     * 배송비용번호(dlv_exp_no)
     */
    private Long dlvExpNo;

    /**
     * 주문배송내용J(ord_dlv_cntnj)
     */
    private String ordDlvCntnj;

    /**
     * 주문배송비용내용J(ord_dlv_exp_cntnj)
     */
    private String ordDlvExpCntnj;

    /**
     * 관리자메모(mgr_memo)
     */
    private String mgrMemo;

    /**
     * 이전데이터등록일시(bef_data_reg_dtm)
     */
    private String befDataRegDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
