package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgUpdateProductPriceRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalEditProductPriceHandler implements LgPortalEditProductProcessorHandler {

    private final LgApiClient lgApiClient;

    @Override
    public LgProductApiResponseDto handle(LgEditProductTargetVo item) {

        val requestDto = LgUpdateProductPriceRequestDto.builder()
                .productNo(item.getProductNo())
                .consumerPrice(item.getNrmlSellPrc())
                .salesPrice(item.getRealSellPrc())
                .vendorPrice(0L) // 곰급사 가격의 경우 0원으로 전송
                .build();

        val responseDto = lgApiClient.sendLgUpdateProductPriceApi(
                requestDto,
                item.getGdsCd(),
                item.getApiSeq()
        );

        if (responseDto != null && !"200".equals(responseDto.getResult())) {
            log.debug("sendLgUpdateProductPriceApi Fail : {},{}", item.getGdsCd(), responseDto.getResult());
        }

        return responseDto;
    }

}
