package com.ezwelesp.batch.hims.order.samsung.service.command.impl;

import com.ezwelesp.batch.hims.order.samsung.mapper.command.SamsungOrderGoodsCommandMapper;
import com.ezwelesp.batch.hims.order.samsung.service.command.SamsungOrderGoodsCommandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@RequiredArgsConstructor
@Service
public class SamsungOrderGoodsCommandServiceImpl implements SamsungOrderGoodsCommandService {

    private final SamsungOrderGoodsCommandMapper samsungOrderGoodsCommandMapper;

    @Override
    @Transactional(value = "primaryCommandTransactionManager")
    public void replaceSpecialCharsInOrOrdBGoodsName() {
        log.info("주문기본 테이블 주문상품요약명 특수문자 치환 시작");
        
        try {
            samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNm40();
            log.debug("&#40; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNm41();
            log.debug("&#41; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNmQuot();
            log.debug("&quot; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNm39();
            log.debug("&#39; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNm35();
            log.debug("&#35; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNmAmp();
            log.debug("&amp; 치환 완료");
            
            log.info("주문기본 테이블 주문상품요약명 특수문자 치환 완료");
        } catch (Exception e) {
            log.error("주문기본 테이블 주문상품요약명 특수문자 치환 실패: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(value = "primaryCommandTransactionManager")
    public void replaceSpecialCharsInOrOrdGdsDGoodsName() {
        log.info("주문상품상세 테이블 상품명 특수문자 치환 시작");
        
        try {
            samsungOrderGoodsCommandMapper.replaceGdsNm40();
            log.debug("&#40; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceGdsNm41();
            log.debug("&#41; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceGdsNmQuot();
            log.debug("&quot; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceGdsNm39();
            log.debug("&#39; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceGdsNm35();
            log.debug("&#35; 치환 완료");
            
            samsungOrderGoodsCommandMapper.replaceGdsNmAmp();
            log.debug("&amp; 치환 완료");
            
            log.info("주문상품상세 테이블 상품명 특수문자 치환 완료");
        } catch (Exception e) {
            log.error("주문상품상세 테이블 상품명 특수문자 치환 실패: {}", e.getMessage(), e);
            throw e;
        }
    }
}
