package com.ezwelesp.batch.lgportal.order.chunk.reader;

import com.ezwelesp.batch.lgportal.entity.ApiBatchSendEntity;
import com.ezwelesp.batch.lgportal.order.mapper.query.ApiBatchQueryMapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@StepScope
public class OrderCancelOstkItemReader extends MyBatisPagingItemReader<ApiBatchSendEntity> {

    public OrderCancelOstkItemReader(@Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory sqlSessionFactory) {

        this.setName(this.getName());
        this.setSqlSessionFactory(sqlSessionFactory);
        this.setQueryId(ApiBatchQueryMapper.class.getName() + ".selectOrderCancelGoodsOstkList");
    }
}
