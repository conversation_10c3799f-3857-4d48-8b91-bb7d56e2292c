<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgconfirm.zeropay.mapper.query.PgDailyErrorQueryMapper">

	<select id="selectZeropayApplyDate" resultType="HashMap">
		select
			to_char(to_date(max(substr(confirm_dt,0,14)),'YYYYMMDDHH24MISS'),'YYYYMMDD') as max_confirm_dt, 
    		to_char(to_date(min(substr(confirm_dt,0,14)),'YYYYMMDDHH24MISS'),'YYYYMMDD') as min_confirm_dt
		from ez_if.ez_ec_order_zeropay
		where reg_dt like to_char(now(),'YYYYMMDD')||'%'	
	</select>
	
</mapper>