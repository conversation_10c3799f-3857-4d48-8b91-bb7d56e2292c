package com.ezwelesp.batch.hims.order.goodsflow.job;

import com.ezwelesp.batch.hims.order.goodsflow.tasklet.GoodsFlowDeleteOldDataTasklet;
import com.ezwelesp.batch.hims.order.goodsflow.tasklet.GoodsFlowSendTraceRequestTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 굿스플로 1년 전 I/F 데이터 삭제 [BA_HIOR00141]
 *  - ez_if.gikimimast, detail, result 삭제
 */
@Configuration
@RequiredArgsConstructor
public class GoodsFlowDeleteOldDataJobConfig {
    private final CommonJobListener commonJobListener;
    private final GoodsFlowDeleteOldDataTasklet goodsFlowDeleteOldDataTasklet;

    @Bean("BA_HIOR00141_2")
    public Job goodsFlowDeleteOldDataJob(JobRepository jobRepository, @Qualifier("BA_HIOR00141_2_STEP") Step goodsFlowDeleteOldDataStep) {
        return new JobBuilder("BA_HIOR00141_2", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(goodsFlowDeleteOldDataStep)
                .build();
    }

    @Bean("BA_HIOR00141_2_STEP")
    public Step goodsFlowDeleteOldDataStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00141_2_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(goodsFlowDeleteOldDataTasklet, transactionManager)
                .build();
    }
}
