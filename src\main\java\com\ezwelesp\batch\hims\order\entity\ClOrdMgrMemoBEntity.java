package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 주문관리자메모기본(ez_or.cl_ord_mgr_memo_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClOrdMgrMemoBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문관리자메모순번(ord_mgr_memo_seq) not null
     */
    private Long ordMgrMemoSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq)
     */
    private Long ordGdsSeq;

    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * 주문메모종류코드(ord_memo_knd_cd)
     */
    private String ordMemoKndCd;

    /**
     * 배송상태코드(dlv_st_cd)
     */
    private String dlvStCd;

    /**
     * 관리자메모(mgr_memo)
     */
    private String mgrMemo;

    /**
     * 관리자명(mgr_nm)
     */
    private String mgrNm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
