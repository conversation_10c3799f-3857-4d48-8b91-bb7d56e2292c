package com.ezwelesp.batch.hims.order.pgcompare.hpointpay.tasklet;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.order.pgcompare.hpointpay.service.HpointpayService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class HpointpayTasklet implements Tasklet {

	private final HpointpayService hpointpayService;
	
	
	@Override
	public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
		
		if("BA_HIOR00017_STEP1".equals(contribution.getStepExecution().getStepName())) {
			log.info("############## 현대포인트 결제수집 START ##############");
			this.hpointpayService.callHpointpayGatherData();
			log.info("############## 현대포인트 결제수집 END ##############");
		}else if("BA_HIOR00017_STEP2".equals(contribution.getStepExecution().getStepName())) {
			log.info("############## 현대포인트 결제대사 START ##############");
			this.hpointpayService.callHpointpayCompareData();
			log.info("############## 현대포인트 결제대사 END ##############");
		}
		return RepeatStatus.FINISHED;
	}
}
