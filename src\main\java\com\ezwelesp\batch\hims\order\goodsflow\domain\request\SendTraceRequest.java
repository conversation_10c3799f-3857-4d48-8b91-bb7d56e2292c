package com.ezwelesp.batch.hims.order.goodsflow.domain.request;

import com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowSendTraceRequestDto;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 배송추적 요청 request (SendTraceRequest API)
 */
@Getter
@Setter
@Builder
@ToString
public class SendTraceRequest {
    private SendTraceRequestItem data;

    @Getter
    @Setter
    @Builder
    @ToString
    public static class SendTraceRequestItem {
        private List<GoodsFlowSendTraceRequestDto> items;
    }
}
