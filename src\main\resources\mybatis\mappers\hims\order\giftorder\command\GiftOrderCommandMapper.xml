<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.giftorder.mapper.command.GiftOrderCommandMapper">

    <update id="updateGvgftOrdB">
        UPDATE ez_or.or_gvgft_ord_b
           SET gvgft_ord_st_cd = #{gvgftOrdStCd}
             , last_mod_dtm = TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
             , last_mod_usr_id = 'BATCH'
             , last_mod_pgm_id = 'BATCH'
         WHERE gvgft_ord_no = #{gvgftOrdNo}
    </update>

    <insert id="insertGvgftOrdBHistory">
        INSERT INTO ez_or.or_gvgft_ord_b_h (
              gvgft_ord_no
            , gvgft_ord_his_seq
            , asp_ord_no
            , gvgft_ord_knd_cd
            , gvgft_ord_st_cd
            , gvgft_ord_cncl_magn_cd
            , rcvr_nm
            , ordr_nm
            , rcvr_mbl_telno
            , prsn_msg_cntn
            , img_path1
            , img_path2
            , apitp_yn
            , frst_reg_dtm
            , frst_reg_usr_id
            , frst_reg_pgm_id
            , last_mod_dtm
            , last_mod_usr_id
            , last_mod_pgm_id
        )
        SELECT gvgft_ord_no
             , (SELECT COALESCE(MAX(gvgft_ord_his_seq), 0) + 1  FROM ez_or.or_gvgft_ord_b WHERE gvgft_ord_no = #{gvgftOrdNo}) AS gvgft_ord_his_seq
             , asp_ord_no
             , gvgft_ord_knd_cd
             , gvgft_ord_st_cd
             , gvgft_ord_cncl_magn_cd
             , rcvr_nm
             , ordr_nm
             , rcvr_mbl_telno
             , prsn_msg_cntn
             , img_path1
             , img_path2
             , apitp_yn
             , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
             , 'BATCH'
             , 'BATCH'
             , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
             , 'BATCH'
             , 'BATCH'
          FROM ez_or.or_gvgft_ord_b
         WHERE gvgft_ord_no = #{gvgftOrdNo}
    </insert>
</mapper>
