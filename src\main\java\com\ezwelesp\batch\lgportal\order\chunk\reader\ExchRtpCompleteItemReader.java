package com.ezwelesp.batch.lgportal.order.chunk.reader;

import com.ezwelesp.batch.lgportal.entity.ApiBatchChgReturnEntity;
import com.ezwelesp.batch.lgportal.order.mapper.query.ApiBatchQueryMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@StepScope
public class ExchRtpCompleteItemReader extends MyBatisPagingItemReader<ApiBatchChgReturnEntity> {

    public ExchRtpCompleteItemReader(@Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory sqlSessionFactory) {

        this.setName(this.getName());
        this.setSqlSessionFactory(sqlSessionFactory);
        this.setQueryId(ApiBatchQueryMapper.class.getName() + ".selectExchRtpCompleteList");

    }
}
