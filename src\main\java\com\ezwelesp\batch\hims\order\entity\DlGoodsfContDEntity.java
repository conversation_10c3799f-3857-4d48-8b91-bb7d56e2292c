package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 굿스플로계약상세(ez_or.dl_goodsf_cont_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlGoodsfContDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 굿스플로계약순번(goodsf_cont_seq) not null
     */
    private Long goodsfContSeq;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 협력사출고위치번호(csp_obnd_loc_no) not null
     */
    private String cspObndLocNo;

    /**
     * 배송회사코드(dlv_co_cd) not null
     */
    private String dlvCoCd;

    /**
     * 상자규격코드(box_spc_cd) not null
     */
    private String boxSpcCd;

    /**
     * 선불배송비용(prpy_dlv_exp) not null
     */
    private BigDecimal prpyDlvExp;

    /**
     * 신용배송비용(crdt_dlv_exp) not null
     */
    private BigDecimal crdtDlvExp;

    /**
     * 착불배송비용(arpay_dlv_exp) not null
     */
    private BigDecimal arpayDlvExp;

    /**
     * 반품배송비용(rtp_dlv_exp) not null
     */
    private BigDecimal rtpDlvExp;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
