package com.ezwelesp.batch.hpas.alarm.service.impl;

import java.util.List;
import org.springframework.stereotype.Service;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreBenefitDto;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreClosedDto;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreUnregisterDto;
import com.ezwelesp.batch.hpas.alarm.mapper.query.EzMemberStoreQueryMapper;
import com.ezwelesp.batch.hpas.alarm.service.EzMemberStoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class EzMemberStoreServiceImpl implements EzMemberStoreService {

    private final EzMemberStoreQueryMapper ezMemberStoreQueryMapper;

    @Override
    public List<EzMemberStoreUnregisterDto> getEzMembersStoreUnregisterList() {
        return ezMemberStoreQueryMapper.selectEzMembersStoreUnregisterList();
    }

    @Override
    public List<EzMemberStoreClosedDto> getEzMemberStoreClosedList(EzMemberStoreClosedDto ezMemberStoreClosedDto) {
        return ezMemberStoreQueryMapper.selectEzMembersStoreClosedList(ezMemberStoreClosedDto);
    }

    @Override
    public List<EzMemberStoreBenefitDto> getEzMemberStoreBenefitList(EzMemberStoreBenefitDto ezMemberStoreBenefitDto) {
        return ezMemberStoreQueryMapper.selectEzMembersStoreBenefitList(ezMemberStoreBenefitDto);
    }

}
