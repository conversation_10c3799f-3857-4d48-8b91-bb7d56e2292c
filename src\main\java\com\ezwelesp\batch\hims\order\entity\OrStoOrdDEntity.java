package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 정기구독주문상세(ez_or.or_sto_ord_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrStoOrdDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 정기구독번호(sto_no) not null
     */
    private String stoNo;

    /**
     * 정기구독주문차수(sto_ord_nos) not null
     */
    private Long stoOrdNos;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 주문일시(ord_dtm) not null
     */
    private String ordDtm;

    /**
     * 정기구독결제결과메시지내용(sto_pymt_rslt_msg_cntn)
     */
    private String stoPymtRsltMsgCntn;

    /**
     * PG종류코드(pg_knd_cd)
     */
    private String pgKndCd;

    /**
     * PG가맹점번호(pg_frcs_no)
     */
    private String pgFrcsNo;

    /**
     * 암호화PG자동결제번호(enc_pg_auto_pymt_no)
     */
    private String encPgAutoPymtNo;

    /**
     * 결제수단코드(pymt_mns_cd) not null
     */
    private String pymtMnsCd;

    /**
     * 결제상태코드(pymt_st_cd) not null
     */
    private String pymtStCd;

    /**
     * 선택적복지포인트결제금액(wfp_pymt_amt) not null
     */
    private BigDecimal wfpPymtAmt;

    /**
     * 카드결제금액(crd_pymt_amt) not null
     */
    private BigDecimal crdPymtAmt;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * 배송비용(dlv_exp) not null
     */
    private BigDecimal dlvExp;

    /**
     * 쿠폰번호(cpn_no)
     */
    private String cpnNo;

    /**
     * 사용자쿠폰번호(usr_cpn_no)
     */
    private Long usrCpnNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
