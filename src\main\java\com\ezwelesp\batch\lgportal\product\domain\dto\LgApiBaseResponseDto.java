package com.ezwelesp.batch.lgportal.product.domain.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgApiBaseResponseDto")
@Getter
@SuperBuilder
public class LgApiBaseResponseDto {
    private String result;                 // 결과 코드
    private String resultMessage;          // 결과 메시지
    private String responseType;           // 결과 응답 구분 값
}
