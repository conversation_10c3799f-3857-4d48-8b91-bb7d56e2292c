package com.ezwelesp.batch.lgportal.order.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

/**
  * 주문조회 요청 시 사용 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see OrderListReqDto
  */
@SuperBuilder(toBuilder = true)
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderListReqDto {

    private String apiKey;

    @Builder.Default
    private String orderStatusCode = "ORD1032";     // 주문완료

    @Builder.Default
    private String deliveryStatusCode = "ACC1022";  // 배송상태코드(배송준비중)

    private String deliveryStatusCodeChanged_above; // 배송상태변경시작일
    private String deliveryStatusCodeChanged_below; // 배송상태변경종료일

    private String salesOrderNo;                    // 판매사 주문번호

    @Builder.Default
    private int pageSize = 99999;                   // 페이지사이즈



}
