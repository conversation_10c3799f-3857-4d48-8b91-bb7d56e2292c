package com.ezwelesp.batch.lgportal.order.job;

import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.tasklet.ExchRtpTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class ExchRtpListJobConfig {
    private final String REG_EXCH_RTP_STEP = LgConstants.JobName.JOB_REG_EXCH_RTP + "_STEP";

    private final ExchRtpTasklet exchRtpTasklet;
    private final CommonJobListener commonJobListener;

    @Bean(LgConstants.JobName.JOB_REG_EXCH_RTP)
    public Job exchRtpJob(JobRepository jobRepository, @Qualifier(REG_EXCH_RTP_STEP) Step orderListStep) {
        return new JobBuilder(LgConstants.JobName.JOB_REG_EXCH_RTP, jobRepository)
                .listener(commonJobListener)
                .start(orderListStep)
                .build();
    }

    @Bean(name = REG_EXCH_RTP_STEP)
    public Step exchRtpStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder(REG_EXCH_RTP_STEP, jobRepository)
                .allowStartIfComplete(true)
                .tasklet(exchRtpTasklet, transactionManager)
                .build();
    }
}
