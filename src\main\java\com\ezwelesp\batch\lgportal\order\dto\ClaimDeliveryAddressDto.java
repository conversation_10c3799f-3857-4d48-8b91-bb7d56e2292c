package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * 클레임 배송주소기본 저장
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto.ClaimDeliveryAddressDto
 * @since 2025.02.27
 */
@SuperBuilder(toBuilder = true)
@Getter
public class ClaimDeliveryAddressDto implements Serializable {

    // 회수 주소 여부
    private boolean withdrawAddress;

    // 수신자명
    private String rcvrNm;

    // 실제수신자명
    private String realRcvrNm;

    // 수신자이메일주소
    private String rcvrEmlAdr;

    // 수신자전화번호
    private String rcvrTelno;

    // 수신자모바일전화번호
    private String rcvrMblTelno;

    // 수신자우편번호
    private String rcvrZipcd;

    // 수신자기본주소
    private String rcvrBasAdr;

    // 수신자상세주소
    private String rcvrDtlAdr;
}
