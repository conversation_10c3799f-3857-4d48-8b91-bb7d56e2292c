package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 클레임사유내역(ez_or.cl_clm_rsn_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClClmRsnLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 클레임상품순번(clm_gds_seq) not null
     */
    private Long clmGdsSeq;

    /**
     * 클레임사유코드(clm_rsn_cd) not null
     */
    private String clmRsnCd;

    /**
     * 클레임사유내용(clm_rsn_cntn)
     */
    private String clmRsnCntn;

    /**
     * 관리자메모(mgr_memo)
     */
    private String mgrMemo;

    /**
     * 클레임귀책사유주체코드(clm_atbr_magn_cd) not null
     */
    private String clmAtbrMagnCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
