package com.ezwelesp.batch.hims.order.reseller.service.impl;

import com.ezwelesp.batch.hims.order.reseller.domain.SuspectedResellerOrderDto;
import com.ezwelesp.batch.hims.order.reseller.mapper.command.SuspectedResellerOrderNotificationCommandMapper;
import com.ezwelesp.batch.hims.order.reseller.mapper.query.SuspectedResellerOrderNotificationQueryMapper;
import com.ezwelesp.batch.hims.order.reseller.service.SuspectedResellerOrderNotificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.reseller.service.impl
 * @since 2025.05.19
 */
@RequiredArgsConstructor
@Service
public class SuspectedResellerOrderNotificationServiceImpl implements SuspectedResellerOrderNotificationService {
    private final SuspectedResellerOrderNotificationCommandMapper commandMapper;
    private final SuspectedResellerOrderNotificationQueryMapper queryMapper;

    @Override
    public List<SuspectedResellerOrderDto> getSuspectedResellerByGdsOneDay() {
        return queryMapper.selectSuspectedResellerByGdsOneDay();
    }

    @Override
    public List<SuspectedResellerOrderDto> getSuspectedResellerByOrder(String searchTermType) {
        return queryMapper.selectSuspectedResellerByOrder(searchTermType);
    }
}
