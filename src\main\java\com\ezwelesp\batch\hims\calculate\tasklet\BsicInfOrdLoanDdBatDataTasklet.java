package com.ezwelesp.batch.hims.calculate.tasklet;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.calculate.mapper.command.BsicInfOrdLoanDdBatCommandMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatDataTasklet implements Tasklet {
    private final BsicInfOrdLoanDdBatCommandMapper bsicInfOrdLoanDdBatCommandMapper;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        // 일상배치 제외여부 가져오기
        String iMoveYn = Optional.ofNullable(chunkContext.getStepContext().getJobParameters().get("iMoveYn"))
                                .map(Object::toString)
                                .filter(s -> !s.isBlank())
                                .orElse(null);
        
        Map <String, Object> parameter = new HashMap<>();
        if (iMoveYn == null) {
            parameter.put("iMoveYn", "N");
        } else {
            parameter.put("iMoveYn", iMoveYn);
        }
        
        bsicInfOrdLoanDdBatCommandMapper.callBsicInfOrdLoanDdBatDataProc(parameter);
        
        return RepeatStatus.FINISHED;
    }
}