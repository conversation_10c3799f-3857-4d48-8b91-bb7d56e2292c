package com.ezwelesp.batch.aasample.test.mapper.command;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserCommandMapper {
    void updateNtcTitl(@Param("ntcTitl") String ntcTitl, @Param("ntcId") Long ntcId);

    void insertNtc(@Param("name") String ntcTitl);
    
    void updateNtcStat(@Param("ntcStat") String ntcStat, @Param("ntcId") Long ntcId);
}