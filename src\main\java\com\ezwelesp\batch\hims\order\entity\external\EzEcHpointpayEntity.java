package com.ezwelesp.batch.hims.order.entity.external;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.experimental.SuperBuilder;

/**
 * PG결제대사모니터링내역
 * ez_ec_order_pay_log
 */
@Data
@SuperBuilder(toBuilder=true)
public class EzEcHpointpayEntity implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * Toss 고유키
	 */
	private String tossKey;
	/**
	 * 결제 거래번호
	 */
	private String authcode;
	/**
	 * 상점ID
	 */
	private String storeId;
	/**
	 * 상점 주문번호
	 */
	private String tossOrderNum;
	/**
	 * 거래유형(A:승인, C:취소, P:부분취소)
	 */
	private String payType;
	/**
	 * 결제수단(CARD 또는 ACCOUNT)
	 */
	private String payMethod;
	/**
	 * 승인/취소 금액
	 */
	private BigDecimal payAmt;
	/**
	 * 승인/취소 일자
	 */
	private String confirmDt;
	/**
	 * 등록일
	 */
	private String regDt;
}
