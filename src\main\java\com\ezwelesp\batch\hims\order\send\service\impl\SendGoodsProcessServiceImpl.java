package com.ezwelesp.batch.hims.order.send.service.impl;

import com.ezwelesp.batch.hims.order.entity.DlOrdSndGdsDEntity;
import com.ezwelesp.batch.hims.order.entity.DlOrdSndGdsDHEntity;
import com.ezwelesp.batch.hims.order.send.domain.CspSendDto;
import com.ezwelesp.batch.hims.order.send.domain.EzwelSendDto;
import com.ezwelesp.batch.hims.order.send.domain.SendGoodsBaseDto;
import com.ezwelesp.batch.hims.order.send.domain.SendResponse;
import com.ezwelesp.batch.hims.order.send.mapper.command.SendGoodsProcessCommandMapper;
import com.ezwelesp.batch.hims.order.send.mapper.query.SendGoodsProcessQueryMapper;
import com.ezwelesp.batch.hims.order.send.service.CspSendManageService;
import com.ezwelesp.batch.hims.order.send.service.EzwelSendManageService;
import com.ezwelesp.batch.hims.order.send.service.SendGoodsProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ezwelesp.batch.hims.order.config.EcpnConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class SendGoodsProcessServiceImpl implements SendGoodsProcessService {
    private final EzwelSendManageService ezwelSendManageService;
    private final CspSendManageService cspSendManageService;
    private final SendGoodsProcessQueryMapper sendGoodsProcessQueryMapper;
    private final SendGoodsProcessCommandMapper sendGoodsProcessCommandMapper;

    @Override
    public List<SendGoodsBaseDto> selectSendGoodsTarget() {
        return sendGoodsProcessQueryMapper.selectSendGoodsTarget();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void sendCsp(SendGoodsBaseDto param) {
        SendResponse response;
        try {
            response = this.cspSendManageService.send(CspSendDto.builder()
                    .cspCd(param.getCspCd())
                    .cspGdsCd(param.getCspGdsCd())
                    .ordrNm(param.getOrdrNm())
                    .optGdsCombSeq(param.getOptGdsCombSeq())
                    .apiCallCount(param.getApiCallCount())
                    .clntCd(param.getClntCd())
                    .rcvrNm(param.getRcvrNm())
                    .rcvrMblTelno(param.getRcvrMblTelno())
                    .ordNo(param.getOrdNo())
                    .ordSndGdsDtlSeq(param.getOrdSndGdsDtlSeq())
                    .gdsCd(param.getGdsCd())
                    .intgGdsInfmSndTypCntn(param.getIntgGdsInfmSndTypCntn())
                    .build());
        } catch (Exception e) {
            log.error("협력사발송 에러: {}", e.getMessage());
            response = SendResponse.builder()
                    .success(false)
                    .message(String.format("협력사발송 에러: %s", e.getMessage()))
                    .build();
        }

        afterProcess(param, response);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void sendEzwel(SendGoodsBaseDto param) {
        SendResponse response;
        try {
            response = this.ezwelSendManageService.send(EzwelSendDto.builder()
                    .rcvrNm(param.getRcvrNm())
                    .rcvrMblTelno(param.getRcvrMblTelno())
                    .ordNo(param.getOrdNo())
                    .clntCd(param.getClntCd())
                    .rdnoSndMagnCd(param.getRdnoSndMagnCd())
                    .tmsgSndKndCd(param.getTmsgSndKndCd())
                    .intgGdsRdnoNo(param.getIntgGdsRdnoNo())
                    .gdsCd(param.getGdsCd())
                    .gdsNm(param.getGdsNm())
                    .gdsImg(param.getGdsImg())
                    .ordGdsOptCntn(param.getOrdGdsOptCntn())
                    .intgGdsInfmSndTypCntn(param.getIntgGdsInfmSndTypCntn())
                    .bcdExpsYn(param.getBcdExpsYn())
                    .bcdTypCd(param.getBcdTypCd())
                    .build());
        } catch (Exception e) {
            log.error("이지웰발송 에러: {}", e.getMessage());
            response = SendResponse.builder()
                    .success(false)
                    .message(String.format("이지웰발송 에러: %s", e.getMessage()))
                    .build();
        }

        afterProcess(param, response);
    }

    /**
     * 발송 후처리
     */
    private void afterProcess(SendGoodsBaseDto param, SendResponse response) {
        // 발송상품상세 이력 등록
        sendGoodsProcessCommandMapper.insertSendGoodsHistory(DlOrdSndGdsDHEntity.builder()
                .ordNo(param.getOrdNo())
                .ordSndSeq(param.getOrdSndSeq())
                .ordGdsSeq(param.getOrdGdsSeq())
                .ordSndGdsDtlSeq(param.getOrdSndGdsDtlSeq())
                .ordSndGdsDtlChgReqCd("06")
                .errMsgCntn(response.getMessage())
                .ecpnOrdStCd("04") // 쿠폰발송
                .build());

        // 발송상품상세 상태 업데이트
        sendGoodsProcessCommandMapper.updateSendGoodsDetailStatus(DlOrdSndGdsDEntity.builder()
                .ordNo(param.getOrdNo())
                .ordSndSeq(param.getOrdSndSeq())
                .ordGdsSeq(param.getOrdGdsSeq())
                .ordSndGdsDtlSeq(param.getOrdSndGdsDtlSeq())
                .ordSndStCd(response.isSuccess() ? OrdSndStCdEnum.CMPT.getCode() : OrdSndStCdEnum.ERROR.getCode())
                .ecpnCspReqInntNo(response.getEcpnCspReqInntNo()) // 협력사발송시 연동 key
                .build());
    }

}
