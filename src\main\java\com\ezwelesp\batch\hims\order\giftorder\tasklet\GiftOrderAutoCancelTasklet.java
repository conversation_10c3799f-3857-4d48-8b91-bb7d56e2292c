package com.ezwelesp.batch.hims.order.giftorder.tasklet;

import com.ezwelesp.batch.hims.order.giftorder.service.GiftOrderAutoCancelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.tasklet
 * @since 2025.05.15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GiftOrderAutoCancelTasklet implements Tasklet {
    private final GiftOrderAutoCancelService giftOrderAutoCancelService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        List<Long> gvgftOrdNoList = giftOrderAutoCancelService.getGiftOrderAutoCancelTargetOrdNoList();

        if (gvgftOrdNoList.isEmpty()) {
            log.debug("GiftOrderAutoCancelTasklet 대상 없음");
        } else {
            for (Long gvgftOrdNo : gvgftOrdNoList) {
                try {
                    giftOrderAutoCancelService.cancelOrder(gvgftOrdNo);
                } catch (Exception e) {
                    log.error("GiftOrderAutoCancelTasklet Failed: {}", e.getMessage(), e);
                }
            }
        }

        return RepeatStatus.FINISHED;
    }
}
