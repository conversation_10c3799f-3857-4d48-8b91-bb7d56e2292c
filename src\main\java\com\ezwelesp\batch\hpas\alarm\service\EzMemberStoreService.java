package com.ezwelesp.batch.hpas.alarm.service;

import java.util.List;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreBenefitDto;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreClosedDto;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreUnregisterDto;

public interface EzMemberStoreService {

    List<EzMemberStoreUnregisterDto> getEzMembersStoreUnregisterList();

    List<EzMemberStoreClosedDto> getEzMemberStoreClosedList(EzMemberStoreClosedDto ezMemberStoreClosedDto);

    List<EzMemberStoreBenefitDto> getEzMemberStoreBenefitList(EzMemberStoreBenefitDto ezMemberStoreBenefitDto);

}
