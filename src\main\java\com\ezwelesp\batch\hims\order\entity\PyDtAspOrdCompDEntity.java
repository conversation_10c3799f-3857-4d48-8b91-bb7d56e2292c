package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 일자별제휴사주문비교상세(ez_or.py_dt_asp_ord_comp_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyDtAspOrdCompDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 제휴사주문비교번호(asp_ord_comp_no) not null
     */
    private Long aspOrdCompNo;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 제휴사주문번호(asp_ord_no) not null
     */
    private String aspOrdNo;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 주문금액(ord_amt) not null
     */
    private BigDecimal ordAmt;

    /**
     * 주문상태코드(ord_st_cd)
     */
    private String ordStCd;

    /**
     * 주문일시(ord_dtm)
     */
    private String ordDtm;

    /**
     * 선택적복지포인트결제금액(wfp_pymt_amt) not null
     */
    private BigDecimal wfpPymtAmt;

    /**
     * 특별포인트결제금액(spp_pymt_amt) not null
     */
    private BigDecimal sppPymtAmt;

    /**
     * 적립금결제금액(mlg_pymt_amt) not null
     */
    private BigDecimal mlgPymtAmt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
