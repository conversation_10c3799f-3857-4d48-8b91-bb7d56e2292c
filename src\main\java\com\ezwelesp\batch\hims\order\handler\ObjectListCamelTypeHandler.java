package com.ezwelesp.batch.hims.order.handler;


import com.ezwelesp.batch.hims.order.utils.OrderJsonUtils;
import com.ezwelesp.framework.utils.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;


/**
 * mybatis snake List object handler
 *
 * @param <T>
 */
public class ObjectListCamelTypeHandler<T> extends BaseTypeHandler<List<T>> {


    private Class<T> clazz;

    public ObjectListCamelTypeHandler(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType)
            throws SQLException {
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getList(rs.getString(columnName));
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getList(rs.getString(columnIndex));
    }

    @Override
    public List<T> getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {

        return getList(cs.getString(columnIndex));
    }


    public List<T> getList(String arr) {

        if (StringUtils.isNotEmpty(arr)) {
            return OrderJsonUtils.deserializeCamelArray(arr, this.clazz);
        }
        else {
            return new ArrayList<T>();
        }

    }


}
