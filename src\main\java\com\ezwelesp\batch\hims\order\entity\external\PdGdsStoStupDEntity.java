package com.ezwelesp.batch.hims.order.entity.external;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PdGdsStoStupDEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = -610850281541932672L;

    /**
     * 상품코드
     */
    private String gdsCd;

    /**
     * 상품정기구독옵션구분코드
     */
    private String gdsStoOptDivCd;

    /**
     * 상품정기구독옵션코드
     */
    private String gdsStoOptCd;
    /**
     * 최초등록일시
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID
     */
    private String lastModPgmId;
}
