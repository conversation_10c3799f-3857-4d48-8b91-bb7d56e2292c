package com.ezwelesp.batch.hpas.alarm.mapper.query;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreBenefitDto;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreClosedDto;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreUnregisterDto;
import com.ezwelesp.batch.hpas.alarm.dto.MonitorJobsDto;

@Mapper
public interface EzMemberStoreQueryMapper {
    List<EzMemberStoreUnregisterDto> selectEzMembersStoreUnregisterList();

    List<EzMemberStoreClosedDto> selectEzMembersStoreClosedList(EzMemberStoreClosedDto ezMemberStoreClosedDto);

    List<EzMemberStoreBenefitDto> selectEzMembersStoreBenefitList(EzMemberStoreBenefitDto ezMemberStoreClosedDto);

    MonitorJobsDto selectMonitorJobList(String jobId);
}
