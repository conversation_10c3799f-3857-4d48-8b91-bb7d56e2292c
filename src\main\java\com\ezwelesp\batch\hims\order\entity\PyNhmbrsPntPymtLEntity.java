package com.ezwelesp.batch.hims.order.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * NH멤버스포인트결제내역(ez_or.py_nhmbrs_pnt_pymt_l)
 */
@Data
public class PyNhmbrsPntPymtLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 결제번호(pymt_no) not null
     */
    private String pymtNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * NH멤버스결제구분코드(nhmbrs_pymt_div_cd) not null
     */
    private String nhmbrsPymtDivCd;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 가족회원번호(fam_mem_no)
     */
    private String famMemNo;

    /**
     * NH멤버스사용자고유번호(nhmbrs_user_key) not null
     */
    private String nhmbrsUserKey;

    /**
     * 거래금액(trd_amt) not null
     */
    private BigDecimal trdAmt;

    /**
     * 결제포인트(pymt_pnt) not null
     */
    private BigDecimal pymtPnt;

    /**
     * 정산일자(stl_dt) not null
     */
    private String stlDt;

    /**
     * 정산취소일자(stl_cncl_dt)
     */
    private String stlCnclDt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
