package com.ezwelesp.batch.hpas.alarm.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import com.ezwelesp.batch.hpas.alarm.tasklet.GoodsFlowCspDataSyncMainTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class GoodsFlowCspDataSyncMainJobConfig {
    private final CommonJobListener commonJobListener;
    private final GoodsFlowCspDataSyncMainTasklet goodsFlowCspDataSyncMainTasklet;

    @Bean("goodsFlowCspDataSyncMainJob")
    public Job goodsFlowCspDataSyncMainJob(JobRepository jobRepository,
            @Qualifier("goodsFlowCspDataSyncMainStep") Step goodsFlowCspDataSyncMainStep) {
        return new JobBuilder("goodsFlowCspDataSyncMainJob", jobRepository)
                .listener(commonJobListener)
                .start(goodsFlowCspDataSyncMainStep)
                .build();
    }

    @Bean("goodsFlowCspDataSyncMainStep")
    public Step goodsFlowCspDataSyncMainStep(JobRepository jobRepository,
            DataSourceTransactionManager transactionManager) {
        return new StepBuilder("goodsFlowCspDataSyncMainStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(goodsFlowCspDataSyncMainTasklet, transactionManager)
                .build();

    }
}
