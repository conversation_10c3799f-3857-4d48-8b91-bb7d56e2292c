package com.ezwelesp.batch.hpas.alarm.service.impl;

import java.util.List;
import org.springframework.stereotype.Service;
import com.ezwelesp.batch.hpas.alarm.dto.EntryCspMainDto;
import com.ezwelesp.batch.hpas.alarm.mapper.query.EntryCspMainQueryMapper;
import com.ezwelesp.batch.hpas.alarm.service.EntryCspMainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class EntryCspMainServiceImpl implements EntryCspMainService {
    private final EntryCspMainQueryMapper entryCspMainQueryMapper;

    @Override
    public int getEntryCspMainLoginCount() {
        return entryCspMainQueryMapper.selectEntryCspMainLoginCount();
    }

    @Override
    public List<EntryCspMainDto> getEntryCspMainLoginList(int pageInt) {
        return entryCspMainQueryMapper.selectEntryCspMainLoginList(pageInt);
    }

    @Override
    public void sendEmailEntryCspMainLogin(EntryCspMainDto entryCspMain) {
        log.info("#############################################");
        log.info("### [입점 승인 후 3일간 시스템 미접속 안내메일 발송] 은 공통기능 제공받아 개발 예정");
        log.info("#############################################");
        this.sendEmailEntryCspMainLogin_csp(entryCspMain);
        this.sendEmailEntryCspMainLogin_mgr(entryCspMain);
    }

    public void sendEmailEntryCspMainLogin_csp(EntryCspMainDto entryCspMain) {
        // sadmin_entryCsp_template_06_1.html
        log.info("협력사 이메일발송 : " + entryCspMain.getCspCd() + " / " + entryCspMain.getCspNm());
    }

    public void sendEmailEntryCspMainLogin_mgr(EntryCspMainDto entryCspMain) {
        // sadmin_entryCsp_template_06_2.html
        log.info("MD 이메일발송 : " + entryCspMain.getCspCd() + " / " + entryCspMain.getCspNm());
    }


    @Override
    public int getEntryCspMainGoodsCount() {
        return entryCspMainQueryMapper.selectEntryCspMainLoginCount();
    }

    @Override
    public List<EntryCspMainDto> getEntryCspMainGoodsList(int pageInt) {
        return entryCspMainQueryMapper.selectEntryCspMainLoginList(pageInt);
    }

    @Override
    public void sendEmailEntryCspMainGoods(EntryCspMainDto entryCspMain) {
        log.info("#############################################");
        log.info("### [입점 승인 후 7일간 상품 미등록 안내메일 발송] 은 공통기능 제공받아 개발 예정");
        log.info("#############################################");
        this.sendEmailEntryCspMainGoods_csp(entryCspMain);
        this.sendEmailEntryCspMainGoods_mgr(entryCspMain);
    }

    public void sendEmailEntryCspMainGoods_csp(EntryCspMainDto entryCspMain) {
        // sadmin_entryCsp_template_07_1.html
        log.info("협력사 이메일발송 : " + entryCspMain.getCspCd() + " / " + entryCspMain.getCspNm());
    }

    public void sendEmailEntryCspMainGoods_mgr(EntryCspMainDto entryCspMain) {
        // sadmin_entryCsp_template_07_2.html
        log.info("MD 이메일발송 : " + entryCspMain.getCspCd() + " / " + entryCspMain.getCspNm());
    }
}
