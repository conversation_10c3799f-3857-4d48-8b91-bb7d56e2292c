package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 숙박주문오류로그(ez_or.or_ldg_ord_err_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrLdgOrdErrGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 숙박주문오류로그순번(ldg_ord_err_log_seq) not null
     */
    private Long ldgOrdErrLogSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 숙박주문오류종류코드(ldg_ord_err_knd_cd)
     */
    private String ldgOrdErrKndCd;

    /**
     * 오류메시지내용(err_msg_cntn)
     */
    private String errMsgCntn;

    /**
     * 요청파라미터내용(req_para_cntn)
     */
    private String reqParaCntn;

    /**
     * 숙박주문서비스종류코드(ldg_ord_srv_knd_cd)
     */
    private String ldgOrdSrvKndCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
