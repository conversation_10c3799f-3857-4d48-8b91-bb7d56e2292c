package com.ezwelesp.batch.hims.order.goodsflow.domain;

import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class GoodsFlowSendTraceRequestDto {
    private Long mid;
    private String transUniqueCode;
    private String sectionCode;
    private String sellerCode;
    private String sellerName;
    private String fromName;
    private String toName;
    private String toMobile;
    private String logisticsCode;
    private String invoiceNo;
    private String dlvretType;
    private String invoicePrintDate;
    private String defCode1;
    private String defCode2;
    private List<GoodsFlowSendTraceRequestDetailDto> requestDetails;
}
