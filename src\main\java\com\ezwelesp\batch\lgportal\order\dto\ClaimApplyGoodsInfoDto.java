package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 클레임신청 상품정보 DTO
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto
 * @since 2025.04.12
 */
@Jacksonized
@Getter
@SuperBuilder(toBuilder = true)
@ToString
public class ClaimApplyGoodsInfoDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 6726526251018005021L;

    // 주문상품순번
    private Long ordGdsSeq;

    // 클레임상품수량
    private Integer clmGdsQty;

    // 배송번호
    private String dlvNo;

   // 배송상품순번
    private Long dlvGdsSeq;

    // 주문번호"
    private String ordNo;

    // 클레임가능상품수량
    private int clmPossGdsQty;

    // 배송상품수량
    private int dlvGdsQty;

    // 상품판매가격
    private BigDecimal gdsSellPrc;

    // 옵션판매가격
    private BigDecimal optSellPrc;

    // 주문발송순번
    private Long ordSndSeq;

    // 주문발송상품상세순번
    private Long ordSndGdsDtlSeq;

    // 주문상품유형
    private String ordGdsTyp;

    // 클레임사유코드
    private String clmRsnCd;
}
