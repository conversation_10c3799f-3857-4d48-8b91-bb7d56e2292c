package com.ezwelesp.batch.hpas.alarm.dto;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * 협력사에 상품최저가 관리 안내SMS 발송 DTO
 * 
 * <AUTHOR>
 * @since 2025.05.30
 */
@Data
public class LowPriceSmsMainDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String cspCd;               // 협력사코드
    private String cspNm;               // 협력사명
    private String chrgrTelno;          // 담당자전화번호

    private String allGdsCnt;           // 전체상품건수
    private String lwprCnftObjGdsCnt;   // 최저가확인대상상품건수
    private String lwprCnftGdsCnt;      // 최저가확인상품건수
    private String prcAdjNeedGdsCnt;    // 가격조정필요상품건수
    private String lwprCnftNdmtGdsCnt;  // 최저가확인불가상품건수
    private String ipdSellGdsCnt;       // 단독판매상품건수
    private String newRegGdsCnt;        // 신규등록상품건수
    private String frstRegDtm;          // 최초등록일시(yyyymmddhhmiss)
}

