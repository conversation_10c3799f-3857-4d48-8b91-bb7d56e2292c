<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.goodsflow.mapper.query.GoodsFlowTraceQueryMapper">
    <sql id="sqlGoodsfDlvIntlColumn">
        dlv_intl_innt_no
        , dlv_intl_insr_div_cd, dlv_no, csp_nm, goodsf_dlv_co_cd, invc_no, invc_no_reg_dtm, sndr_nm, ordr_nm, sndr_zipcd
        , sndr_bas_adr, sndr_dtl_adr, sndr_telno1, sndr_telno2, rcvr_nm, rcvr_zipcd, rcvr_bas_adr, rcvr_dtl_adr, rcvr_telno1
        , rcvr_telno2, bas_fare_budn_magn_cd, user_key, dlv_intl_ord_innt_no, goodsf_innt_dlv_no, gds_cd, ord_gds_seq, gds_nm
        , ord_gds_opt_cntn, ord_qty, gds_prc, ord_dtm, pymt_dtm, goodsf_dlv_div_cd, goodsf_data_intl_chck_cd, goodsf_usr_dfn_cd1
        , goodsf_usr_dfn_cd2, goodsf_cdlv_nos, dpst_cnft_dtm, frst_reg_dtm, frst_reg_usr_id, frst_reg_pgm_id
        , last_mod_dtm, last_mod_usr_id, last_mod_pgm_id
    </sql>

    <select id="selectSequence" resultType="java.lang.Long">
        select nextval(#{seqName})
    </select>

    <select id="selectDeliveryTraceTargetList" resultType="com.ezwelesp.batch.hims.order.entity.DlGoodsfDlvIntlBEntity">
        select <include refid="sqlGoodsfDlvIntlColumn"/>
          from (select a.*, row_number() over(partition by a.goodsf_innt_dlv_no order by a.dlv_intl_innt_no) rn
                   from ez_or.dl_goodsf_dlv_intl_b a
                  where a.goodsf_data_intl_chck_cd is null
                    and api_intl_dtm <![CDATA[>=]]> to_char(now() - interval '30 days', 'YYYYMMDDHH24MISS')
                  ) t
         where t.rn = 1
    </select>

    <select id="selectDeliveryTraceDetailByInntDlvNo" resultType="com.ezwelesp.batch.hims.order.entity.DlGoodsfDlvIntlBEntity">
        select <include refid="sqlGoodsfDlvIntlColumn"/>
          from ez_or.dl_goodsf_dlv_intl_b
         where goodsf_innt_dlv_no = #{goodsfInntDlvNo}
           and goodsf_data_intl_chck_cd is null
         order by dlv_intl_innt_no
    </select>

    <select id="selectDeliveryFinishList" resultType="com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowDeliveryFinishDto">
        select r.transUniqueCode
              , r.seq
              , r.logisticsCode
              , r.invoiceNo
              , r.dlvStatType
              , r.exceptionCode
              , r.exceptionName
              , r.errorCode
              , r.errorName
              , r.proc_yn
              , r.ezwel_proc_date
              , m.defCode1
              , m.defCode2
        from ez_if.gikimimast m
        join ez_if.gikimiresult r on r.transUniqueCode = m.transUniqueCode
       where r.proc_yn = 'N'
         and r.createDateTime >= to_char(now() - interval '90 days', 'YYYY-MM-DD HH24:MI:SS')
         and exists (select 1 from ez_or.dl_goodsf_dlv_intl_b where goodsf_innt_dlv_no = m.transUniqueCode)
    </select>


    <select id="selectInterlockBaseByGoodsfInntDlvNo" resultType="com.ezwelesp.batch.hims.order.entity.DlGoodsfDlvIntlBEntity">
        select <include refid="sqlGoodsfDlvIntlColumn"/>
          from ez_or.dl_goodsf_dlv_intl_b
         where goodsf_innt_dlv_no = #{goodsfInntDlvNo}
    </select>

    <select id="selectDeliveryBaseInfo" resultType="com.ezwelesp.batch.hims.order.entity.DlDlvBEntity">
        select dlv_no
              , ord_no
              , clm_no
              , dlv_st_cd
              , dlv_cmpt_dtm
          from ez_or.dl_dlv_b
         where dlv_no = #{dlvNo}
    </select>

    <resultMap id="SendTraceRequestTargetResultMap" type="com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowSendTraceRequestDto">
        <id column="mid" jdbcType="VARCHAR" property="mid"/>
        <result column="transUniqueCode" jdbcType="VARCHAR" property="transUniqueCode"/>
        <result column="sectionCode" jdbcType="VARCHAR" property="sectionCode"/>
        <result column="sellerCode" jdbcType="VARCHAR" property="sellerCode"/>
        <result column="sellerName" jdbcType="VARCHAR" property="sellerName"/>
        <result column="fromName" jdbcType="NUMERIC" property="fromName"/>
        <result column="toName" jdbcType="VARCHAR" property="toName"/>
        <result column="toMobile" jdbcType="VARCHAR" property="toMobile"/>
        <result column="logisticsCode" jdbcType="VARCHAR" property="logisticsCode"/>
        <result column="invoiceNo" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="dlvretType" jdbcType="VARCHAR" property="dlvretType"/>
        <result column="invoicePrintDate" jdbcType="VARCHAR" property="invoicePrintDate"/>
        <result column="defCode1" jdbcType="VARCHAR" property="defCode1"/>
        <result column="defCode2" jdbcType="VARCHAR" property="defCode2"/>
        <result column="requestDetails" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowSendTraceRequestDetailDto"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="requestDetails"/>
    </resultMap>

    <select id="selectSendTraceRequestTarget" resultMap="SendTraceRequestTargetResultMap" flushCache="true">
        select m.mid
              , m.transUniqueCode
              , m.sectionCode
              , m.sellerCode
              , m.sellerName
              , m.fromName
              , m.toName
              , m.toMobile
              , m.logisticsCode
              , m.invoiceNo
              , m.dlvretType
              , m.invoicePrintDate
              , m.defCode1
              , m.defCode2
              , d.detailJson as requestDetails
          from ez_if.gikimimast m
          left join lateral (
                select a.mid, json_agg(
                        json_build_object('order_no', a.orderNo
                                           , 'order_line', a.orderLine
                                           , 'item_code', a.itemCode
                                           , 'item_name', a.itemName
                                           , 'item_option', a.itemOption
                                           , 'item_qty', a.itemQty
                                           , 'item_price', a.itemPrice
                                           , 'order_date', a.orderDate
                                           , 'payment_date', a.paymentDate)
                    ) as detailJson
                   from ez_if.gikimidetail a
                  where a.mid = m.mid
                  group by a.mid) d on d.mid = m.mid
        where m.edi_datetime is null
          and create_datetime >= to_char(now() - interval '7 day', 'YYYYMMDDHH24MISS')
        limit 500
    </select>
</mapper>
