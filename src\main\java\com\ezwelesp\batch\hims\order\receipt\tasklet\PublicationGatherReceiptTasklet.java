package com.ezwelesp.batch.hims.order.receipt.tasklet;


import com.ezwelesp.batch.hims.order.apiresponse.OrderBatchBusinessHttpStatus;
import com.ezwelesp.batch.hims.order.receipt.domain.CashPublicationGatherDto;
import com.ezwelesp.batch.hims.order.receipt.service.CashReceiptTransferService;
import com.ezwelesp.batch.hims.order.receipt.service.command.CashReceiptCommandService;
import com.ezwelesp.batch.hims.order.receipt.service.query.CashReceiptQueryService;
import com.ezwelesp.framework.exception.ServiceException;
import com.ezwelesp.framework.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class PublicationGatherReceiptTasklet implements Tasklet {
    private final CashReceiptQueryService cashReceiptQueryService;
    private final CashReceiptTransferService cashReceiptTransferService;
    private final CashReceiptCommandService cashReceiptCommandService;



    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {

        log.debug("chunkContext:{}",chunkContext.getStepContext().getJobParameters().get("work.date"));

        log.debug("date:{}",chunkContext.getStepContext().getJobParameters().get("date"));
        try {

            String publicationDate = chunkContext.getStepContext().getJobParameters().get("date")==null? DateUtils.dateTimeNow("yyyyMMdd"):(String) chunkContext.getStepContext().getJobParameters().get("date");
            CashPublicationGatherDto paramDto = new CashPublicationGatherDto();
            paramDto.setPublicationDate(publicationDate);
            int totalCount =
                    cashReceiptQueryService.getCashReceiptPublicationGatherCount(paramDto);
            int processCount = 0;
            int roopCount = 0;
            for(;;) {
                List<CashPublicationGatherDto> publicationList =
                        cashReceiptQueryService.getCashReceiptPublicationGather(paramDto);
                log.debug("publicationList size:{}", publicationList.size());
                if (publicationList.size() > 0) {
                    for(CashPublicationGatherDto dto:publicationList) {
                        paramDto.setOrdNo(dto.getOrdNo());
                        paramDto.setClmNo(dto.getClmNo());
                        paramDto.setOrdGdsSeq(dto.getOrdGdsSeq());
                        paramDto.setCsrcHndwPrcsNos(dto.getCsrcHndwPrcsNos());
                        // 원로직은 루프

                        if (dto.getCsrcPblcCnclDt() != null) { // 취소건
                            cashReceiptTransferService.cashReciptIssueCancel(dto);

                            log.debug("!######################## PublicationGatherReceiptTasklet return value");
//                            log.debug("CashPublicationGatherDto,{}", JsonObjectConverter.serialize(dto));
                            try {
                                if ("0000".equals(dto.getResultCode())) {
                                    // 정상처리
                                    if (cashReceiptCommandService.modifyPublicationReceiptCancel(dto) != 1) {
                                        throw new ServiceException(
                                                OrderBatchBusinessHttpStatus.FAIL_ORDER_BATCH_ERROR_TEST);
                                    }
                                }
                                else {
                                    // 오류 로그 추가
                                    cashReceiptCommandService.createPublicationReceiptError(dto);

                                    if (("3010").equals(dto.getResultCode()) || ("S101").equals(dto.getResultCode())) {
                                        // 현금영수증 대외계 시스템 장애(3010), 승인처리 중 DB에러(S101)
                                        // 취소타켓일자 갱신
                                        cashReceiptCommandService.modifyPublicationReceiptCancelError(dto);
                                    }
                                }
                            } catch (ServiceException e) {
                                dto.setResultMsg(dto.getResultMsg() + "-정상취소등록후 업데이트 불가");
                                cashReceiptCommandService.createPublicationReceiptError(dto);
                            }
                        }
                        else {
                            cashReceiptTransferService.cashReciptIssue(dto);

                            log.debug("!######################## PublicationGatherReceiptTasklet return value");
//                            log.debug("CashPublicationGatherDto,{}", JsonObjectConverter.serialize(dto));
                            try {
                                if ("0000".equals(dto.getResultCode())) {
                                    // 정상처리
                                    if (cashReceiptCommandService.modifyPublicationReceipt(dto) != 1) {
                                        throw new ServiceException(
                                                OrderBatchBusinessHttpStatus.FAIL_ORDER_BATCH_ERROR_TEST);
                                    }
                                }
                                else {

                                    // 오류 로그 추가
                                    cashReceiptCommandService.createPublicationReceiptError(dto);

                                    if (("9953").equals(dto.getResultCode()) || ("8016").equals(dto.getResultCode())
                                            || ("S101").equals(dto.getResultCode()) || ("3010").equals(
                                            dto.getResultCode())) {

                                        //미등록 사업자번호(9953), 도서공연비 승인불가 가맹점(8016), 승인처리 중 DB에러(S101), 현금영수증 대외계 시스템 장애(3010)
                                        // 발행타켓일자 갱신
                                        cashReceiptCommandService.modifyPublicationReceiptError(dto);
                                    }
                                    else if (("9958").equals(dto.getResultCode()) || ("S411").equals(
                                            dto.getResultCode())) {
                                        // 신분확인 오류(9958), 전문데이터길이오류(S411)
                                        // 국세청 지정번호, 발행타켓일자 갱신
                                        dto.setEncCsrcPblcNo("0100001234");  /** 이니시스 신분오류 국세청지정번호(************)로 대체 */
                                        dto.setCsrcPblcUsgCd("0");
                                        dto.setOrdrMblTelno("0100001234");
                                        cashReceiptCommandService.modifyPublicationReceiptNumError(dto);
                                    }
                                    else {
                                        // 이외 오류건 에러건 삭제처리
                                        cashReceiptCommandService.deletePublicationReceiptError(dto);
                                    }
                                }
                            } catch (ServiceException e) {
                                dto.setResultMsg(dto.getResultMsg() + "-정상등록후 업데이트 불가");
                                cashReceiptCommandService.createPublicationReceiptError(dto);
                            }
                        }
                    }
                    log.debug("!######################## PublicationGatherReceiptTasklet method loop");
                    log.debug("publist,{},{}", publicationList.size(),roopCount++);
                    processCount+=publicationList.size();

                }else{
                    log.debug("!######################## PublicationGatherReceiptTasklet method loop");
                    log.debug("publist,{},{}", publicationList.size(),roopCount++);
                    break;
                }


            }
            log.debug("!######################## PublicationGatherReceiptTasklet method end");
            log.debug("!총 송수신 갯수,{}",totalCount);
            log.debug("!처리 송수신 갯수,{}",processCount);
            log.debug("!순환 페이징,{}",roopCount);

        } catch (Exception e) {
            log.error("PublicationGatherReceiptTasklet Failed: {}", e.getMessage(), e);
            log.error(e.getMessage(),e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.debug("!######################## PublicationGatherReceiptTasklet method finish");




        return RepeatStatus.FINISHED;
    }
}
