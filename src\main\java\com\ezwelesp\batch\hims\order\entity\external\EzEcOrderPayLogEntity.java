package com.ezwelesp.batch.hims.order.entity.external;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.experimental.SuperBuilder;

/**
 * PG결제대사모니터링내역
 * ez_ec_order_pay_log
 */
@Data
@SuperBuilder(toBuilder=true)
public class EzEcOrderPayLogEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * TID
     */
    private String tid;

    /**
     * 상태코드
     */
    private String statusCd;

    /**
     * 주문번호(구분자_이지웰주문번호 형식)
     */
    private String orderNum;

    /**
     * 이지웰주문번호
     */
    private Long ezwelOrderNum;

    /**
     * 상점아이디
     */
    private String storeId;

    /**
     * 거래유형 C:카드, B:실시간계좌이체
     */
    private String dealType;

    /**
     * 기기유형 일반/모바일 등등
     */
    private String deviceType;

    /**
     * 결제유형 신용카드/ASP/실시간계좌이체 등등
     */
    private String payType;

    /**
     * 거래상태 승인/취소/매입후취소 등등
     */
    private String dealStatus;

    /**
     * 승인일 YYYYMMDD
     */
    private String confirmDd;

    /**
     * 승인시 HHMMSS
     */
    private String confirmTm;

    /**
     * 취소일 YYYYMMDD
     */
    private String cancelDd;

    /**
     * 취소시 HHMMSS
     */
    private String cancelTm;

    /**
     * 거래금액
     */
    private BigDecimal dealAmt;

    /**
     * 취소금액
     */
    private BigDecimal cancelAmt;

    /**
     * 취소TID
     */
    private String cancelTid;

    /**
     * 신용카드금액
     */
    private BigDecimal cardAmt;

    /**
     * 제휴포인트
     */
    private String alliPoint;

    /**
     * 할부개월수
     */
    private String instMm;

    /**
     * 카드유형 삼성계열/BC계열 등등
     */
    private String cardType;

    /**
     * 승인번호
     */
    private String confirmNum;

    /**
     * 간편결제 일반결제/삼성페이 등등
     */
    private String simplePay;

    /**
     * 취소잔액
     */
    private BigDecimal cancelRemainAmt;

    /**
     * 은행명
     */
    private String bankNm;

    /**
     * 상품명
     */
    private String goodsNm;

    /**
     * 지불일시
     */
    private String payDt;

    /**
     * PG사 타입(I:이니시스, M:메인페이)
     */
    private String pgType;

    /**
     * 취소매입 완료일시
     */
    private String payCancelDt;



}
