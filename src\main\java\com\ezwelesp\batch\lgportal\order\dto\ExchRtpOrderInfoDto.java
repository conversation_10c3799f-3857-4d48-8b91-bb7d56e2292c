package com.ezwelesp.batch.lgportal.order.dto;

import com.ezwelesp.batch.hims.order.entity.DlDlvAdrBEntity;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
  * 교환/반품 주문 정보 Dto
  *
  * <AUTHOR>
  * @since 2025.04.08
  * @see com.ezwelesp.batch.lgportal.entity
  */
@Jacksonized
@Getter
@SuperBuilder
public class ExchRtpOrderInfoDto extends DlDlvAdrBEntity {
    /**
     * 배송회사코드(dlv_co_cd)
     */
    private String dlvCoCd;

    /**
     * 운송장번호(invc_no)
     */
    private String invcNo;
}
