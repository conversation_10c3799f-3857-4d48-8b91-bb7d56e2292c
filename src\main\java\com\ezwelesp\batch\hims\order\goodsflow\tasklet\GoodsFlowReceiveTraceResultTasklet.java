package com.ezwelesp.batch.hims.order.goodsflow.tasklet;

import com.ezwelesp.batch.hims.order.goodsflow.domain.response.ReceiveTraceResultResponse;
import com.ezwelesp.batch.hims.order.goodsflow.service.GoodsFlowTraceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsFlowReceiveTraceResultTasklet implements Tasklet {
    private final GoodsFlowTraceService goodsFlowTraceService;
    private static final int MAX_SIZE = 100;


    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            // 배송결과 수신 API 호출
            ReceiveTraceResultResponse result = goodsFlowTraceService.getReceiveTraceResultByApi();

            if (!result.isSuccess() || result.getData() == null) {
                log.info("굿스플로 배송결과 수신 API 결과 없음.. {}", result.getError());
                return RepeatStatus.FINISHED;
            }

            List<ReceiveTraceResultResponse.Item> itemList = result.getData().getItems();
            
            // 배송결과 수신 응답을 나눠서 처리 (MAX_SIZE: 커밋단위)
            for (int fromIndex = 0; fromIndex < itemList.size(); fromIndex += MAX_SIZE) {
                int toIndex = Math.min(fromIndex + MAX_SIZE, itemList.size());
                goodsFlowTraceService.insertReceiveTraceResultByApi(itemList.subList(fromIndex, toIndex));
            }
        } catch (Exception e) {
            log.error("GoodsFlowReceiveTraceResultTasklet failed", e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.debug("GoodsFlowReceiveTraceResultTasklet finished");

        return RepeatStatus.FINISHED;
    }
}
