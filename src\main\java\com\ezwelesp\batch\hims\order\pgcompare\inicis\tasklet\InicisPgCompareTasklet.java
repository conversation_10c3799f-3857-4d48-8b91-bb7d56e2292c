package com.ezwelesp.batch.hims.order.pgcompare.inicis.tasklet;

import com.ezwelesp.batch.hims.order.pgcompare.inicis.service.PgCompareService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class InicisPgCompareTasklet implements Tasklet {

    private final PgCompareService pgCompareService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {


            log.debug("!######################## InicisPgCompareTasklet method start");
            pgCompareService.connectInicisPgCompareData();
            log.debug("!######################## InicisPgCompareTasklet method start2");

        } catch (Exception e) {
            log.error("InicisPgCompareTasklet Failed: {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }


        log.debug("!######################## InicisPgCompareTasklet method finish");

        return RepeatStatus.FINISHED;
    }
}
