package com.ezwelesp.batch.hims.order.delivery.service.impl;

import com.ezwelesp.batch.hims.order.delivery.domain.PenddingDeliveryDto;
import com.ezwelesp.batch.hims.order.delivery.mapper.query.PenddingDeliveryQueryMapper;
import com.ezwelesp.batch.hims.order.delivery.service.PenddingDeliveryMailSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PenddingDeliveryMailSendServiceImpl implements PenddingDeliveryMailSendService {
    private final PenddingDeliveryQueryMapper penddingDeliveryQueryMapper;
    private static final DateTimeFormatter YYYYMMDD_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public List<PenddingDeliveryDto> getLongTermPenddingDeliveryList() {
        LocalDate today = LocalDate.now();
        String startDate = getStartDate(today, true);
        String endDate = getEndDate(today, true);

        return penddingDeliveryQueryMapper.selectPenddingDeliveryList(startDate, endDate);
    }

    @Override
    public List<PenddingDeliveryDto> getPenddingDeliveryList() {
        LocalDate today = LocalDate.now();
        String startDate = getStartDate(today, false);
        String endDate = getEndDate(today, false);
        return penddingDeliveryQueryMapper.selectPenddingDeliveryList(startDate, endDate);
    }

    /**
     * 조회 시작일자 (장기미배송건 -210일, 미배송건 -90일)
     */
    private String getStartDate(LocalDate today, boolean isLongTerm) {
        return isLongTerm ? today.minusDays(210).format(YYYYMMDD_FORMATTER)
                : today.minusDays(90).format(YYYYMMDD_FORMATTER);
    }

    /**
     * 조회 종료일자  (장기미배송건 -30일, 미배송건 오늘기준 영업일 -3일 (계산필요)) TODO 미배송건 일감 진행시 쿼리 만들어 처리하기
     */
    private String getEndDate(LocalDate today, boolean isLongTerm) {
        return isLongTerm ? today.minusDays(30).format(YYYYMMDD_FORMATTER)
                : today.minusDays(90).format(YYYYMMDD_FORMATTER);
    }

    // TODO 메일발송 기능 붙일때 쓰기 - asis 기준 내부메일 수신 대상자
    private void toMail() {
        String to_mail = "";

//		to_mail += "<EMAIL>,"; 제외
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
//		to_mail += "<EMAIL>,"; //제외
//		to_mail += "<EMAIL>,"; 제외
//		to_mail += "<EMAIL>,"; 제외
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
//		to_mail += "<EMAIL>,"; 제외
        to_mail += "<EMAIL>,";
//		to_mail += "<EMAIL>,"; 제외
        to_mail += "<EMAIL>,";

        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";
        to_mail += "<EMAIL>,";

        to_mail += "<EMAIL>,";

        to_mail += "<EMAIL>,"; 	//김린아
        to_mail += "<EMAIL>,"; 		//송예빈
        to_mail += "<EMAIL>,"; 	//이나연
        to_mail += "<EMAIL>,";//김민지
        to_mail += "<EMAIL>,";		//김로희

        to_mail += "<EMAIL>";
    }

}
