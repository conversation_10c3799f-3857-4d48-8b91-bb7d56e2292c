package com.ezwelesp.batch.hims.order.utils;

import java.net.http.HttpClient;
import java.time.Duration;
import java.util.List;
import java.util.Map;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.JdkClientHttpRequestFactory;
import org.springframework.web.client.RestClient;

import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.request.SyncRequest;
import com.ezwelesp.framework.utils.request.dto.Header;

public class RestClientUtil {

	
	
	public static AjaxResult requestApi(String url, int timeout, List<Header> headerList, Object requestBody) {

        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, null, null);
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            try (SSLSocket socket = (SSLSocket)sslSocketFactory.createSocket()) {
                socket.setEnabledProtocols(new String[]{"TLSv1", "TLSv1.1", "TLSv1.2"});
            }

            HttpClient httpClient = HttpClient.newBuilder()
                    .sslContext(sslContext)
                    .connectTimeout(Duration.ofSeconds(timeout))
                    .build();

            RestClient restClient = RestClient.builder()
                    .requestFactory(new JdkClientHttpRequestFactory(httpClient))
                    .build();

            headerList.add(new Header("Content-Type", "application/json; charset=utf-8"));


            return new SyncRequest<Object>(restClient, "").post(url, headerList, null, requestBody);

        } catch (Exception e) {
        	e.printStackTrace();
            throw new RuntimeException("API failed", e);
        }

    }
	
	public static ResponseEntity<String> requestApiGet(String url, int timeout, List<Header> headerList, Map<String,Object> sendJson) {

        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, null, null);
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            try (SSLSocket socket = (SSLSocket)sslSocketFactory.createSocket()) {
                socket.setEnabledProtocols(new String[]{"TLSv1", "TLSv1.1", "TLSv1.2"});
            }
           	String queryString = "";
			for(String key : sendJson.keySet()) {
				queryString += ("".equals(queryString) ? "" : "&") + key + "=" + sendJson.get(key);
			}

            HttpClient httpClient = HttpClient.newBuilder()
                    .sslContext(sslContext)
                    .connectTimeout(Duration.ofSeconds(timeout))
                    .build();

            RestClient restClient = RestClient.builder()
                    .requestFactory(new JdkClientHttpRequestFactory(httpClient))
                    .build();

            headerList.add(new Header("Content-Type", "application/json; charset=utf-8"));
            
            return restClient.get()
                    .uri(url + "?" + queryString)
                    .headers(httpHeaders -> {
                        for (Header header : headerList) {
                            httpHeaders.set(header.getName(), header.getValue());
                        }
                    })
                    .accept(MediaType.APPLICATION_JSON)
                    .retrieve()
                    .toEntity(String.class);
            
            
        } catch (Exception e) {
        	e.printStackTrace();
            throw new RuntimeException("API failed", e);
        }

    }
}
