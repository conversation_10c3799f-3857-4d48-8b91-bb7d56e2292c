package com.ezwelesp.batch.hims.order.csp.domain;

import lombok.Data;

@Data
public class CspOrderCurrentStateDto {
    private String cspCd; // 협력사코드
    private String mgrMblTelno; // 협력사 담당자 전화번호
    private int ordCnt; // 주문건수
    private int dlvDlayCnt; // 배송지연건수
    private int dlvWaitCnt; // 배송대기건수
    private int todayObndClsg; // 오늘출고마감건수
    private int gdsInqWaitCnt; // 상품문의답변대기건수
    private int gdsInqOverWaitCnt; // 상품문의답변대기 24시간 초과건수
    private int cspInqCnt; // CS문의건수 (일반)
    private int cspEmgyInqCnt; // CS문의건수 (긴급)
    private int exchAplCnt; // 교환신청건수
    private int rtpAplCnt; // 반품신청건수
    private int exchDlayCnt; // 교환지연건수
    private int rtpRfndDlayCnt; // 반품환불지연건수
    private int exchPkupDlayCnt; // 교환수거지연건수
    private int rtpPkupDlayCnt; // 반품수거지연건수
}