package com.ezwelesp.batch.hims.order.reseller.mapper.query;

import com.ezwelesp.batch.hims.order.reseller.domain.SuspectedResellerOrderDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.reseller.mapper.query
 * @since 2025.05.19
 */
@Mapper
public interface SuspectedResellerOrderNotificationQueryMapper {

    List<SuspectedResellerOrderDto> selectSuspectedResellerByGdsOneDay();

    List<SuspectedResellerOrderDto> selectSuspectedResellerByOrder(String searchTermType);
}
