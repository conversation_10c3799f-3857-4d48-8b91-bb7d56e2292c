package com.ezwelesp.batch.hims.order.config;

import lombok.Getter;

import java.util.Set;

/**
 * <AUTHOR>
 * @see com.ezwelesp.fo.order.rest.claim.config
 * @since 2025.03.05
 */
public class ClaimConstants {

    public static final String DATE_FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String CLAIM_Y = "Y";
    public static final String CLAIM_N = "N";

    public static final String GIFT_ORDER_CANCEL_API_URL = "/order/api/v1/gift/claim/cancel";

    @Getter
    public enum PickUpMethodCodeEnum {
        VISIT("R", "방문수거"),
        DIRECT("D", "직접발송"),
        REQUEST_RETURN("H", "회수요청"),
        ;

        private final String code;
        private final String name;

        PickUpMethodCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    @Getter
    public enum GoodsTypeDetailCodeEnum {
        GENERAL_DELIVERY("1001", "일반배송"),
        SELF_DELIVERY("1002", "자체배송"),
        CUSTOM_MADE("1003", "주문제작"),
        INSTALL_RESERVATION("1004", "설치예약"),
        COLD_FOOD("1007", "신선/냉장/냉동식품"),
        CARGO_DELIVERY("1009", "화물택배"),
        INSPECTION_DELIVERY("1020", "검수배송"),
        PREORDER_DELIVERY("1021", "예약배송"),
        GIFT_CERTIFICATE("1022", "지류상품권"),
        RENTAL("1023", "렌탈"),
        TRAVEL_ACCOMMODATION_PERFORMANCE("2001", "여행/숙박/공연"),
        MOBILE_ECOUPON("2002", "모바일E쿠폰"),
        MOBILE_GIFT_CERTIFICATE("2003", "모바일상품권"),
        ASP("3001", "ASP연동"),
        EZ_BOOKS("3002", "EZ북스"),
        ;

        private final String code;
        private final String name;

        GoodsTypeDetailCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    @Getter
    public enum ClaimKindCodeEnum {
        CANCEL("CNCL", "취소"),
        RETURN("RTP", "반품"),
        EXCHANGE("EXCH", "교환"),
        ;

        private final String code;
        private final String name;

        ClaimKindCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        private static final Set<ClaimKindCodeEnum> exchangeAndReturn = Set.of(
                ClaimKindCodeEnum.EXCHANGE,
                ClaimKindCodeEnum.RETURN
        );
    }


    @Getter
    public enum ClaimStatusCodeEnum {
        APPLY("APL", "신청"),
        ACCEPT("ACPT", "접수"),
        COMPLETION("CMPT", "완료"),
        WITHDRAWAL("WTDR", "철회"),
        REFUND_STANDBY("RFND_STBY", "배송비용환불대기"),
        PAYMENT_STANDBY("PYMT_STBY", "결제대기"),
        ;

        private final String code;
        private final String name;

        ClaimStatusCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    @Getter
    public enum FOClaimCancelStatusEnum {
        PROGRESS("IPR", "취소 처리중"),
        REQUEST("REQ", "주문취소요청"),
        APPLY("APL", "취소신청"),
        COMPLETE("CMPT", "취소완료"),
        WITHDRAW("WTDR", "주문취소요청(철회)"),
        ;

        private final String code;
        private final String name;

        FOClaimCancelStatusEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    @Getter
    public enum FOClaimExchangeStatusEnum {
        APPLY("ACPT", "교환신청"),
        ACCEPT("APL", "교환접수"),
        IN_PICKUP("IPKUP", "수거중"),
        PICKUP_COMPLETE("PKUP_CMPT", "수거완료"),
        IN_PROGRESS("IPR", "교환 처리중"),
        IN_ORGL_SNDB("IORGL_SNDB", "원상품 반송중"),
        IN_EXCH_DLV("IEXCH_DLV", "교환상품 배송중"),
        COMPLETE("EXCH_CMPT", "교환완료"),
        ORGL_COMPLETE("EXCH_ORGL_CMPT", "교환완료(원상품 반송)"),
        WITHDRAW("WTDR", "교환취소"),
        ;

        private final String code;
        private final String name;

        FOClaimExchangeStatusEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    @Getter
    public enum FOClaimReturnStatusEnum {
        APPLY("ACPT", "반품신청"),
        ACCEPT("APL", "반품접수"),
        IN_PICKUP("IPKUP", "수거중"),
        PICKUP_COMPLETE("PKUP_CMPT", "수거완료"),
        IN_PROGRESS("IPR", "반품 처리중"),
        IN_ORGL_SNDB("IORGL_SNDB", "원상품 반송중"),
        COMPLETE("RTP_CMPT", "반품완료"),
        ORGL_COMPLETE("RTP_ORGL_CMPT", "반품완료(원상품 반송)"),
        WITHDRAW("WTDR", "반품취소"),
        ;

        private final String code;
        private final String name;

        FOClaimReturnStatusEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    @Getter
    public enum ClaimRefundStatusCodeEnum {
        REQUISITION("REQ", "환불요청"),
        ERROR("ERR", "환불오류"),
        COMPLETION("CMPT", "환불완료"),
        STANDBY("STBY", "환불대기"),
        ;

        private final String code;
        private final String name;

        ClaimRefundStatusCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        private static final Set<String> cancelRequest = Set.of(
                ClaimRefundStatusCodeEnum.ERROR.code,
                ClaimRefundStatusCodeEnum.REQUISITION.code,
                ClaimRefundStatusCodeEnum.STANDBY.code
        );

        public static boolean isCancelRequest(String refundStatus) {
            return cancelRequest.contains(refundStatus);
        }
    }


    @Getter
    public enum ClaimRefuseApplyDetailCodeEnum {
        ACCEPT("ACPT", "접수"),
        APPROVAL("APV", "승인"),
        REJECT("RJCT", "반려"),
        CANCEL("CNCL", "취소"),
        ;

        private final String code;
        private final String name;

        ClaimRefuseApplyDetailCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        private static final Set<String> exchangeReturnProcess = Set.of(
                ClaimRefuseApplyDetailCodeEnum.ACCEPT.code,
                ClaimRefuseApplyDetailCodeEnum.CANCEL.code
        );

        public static boolean isExchangeReturnProcess(String status) {
            return exchangeReturnProcess.contains(status);
        }
    }

}
