package com.ezwelesp.batch.hims.common.test;

import java.nio.charset.StandardCharsets;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobScope;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

import com.ezwelesp.biz.common.security.InstantAuthorization;

import lombok.RequiredArgsConstructor;

/**
 * 비지니스 라이브러리 통합 테스트 배치
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class TestJobConfig {
	
    @Bean
    Job commonTestJob(JobRepository jobRepository) {
        return new JobBuilder("commonTestJob", jobRepository)
                .start(this.commonTestStep(null, null))
                .build();
    }

    @Bean
    @JobScope
    Step commonTestStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("commonTestStep", jobRepository)
                .tasklet(this.testTasklet(null), transactionManager)
                .build();
    }
    
    @Bean
    @StepScope
    Tasklet testTasklet(@Value("${rest.client.base-url}") String apiBaseUrl) {
    	return (contribution, chunkContext) -> {
    		var jp			= chunkContext.getStepContext().getJobParameters();
    		var testParam	= jp.getOrDefault("tp", "NOT-PRESENTED");
    		System.out.println("test parameter " + testParam);
    		System.out.println("api base url " + apiBaseUrl);
    		
    		var	webClient	= WebClient.builder()
    				.baseUrl(apiBaseUrl)
    				.build();
    		var webBoRes	= InstantAuthorization.injectAuthorization(webClient, "HIMS", null, "pat")
    			.get()
    			.uri("/hi-common/api/v1/hims/whoami")
    			.accept(MediaType.APPLICATION_JSON)
    			.retrieve()
    			.bodyToMono(String.class)
    			.block();
    		System.out.println("[WebClient] BO whoami -> " + webBoRes);
    		
    		var webFoRes	= InstantAuthorization.injectAuthorization(webClient, "FO", "withus", "pat")
        			.get()
        			.uri("/mypage/api/v1/private/auth/whoami")
        			.accept(MediaType.APPLICATION_JSON)
        			.retrieve()
        			.bodyToMono(String.class)
        			.block(); 
    		
    		System.out.println("[WebClient] FO whoami -> " + webFoRes);
    		
    		
    		var restClient	= RestClient.builder()
    				.baseUrl(apiBaseUrl)
    				.build();
    		
    		
    		var restBoRes	= InstantAuthorization.injectAuthorization(restClient, "HIMS", null, "pat")
        			.get()
        			.uri("/hi-common/api/v1/hims/whoami")
        			.accept(MediaType.APPLICATION_JSON)
        			.retrieve()
        			.body(String.class);
    		
    		System.out.println("[RestClient] BO whoami -> " + restBoRes);
    		
    		var restFoRes	= InstantAuthorization.injectAuthorization(restClient, "FO", "kt", "pat")
        			.get()
        			.uri("/mypage/api/v1/private/auth/whoami")
        			.accept(MediaType.APPLICATION_JSON)
        			.retrieve()
        			.body(String.class);
    		
    		System.out.println("[RestClient] FO whoami -> " + restFoRes);
    		
    		
    		var webTextRes	= InstantAuthorization.injectAuthorization(webClient, "HIMS", null, "pat")
        			.get()
        			.uri("/hi-common/api/v1/hims/whoami/{testParam}", "abcd가나다라")
        			.accept(MediaType.APPLICATION_JSON)
        			.retrieve()
        			.bodyToMono(String.class)
        			.block();
        	System.out.println("[WebClient] BO test -> " + webTextRes);
    		
   		
    		return RepeatStatus.FINISHED;
    	};
    }
}
