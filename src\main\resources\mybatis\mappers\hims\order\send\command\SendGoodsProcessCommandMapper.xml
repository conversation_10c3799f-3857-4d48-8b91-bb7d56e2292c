<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.send.mapper.command.SendGoodsProcessCommandMapper">

    <update id="updateSendGoodsDetailStatus">
        update ez_or.dl_ord_snd_gds_d
        set snd_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , ord_snd_st_cd = #{ordSndStCd}
          <if test="ecpnCspReqInntNo != null and ecpnCspReqInntNo != ''">
              , ecpn_csp_req_innt_no = #{ecpnCspReqInntNo}
          </if>
          <if test='ordSndStCd == "03"'> /* 발송오류 */
              , snd_err_ocrn_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          </if>
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        where ord_no = #{ordNo}
          and ord_snd_seq = #{ordSndSeq}
          and ord_gds_seq = #{ordGdsSeq}
          and ord_snd_gds_dtl_seq = #{ordSndGdsDtlSeq}
    </update>

    <insert id="insertSendGoodsHistory">
        insert into ez_or.dl_ord_snd_gds_d_h
            ( ord_snd_gds_dtl_his_seq, ord_no, ord_snd_seq, ord_gds_seq, ord_snd_gds_dtl_seq, ord_snd_gds_dtl_chg_req_cd
            , ord_snd_gds_dtl_chg_insr_cval, crtf_csp_nm, ecpn_csp_trd_no, chg_bef_vlid_term_end_dtm
            , rcvr_mbl_telno, ord_st_cd, ord_snd_st_cd, ord_snd_gds_use_st_cd, ord_snd_gds_rdno_st_cd
            , ord_snd_gds_stl_st_cd, stl_ym, txin_rcv_knd_cd, err_msg_cntn, ecpn_ord_st_cd, rdno_snd_req_magn_cd
            , intg_gds_rdno_no, msg_ttl, snd_msg_cntn, snd_req_dtm, snd_dtm, snd_err_ocrn_dtm, ecpn_snd_rslt_cd
            , ecpn_snd_rslt_msg_cntn, ecpn_snd_rslt_innt_no, frst_reg_dtm, frst_reg_usr_id, frst_reg_pgm_id, last_mod_dtm, last_mod_usr_id, last_mod_pgm_id)
        select (select nextval('ez_or.sq_dl_ord_snd_gds_d_h'))
              , dosgd.ord_no
              , dosgd.ord_snd_seq
              , dosgd.ord_gds_seq
              , dosgd.ord_snd_gds_dtl_seq
              , #{ordSndGdsDtlChgReqCd} /* 06: SMS/LMS/MMS 발송 */
              , #{ordSndGdsDtlChgInsrCval}
              , #{crtfCspNm}
              , #{ecpnCspTrdNo}
              , #{chgBefVlidTermEndDtm}
              , dosb.rcvr_mbl_telno
              , oob.ord_st_cd
              , dosgd.ord_snd_st_cd
              , dosgd.ord_snd_gds_use_st_cd
              , dosgd.ord_snd_gds_rdno_st_cd
              , dosgd.ord_snd_gds_stl_st_cd
              , dosgd.stl_ym
              , dosgd.txin_rcv_knd_cd
              , #{errMsgCntn}
              , #{ecpnOrdStCd} /* 04:쿠폰발송, 05: 쿠폰재발송, 10: 유효기간만료알림발송 */
              , '02' /* 관리자 */
              , dosgd.intg_gds_rdno_no
              , #{msgTtl}
              , #{sndMsgCntn}
              , to_char(now(), 'YYYYMMDDHH24MISS')
              , dosgd.snd_dtm
              , dosgd.snd_err_ocrn_dtm
              , #{ecpnSndRsltCd}
              , #{ecpnSndRsltMsgCntn}
              , #{ecpnSndRsltInntNo}
              , to_char(now(), 'YYYYMMDDHH24MISS')
              , 'BATCH'
              , 'BATCH'
              , to_char(now(), 'YYYYMMDDHH24MISS')
              , 'BATCH'
              , 'BATCH'
          from ez_or.dl_ord_snd_gds_d dosgd
          join ez_or.dl_ord_snd_b dosb on dosb.ord_no = dosgd.ord_no and dosb.ord_snd_seq = dosgd.ord_snd_seq
          join ez_or.or_ord_b oob on oob.ord_no = dosb.ord_no
         where dosgd.ord_no = #{ordNo}
           and dosgd.ord_snd_seq = #{ordSndSeq}
           and dosgd.ord_gds_seq = #{ordGdsSeq}
           and dosgd.ord_snd_gds_dtl_seq = #{ordSndGdsDtlSeq}
    </insert>
</mapper>
