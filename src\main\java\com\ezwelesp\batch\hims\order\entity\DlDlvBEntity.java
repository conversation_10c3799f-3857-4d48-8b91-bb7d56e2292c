package com.ezwelesp.batch.hims.order.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 배송기본(ez_or.dl_dlv_b)
 */
@Jacksonized
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DlDlvBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송번호(dlv_no) not null
     */
    private String dlvNo;

    /**
     * 원본배송번호(orgl_dlv_no)
     */
    private String orglDlvNo;

    /**
     * 분리배송여부(divd_dlv_yn) not null
     */
    private String divdDlvYn;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 배송주소번호(dlv_adr_no) not null
     */
    private Long dlvAdrNo;

    /**
     * 배송비용번호(dlv_exp_no) not null
     */
    private Long dlvExpNo;

    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 협력사출고위치번호(csp_obnd_loc_no)
     */
    private String cspObndLocNo;

    /**
     * 배송정책순번(dlv_plcy_seq) not null
     */
    private Long dlvPlcySeq;

    /**
     * 배송회사코드(dlv_co_cd)
     */
    private String dlvCoCd;

    /**
     * 배송상태코드(dlv_st_cd)
     */
    private String dlvStCd;

    /**
     * 회수상태코드(wtdw_st_cd)
     */
    private String wtdwStCd;

    /**
     * 배송정책코드(dlv_plcy_cd)
     */
    private String dlvPlcyCd;

    /**
     * 배송출고유형코드(dlv_obnd_typ_cd)
     */
    private String dlvObndTypCd;

    /**
     * 배송연동회사전송결과코드(dlv_intl_co_trms_rslt_cd)
     */
    private String dlvIntlCoTrmsRsltCd;

    /**
     * 수거방법코드(pkup_mthd_cd)
     */
    private String pkupMthdCd;

    /**
     * 운송장번호(invc_no)
     */
    private String invcNo;

    /**
     * 배송예정일자(dlv_due_dt)
     */
    private String dlvDueDt;

    /**
     * 배송희망일자(dlv_hope_dt)
     */
    private String dlvHopeDt;

    /**
     * 배송희망시분(dlv_hope_hm)
     */
    private String dlvHopeHm;

    /**
     * 배송시작마감일자(dlv_strt_clsg_dt)
     */
    private String dlvStrtClsgDt;

    /**
     * 토요일배송예상시각시(sat_dlv_expt_hh)
     */
    private String satDlvExptHh;

    /**
     * 배송처리관리자메모(dlv_prcs_mgr_memo)
     */
    private String dlvPrcsMgrMemo;

    /**
     * 배송지연일수(dlv_dlay_dcnt) not null
     */
    private Integer dlvDlayDcnt;

    /**
     * 배송예정지연일수(dlv_due_dlay_dcnt) not null
     */
    private Integer dlvDueDlayDcnt;

    /**
     * 전체배송지연일수(all_dlv_dlay_dcnt) not null
     */
    private Integer allDlvDlayDcnt;

    /**
     * 배송대기시작일시(dlv_stby_strt_dtm)
     */
    private String dlvStbyStrtDtm;

    /**
     * 배송준비시작일시(dlv_rdy_strt_dtm)
     */
    private String dlvRdyStrtDtm;

    /**
     * 배송시작일시(dlv_strt_dtm)
     */
    private String dlvStrtDtm;

    /**
     * 배송완료일시(dlv_cmpt_dtm)
     */
    private String dlvCmptDtm;

    /**
     * 배송취소일시(dlv_cncl_dtm)
     */
    private String dlvCnclDtm;

    /**
     * 운송장번호등록일시(invc_no_reg_dtm)
     */
    private String invcNoRegDtm;

    /**
     * 배송준비변경관리자ID(dlv_rdy_chg_mgr_id)
     */
    private String dlvRdyChgMgrId;

    /**
     * 배송시작변경관리자ID(dlv_strt_chg_mgr_id)
     */
    private String dlvStrtChgMgrId;

    /**
     * 배송완료변경관리자ID(dlv_cmpt_chg_mgr_id)
     */
    private String dlvCmptChgMgrId;

    /**
     * 배송취소변경관리자ID(dlv_cncl_chg_mgr_id)
     */
    private String dlvCnclChgMgrId;

    /**
     * 운송장번호변경관리자ID(invc_no_chg_mgr_id)
     */
    private String invcNoChgMgrId;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
