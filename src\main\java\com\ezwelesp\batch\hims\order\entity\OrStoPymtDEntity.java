package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 정기구독결제상세(ez_or.or_sto_pymt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrStoPymtDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 정기구독번호(sto_no) not null
     */
    private String stoNo;

    /**
     * 정기구독주문차수(sto_ord_nos) not null
     */
    private Long stoOrdNos;

    /**
     * 결제수단코드(pymt_mns_cd) not null
     */
    private String pymtMnsCd;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
