package com.ezwelesp.batch.lgportal.product.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

import java.time.LocalDateTime;

@Alias("LgProductQnaListDto")
@Getter
@SuperBuilder
public class LgProductQnaListDto {

    private Integer productInquiryNo;        // 상품 문의 번호
    private Integer productAnswerNo;         // 상품 문의 답변 번호
    private String siteID;                   // 사이트 아이디
    private String siteName;                 // 사이트명
    private String productName;              // 상품명
    private String title;                    // 상품 문의 제목
    private String contents;                 // 상품 문의 내용
    private String memberID;                 // 질문자 아이디
    private String memberName;               // 질문자명

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime created;           // 상품 문의 등록일

    private String statusCode;               // 처리 현황 코드
    private String statusCodeName;           // 처리 현황 코드명
    private String answerer;                 // 답변자명

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime answerDate;        // 답변 일

    private String postDate;                 // 답변 경과 일
    private String withdrawalYN;             // 철수 유무
    private Integer productNo;               // 상품 번호
    private Integer memberNo;                // 회원 일련번호
    private String entryComName;             // 입점사명

    public String getProductInquiryNoString() {
        return String.valueOf(productInquiryNo);
    }
}
