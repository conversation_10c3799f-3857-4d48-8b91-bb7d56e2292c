package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgUpdateProductBasicRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalEditProductBasicInfoHandler implements LgPortalEditProductProcessorHandler {

    private final LgApiClient lgApiClient;

    @Override
    public LgProductApiResponseDto handle(LgEditProductTargetVo item) {
        val requestDto = LgUpdateProductBasicRequestDto.builder()
                .productNo(item.getProductNo())
                .productName(item.getGdsNm()) // 상품명
                .brandNo(item.getBrandNo()) // 브랜드코드(개발:13, 운영:39)
                .modelName(item.getGdsMdlNm()) // 모델명
                .madeCountry(item.getOrgnNm()) // 원산지
                .madeCompany(item.getMnfcCoNm()) // 제조사
                .productSummary(item.getGdsSimpDesc()) // 상품요약
                .taxYN(item.getTaxnKndCd()) // 과세방식(Y:과세, N:비과세)
                .orderMaxCnt("Y".equals(item.getT1BuyPossMaxQtyUseYn()) ? item.getT1BuyPossMaxQty() : 0)
                .sumLimitCnt("Y".equals(item.getPsn1BuyPossMaxQtyUseYn()) ? item.getPsn1BuyPossMaxQty() : 0)
                .build();

        val responseDto = lgApiClient.sendLgUpdateProductBasicApi(
                requestDto,
                item.getGdsCd(),
                item.getApiSeq()
        );

        if (responseDto != null && !"200".equals(responseDto.getResult())) {
            log.debug("sendLgUpdateProductBasicApi Fail : {},{}", item.getGdsCd(), responseDto.getResult());
        }

        return responseDto;
    }

}
