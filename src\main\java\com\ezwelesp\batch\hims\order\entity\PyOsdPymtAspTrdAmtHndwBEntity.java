package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 외부결제제휴사거래금액수기기본(ez_or.py_osd_pymt_asp_trd_amt_hndw_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyOsdPymtAspTrdAmtHndwBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 외부결제제휴사거래금액수기번호(osd_pymt_asp_trd_amt_hndw_no) not null
     */
    private Long osdPymtAspTrdAmtHndwNo;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 표준카테고리코드1(std_ctgr_cd1) not null
     */
    private String stdCtgrCd1;

    /**
     * 표준카테고리코드2(std_ctgr_cd2) not null
     */
    private String stdCtgrCd2;

    /**
     * 표준메뉴코드(std_menu_cd) not null
     */
    private String stdMenuCd;

    /**
     * 사용여부(use_yn) not null
     */
    private String useYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
