package com.ezwelesp.batch.hims.order.goodsflow.client;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "goodsflow")
public class GoodsFlowConfig {
    private Map<String, Config> server = new HashMap<>();

    @Getter
    @Setter
    @ToString
    public static class Config {
        private String host;
        private long timeout;
        private String apiKey;
    }
}
