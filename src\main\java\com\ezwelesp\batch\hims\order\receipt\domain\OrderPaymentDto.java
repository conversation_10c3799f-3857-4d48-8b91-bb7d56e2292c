package com.ezwelesp.batch.hims.order.receipt.domain;

import lombok.Data;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


@Jacksonized
@Data
@SuperBuilder
public class OrderPaymentDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;

    /**
     * 결제번호(pymt_no) not null
     */
    private String pymtNo;

    /**
     * 원본결제번호(orgl_pymt_no)
     */
    private String orglPymtNo;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * PG가맹점번호(pg_frcs_no)
     */
    private String pgFrcsNo;

    /**
     * PG승인번호(pg_apv_no)
     */
    private String pgApvNo;

    /**
     * 배송비용결제여부(dlv_exp_pymt_yn) not null
     */
    private String dlvExpPymtYn;

    /**
     * 결제구분코드(pymt_div_cd) not null
     */
    private String pymtDivCd;

    /**
     * 결제차수(pymt_nos) not null
     */
    private Long pymtNos;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * 결제상태코드(pymt_st_cd) not null
     */
    private String pymtStCd;

    /**
     * 결제수단코드(pymt_mns_cd)
     */
    private String pymtMnsCd;

    /**
     * 추가결제방법코드(add_pymt_mthd_cd)
     */
    private String addPymtMthdCd;

    /**
     * 결제일시(pymt_dtm)
     */
    private String pymtDtm;

    /**
     * 결제취소환불일시(pymt_cncl_rfnd_dtm)
     */
    private String pymtCnclRfndDtm;

    /**
     * 복지제도포인트구분코드(wsp_div_cd)
     */
    private String wspDivCd;

    /**
     * 복지제도포인트코드(wsp_cd)
     */
    private String wspCd;

    /**
     * 현금영수증발행대상금액(csrc_pblc_obj_amt) not null
     */
    private BigDecimal csrcPblcObjAmt;

    /**
     * 결제파라미터내용J(pymt_para_cntnj)
     */
    private String pymtParaCntnj;

    /**
     * 결제처리메모(pymt_prcs_memo)
     */
    private String pymtPrcsMemo;

    /**
     * 배송비용결제취소메모(dlv_exp_pymt_cncl_memo)
     */
    private String dlvExpPymtCnclMemo;

    /**
     * 배송비용결제메모(dlv_exp_pymt_memo)
     */
    private String dlvExpPymtMemo;

    /**
     * 우리모아포인트승인번호(wmpnt_apv_no)
     */
    private Long wmpntApvNo;

    /**
     * 카드사명(crdc_nm)
     */
    private String crdcNm;

    /**
     * 카드종류코드(crd_knd_cd)
     */
    private String crdKndCd;

    /**
     * 암호화카드번호(enc_crd_no)
     */
    private String encCrdNo;

    /**
     * 카드사승인번호(crdc_apv_no)
     */
    private String crdcApvNo;

    /**
     * 카드결제무이자할부여부(crd_pymt_wint_inst_yn) not null
     */
    private String crdPymtWintInstYn;

    /**
     * 카드할부개월수(crd_inst_mcnt)
     */
    private Integer crdInstMcnt;

    /**
     * 카드결제부분취소가능여부(crd_pymt_inpa_poss_yn) not null
     */
    private String crdPymtInpaPossYn;

    /**
     * 결제카드대체번호(pymt_crd_rplc_no)
     */
    private String pymtCrdRplcNo;

    /**
     * 간편결제종류코드(smpy_knd_cd)
     */
    private String smpyKndCd;

    /**
     * 네이버페이승인번호(nvpay_apv_no)
     */
    private String nvpayApvNo;

    /**
     * 금융기관코드(fnns_cd)
     */
    private String fnnsCd;

    /**
     * 배송비용환불대상코드(dlv_exp_rfnd_obj_cd)
     */
    private String dlvExpRfndObjCd;

    /**
     * 코나아이주문번호(konai_ord_no)
     */
    private String konaiOrdNo;

    /**
     * 코나아이거래일시(konai_trd_dtm)
     */
    private String konaiTrdDtm;

    /**
     * 포인트제도마감일자(pnt_rgm_clsg_dt)
     */
    private String pntRgmClsgDt;

    /**
     * 제도마감이후포인트소멸금액(rgm_clsg_aft_pnt_extn_amt) not null
     */
    private BigDecimal rgmClsgAftPntExtnAmt;

    /**
     * E쿠폰협력사연동결과코드(ecpn_csp_intl_rslt_cd)
     */
    private String ecpnCspIntlRsltCd;

    /**
     * E쿠폰복지제도포인트우선차감일시(ecpn_wsp_pfr_sbtr_dtm)
     */
    private String ecpnWspPfrSbtrDtm;

    /**
     * E쿠폰복지제도포인트확정차감일시(ecpn_wsp_cnfm_sbtr_dtm)
     */
    private String ecpnWspCnfmSbtrDtm;


}
