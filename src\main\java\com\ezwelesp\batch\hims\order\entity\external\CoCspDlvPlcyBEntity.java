package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 협력사배송정책기본(ez_co.co_csp_dlv_plcy_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class CoCspDlvPlcyBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 배송정책순번(dlv_plcy_seq) not null
     */
    private Long dlvPlcySeq;

    /**
     * 배송정책코드(dlv_plcy_cd) not null
     */
    private String dlvPlcyCd;

    /**
     * 상품배송마감시각시(gds_dlv_clsg_hh)
     */
    private String gdsDlvClsgHh;

    /**
     * 주문이후출고완료최대일수(ord_aft_obnd_cmpt_max_dcnt)
     */
    private Integer ordAftObndCmptMaxDcnt;

    /**
     * 대표정책여부(rps_plcy_yn) not null
     */
    private String rpsPlcyYn;

    /**
     * 사용여부(use_yn) not null
     */
    private String useYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
