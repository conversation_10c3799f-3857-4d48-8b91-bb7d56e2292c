package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 도서주문배송상세(ez_or.or_book_ord_dlv_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrBookOrdDlvDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 제휴사주문번호(asp_ord_no) not null
     */
    private String aspOrdNo;

    /**
     * 도서주문상품순번(book_ord_gds_seq) not null
     */
    private Long bookOrdGdsSeq;

    /**
     * 운송장번호(invc_no) not null
     */
    private String invcNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 도서상품배송상태코드(book_gds_dlv_st_cd) not null
     */
    private String bookGdsDlvStCd;

    /**
     * 배송회사코드(dlv_co_cd)
     */
    private String dlvCoCd;

    /**
     * 배송완료일시(dlv_cmpt_dtm)
     */
    private String dlvCmptDtm;

    /**
     * 배송상품수량(dlv_gds_qty)
     */
    private Integer dlvGdsQty;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
