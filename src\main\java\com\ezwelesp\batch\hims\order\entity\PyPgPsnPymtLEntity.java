package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * PG개인결제내역(ez_or.py_pg_psn_pymt_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyPgPsnPymtLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * PG개인결제내역순번(pg_psn_pymt_lst_seq) not null
     */
    private Long pgPsnPymtLstSeq;

    /**
     * PG종류코드(pg_knd_cd) not null
     */
    private String pgKndCd;

    /**
     * PG승인번호(pg_apv_no) not null
     */
    private String pgApvNo;

    /**
     * 취소PG승인번호(cncl_pg_apv_no) not null
     */
    private String cnclPgApvNo;

    /**
     * PG가맹점번호(pg_frcs_no)
     */
    private String pgFrcsNo;

    /**
     * PG결제구분코드(pg_pymt_div_cd)
     */
    private String pgPymtDivCd;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 채널코드(ch_cd)
     */
    private String chCd;

    /**
     * PG결제채널코드(pg_pymt_ch_cd)
     */
    private String pgPymtChCd;

    /**
     * 주문일시(ord_dtm)
     */
    private String ordDtm;

    /**
     * 고객사코드(clnt_cd)
     */
    private String clntCd;

    /**
     * 주문상태코드(ord_st_cd)
     */
    private String ordStCd;

    /**
     * 주문금액(ord_amt) not null
     */
    private BigDecimal ordAmt;

    /**
     * 결제수단코드(pymt_mns_cd)
     */
    private String pymtMnsCd;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * 결제취소금액(pymt_cncl_amt) not null
     */
    private BigDecimal pymtCnclAmt;

    /**
     * 결제일시(pymt_dtm)
     */
    private String pymtDtm;

    /**
     * PG결제대사상태코드(pg_pymt_cmpr_st_cd)
     */
    private String pgPymtCmprStCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
