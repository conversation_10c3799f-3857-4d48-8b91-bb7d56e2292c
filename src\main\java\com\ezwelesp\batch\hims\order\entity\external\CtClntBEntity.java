package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 고객사기본(ez_ct.ct_clnt_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class CtClntBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 복지몰번호(hezo_no)
     */
    private Long hezoNo;

    /**
     * 고객사명(clnt_nm)
     */
    private String clntNm;

    /**
     * 대표자명(rpsr_nm)
     */
    private String rpsrNm;

    /**
     * 사업자등록번호(bzrn)
     */
    private String bzrn;

    /**
     * 우편번호(zipcd)
     */
    private String zipcd;

    /**
     * 기본주소(bas_adr)
     */
    private String basAdr;

    /**
     * 상세주소(dtl_adr)
     */
    private String dtlAdr;

    /**
     * 팩스전화번호(fax_telno)
     */
    private String faxTelno;

    /**
     * 전화번호(telno)
     */
    private String telno;

    /**
     * 사업분류코드(bz_cls_cd)
     */
    private String bzClsCd;

    /**
     * 고객사형태코드(clnt_shap_cd)
     */
    private String clntShapCd;

    /**
     * 고객사분류코드(clnt_cls_cd)
     */
    private String clntClsCd;

    /**
     * 계약여부(cont_yn) not null
     */
    private String contYn;

    /**
     * 알림사용여부(infm_use_yn) not null
     */
    private String infmUseYn;

    /**
     * 제도마감월(rgm_clsg_mm)
     */
    private String rgmClsgMm;

    /**
     * 제도혜택EMS수신가능여부(rgm_bnft_ems_rcv_poss_yn) not null
     */
    private String rgmBnftEmsRcvPossYn;

    /**
     * 제도혜택문자메시지수신가능여부(rgm_bnft_tmsg_rcv_poss_yn) not null
     */
    private String rgmBnftTmsgRcvPossYn;

    /**
     * 적립금사용여부(mlg_use_yn) not null
     */
    private String mlgUseYn;

    /**
     * 통합로그인사용여부(untd_lgin_use_yn) not null
     */
    private String untdLginUseYn;

    /**
     * 세금계산서발행여부(txin_pblc_yn) not null
     */
    private String txinPblcYn;

    /**
     * 청구서분리기준코드(invcs_divd_bsic_cd)
     */
    private String invcsDivdBsicCd;

    /**
     * 정산청구서분리유형코드(stl_invcs_divd_typ_cd)
     */
    private String stlInvcsDivdTypCd;

    /**
     * 영업담당관리자ID(sls_chrg_mgr_id)
     */
    private String slsChrgMgrId;

    /**
     * 포인트사용동의여부(pnt_use_agr_yn) not null
     */
    private String pntUseAgrYn;

    /**
     * 포인트정책동의사용여부(pnt_plcy_agr_use_yn) not null
     */
    private String pntPlcyAgrUseYn;

    /**
     * 포인트충전사용여부(pnt_elctc_use_yn) not null
     */
    private String pntElctcUseYn;

    /**
     * 특별포인트배정안내문자메시지사용여부(spp_asg_gd_tmsg_use_yn) not null
     */
    private String sppAsgGdTmsgUseYn;

    /**
     * 선택적복지포인트배정안내문자메시지사용여부(wfp_asg_gd_tmsg_use_yn) not null
     */
    private String wfpAsgGdTmsgUseYn;

    /**
     * 포인트합계마이너스검토제외여부(pnt_tot_mnus_chck_excld_yn) not null
     */
    private String pntTotMnusChckExcldYn;

    /**
     * 퇴직사용자환급신청사용여부(retr_usr_dwb_apl_use_yn) not null
     */
    private String retrUsrDwbAplUseYn;

    /**
     * 고객사공통환급신청처리주체코드(cc_dwb_apl_prcs_magn_cd)
     */
    private String ccDwbAplPrcsMagnCd;

    /**
     * 제도본인인증매년사용여부(rgm_self_crtf_evyr_use_yn) not null
     */
    private String rgmSelfCrtfEvyrUseYn;

    /**
     * 제휴사페이지이동서비스사용여부(asp_page_mv_srv_use_yn) not null
     */
    private String aspPageMvSrvUseYn;

    /**
     * 제휴사헤더연동템플릿번호(asp_hdr_intl_tmpl_no)
     */
    private String aspHdrIntlTmplNo;

    /**
     * 복지카드사용여부(wfcd_use_yn) not null
     */
    private String wfcdUseYn;

    /**
     * 특별포인트검색기간범위코드(spp_srch_term_scop_cd)
     */
    private String sppSrchTermScopCd;

    /**
     * 특별포인트검색기간범위숫자값(spp_srch_term_scop_nval)
     */
    private BigDecimal sppSrchTermScopNval;

    /**
     * 선택적복지포인트검색가능최대일수(wfp_srch_poss_max_dcnt)
     */
    private Integer wfpSrchPossMaxDcnt;

    /**
     * 특별포인트검색가능최대일수(spp_srch_poss_max_dcnt)
     */
    private Integer sppSrchPossMaxDcnt;

    /**
     * 복지몰서비스사용여부(hezo_srv_use_yn) not null
     */
    private String hezoSrvUseYn;

    /**
     * 고객사유형코드(clnt_typ_cd) not null
     */
    private String clntTypCd;

    /**
     * 선택적복지포인트운영여부(wfp_oper_yn) not null
     */
    private String wfpOperYn;

    /**
     * 특별포인트운영여부(spp_oper_yn) not null
     */
    private String sppOperYn;

    /**
     * 선택적복지포인트실링적용여부(wfp_ceil_aply_yn) not null
     */
    private String wfpCeilAplyYn;

    /**
     * 특별포인트실링적용여부(spp_ceil_aply_yn) not null
     */
    private String sppCeilAplyYn;

    /**
     * 주문자정보수정가능여부(ordr_inf_mod_poss_yn) not null
     */
    private String ordrInfModPossYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

    /**
     * 선택적복지포인트검색기간범위코드(wfp_srch_term_scop_cd)
     */
    private String wfpSrchTermScopCd;

    /**
     * 선택적복지포인트검색기간범위숫자값(wfp_srch_term_scop_nval)
     */
    private BigDecimal wfpSrchTermScopNval;

    /**
     * 선택적복지포인트사용기한코드(wfp_use_dlin_cd)
     */
    @Builder.Default
    private String wfpUseDlinCd = "1";

}
