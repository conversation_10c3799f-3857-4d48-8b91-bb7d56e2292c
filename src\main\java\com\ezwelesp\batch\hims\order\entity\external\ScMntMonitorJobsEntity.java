package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * [비표준]모니터링JOB정보
 * sc_mnt_monitor_jobs
 */
@Data
public class ScMntMonitorJobsEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 4186275896320108531L;

    /**
     * 모니터링잡아이디
     */
    private String jobId;

    /**
     * 모니터링잡명칭
     */
    private String jobNm;

    /**
     * 모니터링잡설명
     */
    private String jobDesc;

    /**
     * 모니터링주기
     */
    private String jobCycle;

    /**
     * 시스템코드
     */
    private String sysCd;

    /**
     * 모니터링대상코드
     */
    private String typeCd;

    /**
     * 알람발송여부
     */
    private String alramYn;

    /**
     * 알람연속발송최대수사용유무
     */
    private String alramCntMaxYn;

    /**
     * 알람연속발송최대수량
     */
    private Long alramCntMax;

    /**
     * 임계치사용유무
     */
    private String criticalValueYn;

    /**
     * 임계치값
     */
    private Long criticalValue;

    /**
     * 임계치단위
     */
    private String criticalValueUnit;

    /**
     * 등록자유저키
     */
    private Long regId;

    /**
     * 수정자 저키
     */
    private Long modiId;

    /**
     * 등록일시
     */
    private String regDt;

    /**
     * 수정일시
     */
    private String modiDt;
}
