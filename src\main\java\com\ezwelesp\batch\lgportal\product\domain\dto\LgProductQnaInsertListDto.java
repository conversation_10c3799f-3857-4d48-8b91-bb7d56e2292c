package com.ezwelesp.batch.lgportal.product.domain.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgProductQnaInsertListDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgProductQnaInsertListDto {
    private String ttl;      // 제목
    private String inqCntn;  // 문의 내용
    private String gdsCd;    // 상품 코드
    private String gdsNm;    // 상품 이름
    private String cspCd;    // 협력사 코드
    private String lgpBbsNo; // LG Portal 게시판 번호
}
