package com.ezwelesp.batch.lgportal.product.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgUpdateProductDeliveryRequestDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgUpdateProductDeliveryRequestDto {
    private String productMappingCode;  // 이지웰 상품번호
    private Integer productNo;          // lg 상품번호
    private Integer shippingLoc;        // 배송지
    private Integer exchangeLoc;        // 교환지
    private Integer returnLoc;          // 반품지
    private Integer addDeliveryCost;    // 도서산간배송비용
    private String displayContents;     // 표시내용
}


