package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 상품옵션조합구성상세(ez_pd.pd_gds_opt_comb_cmp_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PdGdsOptCombCmpDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 상품코드(gds_cd) not null
     */
    private String gdsCd;

    /**
     * 옵션상품조합순번(opt_gds_comb_seq) not null
     */
    private Long optGdsCombSeq;

    /**
     * 옵션상품순번(opt_gds_seq) not null
     */
    private Long optGdsSeq;

    /**
     * 옵션상품조합구성명(opt_gds_comb_cmp_nm) not null
     */
    private String optGdsCombCmpNm;

    /**
     * 정렬순서(sort_ordg) not null
     */
    private Integer sortOrdg;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
