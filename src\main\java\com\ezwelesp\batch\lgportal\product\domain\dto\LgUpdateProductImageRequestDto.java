package com.ezwelesp.batch.lgportal.product.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgUpdateProductImageRequestDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgUpdateProductImageRequestDto {
    private String productMappingCode;  // 이지웰 상품번호
    private Integer productNo;          // lg 상품번호
    private String mainImageType;       // 메인 이미지 유형 (I : 이미지 유형, L:동영상 링크)
    private String mainImageUrl;        // 메인 이미지 Url

}


