package com.ezwelesp.batch.hpas.alarm.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import com.ezwelesp.batch.hpas.alarm.tasklet.EntryCspMainTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class EntryCspMainJobConfig {
    private final CommonJobListener commonJobListener;
    private final EntryCspMainTasklet entryCspMainTasklet;

    @Bean("entryCspMainJob")
    public Job entryCspMainJob(JobRepository jobRepository,
            @Qualifier("entryCspMainStep") Step entryCspMainStep) {
        return new JobBuilder("entryCspMainJob", jobRepository)
                .listener(commonJobListener)
                .start(entryCspMainStep)
                .build();
    }

    @Bean("entryCspMainStep")
    public Step entryCspMainStep(JobRepository jobRepository,
            DataSourceTransactionManager transactionManager) {
        return new StepBuilder("entryCspMainStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(entryCspMainTasklet, transactionManager)
                .build();

    }
}
