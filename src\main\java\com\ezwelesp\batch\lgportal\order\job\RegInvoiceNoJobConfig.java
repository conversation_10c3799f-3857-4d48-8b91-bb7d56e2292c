package com.ezwelesp.batch.lgportal.order.job;

import com.ezwelesp.batch.lgportal.entity.ApiBatchDlvrEntity;
import com.ezwelesp.batch.lgportal.order.chunk.reader.DeliveryCompleteItemReader;
import com.ezwelesp.batch.lgportal.order.chunk.reader.RegInvoiceNoItemReader;
import com.ezwelesp.batch.lgportal.order.chunk.writer.DeliveryCompleteItemWriter;
import com.ezwelesp.batch.lgportal.order.chunk.writer.RegInvoiceNoItemWriter;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.listener.CommonJobListener;
import com.ezwelesp.batch.listener.CommonStepListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class RegInvoiceNoJobConfig {

    private final String JOB_REG_INVOICE_NO_STEP = LgConstants.JobName.JOB_REG_INVOICE_NO + "_STEP";

    private final CommonJobListener commonJobListener;

    @Value("${lgportal.chunk-size}")
    private int chunkSize;


    @Bean(LgConstants.JobName.JOB_REG_INVOICE_NO)
    public Job regInvoiceNoJob(JobRepository jobRepository, @Qualifier(JOB_REG_INVOICE_NO_STEP) Step regInvoiceNoStep) {
        return new JobBuilder(LgConstants.JobName.JOB_REG_INVOICE_NO, jobRepository)
                .listener(commonJobListener)
                .start(regInvoiceNoStep)
                .build();
    }

    @Bean(name = JOB_REG_INVOICE_NO_STEP)
    public Step regInvoiceNoStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager,
            RegInvoiceNoItemReader regInvoiceNoItemReader,
            RegInvoiceNoItemWriter regInvoiceNoItemWriter, CommonStepListener commonStepListener) {

        return new StepBuilder(JOB_REG_INVOICE_NO_STEP, jobRepository)
                .allowStartIfComplete(true)
                .<ApiBatchDlvrEntity, ApiBatchDlvrEntity>chunk(chunkSize, transactionManager)
                .transactionManager(transactionManager)
                .reader(regInvoiceNoItemReader)
                .writer(regInvoiceNoItemWriter)
                .listener(commonStepListener)
                .build();

    }
}
