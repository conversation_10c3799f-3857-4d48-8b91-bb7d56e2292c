package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.File;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 반품접수 상품
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto
 * @since 2025.05.09
 */
@Getter
@SuperBuilder
@Jacksonized
public class ClaimReturnApplyGoodsDto implements Serializable {
    @Serial
    private static final long serialVersionUID = -1096514438236557903L;

    // 주문상품순번
    private long ordGdsSeq;

    // 클레임요청수량
    private int clmGdsQty;

    // 클레임사유코드
    private String clmRsnCd;

    // 클레임사유내용
    private String clmRsnCntn;

    // 클레임 첨부파일
    private List<File> claimAttachFileList;

    /*
     * Batch 에서 추가되는 파라미터
     */

    // LG포털API배송상세번호
    private String lgpApiDlvDtlNo;

}
