package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * H포인트결제내역(ez_or.py_hpnt_pymt_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyHpntPymtLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 결제번호(pymt_no) not null
     */
    private String pymtNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 결제구분코드(pymt_div_cd) not null
     */
    private String pymtDivCd;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 가족회원번호(fam_mem_no)
     */
    private String famMemNo;

    /**
     * H포인트통합회원번호(hpnt_untd_mem_no) not null
     */
    private String hpntUntdMemNo;

    /**
     * H포인트사용유형코드(hpnt_use_typ_cd) not null
     */
    private String hpntUseTypCd;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * 결제포인트(pymt_pnt) not null
     */
    private BigDecimal pymtPnt;

    /**
     * H포인트플러스포인트금액(hpnt_plspnt_amt)
     */
    private BigDecimal hpntPlspntAmt;

    /**
     * H포인트플러스쿠폰번호(hpnt_plus_cpn_no)
     */
    private String hpntPlusCpnNo;

    /**
     * 정산일자(stl_dt) not null
     */
    private String stlDt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
