package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 공동구매상품상세(ez_or.or_grpp_gds_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrGrppGdsDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 공동구매코드(grpp_cd) not null
     */
    private String grppCd;

    /**
     * 상품코드(gds_cd) not null
     */
    private String gdsCd;

    /**
     * 판매가능상품수량(sell_poss_gds_qty)
     */
    private Integer sellPossGdsQty;

    /**
     * 우선순위(prr)
     */
    private Integer prr;

    /**
     * 공동구매상품수량추가여부(grpp_gds_qty_add_yn) not null
     */
    private String grppGdsQtyAddYn;

    /**
     * 공동구매광고상품여부(grpp_ad_gds_yn) not null
     */
    private String grppAdGdsYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
