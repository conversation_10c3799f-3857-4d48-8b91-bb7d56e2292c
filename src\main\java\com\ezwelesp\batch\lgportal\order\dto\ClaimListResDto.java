package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Data;

import java.util.List;

/**
  * 교환/반품 대상 리스트 사용 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see ClaimListResDto
  */
@Data
public class ClaimListResDto {

    private Long salesOrderExceptNo;
    private Long salesOrderNo;
    private String orderName;
    private String orderDate;
    private String exceptStatusCode;
    private String siteName;
    private String orderStatusCode;
    private String orderNo;
    private String exceptTypeCode;
    private String exceptDetailTypeCode;
    private String memo;

    private List<OrderItemExcept> orderItemExcepts;
    private List<History> exceptionHistory;

    @Data
    public static class OrderItemExcept {
        private Long orderItemExceptNo;
        private String deliveryStatusCode;
        private String cancelYN;
        private String productCode;
        private String productName;
        private String productNo;
        private String skuVal;
        private String skuCode;
        private Integer orderCnt;
        private Integer remainCnt;
        private Integer cancelCnt;
        private String modifierName;
        private String changed;
        private Long orderItemNo;
        private Long orgOrderItemNo;
    }

    @Data
    public static class History {
        private String exceptTypeCode;
        private String exceptStatusCode;
        private String exceptDetailTypeCode;
        private String memo;
        private String exceptStatusUpdateDate;
    }
}
