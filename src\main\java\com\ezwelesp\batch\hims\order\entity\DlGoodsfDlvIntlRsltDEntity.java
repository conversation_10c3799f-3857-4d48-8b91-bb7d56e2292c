package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 굿스플로배송연동결과상세(ez_or.dl_goodsf_dlv_intl_rslt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlGoodsfDlvIntlRsltDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송연동고유번호(dlv_intl_innt_no) not null
     */
    private String dlvIntlInntNo;

    /**
     * 운송장번호(invc_no) not null
     */
    private String invcNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq)
     */
    private Long ordGdsSeq;

    /**
     * 배송회사코드(goodsf_dlv_co_cd) not null
     */
    private String goodsfDlvCoCd;

    /**
     * 배송상품수량(dlv_gds_qty)
     */
    private Integer dlvGdsQty;

    /**
     * 배송연동상태코드(dlv_intl_st_cd) not null
     */
    private String dlvIntlStCd;

    /**
     * 배송연동처리완료일시(dlv_intl_prcs_cmpt_dtm) not null
     */
    private String dlvIntlPrcsCmptDtm;

    /**
     * 배송상품연동오류코드(dlv_gds_intl_err_cd)
     */
    private String dlvGdsIntlErrCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
