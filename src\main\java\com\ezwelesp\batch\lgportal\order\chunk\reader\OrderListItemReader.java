package com.ezwelesp.batch.lgportal.order.chunk.reader;

import com.ezwelesp.batch.lgportal.entity.*;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.OrderListReqDto;
import com.ezwelesp.batch.lgportal.order.dto.OrderListResDto;
import com.ezwelesp.batch.lgportal.order.mapper.query.OrderQueryMapper;
import com.ezwelesp.batch.lgportal.order.mapper.query.ProductQueryMapper;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.DateUtils;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemReader;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

/**
 * The type Order list item reader.
 */
@Slf4j
@Component
@StepScope
@RequiredArgsConstructor
public class OrderListItemReader implements ItemReader<OrOrdBEntity> {

    private final String serverHost;
    private final int timeout;
    private final String apiKey;
    private final OrderQueryMapper orderQueryMapper;
    private final ProductQueryMapper productQueryMapper;

    private String beforeTime;
    private String nowTime;

    @Override
    public OrOrdBEntity read() throws Exception {

        OrderListReqDto orderListReqDto = OrderListReqDto.builder().build();

        try {
            AjaxResult
                    result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_GET_DELIVERYSLIP, this.timeout, JsonObjectConverter.serialize(
                    orderListReqDto.toBuilder()
                            .apiKey(apiKey)
                            .deliveryStatusCodeChanged_above(beforeTime)
                            .deliveryStatusCodeChanged_below(nowTime)
                            .build()
            ));


            if (StringUtils.equals(String.valueOf(HttpStatus.OK.value()), result.get("result").toString())) {

                ObjectMapper objectMapper = this.configMapper();
                List<OrderListResDto> resDtoList = objectMapper.convertValue(result.get("items"),
                        new TypeReference<>() {
                        });

                log.debug("size {}" , resDtoList.size());

                List<OrderTmpEntity> orderTmpEntityList = resDtoList.stream().map(this::setOrderTmpEntity).collect(Collectors.toList());




            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("LG portal api request failed");
        }

        return null;
    }

    private OrderTmpEntity setOrderTmpEntity(OrderListResDto resDto) {


        //주문일시 ordDtm Date date = sdf.parse(String.valueOf(((JSONObject)deliverySlipItem.get(0)).get("created")));
        //orderDate = dateParser.format(date);

                /*// 주문상태(orderStatusCode)는 요청상태와 동일



                -- orderParamMap.put("payType", "9055");

                pymt_prcs_memo orderParamMap.put("payMemo", "lgcns-api연동주문");
                orderParamMap.put("payKind", "1001");
                포인트결제 1001 orderParamMap.put("payCd", "1001");
                결제대기 STBY orderParamMap.put("payStatus", "1001");
                orderParamMap.put("payTypeUseYn", "YNNNNNNNNNNNNN");
                GNRL ordTypCd 주문유형코드
                */

        List<OrderListResDto.DeliveryItem> deliveryItems = resDto.getDeliveryItems();
        OrderListResDto.DeliveryItem deliveryItem = deliveryItems.get(0);

        // 주문일시
        String ordDtm = deliveryItem.getCreated();

        if (StringUtils.isNotEmpty(ordDtm)) {
            ordDtm = DateUtils.parseDateToStr(DateUtils.YYYYMMDDHHMMSS, DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, ordDtm));
        }

        // 주문상품 정보
        List<OrderGdsTmpEntity> gdsTmpEntityList = this.setOrderGdsTmpEntityList(deliveryItems);

        // 배송주소 정보
        OrderDlvTmpEntity orderDlvTmpEntity = OrderDlvTmpEntity.builder()
                .dlvStCd(LgConstants.DlvStCd.findEzwelCode("ACC1022"))
                .rcvrNm(resDto.getRecvName())
                .rcvrMblTelno(resDto.getRecvMobilePhone())
                .rcvrZipcd(resDto.getZipcode())
                .rcvrBasAdr(resDto.getAddr())
                .rcvrDtlAdr(resDto.getAddr2())
                .build();
        
        // 주문정보
        return OrderTmpEntity.builder()
                .clntOrdNo(String.valueOf(resDto.getSalesOrderNo()))
                .ordStCd(LgConstants.OrdStCd.findEzwelCode(resDto.getOrderStatusCode()))
                .ordTypCd("GNRL")
                .userKey("1000000000")
                .clntCd("lgapi")
                .aspOrdCspCd("10000811")
                .acssDvcCd("PCWB")
                .stdMenuCd("10001942")
                .ordDtm(ordDtm)
                .gdsTmpEntityList(gdsTmpEntityList)
                .dlvTmpEntity(orderDlvTmpEntity)
                .build();

        /* 주문정보 중 미적용 정보
        ordTypCd orderParamMap.put("orderType", "1002");
        orderParamMap.put("levelCd", "10101013");
        orderParamMap.put("cspId", "ezshop");*/
    }

    private List<OrderGdsTmpEntity> setOrderGdsTmpEntityList(List<OrderListResDto.DeliveryItem> deliveryItems) {
        return deliveryItems.stream().flatMap(deliveryItem ->
                this.setOrderGdsTmpEntitys(deliveryItem).stream()).collect(Collectors.toList());
    }

    private List<OrderGdsTmpEntity> setOrderGdsTmpEntitys(OrderListResDto.DeliveryItem deliveryItem) {

        List<OrderGdsTmpEntity> gdsTmpEntityList = new ArrayList<>();

        //this.odOrdQueryMapper.selectApiDlvrDetailNoCount(deliveryItem.)

        PdGdsCEntity pdGdsCEntity = this.productQueryMapper.selectGoods(deliveryItem.getProductCode());

        //TODO-JSKIM27 상품옵션에 따라 'insertSKUSoldout' 생성 데이터 준비,
        // 상품, 옵션 가격 정보는 데이터 생성 시점에 재조회하는 방식으로 적용
        // workspace-batch 272 line

        //orderParamMap.put("cspDlvrId", goodsEzMap.get("CSP_DLVR_ID"));

        if (this.productQueryMapper.selectGoodsOptionBase(deliveryItem.getProductCode()) > 0) {
            String optTypeCd = pdGdsCEntity.getGdsOptTypCd(); // 상품옵션유형코드
            if (StringUtils.equals(LgConstants.GoodsOptionType.SINGLE.getCode(), optTypeCd)) {
                // 단일형
            }
            else {
                // 조합형
            }
        }



        //PdGdsOptCombBEntity pdGdsOptCombBEntity = this.productQueryMapper.selectGoodsOption(deliveryItem.getProductCode());

        gdsTmpEntityList.add(OrderGdsTmpEntity.builder()
                .chCd("HEWS")   // 복지샵
                .gdsNm(deliveryItem.getProductName())
                .gdsCd(deliveryItem.getProductCode())
                .ordGdsQty(deliveryItem.getRealOrderCnt())              // 주문상품수량
                .cspCd(pdGdsCEntity.getCspCd())
                .hezoStdCtgrCd(deliveryItem.getStandardCategoryNo())    // 복지몰표준카테소리
                .lgpApiDlvNo(String.valueOf(deliveryItem.getDeliveryCmdNo()))
                .lgpApiDlvDtlNo(String.valueOf(deliveryItem.getOrderItemNo()))
                .productNo(deliveryItem.getProductNo())
                .build());

        return gdsTmpEntityList;
    }

    /**
     * Job실행 파라미터(시작, 종료 시간등) 설정
     *  - 'Program arguments' 에 --mode=all --beforeTime="2014-03-01 00:00:00" --nowTime="2015-12-01 23:59:59" 와 같이 설정
     *
     * @param mode the mode
     * @param beforeTime the before time
     * @param nowTime the now time
     */
    public void setMode(String mode, String beforeTime, String nowTime) {
        if (StringUtils.isEmpty(nowTime) && StringUtils.isEmpty(beforeTime)) {

            Calendar cal = Calendar.getInstance();

            if (StringUtils.equals("all", mode)) {
                cal.add(Calendar.DATE, -1);
                this.nowTime = DateUtils.dateTime(cal.getTime()) + " 23:59:59";
                this.beforeTime = DateUtils.dateTime(cal.getTime()) + " 00:00:00";
            }
            else {
                cal.add(Calendar.HOUR, 1);
                this.nowTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, cal.getTime());
                cal.add(Calendar.HOUR, -12);
                this.beforeTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, cal.getTime());
            }
        }
        else {
            this.beforeTime = beforeTime;
            this.nowTime = nowTime;
        }
    }

    private ObjectMapper configMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        return mapper;
    }

}
