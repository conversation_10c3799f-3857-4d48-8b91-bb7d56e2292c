package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 상품평균배송일수집계(ez_or.dl_gds_avg_dlv_dcnt_s)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlGdsAvgDlvDcntSEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 상품코드(gds_cd) not null
     */
    private String gdsCd;

    /**
     * 평균배송일수(avg_dlv_dcnt) not null
     */
    private Integer avgDlvDcnt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
