package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
  * 품절취소 요청 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see OrderCancelOstkReqDto
  */
@Data
public class OrderCancelOstkReqDto {
    private String apiKey;

    // 공통사유코드 '운영자취소'
    private String exceptTypeCode;

    // 취소상세 사유코드 '재고이상'
    private String exceptDetailTypeCode;
    
    // 취소사유
    private String memo;

    // 판매주문번호
    private String saleOrderNo;

    private List<OrderGoods> products;

    public OrderCancelOstkReqDto(String apiKey) {
        this.apiKey = apiKey;
        this.exceptTypeCode = "ORD1012";
        this.exceptDetailTypeCode = "ORD101H";
        this.memo = "품절";

        this.products = new ArrayList<>();
    }

    @Builder
    @Data
    public static class OrderGoods {
        private Long orderItemNo;
        private Integer requestCnt;
    }

}
