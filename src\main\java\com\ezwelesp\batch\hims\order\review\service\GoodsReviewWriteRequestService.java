package com.ezwelesp.batch.hims.order.review.service;

import com.ezwelesp.batch.hims.order.entity.OrOrdGdsDEntity;
import com.ezwelesp.batch.hims.order.review.domain.GoodsReviewWriteRequestDto;

import java.util.List;

public interface GoodsReviewWriteRequestService {

    List<GoodsReviewWriteRequestDto> getGoodsReviewWriteRqAppPushTarget();
    List<GoodsReviewWriteRequestDto> getEzCheckinAppPushTarget();
    void updateOrderGoodsPushYn(List<OrOrdGdsDEntity> list);
}
