package com.ezwelesp.batch.hims.client.contract.mapper.command;

import com.ezwelesp.batch.hims.client.contract.domain.CtSlsContBAutoExtendDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> 임종갑 플그림
 * @see : com.ezwelesp.batch.hims.client.contract.mapper.command
 * @since : 2025-06-16
 */
@Mapper
public interface ContractAutoExtendCommandMapper {
    /**
     * 계약 종료일자 연장
     *
     * <AUTHOR>
     * @since 임종갑 플그림
     * 
     */
    void modifyContractExtend(CtSlsContBAutoExtendDto ctSlsContBAutoExtendDto);

    /**
     * 계약기본 정보 히스토리 저장
     *
     * <AUTHOR>
     * @since 임종갑 플그림
     * 
     */
    void insertCtSlsContBHistory(CtSlsContBAutoExtendDto ctSlsContBAutoExtendDto);
}
