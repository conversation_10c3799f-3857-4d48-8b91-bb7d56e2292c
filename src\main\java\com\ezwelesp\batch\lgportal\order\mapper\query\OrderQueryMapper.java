package com.ezwelesp.batch.lgportal.order.mapper.query;

import com.ezwelesp.batch.lgportal.entity.OrOrdBEntity;
import com.ezwelesp.batch.lgportal.order.dto.ExchRtpGoodsInfoDto;
import com.ezwelesp.batch.lgportal.order.dto.ExchRtpOrderInfoDto;

/**
 * The interface user query mapper.
 */
public interface OrderQueryMapper {
    int selectOrderCountByClientOrderNum(String clientOrderNum);

    OrOrdBEntity selectOrderInfoByClientOrderNum(String clientOrderNum);

    int selectApiDlvrDetailNoCount(String orderNum, String deliveryItemNo);
}
