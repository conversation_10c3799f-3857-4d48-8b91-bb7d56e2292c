package com.ezwelesp.batch.lgportal.product.chunk.reader;

import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@StepScope
public class LgPortalEditProductSoldOutReader extends MyBatisPagingItemReader<LgEditProductTargetVo> {
    // 1회 read 시 가져올 row 개수
    private final int PAGE_SIZE = 1000;

    public LgPortalEditProductSoldOutReader(
            @Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory sqlSessionFactory
    ) {
        this.setName("LgPortalEditProductSoldOutReader");
        this.setSqlSessionFactory(sqlSessionFactory);
        this.setQueryId(
                "com.ezwelesp.batch.lgportal.product.mapper.query.ProductLGPortalQueryMapper.selectLgEditProductSoldOutTarget");
        this.setPageSize(PAGE_SIZE);
    }

}
