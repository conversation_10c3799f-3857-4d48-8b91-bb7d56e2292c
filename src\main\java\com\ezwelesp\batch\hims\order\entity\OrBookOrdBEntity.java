package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 도서주문기본(ez_or.or_book_ord_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrBookOrdBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 제휴사주문번호(asp_ord_no) not null
     */
    private String aspOrdNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 가족회원번호(fam_mem_no)
     */
    private String famMemNo;

    /**
     * 최종주문번호(last_ord_no) not null
     */
    private String lastOrdNo;

    /**
     * 도서주문상태코드(book_ord_st_cd) not null
     */
    private String bookOrdStCd;

    /**
     * 주문금액(ord_amt) not null
     */
    private BigDecimal ordAmt;

    /**
     * 주문수량(ord_qty) not null
     */
    private Integer ordQty;

    /**
     * 주문일시(ord_dtm) not null
     */
    private String ordDtm;

    /**
     * 선물하기주문여부(gvgft_ord_yn) not null
     */
    private String gvgftOrdYn;

    /**
     * 선물하기배송주소입력일시(gvgft_dlv_adr_insr_dtm)
     */
    private String gvgftDlvAdrInsrDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
