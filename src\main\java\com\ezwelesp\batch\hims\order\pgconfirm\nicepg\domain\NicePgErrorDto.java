package com.ezwelesp.batch.hims.order.pgconfirm.nicepg.domain;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@RequiredArgsConstructor
@Data
public class NicePgErrorDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private String errType;
	private String orderNum;

	private String clientCd;
	private String orderStatus;
	private String aspOrderNum;

	private String orderType;

	private BigDecimal recgPrice;
	private BigDecimal pgAmt;

	private String goodsNm;
	private String pgType;
	private String payType;


	private String storeId;
	private String pgDate;
	private String cancelTid;
	private int confirmNum;
	private String shopOrderNum;
	private String deviceType;

}
