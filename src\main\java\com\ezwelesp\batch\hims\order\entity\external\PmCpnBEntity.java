package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 쿠폰기본(ez_pm.pm_cpn_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PmCpnBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 쿠폰번호(cpn_no) not null
     */
    private String cpnNo;

    /**
     * 쿠폰명(cpn_nm) not null
     */
    private String cpnNm;

    /**
     * 쿠폰상태코드(cpn_st_cd) not null
     */
    private String cpnStCd;
    /**
     * 쿠폰적용채널코드(cpn_aply_ch_cd)
     */
    private String cpnAplyChCd;

    /**
     * 쿠폰발행목적코드(cpn_pblc_prps_cd)
     */
    private String cpnPblcPrpsCd;

    /**
     * 쿠폰발행목적상세코드(cpn_pblc_prps_dtl_cd)
     */
    private String cpnPblcPrpsDtlCd;
    /**
     * 쿠폰발급방식코드(cpn_issu_way_cd)
     */
    private String cpnIssuWayCd;
    /**
     * 쿠폰발급방식상세코드(cpn_issu_way_dtl_cd)
     */
    private String cpnIssuWayDtlCd;

    /**
     * 접속디바이스코드(acss_dvc_cd) not null
     */
    private String acssDvcCd;

    /**
     * 쿠폰발급방법코드(cpn_issu_mthd_cd)
     */
    private String cpnIssuMthdCd;

    /**
     * 쿠폰발행주체코드(cpn_pblc_magn_cd) not null
     */
    private String cpnPblcMagnCd;

    /**
     * 외부지원상세번호(osd_afe_dtl_no)
     */
    private Long osdAfeDtlNo;

    /**
     * 쿠폰정산주체코드(cpn_stl_magn_cd)
     */
    private String cpnStlMagnCd;

    /**
     * 쿠폰할인종류코드(cpn_dc_knd_cd) not null
     */
    private String cpnDcKndCd;

    /**
     * 쿠폰할인금액(cpn_dc_amt) not null
     */
    private BigDecimal cpnDcAmt;

    /**
     * 쿠폰할인율(cpn_dc_rt) not null
     */
    private Double cpnDcRt;

    /**
     * 쿠폰최대할인금액(cpn_max_dc_amt) not null
     */
    private BigDecimal cpnMaxDcAmt;

    /**
     * 쿠폰발급조건코드(cpn_issu_cond_cd) not null
     */
    private String cpnIssuCondCd;

    /**
     * 쿠폰구매수량기준코드(cpn_buy_qty_bsic_cd)
     */
    private String cpnBuyQtyBsicCd;

    /**
     * 쿠폰종류코드(cpn_knd_cd)
     */
    private String cpnKndCd;

    /**
     * 쿠폰사용자조건코드(cpn_usr_cond_cd)
     */
    private String cpnUsrCondCd;

    /**
     * 쿠폰사용가능수량코드(cpn_use_poss_qty_cd)
     */
    private String cpnUsePossQtyCd;

    /**
     * 주문유형코드(ord_typ_cd)
     */
    private String ordTypCd;

    /**
     * 자동발급쿠폰여부(auto_issu_cpn_yn) not null
     */
    private String autoIssuCpnYn;

    /**
     * 쿠폰담당HIFI부서코드(cpn_chrg_hifi_dept_cd)
     */
    private String cpnChrgHifiDeptCd;
    /**
     * 쿠폰담당HIFI부서명(cpn_chrg_hifi_dept_nm)
     */
    private String cpnChrgHifiDeptNm;

    /**
     * 쿠폰발급기간시작일시(cpn_issu_term_strt_dtm) not null
     */
    private String cpnIssuTermStrtDtm;

    /**
     * 쿠폰발급기간종료일시(cpn_issu_term_end_dtm) not null
     */
    private String cpnIssuTermEndDtm;

    /**
     * 쿠폰발급조건시작일시(cpn_issu_cond_strt_dtm)
     */
    private String cpnIssuCondStrtDtm;

    /**
     * 쿠폰발급조건종료일시(cpn_issu_cond_end_dtm)
     */
    private String cpnIssuCondEndDtm;

    /**
     * 발급대상고객사제외여부(issu_obj_clnt_excld_yn) not null
     */
    private String issuObjClntExcldYn;

    /**
     * 쿠폰유효기간종류코드(cpn_vlid_term_knd_cd)
     */
    private String cpnVlidTermKndCd;

    /**
     * 유효기간시작일시(vlid_term_strt_dtm)
     */
    private String vlidTermStrtDtm;

    /**
     * 유효기간종료일시(vlid_term_end_dtm)
     */
    private String vlidTermEndDtm;

    /**
     * 쿠폰유효시작일수(cpn_vlid_strt_dcnt)
     */
    private Integer cpnVlidStrtDcnt;

    /**
     * 쿠폰유효종료일수(cpn_vlid_end_dcnt)
     */
    private Integer cpnVlidEndDcnt;

    /**
     * 전체채널적용여부(all_ch_aply_yn) not null
     */
    private String allChAplyYn;

    /**
     * 전체카테고리적용여부(all_ctgr_aply_yn) not null
     */
    private String allCtgrAplyYn;

    /**
     * 전체고객사적용여부(all_clnt_aply_yn) not null
     */
    private String allClntAplyYn;

    /**
     * 고객사제외설정여부(clnt_excld_stup_yn) not null
     */
    private String clntExcldStupYn;

    /**
     * 전체상품적용여부(all_gds_aply_yn) not null
     */
    private String allGdsAplyYn;

    /**
     * 전체협력사적용여부(all_csp_aply_yn) not null
     */
    private String allCspAplyYn;

    /**
     * 발급최소구매금액(issu_min_buy_amt) not null
     */
    private BigDecimal issuMinBuyAmt;

    /**
     * 동일상품사용가능수(same_gds_use_poss_cnt) not null
     */
    private Integer sameGdsUsePossCnt;

    /**
     * 중복할인가능여부(dup_dc_poss_yn) not null
     */
    private String dupDcPossYn;

    /**
     * 옵션가격포함여부(opt_prc_incl_yn) not null
     */
    private String optPrcInclYn;
    /**
     * 구매가능최소수량(buy_poss_min_qty) not null
     */
    private Integer buyPossMinQty;

    /**
     * 최소구매가능수량(min_buy_poss_qty) not null
     */
    private Integer minBuyPossQty;

    /**
     * 우선순위(prr) not null
     */
    private Integer prr;

    /**
     * 민원처리쿠폰여부(cvap_prcs_cpn_yn) not null
     */
    private String cvapPrcsCpnYn;

    /**
     * 쿠폰사용가능최소구매금액(cpn_use_poss_min_buy_amt) not null
     */
    private BigDecimal cpnUsePossMinBuyAmt;

    /**
     * 1회최대발급가능수량(t1_max_issu_poss_qty) not null
     */
    private Integer t1MaxIssuPossQty;

    /**
     * 동일인재발급가능수량(smpr_rissu_poss_qty) not null
     */
    private Integer smprRissuPossQty;

    /**
     * 최대발급가능수량(max_issu_poss_qty) not null
     */
    private Integer maxIssuPossQty;

    /**
     * 최대사용가능수량(max_use_poss_qty) not null
     */
    private Integer maxUsePossQty;

    /**
     * 전체사용가능쿠폰할인금액(all_use_poss_cpn_dc_amt) not null
     */
    private BigDecimal allUsePossCpnDcAmt;

    /**
     * 쿠폰설명(cpn_desc)
     */
    private String cpnDesc;

    /**
     * 쿠폰사용중지사유내용(cpn_use_abrt_rsn_cntn)
     */
    private String cpnUseAbrtRsnCntn;

    /**
     * 쿠폰품의서번호(cpn_rnrb_no)
     */
    private String cpnRnrbNo;

    /**
     * 쿠폰발급이전인증필수여부(cpn_issu_bef_crtf_mndr_yn) not null
     */
    private String cpnIssuBefCrtfMndrYn;

    /**
     * 쿠폰승인상태코드(cpn_apv_st_cd)
     */
    private String cpnApvStCd;

    /**
     * 쿠폰승인상태수정일시(cpn_apv_st_mod_dtm)
     */
    private String cpnApvStModDtm;

    /**
     * 승인요청사유내용(apv_req_rsn_cntn)
     */
    private String apvReqRsnCntn;

    /**
     * 승인반려사유내용(apv_rjct_rsn_cntn)
     */
    private String apvRjctRsnCntn;

    /**
     * 승인요청관리자ID(apv_req_mgr_id)
     */
    private String apvReqMgrId;

    /**
     * 승인반려관리자ID(apv_rjct_mgr_id)
     */
    private String apvRjctMgrId;

    /**
     * 승인관리자ID1(apv_mgr_id1)
     */
    private String apvMgrId1;

    /**
     * 승인관리자ID2(apv_mgr_id2)
     */
    private String apvMgrId2;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
