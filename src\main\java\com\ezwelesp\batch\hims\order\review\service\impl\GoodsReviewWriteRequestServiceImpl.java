package com.ezwelesp.batch.hims.order.review.service.impl;

import com.ezwelesp.batch.hims.order.entity.OrOrdGdsDEntity;
import com.ezwelesp.batch.hims.order.review.domain.GoodsReviewWriteRequestDto;
import com.ezwelesp.batch.hims.order.review.mapper.command.GoodsReviewWriteRequestCommandMapper;
import com.ezwelesp.batch.hims.order.review.mapper.query.GoodsReviewWriteRequestQueryMapper;
import com.ezwelesp.batch.hims.order.review.service.GoodsReviewWriteRequestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class GoodsReviewWriteRequestServiceImpl implements GoodsReviewWriteRequestService {
    private final GoodsReviewWriteRequestQueryMapper goodsReviewWriteRequestQueryMapper;
    private final GoodsReviewWriteRequestCommandMapper goodsReviewWriteRequestCommandMapper;


    @Override
    public List<GoodsReviewWriteRequestDto> getGoodsReviewWriteRqAppPushTarget() {
        return goodsReviewWriteRequestQueryMapper.selectGoodsReviewWriteRequestTarget();
    }

    @Override
    public List<GoodsReviewWriteRequestDto> getEzCheckinAppPushTarget() {
        return goodsReviewWriteRequestQueryMapper.selectEzCheckinAppPushTarget();
    }

    @Override
    public void updateOrderGoodsPushYn(List<OrOrdGdsDEntity> list) {
        goodsReviewWriteRequestCommandMapper.updateOrderGoodsPushYn(list);
    }
}
