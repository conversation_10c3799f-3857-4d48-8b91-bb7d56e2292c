package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 접종고객사검진기관상세(ez_or.or_inocl_clnt_chkup_istn_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrInoclClntChkupIstnDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 접종코드(inocl_cd) not null
     */
    private String inoclCd;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 검진기관코드(chkup_istn_cd) not null
     */
    private String chkupIstnCd;

    /**
     * 정렬순서(sort_ordg) not null
     */
    private Integer sortOrdg;

    /**
     * 사용여부(use_yn) not null
     */
    private String useYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
