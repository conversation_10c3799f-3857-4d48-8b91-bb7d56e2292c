package com.ezwelesp.batch.aasample.test.tasklet;

import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.aasample.test.mapper.command.UserCommandMapper;
import com.ezwelesp.batch.aasample.test.mapper.query.UserQueryMapper;

@Component
@RequiredArgsConstructor
public class Tasklet1 implements Tasklet {
    private final UserQueryMapper userQueryMapper;
    private final UserCommandMapper userCommandMapper;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        Long ntcId = userQueryMapper.selectMaxId();
        System.out.println("================================Max User ID: " + ntcId + "=============");

        userCommandMapper.insertNtc("test");
        System.out.println("================================Insert     : " + ntcId + ": " + "test============");
        return RepeatStatus.FINISHED;
    }
}
