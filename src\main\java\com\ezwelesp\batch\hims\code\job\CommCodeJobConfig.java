package com.ezwelesp.batch.hims.code.job;

import com.ezwelesp.batch.hims.code.tasklet.CommCodeTasklet1;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobScope;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * <AUTHOR> CommCodeJobConfig
 * @date : 2025-02-14
 * @see : com.ezwelesp.batch.aasample.test.job
 */
@Configuration
@RequiredArgsConstructor
public class CommCodeJobConfig {
    private final CommonJobListener commonJobListener;
    private final CommCodeTasklet1 tasklet1;

    @Bean("commCodeJob")
    public Job commCodeJob(JobRepository jobRepository,
            @Qualifier("commCodeStep") Step step) {
        return new JobBuilder("commCodeJob", jobRepository)
                .listener(commonJobListener)
                .start(step)
                .build();
    }

    /**
     * 1. if_meta_comm_code -> cm_comm_c 이관.
     * 2. if_meta_comm_code_dtl -> cm_comm_dtl_c 이관.
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("commCodeStep")
    @JobScope
    public Step commCodeStep(JobRepository jobRepository, @Value("#{jobParameters['requestDate']}") String requestDate,
            DataSourceTransactionManager transactionManager) {
        return new StepBuilder("commCodeStep", jobRepository)
                .tasklet(tasklet1, transactionManager)
                .build();
    }

}
