package com.ezwelesp.batch.hpas.alarm.dto;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * 협력사 교환/반품 미처리 안내메일 발송 DTO
 * 
 * <AUTHOR>
 * @since 2025.05.30
 */
@Data
public class ReturnExchangeMailMainDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String templateFileNm = "";

    public String getTemplitFileNm() {
        return templateFileNm;
    }

    //private ExcelUtil wb;
    private String subject; // 메일 제목    
    private String mailReceiver; // 수신자
    private String mailContent; // 메일내용

    private ReturnExchangeMailMainDto mailContents;

    // email 발송을 위한 user정보 setting
    private String cspNm; //협력사명
    private String cspCd; //협력사코드
    private String mgrId; //담당MD ID
    private String orderNum;
    private String byDt; //완료요청일
    private String orderDt; //주문일자
    private String exchangeDt; //교환요청일자
    private String returnDt; //반품요청일자
    private String goodsCd;
    private String goodsNm;
    private String orderGoodsSeq; // 주문번호
    private String totalDt;

    private String exchgStatusNm;
    private String orderStatusNm;
    private String orderStatus;

    private String enterMgrNm;
    private String enterMgrEmail; //md 담당자 이메일    
    private String email; //협력사 담당자 이메일

    private String reqDt; //  처리요청기간
    private String startDt; //  조회기간 시작
    private String endDt; //  조회기간 끝

    private int count;
    private int count1; //주문완료 count
    private int count2; //출고준비중 count
    private int count3; //배송중 count
}

