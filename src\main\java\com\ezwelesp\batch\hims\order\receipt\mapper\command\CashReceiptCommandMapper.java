package com.ezwelesp.batch.hims.order.receipt.mapper.command;

import com.ezwelesp.batch.hims.order.entity.PyCsrcBEntity;
import com.ezwelesp.batch.hims.order.receipt.domain.CashPublicationBuilderDto;
import com.ezwelesp.batch.hims.order.receipt.domain.CashPublicationGatherDto;
import com.ezwelesp.batch.hims.order.receipt.domain.WithDrawalReceiptDto;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CashReceiptCommandMapper {
    /**
     * 현금영수증 기본 등록
     *
     * @param receipt
     * @return
     */

    int insertCashReceipt(CashPublicationBuilderDto receipt);

    /**
     * 현금영수증 기발급항목 철회
     *
     * @param receipt
     * @return
     */
    int insertCashReceiptWithdrawal(WithDrawalReceiptDto receipt);


    int selectWithdrawalReceipt(WithDrawalReceiptDto receipt);

    Long selectClaimCashReceiptNextNumber(String ordNo, Long ordGdsSeq);


    int updatePublicationReceipt(CashPublicationGatherDto receipt);

    int updatePublicationReceiptError(CashPublicationGatherDto cashPubGatherDto);

    int updatePublicationReceiptNumError(CashPublicationGatherDto cashPubGatherDto);

    int deletePublicationReceiptError(CashPublicationGatherDto cashPubGatherDto);

    int updatePublicationReceiptCancel(CashPublicationGatherDto receipt);

    int updatePublicationReceiptCancelError(CashPublicationGatherDto receipt);

    int insertPublicationReceiptError(CashPublicationGatherDto receipt);
}

