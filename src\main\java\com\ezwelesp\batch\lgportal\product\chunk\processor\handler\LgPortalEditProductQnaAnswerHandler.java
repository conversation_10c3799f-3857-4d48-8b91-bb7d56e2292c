package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaAnswerRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalEditProductQnaAnswerHandler implements LgPortalEditProductProcessorHandler {

    private final LgApiClient lgApiClient;

    @Override
    public LgProductApiResponseDto handle(LgEditProductTargetVo item) {

        val title = "문의 답변드립니다.";
        val content = "고객님 안녕하십니까. 본 게시판은[상품에 관련된 문의]만 처리가 가능하므로,<br/>"
                + "주문/교환/반품/환불 관련하여 불편사항이나 도움이 필요하시다면 LG CNS 고객센터로 문의 부탁드립니다.<br/>"
                + "[상품 문의 답변]<br/>"
                + item.getValueArr();

        val requestDto = LgProductQnaAnswerRequestDto.builder()
                .productInquiryNo(item.getBoardNo())
                .answerTitle(title)
                .answerContents(content)
                .build();

        val responseDto = lgApiClient.sendLgUpdateProductQnaAnswerApi(
                requestDto,
                item.getGdsCd(),
                item.getApiSeq()
        );

        if (responseDto != null && !"200".equals(responseDto.getResult())) {
            log.debug("sendLgUpdateProductQnaAnswerApi Fail : {},{}", item.getGdsCd(), responseDto.getResult());
        }

        return responseDto;
    }

}
