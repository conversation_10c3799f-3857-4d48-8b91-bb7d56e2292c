package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 주문발송상품상세이력(ez_or.dl_ord_snd_gds_d_h)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlOrdSndGdsDHEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문발송상품상세이력순번(ord_snd_gds_dtl_his_seq) not null
     */
    private Long ordSndGdsDtlHisSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문발송순번(ord_snd_seq) not null
     */
    private Long ordSndSeq;

    /**
     * 주문상품순번(ord_gds_seq)
     */
    private Long ordGdsSeq;

    /**
     * 주문발송상품상세순번(ord_snd_gds_dtl_seq)
     */
    private Long ordSndGdsDtlSeq;

    /**
     * 주문발송상품상세변경요청코드(ord_snd_gds_dtl_chg_req_cd)
     */
    private String ordSndGdsDtlChgReqCd;

    /**
     * 주문발송상품상세변경입력문자값(ord_snd_gds_dtl_chg_insr_cval)
     */
    private String ordSndGdsDtlChgInsrCval;

    /**
     * 인증협력사명(crtf_csp_nm)
     */
    private String crtfCspNm;

    /**
     * E쿠폰협력사거래번호(ecpn_csp_trd_no)
     */
    private String ecpnCspTrdNo;

    /**
     * 변경이전유효기간종료일시(chg_bef_vlid_term_end_dtm)
     */
    private String chgBefVlidTermEndDtm;

    /**
     * 수신자모바일전화번호(rcvr_mbl_telno)
     */
    private String rcvrMblTelno;

    /**
     * 주문상태코드(ord_st_cd)
     */
    private String ordStCd;

    /**
     * 주문발송상태코드(ord_snd_st_cd)
     */
    private String ordSndStCd;

    /**
     * 주문발송상품사용상태코드(ord_snd_gds_use_st_cd)
     */
    private String ordSndGdsUseStCd;

    /**
     * 주문발송상품난수상태코드(ord_snd_gds_rdno_st_cd)
     */
    private String ordSndGdsRdnoStCd;

    /**
     * 주문발송상품정산상태코드(ord_snd_gds_stl_st_cd)
     */
    private String ordSndGdsStlStCd;

    /**
     * 정산년월(stl_ym)
     */
    private String stlYm;

    /**
     * 세금계산서수신종류코드(txin_rcv_knd_cd)
     */
    private String txinRcvKndCd;

    /**
     * 오류메시지내용(err_msg_cntn)
     */
    private String errMsgCntn;

    /**
     * E쿠폰주문상태코드
     */
    private String ecpnOrdStCd;

    /**
     * 난수발송요청주체코드
     */
    private String rdnoSndReqMagnCd;

    /**
     * 무형상품난수번호
     */
    private String intgGdsRdnoNo;

    /**
     * 메시지제목
     */
    private String msgTtl;

    /**
     * 발송메시지내용
     */
    private String sndMsgCntn;

    /**
     * 발송요청일시
     */
    private String sndReqDtm;

    /**
     * 발송일시
     */
    private String sndDtm;

    /**
     * 발송오류발생일시
     */
    private String sndErrOcrnDtm;

    /**
     * E쿠폰발송결과코드
     */
    private String ecpnSndRsltCd;

    /**
     * E쿠폰발송결과메시지내용
     */
    private String ecpnSndRsltMsgCntn;

    /**
     * E쿠폰발송결과고유번호
     */
    private String ecpnSndRsltInntNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
