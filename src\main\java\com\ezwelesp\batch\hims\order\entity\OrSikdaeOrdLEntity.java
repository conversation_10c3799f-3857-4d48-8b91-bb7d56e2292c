package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 식권대장주문내역(ez_or.or_sikdae_ord_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrSikdaeOrdLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 정산년월(stl_ym) not null
     */
    private String stlYm;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상태코드(ord_st_cd) not null
     */
    private String ordStCd;

    /**
     * 주문일시(ord_dtm) not null
     */
    private String ordDtm;

    /**
     * 취소일시(cncl_dtm)
     */
    private String cnclDtm;

    /**
     * 주문금액(ord_amt) not null
     */
    private BigDecimal ordAmt;

    /**
     * 원본주문번호(orgl_ord_no) not null
     */
    private String orglOrdNo;

    /**
     * 원본주문일시(orgl_ord_dtm) not null
     */
    private String orglOrdDtm;

    /**
     * 복지몰메뉴코드(hezo_menu_cd)
     */
    private String hezoMenuCd;

    /**
     * 원본주문수수료율(orgl_ord_cms_rt)
     */
    private Double orglOrdCmsRt;

    /**
     * 최종주문번호(last_ord_no)
     */
    private String lastOrdNo;

    /**
     * 최종주문상태코드(last_ord_st_cd)
     */
    private String lastOrdStCd;

    /**
     * 최종주문금액(last_ord_amt) not null
     */
    private BigDecimal lastOrdAmt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
