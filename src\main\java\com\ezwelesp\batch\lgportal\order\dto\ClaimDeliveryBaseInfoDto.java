package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 교환/반품 배송기본 정보
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto.ClaimDeliveryBaseInfoDto
 * @since 2025.02.26
 */
@SuperBuilder(toBuilder = true)
@Getter
public class ClaimDeliveryBaseInfoDto implements Serializable {

    //  분리배송여부
    private String divdDlvYn;

    //  협력사코드
    private String cspCd;

    //  협력사출고위치번호
    private String cspObndLocNo;

    //  배송정책순번
    private long dlvPlcySeq;

    //  배송회사코드
    private String dlvCoCd;

    //  배송상태코드
    private String dlvStCd;

    //  회수상태코드
    private String wtdwStCd;

    //  회수배송여부
    private String wtdwDlvYn;

    //  배송정책코드
    private String dlvPlcyCd;

    //  배송출고유형코드
    private String dlvObndTypCd;

    //  배송연동회사전송결과코드
    private String dlvIntlCoTrmsRsltCd;

    //  수거방법코드
    private String pkupMthdCd;

    //  운송장번호
    private String invcNo;

    //  배송예정일자
    private String dlvDueDt;

    //  배송희망일자
    private String dlvHopeDt;

    //  배송희망시분
    private String dlvHopeHm;

    //  배송시작마감일자
    private String dlvStrtClsgDt;

    //  배송처리관리자메모
    private String dlvPrcsMgrMemo;

    //  배송대기시작일시
    private String dlvStbyStrtDtm;

    //  배송준비시작일시
    private String dlvRdyStrtDtm;

    //  운송장번호등록일시
    private String invcNoRegDtm;

    //  배송준비변경관리자ID
    private String dlvRdyChgMgrId;

    //  운송장번호변경관리자ID
    private String invcNoChgMgrId;

    //  배송/회수 상품정보 리스트
    private List<ClaimDeliveryGoodsDto> deliveryGoodsList;
}
