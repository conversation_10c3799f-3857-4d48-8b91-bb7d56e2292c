package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.tasklet.BsicInfOrdLoanDdBatEtcTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatEtcJobConfig {
    private final CommonJobListener commonJobListener;
    private final BsicInfOrdLoanDdBatEtcTasklet bsicInfOrdLoanDdBatEtcTasklet;

    /**
     * 고객사 사용내역, 소속분리, 카드정보, 특별포인트정보 이관 일배치
     * 주기 : TODO 매일 오전 3시 0분 0초 (AS-IS 기준)
     * 설명 : 고객사 사용내역, 소속분리, 카드정보, 특별포인트정보 이관 일배치
     * 
     * @param jobRepository
     * @param bsicInfOrdLoanDdBatEtcTasklet
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatEtcJob")
    Job bsicInfOrdLoanDdBatEtcJob(
            JobRepository jobRepository,
            Step bsicInfOrdLoanDdBatEtcTaskletStep
    ) {
        return new JobBuilder("bsicInfOrdLoanDdBatEtcJob", jobRepository)
                .listener(commonJobListener)
                .start(bsicInfOrdLoanDdBatEtcTaskletStep) // 고객사 사용내역, 소속분리, 카드정보, 특별포인트정보 이관 프로시저 호출 Step
                .build();
    }
    
    /**
     * 고객사 사용내역, 소속분리, 카드정보, 특별포인트정보 이관 프로시저 호출
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatEtcTaskletStep")
    Step bsicInfOrdLoanDdBatEtcTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("bsicInfOrdLoanDdBatEtcTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(bsicInfOrdLoanDdBatEtcTasklet, transactionManager)
                .build();
    }
}