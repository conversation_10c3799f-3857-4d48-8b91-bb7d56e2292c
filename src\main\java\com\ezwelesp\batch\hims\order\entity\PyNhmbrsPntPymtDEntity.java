package com.ezwelesp.batch.hims.order.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * NH멤버스포인트결제상세(ez_or.py_nhmbrs_pnt_pymt_d)
 */
@Data
public class PyNhmbrsPntPymtDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 결제번호(pymt_no) not null
     */
    private String pymtNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * NH멤버스결제구분코드(nhmbrs_pymt_div_cd) not null
     */
    private String nhmbrsPymtDivCd;

    /**
     * NH멤버스거래고유번호(nhmbrs_trd_innt_no) not null
     */
    private String nhmbrsTrdInntNo;

    /**
     * 잔여포인트(rmn_pnt) not null
     */
    private BigDecimal rmnPnt;

    /**
     * 사용포인트(use_pnt) not null
     */
    private BigDecimal usePnt;

    /**
     * 현금영수증대상포인트(csrc_obj_pnt) not null
     */
    private BigDecimal csrcObjPnt;

    /**
     * 전체사용포인트(all_use_pnt) not null
     */
    private BigDecimal allUsePnt;

    /**
     * 추가사용포인트(add_use_pnt) not null
     */
    private BigDecimal addUsePnt;

    /**
     * 인센티브사용포인트(inct_use_pnt) not null
     */
    private BigDecimal inctUsePnt;

    /**
     * 현대이지웰사용포인트(ezwl_use_pnt) not null
     */
    private BigDecimal ezwlUsePnt;

    /**
     * 재적립포인트(racm_pnt) not null
     */
    private BigDecimal racmPnt;

    /**
     * 부가가치세제외금액(vat_excld_amt) not null
     */
    private BigDecimal vatExcldAmt;

    /**
     * 승인요청일시(apv_req_dtm) not null
     */
    private String apvReqDtm;

    /**
     * 승인일자(apv_dt) not null
     */
    private String apvDt;

    /**
     * NH멤버스승인번호(nhmbrs_apv_no) not null
     */
    private String nhmbrsApvNo;

    /**
     * 정산일자(stl_dt) not null
     */
    private String stlDt;

    /**
     * NH멤버스요청가맹점번호(nhmbrs_req_frcs_no) not null
     */
    private String nhmbrsReqFrcsNo;

    /**
     * NH멤버스요청제휴사번호(nhmbrs_req_asp_no) not null
     */
    private String nhmbrsReqAspNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
