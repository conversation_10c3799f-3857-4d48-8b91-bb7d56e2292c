<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "mybatis-3-config.dtd">
<configuration>
    <!-- 전역 매개 변수 -->
    <settings>
        <!-- 캐시를 사용하거나 사용하지 않도록 합니다 -->
        <setting name="cacheEnabled" value="true" />
        <!-- JDBC에서 홈 키 자동 생성을 지원합니다 -->
        <setting name="useGeneratedKeys" value="false" />
        <!-- 기본 실행기 설정SIMPLE은 일반 액추에이터입니다. REUSE 액추에이터는 사전 처리 구문(prepared statements)을 재사용합니다. BATCH
        액추에이터는 구문을 강조하여 대량 업데이트를 수행합니다. -->
        <setting name="defaultExecutorType" value="SIMPLE" />
        <!-- MyBatis에서 사용할 로그의 구현 방법을 지정합니다. -->
        <setting name="logImpl" value="SLF4J" />

        <setting name="jdbcTypeForNull" value="NULL" />

        <!-- 가져온 값이 null일 때 setter나 맵의 put 메소드를 호출할지를 명시 Map.keySet() 이나 null값을 초기화할때 유용하다. int,
        boolean 등과 같은 원시타입은 null을 설정할 수 없다는 점은 알아두면 좋다. -->
        <setting name="callSettersOnNulls" value="true" />
        <!-- CamelCase 사용하여 필드 변환하기 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
    </settings>
</configuration>