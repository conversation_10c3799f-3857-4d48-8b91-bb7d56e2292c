<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.samsung.mapper.command.SamsungOrderGoodsCommandMapper">

    <update id="replaceOrdGdsSmryNm40">
        <![CDATA[
        update ez_or.or_ord_b
        set ord_gds_smry_nm = replace(ord_gds_smry_nm, '&#40;', '(')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&#40;%'
        ]]>
    </update>

    <update id="replaceOrdGdsSmryNm41">
        <![CDATA[
        update ez_or.or_ord_b
        set ord_gds_smry_nm = replace(ord_gds_smry_nm, '&#41;', ')')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&#41;%'
        ]]>
    </update>

    <update id="replaceOrdGdsSmryNmQuot">
        <![CDATA[
        update ez_or.or_ord_b
        set ord_gds_smry_nm = replace(ord_gds_smry_nm, '&quot;', '"')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&quot;%'
        ]]>
    </update>

    <update id="replaceOrdGdsSmryNm39">
        <![CDATA[
        update ez_or.or_ord_b
        set ord_gds_smry_nm = replace(ord_gds_smry_nm, '&#39;', '''')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&#39;%'
        ]]>
    </update>

    <update id="replaceOrdGdsSmryNm35">
        <![CDATA[
        update ez_or.or_ord_b
        set ord_gds_smry_nm = replace(ord_gds_smry_nm, '&#35;', '#')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&#35;%'
        ]]>
    </update>

    <update id="replaceOrdGdsSmryNmAmp">
        <![CDATA[
        update ez_or.or_ord_b
        set ord_gds_smry_nm = replace(ord_gds_smry_nm, '&amp;', '&')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&amp;%'
        ]]>
    </update>

    <update id="replaceGdsNm40">
        <![CDATA[
        update ez_or.or_ord_gds_d ogd
        set gds_nm = replace(gds_nm, '&#40;', '(')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        from ez_or.or_ord_b oob
        where oob.ord_no = ogd.ord_no
          and oob.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and gds_nm like '%&#40;%'
        ]]>
    </update>

    <update id="replaceGdsNm41">
        <![CDATA[
        update ez_or.or_ord_gds_d ogd
        set gds_nm = replace(gds_nm, '&#41;', ')')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        from ez_or.or_ord_b oob
        where oob.ord_no = ogd.ord_no
          and ogd.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and gds_nm like '%&#41;%'
        ]]>
    </update>

    <update id="replaceGdsNmQuot">
        <![CDATA[
        update ez_or.or_ord_gds_d ogd
        set gds_nm = replace(gds_nm, '&quot;', '"')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        from ez_or.or_ord_b oob
        where oob.ord_no = ogd.ord_no
          and ogd.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and gds_nm like '%&quot;%'
        ]]>
    </update>

    <update id="replaceGdsNm39">
        <![CDATA[
        update ez_or.or_ord_gds_d ogd
        set gds_nm = replace(gds_nm, '&#39;', '''')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        from ez_or.or_ord_b oob
        where oob.ord_no = ogd.ord_no
          and ogd.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and gds_nm like '%&#39;%'
        ]]>
    </update>

    <update id="replaceGdsNm35">
        <![CDATA[
        update ez_or.or_ord_gds_d ogd
        set gds_nm = replace(gds_nm, '&#35;', '#')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        from ez_or.or_ord_b oob
        where oob.ord_no = ogd.ord_no
          and ogd.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and gds_nm like '%&#35;%'
        ]]>
    </update>

    <update id="replaceGdsNmAmp">
        <![CDATA[
        update ez_or.or_ord_gds_d ogd
        set gds_nm = replace(gds_nm, '&amp;', '&')
          , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
          , last_mod_usr_id = 'BATCH'
          , last_mod_pgm_id = 'BATCH'
        from ez_or.or_ord_b oob
        where oob.ord_no = ogd.ord_no
          and ogd.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and gds_nm like '%&amp;%'
        ]]>
    </update>
</mapper>
