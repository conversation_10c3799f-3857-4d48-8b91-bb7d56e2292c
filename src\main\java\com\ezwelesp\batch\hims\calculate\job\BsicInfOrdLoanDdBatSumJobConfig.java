package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.tasklet.BsicInfOrdLoanDdBatSumTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatSumJobConfig {
    private final CommonJobListener commonJobListener;
    private final BsicInfOrdLoanDdBatSumTasklet bsicInfOrdLoanDdBatSumTasklet;

    /**
     * 요약관리 / 고객사별 집계 데이터 생성 일배치
     * 주기 : TODO 매일 오전 3시 0분 0초 (AS-IS 기준)
     * 설명 : 요약관리 / 고객사별 집계 데이터 생성 일배치
     * 
     * @param jobRepository
     * @param bsicInfOrdLoanDdBatSumTaskletStep
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatSumJob")
    Job bsicInfOrdLoanDdBatSumJob(
            JobRepository jobRepository,
            Step bsicInfOrdLoanDdBatSumTaskletStep
    ) {
        return new JobBuilder("bsicInfOrdLoanDdBatSumJob", jobRepository)
                .listener(commonJobListener)
                .start(bsicInfOrdLoanDdBatSumTaskletStep) // 요약관리 / 고객사별 집계 데이터 생성 프로시저 호출 Step
                .build();
    }

    /**
     * 요약관리 / 고객사별 집계 데이터 생성 프로시저 호출
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatSumTaskletStep")
    Step bsicInfOrdLoanDdBatSumTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("bsicInfOrdLoanDdBatSumTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(bsicInfOrdLoanDdBatSumTasklet, transactionManager)
                .build();
    }
}