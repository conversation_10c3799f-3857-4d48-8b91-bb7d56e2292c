package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 쿠폰사용상세(ez_pm.pm_cpn_use_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PmCpnUseDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 쿠폰사용상세번호(cpn_use_dtl_no) not null
     */
    private Long cpnUseDtlNo;

    /**
     * 사용자쿠폰번호(usr_cpn_no)
     */
    private Long usrCpnNo;

    /**
     * 쿠폰번호(cpn_no) not null
     */
    private String cpnNo;

    /**
     * 정기구독주문차수(sto_ord_nos)
     */
    private Long stoOrdNos;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 고객사명(clnt_nm) not null
     */
    private String clntNm;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;
    /**
     * 가족회원번호(fam_mem_no)
     */
    private String famMemNo;

    /**
     * 사용자명(usr_nm) not null
     */
    private String usrNm;

    /**
     * 채널코드(ch_cd) not null
     */
    private String chCd;

    /**
     * 표준카테고리코드(std_ctgr_cd)
     */
    private String stdCtgrCd;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 원본주문번호(orgl_ord_no)
     */
    private String orglOrdNo;

    /**
     * 주문쿠폰번호(ord_cpn_no)
     */
    private Long ordCpnNo;
    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * 상품코드(gds_cd)
     */
    private String gdsCd;

    /**
     * 협력사코드(csp_cd)
     */
    private String cspCd;

    /**
     * 쿠폰할인금액(cpn_dc_amt) not null
     */
    private BigDecimal cpnDcAmt;

    /**
     * 쿠폰사용상태코드(cpn_use_st_cd) not null
     */
    private String cpnUseStCd;

    /**
     * 사용취소일시(use_cncl_dtm)
     */
    private String useCnclDtm;

    /**
     * 협력사부담비율(csp_budn_rt) not null
     */
    private Double cspBudnRt;

    /**
     * 현대이지웰부담금액(ezwl_budn_amt) not null
     */
    private BigDecimal ezwlBudnAmt;

    /**
     * 협력사부담금액(csp_budn_amt) not null
     */
    private BigDecimal cspBudnAmt;

    /**
     * 주문상품수량(ord_gds_qty)
     */
    private Integer ordGdsQty;

    /**
     * 유효기간시작일시(vlid_term_strt_dtm) not null
     */
    private String vlidTermStrtDtm;

    /**
     * 유효기간종료일시(vlid_term_end_dtm) not null
     */
    private String vlidTermEndDtm;

    /**
     * 사용자쿠폰발급일시(usr_cpn_issu_dtm) not null
     */
    private String usrCpnIssuDtm;

    /**
     * 부담고객사코드(budn_clnt_cd)
     */
    private String budnClntCd;

    /**
     * 고객사부담비율(clnt_budn_rt)
     */
    private Double clntBudnRt;

    /**
     * 고객사부담금액(clnt_budn_amt) not null
     */
    private BigDecimal clntBudnAmt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
