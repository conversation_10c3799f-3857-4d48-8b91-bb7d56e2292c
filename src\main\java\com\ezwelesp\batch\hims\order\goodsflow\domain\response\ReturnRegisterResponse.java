package com.ezwelesp.batch.hims.order.goodsflow.domain.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 굿스플로 반품등록 response
 */
@Getter
@Setter
@ToString
public class ReturnRegisterResponse {
    private boolean success;
    private Object data;
    private Error error;

    @Getter
    @Setter
    @ToString
    public static class Error {
        private int status;
        private String message;
        private int totalItems;
        private Detail detail;
    }

    @Getter
    @Setter
    @ToString
    public static class Detail {
        private List<Item> items;
    }

    @Getter
    @Setter
    @ToString
    public static class Item {
        private int rSeq;
        private String idValue;
        private String message;
        private List<DetailError> detailErrors;
    }

    @Getter
    @Setter
    @ToString
    public static class DetailError {
        private String property;
        private String message;
    }

}
