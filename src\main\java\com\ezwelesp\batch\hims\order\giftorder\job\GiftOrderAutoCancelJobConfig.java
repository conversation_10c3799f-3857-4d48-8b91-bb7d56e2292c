package com.ezwelesp.batch.hims.order.giftorder.job;

import com.ezwelesp.batch.hims.order.giftorder.tasklet.GiftOrderAutoCancelTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 선물하기 거절/4일경과 자동취소 배치 [BA_HIOR00100]
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.job
 * @since 2025.05.15
 */
@Configuration
@RequiredArgsConstructor
public class GiftOrderAutoCancelJobConfig {
    private final CommonJobListener commonJobListener;
    private final GiftOrderAutoCancelTasklet giftOrderAutoCancelTasklet;

    @Bean("BA_HIOR00100")
    public Job giftOrderAutoCancelJob(JobRepository jobRepository,
            @Qualifier("BA_HIOR00100_STEP") Step giftOrderAutoCancelStep) {
        return new JobBuilder("BA_HIOR00100", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(giftOrderAutoCancelStep)
                .build();
    }

    @Bean("BA_HIOR00100_STEP")
    public Step giftOrderAutoCancelStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00100_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(giftOrderAutoCancelTasklet, transactionManager)
                .build();
    }
}
