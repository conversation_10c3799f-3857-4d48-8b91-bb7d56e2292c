package com.ezwelesp.batch.hims.order.pgconfirm.zeropay.tasklet;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.order.pgconfirm.zeropay.service.PgDailyErrorService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class PgDailyErrorTasklet implements Tasklet{
	
	private final PgDailyErrorService pgDailyErrorService;
	
	@Override
	public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
		log.info("############## 동백몰 결제대사 START ##############" + contribution.getStepExecution().getStepName());
		this.pgDailyErrorService.callBatch();
		log.info("############## 동백몰 결제대사 START ##############" + contribution.getStepExecution().getStepName());
		return RepeatStatus.FINISHED;
	}

}
