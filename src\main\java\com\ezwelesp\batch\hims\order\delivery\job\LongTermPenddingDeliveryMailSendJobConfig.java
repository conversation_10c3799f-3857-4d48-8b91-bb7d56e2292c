package com.ezwelesp.batch.hims.order.delivery.job;

import com.ezwelesp.batch.hims.order.delivery.tasklet.LongTermPenddingDeliveryMailSendTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 장기미배송건 내부메일 발송 [BA_HIOR00097]
 */
@Configuration
@RequiredArgsConstructor
public class LongTermPenddingDeliveryMailSendJobConfig {
    private final CommonJobListener commonJobListener;
    private final LongTermPenddingDeliveryMailSendTasklet longTermPenddingDeliveryMailSendTasklet;

    @Bean("BA_HIOR00097")
    public Job longTermPenddingDeliveryMailSendJob(JobRepository jobRepository, @Qualifier("BA_HIOR00097_STEP") Step longTermPenddingDeliveryMailSendStep) {
        return new JobBuilder("BA_HIOR00097", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(longTermPenddingDeliveryMailSendStep)
                .build();
    }

    @Bean("BA_HIOR00097_STEP")
    public Step longTermPenddingDeliveryMailSendStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00097_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(longTermPenddingDeliveryMailSendTasklet, transactionManager)
                .build();
    }
}
