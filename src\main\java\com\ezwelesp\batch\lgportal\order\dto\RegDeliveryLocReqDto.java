package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
  * 출고/반품/교환지 저장 요청 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see RegDeliveryLocReqDto
  */
@Data
public class RegDeliveryLocReqDto {
    private String apiKey;

    private String deliveryAddrTypeCode;
    private String deliveryName;
    private String baseSettingYN;
    private String postCode;
    private String deliveryAddr;
    private String deliveryAddrDetail;
    private String telNo1;
    private String deliveryComNo;
    private String deliveryCostTypeCode;
    private String deliveryMappingCode;

    private List<DeliveryCost> entEntryComDeliveryCostList;

    public RegDeliveryLocReqDto(String apiKey) {
        this.apiKey = apiKey;
        this.entEntryComDeliveryCostList = new ArrayList<>();
    }

    @Builder
    @Data
    public static class DeliveryCost {
        @Builder.Default
        private String productMinPrice = "0";

        @Builder.Default
        private String productMaxPrice = "99999999";

        private String deliveryCost;
    }

}
