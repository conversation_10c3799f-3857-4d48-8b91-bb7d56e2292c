package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 고객사상세(ez_ct.ct_clnt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class CtClntDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 광역지역명(wdr_area_nm)
     */
    private String wdrAreaNm;

    /**
     * 신규고객사이벤트사용여부(new_clnt_evnt_use_yn) not null
     */
    private String newClntEvntUseYn;

    /**
     * 신규고객사이벤트시작일자(new_clnt_evnt_strt_dt)
     */
    private String newClntEvntStrtDt;

    /**
     * 신규고객사이벤트종료일자(new_clnt_evnt_end_dt)
     */
    private String newClntEvntEndDt;

    /**
     * 신규고객사이벤트내용(new_clnt_evnt_cntn)
     */
    private String newClntEvntCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
