<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.goodsflow.mapper.command.GoodsFlowTraceCommandMapper">

    <update id="updateIntlChckCd">
        update ez_or.dl_goodsf_dlv_intl_b
           set goodsf_data_intl_chck_cd = #{intlChckCd}
             , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
             , last_mod_usr_id = 'BATCH'
             , last_mod_pgm_id = 'BATCH'
         where dlv_intl_innt_no in
              <foreach collection="list" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <insert id="insertGikimiMaster">
        insert into ez_if.gikimimast
              ( mid
              , transUniqueCode
              , sectionCode
              , memberCode
              , sellerCode
              , sellerName
              , fromName
              , toName
              , toMobile
              , logisticsCode
              , invoiceNo
              , dlvretType
              , invoicePrintDate
              , err_code
              , edi_datetime
              , create_datetime
              , defCode1
              , defCode2)
        values (#{mid}
              , #{transUniqueCode}
              , #{sectionCode}
              , #{memberCode}
              , #{sellerCode}
              , #{sellerName}
              , #{fromName}
              , #{toName}
              , #{toMobile}
              , #{logisticsCode}
              , #{invoiceNo}
              , #{dlvretType}
              , #{invoicePrintDate}
              , #{errCode}
              , #{ediDatetime}
               , to_char(now(), 'YYYYMMDDHH24MISS')
              , #{defCode1}
              , #{defCode2}
               )
    </insert>

    <insert id="insertGikimiDetail">
        insert into ez_if.gikimidetail
              ( mid
              , idseq
              , orderNo
              , orderLine
              , itemCode
              , itemName
              , itemOption
              , itemQty
              , itemPrice
              , orderDate
              , paymentDate
              )
        values
            <foreach collection="list" item="item" separator=",">
                ( #{item.mid}
                , #{item.idseq}
                , #{item.orderNo}
                , #{item.orderLine}
                , #{item.itemCode}
                , #{item.itemName}
                , #{item.itemOption}
                , #{item.itemQty}
                , #{item.itemPrice}
                , #{item.orderDate}
                , #{item.paymentDate}
                )
            </foreach>
    </insert>

    <insert id="mergeDlGoodsfDlvIntlRslt">
        merge into ez_or.dl_goodsf_dlv_intl_rslt_d as tgt
            using (values ( #{dlvIntlInntNo}
                            , #{invcNo}
                            , #{dlvIntlStCd}
                            , #{dlvGdsIntlErrCd})) as src (dlv_intl_innt_no, invc_no, dlv_intl_st_cd, dlv_gds_intl_err_cd)
               on (tgt.dlv_intl_innt_no = src.dlv_intl_innt_no and tgt.invc_no = src.invc_no)
             when matched
                 then update set dlv_intl_st_cd = src.dlv_intl_st_cd
                                 , dlv_intl_prcs_cmpt_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
                                 , dlv_gds_intl_err_cd = src.dlv_gds_intl_err_cd
                                 , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
                                 , last_mod_usr_id = 'BATCH'
                                 , last_mod_pgm_id = 'BATCH'
            when not matched
                then insert (dlv_intl_innt_no
                , invc_no
                , ord_no
                , ord_gds_seq
                , goodsf_dlv_co_cd
                , dlv_gds_qty
                , dlv_intl_st_cd
                , dlv_intl_prcs_cmpt_dtm
                , dlv_gds_intl_err_cd
                , frst_reg_dtm
                , frst_reg_usr_id
                , frst_reg_pgm_id
                , last_mod_dtm
                , last_mod_usr_id
                , last_mod_pgm_id)
                values ( #{dlvIntlInntNo}
                       , #{invcNo}
                       , #{ordNo}
                       , #{ordGdsSeq}
                       , #{goodsfDlvCoCd}
                       , #{dlvGdsQty}
                       , #{dlvIntlStCd}
                       , to_char(now(), 'YYYYMMDDHH24MISS')
                       , #{dlvGdsIntlErrCd}
                       , to_char(now(), 'YYYYMMDDHH24MISS')
                       , 'BATCH'
                       , 'BATCH'
                       , to_char(now(), 'YYYYMMDDHH24MISS')
                       , 'BATCH'
                       , 'BATCH'
                       )
    </insert>

    <update id="updateGikimiResultStatus">
        update ez_if.gikimiresult
            set proc_yn = 'Y'
              , ezwel_proc_date = to_char(now(), 'YYYYMMDDHH24MISS')
         where transUniqueCode = #{transUniqueCode}
           and seq = #{seq}
    </update>

    <update id="updateDeliveryFinishStatus">
        update ez_or.dl_dlv_b
           set dlv_intl_co_trms_rslt_cd = 'I'
             , dlv_cmpt_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
             , dlv_st_cd = 'CMPT'
             , dlv_cmpt_chg_mgr_id = 'BATCH'
             , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
             , last_mod_usr_id = 'BATCH'
             , last_mod_pgm_id = 'BATCH'
        where dlv_no = #{dlvNo}
          and invc_no = #{invcNo}
    </update>

    <update id="updateDeliveryResultCode">
        update ez_or.dl_dlv_b
           set dlv_intl_co_trms_rslt_cd = #{dlvIntCoTrmsRsltCd}
             , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
             , last_mod_usr_id = 'BATCH'
             , last_mod_pgm_id = 'BATCH'
        where dlv_no = #{dlvNo}
          and invc_no = #{invcNo}
    </update>

    <update id="updateSendTraceRequestResult">
        with v_target as (
            select t.mid
                  , t.err_code
                  , t.err_desc
              from <foreach collection='list' item="item" separator="," open="(values " close=")">
                    ( #{item.mid}
                    , #{item.errCode}
                    , #{item.errDesc})
                   </foreach> as t(mid, err_code, err_desc)
            )
        update ez_if.gikimimast m
           set err_code = vt.err_code
             , err_desc = vt.err_desc
             , edi_datetime = to_char(now(), 'YYYYMMDDHH24MISS')
         from v_target vt
        where m.mid = vt.mid
    </update>

    <insert id="mergeGikimiresult">
        merge into ez_if.gikimiresult as tgt
            using (values (#{transUniqueCode}, #{seq})) as src (transuniquecode, seq)
            on (tgt.transuniquecode = src.transuniquecode and tgt.seq = src.seq)
            when not matched
                then insert (transuniquecode
                , seq
                , sectioncode
                , logisticscode
                , invoiceno
                , dlvstattype
                , procdatetime
                , exceptioncode
                , exceptionname
                , branchname
                , branchtel
                , employeename
                , employeetel
                , employeemsg
                , taker
                , errorcode
                , errorname
                , defcode1
                , defcode2
                , createdatetime
                , proc_yn
                , ezwel_proc_date)
                values (#{transUniqueCode}
                       , #{seq}
                       , #{sectionCode}
                       , #{logisticsCode}
                       , #{invoiceNo}
                       , #{dlvStatType}
                       , #{procDateTime}
                       , #{exceptionCode}
                       , #{exceptionName}
                       , #{branchName}
                       , #{branchTel}
                       , #{employeeName}
                       , #{employeeTel}
                       , #{employeeMsg}
                       , #{taker}
                       , #{errorCode}
                       , #{errorName}
                       , #{defCode1}
                       , #{defCode2}
                       , to_char(now(), 'YYYY-MM-DD HH24:MI:SS')
                       , #{procYn}
                       , #{ezwelProcDate}
                       )
    </insert>

    <delete id="deleteGikimimast">
        delete ez_if.gikimimast
		 where create_datetime <![CDATA[<]]> to_char(now() - interval '12 month', 'YYYYMMDD') || '000000'
    </delete>

    <delete id="deleteGikimidetail">
        delete ez_if.gikimidetail d
         where exists (select 1
                           from gikimimast m
                          where m.mid = d.mid
                            and m.create_datetime <![CDATA[<]]> to_char(now() - interval '12 month', 'YYYYMMDD') || '000000')
    </delete>

    <delete id="deleteGikimiresult">
        delete ez_if.gikimiresult
         where createdatetime <![CDATA[<]]> to_char(now() - interval '12 month', 'YYYY-MM-DD') || ' 00:00:00'
    </delete>
</mapper>
