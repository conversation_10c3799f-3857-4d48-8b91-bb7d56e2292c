Configuration:
  status: WARN

  Appenders:
    Console:
      name: Console
      target: SYSTEM_OUT
      PatternLayout:
        disableAnsi: true
        pattern: "[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%p] [%t] [%C] %m%n"

  Loggers:
    Logger:
      # SQL 로그
      - name: jdbc.sqlonly
        level: DEBUG
        additivity: false
        AppenderRef:
          - ref: Console
  
      # 파라미터 로그
      - name: jdbc.sqltiming
        level: DEBUG
        additivity: false
        AppenderRef:
          - ref: Console

      # 결과셋 로그 끄기
      - name: jdbc.resultset
        level: ERROR
        additivity: false
        AppenderRef:
          - ref: Console

      # 커넥션 로그 끄기
      - name: jdbc.connection
        level: ERROR
        additivity: false
        AppenderRef:
          - ref: Console

      # JDBC 메소드 추적 로그 끄기
      - name: jdbc.audit
        level: ERROR
        additivity: false
        AppenderRef:
          - ref: Console

    Root:
      level: INFO
      AppenderRef:
        - ref: Console

