package com.ezwelesp.batch.hims.order.receipt.domain;

import lombok.Data;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 현금영수증기본(ez_or.py_csrc_b)
 */
@Data
public class CashPublicationGatherDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 현금영수증수기처리차수(csrc_hndw_prcs_nos) not null
     */
    private Long csrcHndwPrcsNos;

    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * 제휴사주문번호
     */
    private String aspOrdNo;

    /**
     * 전체결제금액
     */
    private BigDecimal allPymtAmt;

    /**
     * 현금영수증발행대상일자
     */
    private String csrcPblcObjDt;

    /**
     * 현금영수증발행용도코드
     */
    private String csrcPblcUsgCd;

    /**
     * 현금영수증발행번호
     */
    private String encCsrcPblcNo;

    /**
     * PG가맹점번호
     */
    private String pgFrcsNo;

    /**
     * 현금영수증발행유형코드
     */
    private String csrcPblcTypCd;

    /**
     * 사용자고유번호
     */
    private String userKey;

    /**
     * 주문자명
     */
    private String ordrNm;

    /**
     * 주문자이메일주소
     */
    private String ordrEmlAdr;

    /**
     * 주문자모바일전화번호
     */
    private String ordrMblTelno;

    /**
     * 현금영수증PG승인번호
     */
    private String csrcPgApvNo;

    /**
     * 현금영수증승인번호
     */
    private String csrcApvNo;

    /**
     * 현금영수증승인일시
     */
    private String csrcApvDtm;

    /**
     * 현금영수증발행취소일자
     */
    private String csrcPblcCnclDt;

    /**
     * 취소현금영수증PG승인번호
     */
    private String cnclCsrcPgApvNo;

    /**
     * 현금영수증취소승인일시
     */
    private String csrcCnclApvDtm;

    /**
     * 상품코드
     */
    private String gdsCd;

    /**
     * 상품명
     */
    private String gdsNm;

    /**
     * 협력사코드
     */
    private String cspCd;

    /**
     * 협력사명
     */
    private String cspNm;

    /**
     * 상품금액비율
     */
    private Double gdsAmtRt;

    /**
     * 상품공급가격
     */
    private BigDecimal gdsSplPrc;

    /**
     * 현금영수증상품발행금액
     */
    private BigDecimal csrcGdsPblcAmt;

    /**
     * 현금영수증공급가격
     */
    private BigDecimal csrcSplPrc;

    /**
     * 현금영수증부가가치세금액
     */
    private BigDecimal csrcVatAmt;

    /**
     * 상품부가가치세금액
     */
    private BigDecimal gdsVatAmt;

    /**
     * 협력사현금영수증발행여부
     */
    private String cspCsrcPblcYn;

    /**
     * 협력사현금영수증발행주체코드
     */
    private String cspCsrcPblcMagnCd;

    /**
     * 현금영수증사업자등록번호대상코드
     */
    private String csrcBzrnObjCd;

    /**
     * 현금영수증발행종류코드
     */
    private String csrcPblcKndCd;

    /**
     * 현금영수증발행대상상품여부
     */
    private String csrcPblcObjGdsYn;

    /**
     * 협력사사업자등록번호
     */
    private String cspBzrn;

    /**
     * 과세종류코드
     */
    private String taxnKndCd;

    /**
     * 표준메뉴코드
     */
    private String stdMenuCd;

    /**
     * 제도마감취소여부
     */
    private String rgmClsgCnclYn;

    /**
     * 현금영수증발행여부
     */
    private String csrcPblcYn;

    /**
     * 비고
     */
    private String rmrk;

    /**
     * 선택적복지포인트현금영수증발행금액
     */
    private BigDecimal wfpCsrcPblcAmt;

    /**
     * 급여차감현금영수증발행금액
     */
    private BigDecimal wgsSbtrCsrcPblcAmt;

    /**
     * 특별포인트현금영수증발행금액
     */
    private BigDecimal sppCsrcPblcAmt;

    /**
     * 계좌이체현금영수증발행금액
     */
    private BigDecimal acntTrnsCsrcPblcAmt;

    /**
     * 가상계좌현금영수증발행금액
     */
    private BigDecimal vtacCsrcPblcAmt;

    /**
     * 지역화폐현금영수증발행금액
     */
    private BigDecimal lbvCsrcPblcAmt;

    /**
     * 대한상공회의소포인트현금영수증발행금액
     */
    private BigDecimal kcciPntCsrcPblcAmt;

    /**
     * 복지대장포인트현금영수증발행금액
     */
    private BigDecimal vndpCsrcPblcAmt;

    /**
     * 대장페이포인트현금영수증발행금액
     */
    private BigDecimal vnppCsrcPblcAmt;

    /**
     * H포인트현금영수증발행금액
     */
    private BigDecimal hpntCsrcPblcAmt;

    /**
     * 네이버페이현금영수증발행금액
     */
    private BigDecimal nvpayCsrcPblcAmt;

    /**
     * NH포인트현금영수증발행금액
     */
    private BigDecimal nhpntCsrcPblcAmt;

    /**
     * 선택적복지포인트여부
     */
    private String wfpYn;

    /**
     * 특별포인트여부
     */
    private String sppYn;

    /**
     * 문화비소득공제대상여부
     */
    private String clxpItdObjYn;

    /**
     * 최초등록일시
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID
     */
    private String lastModPgmId;


    private String resultCode;
    private String resultMsg;
    private String tid;
    private String applNum;
    private String applDate;
    private String applTime;
    private int applSupplyAmt;
    private int applVatAmt;

    private String cancelNum;
    private String cancelTime;

    private String jsonStr;

    private String publicationDate;

}
