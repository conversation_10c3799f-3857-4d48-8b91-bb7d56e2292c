<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hpas.alarm.mapper.query.LowPriceSmsMainQueryMapper">

	<sql id="lowPriceSmsMainList">
	    select pglps.csp_cd
	         , ccb.csp_nm
	         , cccd.chrgr_telno
	         , pglps.all_gds_cnt
	         , pglps.lwpr_cnft_obj_gds_cnt
	         , pglps.lwpr_cnft_gds_cnt
	         , pglps.prc_adj_need_gds_cnt
	         , pglps.lwpr_cnft_ndmt_gds_cnt
	         , pglps.ipd_sell_gds_cnt
	         , pglps.new_reg_gds_cnt
	         , pglps.frst_reg_dtm
	      from ez_pd.pd_gds_lwst_prc_s pglps   -- 상품최저가격집계
	     inner join (
	                 select csp_cd
		                  , max(gds_lwpr_sum_seq) as gds_lwpr_sum_seq
	                   from ez_pd.pd_gds_lwst_prc_s
		              where frst_reg_dtm like (
		                                       select substr(frst_reg_dtm,0,8)
		                                         from ez_pd.pd_gds_lwst_prc_s
		                                        where gds_lwpr_sum_seq = (select max(gds_lwpr_sum_seq) from ez_pd.pd_gds_lwst_prc_s)) || '%'
	                    and (prc_adj_need_gds_cnt > 0 or lwpr_cnft_ndmt_gds_cnt > 0)
	                    and csp_cd is not null
	                  group by csp_cd) x
	        on pglps.gds_lwpr_sum_seq = x.gds_lwpr_sum_seq
	     inner join ez_co.co_csp_b ccb         -- 협력사기본
	        on ccb.csp_cd =  pglps.csp_cd
	      left join ez_co.co_csp_chrgr_d cccd  -- 협력사담당자상세
	        on cccd.csp_cd = ccb.csp_cd
	</sql>

    <select id="selectLowPriceSmsMainCount">
    	select count(*)
    	  from (
			    <include refid="lowPriceSmsMainList" />
		)
    </select>

    <select id="selectLowPriceSmsMainList">
    	select *
    	  from (
			    select row_number() over (order by csp_cd) as num
			         , a.*
			      from (
					    <include refid="lowPriceSmsMainList" />
				) a
		)
    	 where num <![CDATA[<=]]> 10000 * #{pageInt}
    	   and num <![CDATA[>=]]> (#{pageInt} - 1)* 10000 + 1
    </select>

</mapper>
