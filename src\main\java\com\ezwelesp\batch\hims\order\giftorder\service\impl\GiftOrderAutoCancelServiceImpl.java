package com.ezwelesp.batch.hims.order.giftorder.service.impl;

import com.ezwelesp.batch.hims.order.config.ClaimConstants;
import com.ezwelesp.batch.hims.order.giftorder.domain.GiftOrderCancelReqDto;
import com.ezwelesp.batch.hims.order.giftorder.mapper.query.GiftOrderQueryMapper;
import com.ezwelesp.batch.hims.order.giftorder.service.GiftOrderAutoCancelService;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.service.impl
 * @since 2025.05.15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GiftOrderAutoCancelServiceImpl implements GiftOrderAutoCancelService {
    private final GiftOrderQueryMapper giftOrderQueryMapper;

    @Value(("${ezwel.apim.host}"))
    private String apimApiHost;

    @Override
    public List<Long> getGiftOrderAutoCancelTargetOrdNoList() {
        return giftOrderQueryMapper.selectGiftOrderAutoCancelTargetOrdNoList();
    }

    @Override
    public void cancelOrder(Long gvgftOrdNo) {
        GiftOrderCancelReqDto reqDto = GiftOrderCancelReqDto.builder()
                .gvgftOrdNo(gvgftOrdNo)
                .build();
        try {
            AjaxResult result = RestClientUtil.requestApi(apimApiHost + ClaimConstants.GIFT_ORDER_CANCEL_API_URL, 10,
                    JsonObjectConverter.serialize(reqDto));
            log.debug("GiftOrderAutoCancelService.cancelOrder({}) result: {} ", gvgftOrdNo, result);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
