package com.ezwelesp.batch.hims.order.goodsflow.job;

import com.ezwelesp.batch.hims.order.goodsflow.tasklet.GoodsFlowBeforeTraceRequestTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * BA_HIOR00081
 * as-is: GoodsFlow DB연동 보내기
 * 굿스플로 배송추적 요청 대상건 I/F 테이블 insert
 */
@Configuration
@RequiredArgsConstructor
public class GoodsFlowBeforeTraceRequestJobConfig {
    private final CommonJobListener commonJobListener;
    private final GoodsFlowBeforeTraceRequestTasklet goodsFlowBeforeTraceRequestTasklet;

    @Bean("BA_HIOR00081")
    public Job goodsFlowBeforeTraceRequestJob(JobRepository jobRepository, @Qualifier("BA_HIOR00081_STEP") Step goodsFlowBeforeTraceRequestStep) {
        return new JobBuilder("BA_HIOR00081", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(goodsFlowBeforeTraceRequestStep)
                .build();
    }

    @Bean("BA_HIOR00081_STEP")
    public Step goodsFlowBeforeTraceRequestStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00081_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(goodsFlowBeforeTraceRequestTasklet, transactionManager)
                .build();
    }
}
