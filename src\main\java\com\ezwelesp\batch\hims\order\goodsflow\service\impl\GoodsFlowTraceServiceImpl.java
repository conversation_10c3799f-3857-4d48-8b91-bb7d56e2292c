package com.ezwelesp.batch.hims.order.goodsflow.service.impl;

import com.ezwelesp.batch.hims.order.entity.DlDlvBEntity;
import com.ezwelesp.batch.hims.order.entity.DlGoodsfDlvIntlBEntity;
import com.ezwelesp.batch.hims.order.entity.DlGoodsfDlvIntlRsltDEntity;
import com.ezwelesp.batch.hims.order.entity.external.GikimidetailEntity;
import com.ezwelesp.batch.hims.order.entity.external.GikimimastEntity;
import com.ezwelesp.batch.hims.order.entity.external.GikimiresultEntity;
import com.ezwelesp.batch.hims.order.goodsflow.client.GoodsFlowClient;
import com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowDeliveryFinishDto;
import com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowSendTraceRequestDto;
import com.ezwelesp.batch.hims.order.goodsflow.domain.request.SendTraceRequest;
import com.ezwelesp.batch.hims.order.goodsflow.domain.request.SendTraceResultRequest;
import com.ezwelesp.batch.hims.order.goodsflow.domain.response.ReceiveTraceResultResponse;
import com.ezwelesp.batch.hims.order.goodsflow.domain.response.SendTraceResponse;
import com.ezwelesp.batch.hims.order.goodsflow.domain.response.SendTraceResultResponse;
import com.ezwelesp.batch.hims.order.goodsflow.mapper.command.GoodsFlowTraceCommandMapper;
import com.ezwelesp.batch.hims.order.goodsflow.mapper.query.GoodsFlowTraceQueryMapper;
import com.ezwelesp.batch.hims.order.goodsflow.service.GoodsFlowTraceService;
import com.ezwelesp.framework.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.ezwelesp.batch.hims.order.config.GoodsFlowConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsFlowTraceServiceImpl implements GoodsFlowTraceService {
    private final GoodsFlowTraceQueryMapper goodsFlowTraceQueryMapper;
    private final GoodsFlowTraceCommandMapper goodsFlowTraceCommandMapper;
    private final GoodsFlowClient goodsFlowClient;

    @Override
    public List<DlGoodsfDlvIntlBEntity> getDeliveryTraceTargetList() {
        return goodsFlowTraceQueryMapper.selectDeliveryTraceTargetList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertDeliveryTraceTraget(DlGoodsfDlvIntlBEntity master) {
        // 동일한 고유배송번호를 가진 대상건 조회
        List<DlGoodsfDlvIntlBEntity> detail = goodsFlowTraceQueryMapper.selectDeliveryTraceDetailByInntDlvNo(master.getGoodsfInntDlvNo());

        long seq = goodsFlowTraceQueryMapper.selectSequence("ez_if.sq_gikimimast");

        // 지키미 마스터 등록
        goodsFlowTraceCommandMapper.insertGikimiMaster(this.entityBuilder(master, seq));

        // 지키미 상세 등록
        goodsFlowTraceCommandMapper.insertGikimiDetail(this.entityListBuilder(detail, seq));

        // 상태 업데이트
        goodsFlowTraceCommandMapper.updateIntlChckCd(detail.stream().map(DlGoodsfDlvIntlBEntity::getDlvIntlInntNo).toList(), "I");
    }

    @Override
    public List<GoodsFlowDeliveryFinishDto> getDeliveryFinishList() {
        return goodsFlowTraceQueryMapper.selectDeliveryFinishList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateDeliveryFinish(GoodsFlowDeliveryFinishDto deliveryFinishDto) {
        // 굿스플로배송연동 목록 조회 (굿스플로고유배송번호 기준)
        List<DlGoodsfDlvIntlBEntity> entityList = goodsFlowTraceQueryMapper.selectInterlockBaseByGoodsfInntDlvNo(deliveryFinishDto.getTransUniqueCode());

        DlGoodsfDlvIntlBEntity entity = entityList.get(0);

        // 배송기본 조회
        DlDlvBEntity deliveryBase = goodsFlowTraceQueryMapper.selectDeliveryBaseInfo(entity.getDlvNo());

        if (deliveryBase == null) {
            throw new ServiceException(); // 배송번호가 없을 수 있나?
        }

        if ("70".equals(deliveryFinishDto.getDlvStatType())) {
            if (deliveryBase.getDlvCmptDtm() != null) {}
            // 배송완료 업데이트
            goodsFlowTraceCommandMapper.updateDeliveryFinishStatus(entity.getDlvNo(), deliveryFinishDto.getInvoiceNo());
        } else {
            // 배송연동회사전송결과코드 업데이트
            goodsFlowTraceCommandMapper.updateDeliveryResultCode(entity.getDlvNo(), deliveryFinishDto.getInvoiceNo(), "99".equals(deliveryFinishDto.getDlvStatType()) ? "F" : "I");
        }

        // 연동결과 merge
        entityList.forEach(e -> {
            goodsFlowTraceCommandMapper.mergeDlGoodsfDlvIntlRslt(DlGoodsfDlvIntlRsltDEntity.builder()
                    .dlvIntlInntNo(e.getDlvIntlInntNo())
                    .invcNo(deliveryFinishDto.getInvoiceNo())
                    .ordNo(e.getDlvIntlOrdInntNo())
                    .ordGdsSeq(e.getOrdGdsSeq())
                    .goodsfDlvCoCd(deliveryFinishDto.getLogisticsCode())
                    .dlvGdsQty(e.getOrdQty())
                    .dlvIntlStCd(deliveryFinishDto.getDlvStatType())
                    .dlvGdsIntlErrCd(deliveryFinishDto.getErrorCode())
                    .build());
        });

        // 지키미 result update
        goodsFlowTraceCommandMapper.updateGikimiResultStatus(GikimiresultEntity.builder()
                .transUniqueCode(deliveryFinishDto.getTransUniqueCode())
                .seq(deliveryFinishDto.getSeq())
                .build());
    }

    @Override
    public List<GoodsFlowSendTraceRequestDto> getSendTraceRequestTargetForApi() {
        return goodsFlowTraceQueryMapper.selectSendTraceRequestTarget();
    }

    /**
     * 배송추적요청 API 호출시 실패건이 포함되어 있으면 전체 실패 처리되므로
     * 실패 대상은 DB 처리 후 제외하고 API 재호출 방식
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void sendTraceRequestByApi(List<GoodsFlowSendTraceRequestDto> param) {
        log.info("sendTraceRequest {} ", param);

        int retryCnt = 3;
        int tryCnt = 0;
        List<GoodsFlowSendTraceRequestDto> currentList = new ArrayList<>(param);

        while (!currentList.isEmpty() && tryCnt <= retryCnt) {
            SendTraceResponse result = goodsFlowClient.post(GoodsFlowApiEnum.SEND_TRACE_REQUEST_URL, SendTraceRequest.builder()
                    .data(SendTraceRequest.SendTraceRequestItem.builder().items(currentList).build()).build(), SendTraceResponse.class);

            // 성공시 종료
            if (result.isSuccess()) {
                goodsFlowTraceCommandMapper.updateSendTraceRequestResult(currentList.stream()
                        .map(item -> GikimimastEntity.builder()
                                .mid(item.getMid())
                                .build())
                        .collect(Collectors.toList()));
                break;
            }

            // 실패 대상 추출
            Map<String, String> failedMap = result.getError().getDetails()
                    .stream()
                    .collect(Collectors.toMap(SendTraceResponse.Details::getTransUniqueCode, SendTraceResponse.Details::getMessage));

            List<GikimimastEntity> failedResult = new ArrayList<>();
            List<GoodsFlowSendTraceRequestDto> retryList = new ArrayList<>();

            for (GoodsFlowSendTraceRequestDto item : currentList) {
                if (failedMap.containsKey(item.getTransUniqueCode())) {
                    failedResult.add(GikimimastEntity.builder()
                            .mid(item.getMid())
                            .errCode("02")
                            .errDesc(failedMap.get(item.getTransUniqueCode()))
                            .build());
                } else {
                    retryList.add(item);
                }
            }

            // 실패하지 않은 대상건 모아서 API 재호출
            currentList.clear();
            currentList.addAll(retryList);

            // 실패건은 DB 처리
            goodsFlowTraceCommandMapper.updateSendTraceRequestResult(failedResult);
            tryCnt++;
        }
    }

    @Override
    public ReceiveTraceResultResponse getReceiveTraceResultByApi() {
        // 배송결과 수신 API 호출
        return goodsFlowClient.post(GoodsFlowApiEnum.RECEIVE_TRACE_RESULT_URL, null, ReceiveTraceResultResponse.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertReceiveTraceResultByApi(List<ReceiveTraceResultResponse.Item> itemList) {
        log.info("insertReceiveTraceResultByApi {}", itemList);

        // 배송결과 등록 후 굿스플로에 응답하기 위한 request
        List<SendTraceResultRequest.Item> requestItemList = new ArrayList<>();

        // 배송결과 등록 (ez_if.gikimiresult)
        for (ReceiveTraceResultResponse.Item item : itemList) {
            goodsFlowTraceCommandMapper.mergeGikimiresult(this.entityBuilder(item));
            requestItemList.add(SendTraceResultRequest.Item.builder()
                    .transUniqueCode(item.getTransUniqueCode())
                    .seq(item.getSeq())
                    .build());
        }

        // 굿스플로 배송결과수신 응답처리 API 호출
        SendTraceResultResponse result = goodsFlowClient.post(GoodsFlowApiEnum.SEND_TRACE_RESULT_RESPONSE_URL, SendTraceResultRequest.builder()
                .data(SendTraceResultRequest.SendTraceResultRequestItem.builder()
                        .items(requestItemList)
                        .build())
                .build(), SendTraceResultResponse.class);

        if (!result.isSuccess()) {
            log.error("insertReceiveTraceResult {}", result);
            throw new ServiceException();
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteGikimimastRecordBeforeOneYear() {
        goodsFlowTraceCommandMapper.deleteGikimidetail();

        goodsFlowTraceCommandMapper.deleteGikimimast();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteGikimiresultRecordBeforeOneYear() {
        goodsFlowTraceCommandMapper.deleteGikimiresult();
    }

    private GikimiresultEntity entityBuilder(ReceiveTraceResultResponse.Item item) {
        return GikimiresultEntity.builder()
                .transUniqueCode(item.getTransUniqueCode())
                .seq(item.getSeq())
                .sectionCode(item.getSectionCode())
                .logisticsCode(item.getLogisticsCode())
                .invoiceNo(item.getInvoiceNo())
                .dlvStatType(item.getDlvStatType())
                .procDateTime(item.getProcDateTime())
                .exceptionCode(item.getExceptionCode())
                .exceptionName(item.getExceptionName())
                .branchName(item.getBranchName())
                .branchTel(item.getBranchTel())
                .employeeName(item.getEmployeeName())
                .employeeTel(item.getEmployeeTel())
                .employeeMsg(item.getEmployeeMsg())
                .taker(item.getTaker())
                .errorCode(item.getErrorCode())
                .errorName(item.getErrorName())
                .defCode1(item.getDefCode1())
                .defCode2(item.getDefCode2())
                // .createDateTime(item.getCreateDateTime()) 굿스플로에서 응답을 주지만 asis 에서 db 등록시 현재날짜로 사용중
                .procYn("N")
                .build();
    }

    private GikimimastEntity entityBuilder(DlGoodsfDlvIntlBEntity master, long seq) {
        return GikimimastEntity.builder()
                .mid(seq)
                .sectionCode(MallCodeEnum.SHOP.name())
                .memberCode(MallCodeEnum.SHOP.getMemberCode())
                .sellerCode(master.getCspNm())
                .sellerName(master.getCspNm())
                .dlvretType(master.getGoodsfDlvDivCd())
                .fromName(master.getSndrNm())
                .toName(master.getRcvrNm())
                .toMobile(master.getRcvrTelno1())
                .logisticsCode(master.getGoodsfDlvCoCd())
                .invoiceNo(master.getInvcNo())
                .invoicePrintDate(master.getInvcNoRegDtm())
                .transUniqueCode(master.getGoodsfInntDlvNo())
                .defCode1(master.getGoodsfUsrDfnCd1())
                .defCode2(master.getGoodsfUsrDfnCd2())
                .build();
    }

    private List<GikimidetailEntity> entityListBuilder(List<DlGoodsfDlvIntlBEntity> list, long seq) {
        AtomicInteger index = new AtomicInteger(1);
        return list.stream().map(e -> GikimidetailEntity.builder()
                        .mid(seq)
                        .idseq(index.getAndIncrement())
                        .orderNo(e.getDlvIntlOrdInntNo())
                        .orderLine(e.getOrdGdsSeq())
                        .itemCode(e.getGdsCd())
                        .itemName(e.getGdsNm())
                        .itemOption(e.getOrdGdsOptCntn())
                        .itemQty(e.getOrdQty())
                        .itemPrice(e.getGdsPrc())
                        .orderDate(e.getOrdDtm())
                        .paymentDate(e.getPymtDtm())
                        .build())
                .collect(Collectors.toList());
    }

}
