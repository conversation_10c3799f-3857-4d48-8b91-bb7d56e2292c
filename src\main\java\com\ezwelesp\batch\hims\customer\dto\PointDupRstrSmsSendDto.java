package com.ezwelesp.batch.hims.customer.dto;

import lombok.Builder;

import java.io.Serial;
import java.io.Serializable;


/**
 * 제휴사 상품 등록/수정 DB 저장용 DTO
 *
 * <AUTHOR>
 * @since 2025.04.23
 */
@Builder
public record PointDupRstrSmsSendDto(
        String callTo
        , String callFrom
        , String svcType
        , String smsText
        , String msgType

) implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
}

