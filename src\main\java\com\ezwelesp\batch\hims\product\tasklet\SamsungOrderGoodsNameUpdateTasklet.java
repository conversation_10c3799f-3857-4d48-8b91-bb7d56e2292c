package com.ezwelesp.batch.hims.product.tasklet;

import com.ezwelesp.batch.hims.order.samsung.service.SamsungOrderGoodsNameUpdateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class SamsungOrderGoodsNameUpdateTasklet implements Tasklet {

    private final SamsungOrderGoodsNameUpdateService samsungOrderGoodsNameUpdateService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            // 1. 주문기본T.주문상품요약명 수정
            samsungOrderGoodsNameUpdateService.replaceSpecialCharsInOrOrdBGoodsName();

            // 2. 주문상품상세T.상품명 수정
            samsungOrderGoodsNameUpdateService.replaceSpecialCharsInOrOrdGdsDGoodsName();
        } catch (Exception e) {
            log.error("SamsungOrderGoodsNameUpdateTasklet failed. {}, {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.debug("SamsungOrderGoodsNameUpdateTasklet finished");
        return RepeatStatus.FINISHED;
    }

}
