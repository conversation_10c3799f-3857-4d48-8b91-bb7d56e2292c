package com.ezwelesp.batch.hims.product.tasklet;

import com.ezwelesp.batch.hims.order.samsung.service.command.SamsungOrderGoodsCommandService;
import com.ezwelesp.batch.hims.order.samsung.service.query.SamsungOrderGoodsQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class SamsungOrderGoodsNameUpdateTasklet implements Tasklet {

    private final SamsungOrderGoodsCommandService samsungOrderGoodsCommandService;
    private final SamsungOrderGoodsQueryService samsungOrderGoodsQueryService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            log.info("Samsung 주문상품명 특수문자 치환 배치 시작");

            // 전체 처리 대상 건수 조회
            int targetOrdersCount = samsungOrderGoodsQueryService.getTargetOrdersCount();
            log.info("처리 대상 주문 건수 (최근 2일): {}", targetOrdersCount);

            if (targetOrdersCount == 0) {
                log.info("처리 대상 주문이 없습니다. 배치를 종료합니다.");
                return RepeatStatus.FINISHED;
            }

            // 1. 주문기본T.주문상품요약명 수정
            log.info("=== 주문기본 테이블 처리 시작 ===");
            processOrOrdBGoodsName();

            // 2. 주문상품상세T.상품명 수정
            log.info("=== 주문상품상세 테이블 처리 시작 ===");
            processOrOrdGdsDGoodsName();

            log.info("Samsung 주문상품명 특수문자 치환 배치 완료");

        } catch (Exception e) {
            log.error("SamsungOrderGoodsNameUpdateTasklet failed. {}, {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
            throw e;
        }

        return RepeatStatus.FINISHED;
    }

    /**
     * 주문기본 테이블 처리
     */
    private void processOrOrdBGoodsName() {
        // Query: 처리 전 검증
        int beforeCount = samsungOrderGoodsQueryService.getOrdGdsSmryNmWithSpecialCharsCount();
        log.info("주문기본 테이블 처리 전 특수문자 포함 건수: {}", beforeCount);

        if (beforeCount == 0) {
            log.info("주문기본 테이블에 처리할 특수문자가 포함된 데이터가 없습니다.");
            return;
        }

        // Command: 실제 데이터 수정
        samsungOrderGoodsCommandService.replaceSpecialCharsInOrOrdBGoodsName();

        // Query: 처리 후 검증
        int afterCount = samsungOrderGoodsQueryService.getOrdGdsSmryNmWithSpecialCharsCount();
        log.info("주문기본 테이블 처리 후 특수문자 포함 건수: {}", afterCount);
        log.info("주문기본 테이블 처리 완료 - 처리된 건수: {}", beforeCount - afterCount);
    }

    /**
     * 주문상품상세 테이블 처리
     */
    private void processOrOrdGdsDGoodsName() {
        // Query: 처리 전 검증
        int beforeCount = samsungOrderGoodsQueryService.getGdsNmWithSpecialCharsCount();
        log.info("주문상품상세 테이블 처리 전 특수문자 포함 건수: {}", beforeCount);

        if (beforeCount == 0) {
            log.info("주문상품상세 테이블에 처리할 특수문자가 포함된 데이터가 없습니다.");
            return;
        }

        // Command: 실제 데이터 수정
        samsungOrderGoodsCommandService.replaceSpecialCharsInOrOrdGdsDGoodsName();

        // Query: 처리 후 검증
        int afterCount = samsungOrderGoodsQueryService.getGdsNmWithSpecialCharsCount();
        log.info("주문상품상세 테이블 처리 후 특수문자 포함 건수: {}", afterCount);
        log.info("주문상품상세 테이블 처리 완료 - 처리된 건수: {}", beforeCount - afterCount);
    }

}
