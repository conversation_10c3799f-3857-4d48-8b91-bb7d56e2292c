package com.ezwelesp.batch.aasample.test.chunk.processor;

import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.aasample.test.domain.Test;

@Component
@StepScope
public class ChunkItemProcessor implements ItemProcessor<Test, Test> {
    private final String ntcTitl;

    public ChunkItemProcessor(@Value("#{jobParameters['ntcTitl']}") String ntcTitl) {
        this.ntcTitl = ntcTitl;
    }

    @Override
    public Test process(Test item) throws Exception {
        return item;
    }

}
