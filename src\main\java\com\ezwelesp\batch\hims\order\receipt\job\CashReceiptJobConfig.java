package com.ezwelesp.batch.hims.order.receipt.job;

import com.ezwelesp.batch.hims.order.receipt.tasklet.OrderReceiptTasklet;
import com.ezwelesp.batch.hims.order.receipt.tasklet.ClaimReceiptTasklet;
import com.ezwelesp.batch.hims.order.receipt.tasklet.PublicationGatherReceiptTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class CashReceiptJobConfig {
    private final CommonJobListener commonJobListener;
    private final OrderReceiptTasklet orderReceiptTasklet;
    private final ClaimReceiptTasklet claimReceiptTasklet;
    private final PublicationGatherReceiptTasklet publicationGatherReceiptTasklet;

    @Bean("BA_HIOR00077")
    public Job cashReceiptJob(JobRepository jobRepository, @Qualifier("BA_HIOR00077_STEP1") Step cashReceiptStep1, @Qualifier("BA_HIOR00077_STEP2") Step cashReceiptStep2, @Qualifier("BA_HIOR00077_STEP3") Step cashReceiptStep3) {
        return new JobBuilder("BA_HIOR00077", jobRepository)
                .listener(commonJobListener)
                .start(cashReceiptStep1)
                .next(cashReceiptStep2)
                .next(cashReceiptStep3)
                .build();
    }

    @Bean("BA_HIOR00077_STEP1")
    public Step cashReceiptStep1(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00077_STEP1", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(orderReceiptTasklet, transactionManager)
                .build();
    }

    @Bean("BA_HIOR00077_STEP2")
    public Step cashReceiptStep2(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00077_STEP2", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(claimReceiptTasklet, transactionManager)
                .build();
    }

    @Bean("BA_HIOR00077_STEP3")
    public Step cashReceiptStep3(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00077_STEP3", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(publicationGatherReceiptTasklet, transactionManager)
                .build();
    }
}
