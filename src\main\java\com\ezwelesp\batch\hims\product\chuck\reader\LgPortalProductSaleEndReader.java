package com.ezwelesp.batch.hims.product.chuck.reader;

import com.ezwelesp.batch.hims.product.domain.dto.LgPortalProductSaleEndDto;
import com.ezwelesp.batch.hims.product.mapper.query.ProductManagementQueryMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@StepScope
public class LgPortalProductSaleEndReader extends MyBatisPagingItemReader<LgPortalProductSaleEndDto> {
    // 1회 read 시 가져올 row 개수
    private final int PAGE_SIZE = 1000;

    public LgPortalProductSaleEndReader(
            @Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory sqlSessionFactory
    ) {
        this.setName("LgPortalProductSaleEndReader");
        this.setSqlSessionFactory(sqlSessionFactory);
        this.setQueryId(
                ProductManagementQueryMapper.class.getName() + ".selectLgPortalProductSaleEndTarget");
        this.setPageSize(PAGE_SIZE);
    }

}
