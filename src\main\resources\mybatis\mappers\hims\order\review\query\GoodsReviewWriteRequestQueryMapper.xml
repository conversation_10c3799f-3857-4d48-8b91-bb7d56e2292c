<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.review.mapper.query.GoodsReviewWriteRequestQueryMapper">
    <resultMap id="GoodsReviewWriteRequestResultMap" type="com.ezwelesp.batch.hims.order.review.domain.GoodsReviewWriteRequestDto">
        <id column="user_key" jdbcType="VARCHAR" property="userKey"/>
        <result column="dvc_innt_cval" jdbcType="VARCHAR" property="dvcInntCval"/>
        <result column="dvc_tkn_cval" jdbcType="VARCHAR" property="dvcTknCval"/>
        <result column="mbl_os_div_cd" jdbcType="VARCHAR" property="mblOsDivCd"/>
        <result column="push_infm_rcv_agr_yn" jdbcType="VARCHAR" property="pushInfmRcvAgrYn"/>
        <result column="mbl_last_lgin_dtm" jdbcType="VARCHAR" property="mblLastLginDtm"/>
        <result column="rgm_gd_push_rcv_agr_yn" jdbcType="VARCHAR" property="rgmGdPushRcvAgrYn"/>
        <result column="bnft_inf_push_rcv_agr_yn" jdbcType="VARCHAR" property="bnftInfPushRcvAgrYn"/>
        <result column="push_infm_way_cd" jdbcType="VARCHAR" property="pushInfmWayCd"/>
        <result column="mbl_telno" jdbcType="VARCHAR" property="mblTelno"/>
        <result column="app_ver_nm" jdbcType="VARCHAR" property="appVerNm"/>
        <result column="app_pckg_nm" jdbcType="VARCHAR" property="appPckgNm"/>
        <result column="orderGoodsList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.OrOrdGdsDEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="orderGoodsList"/>
    </resultMap>

    <select id="selectGoodsReviewWriteRequestTarget" resultMap="GoodsReviewWriteRequestResultMap">
        select ord.user_key
              , ord.ordGdsJson as orderGoodsList
              , cudb.dvc_innt_cval
              , cudb.dvc_tkn_cval
              , cudb.mbl_os_div_cd
              , cudb.push_infm_rcv_agr_yn
              , cudb.mbl_last_lgin_dtm
              , cudb.rgm_gd_push_rcv_agr_yn
              , cudb.bnft_inf_push_rcv_agr_yn
              , cudb.push_infm_way_cd
              , cudb.app_ver_nm
              , cudb.app_pckg_nm
              , cub.mbl_telno
              , case when cudb.app_pckg_nm is null then 'bokji'
                     else case when cudb.app_pckg_nm = 'com.ezwel.ibene' then 'KT그룹'
                               when cudb.app_pckg_nm = 'com.ezwel.hhig' then '현대중공업그룹'
                               when cudb.app_pckg_nm = 'com.ezwel.ontongdaejeon' then '온통대전'
                               when cudb.app_pckg_nm = 'com.ezwel.hyundai' then '현대자동차그룹'
                               when cudb.app_pckg_nm = 'com.ezwel.youthjobaba' then '경기청년몰'
                               when cudb.app_pckg_nm = 'com.ezwel.nonghyuplogin' then '농협몰'
                               when cudb.app_pckg_nm = 'com.ezwel.milipass' then '밀리패스'
                               else 'bokji' end
                end as affNm
          from (select t.user_key, json_agg(t order by t.ord_no desc, t.ord_gds_seq) as ordGdsJson
                   from (
                          select oob.user_key, oob.ord_no, ddgd.ord_gds_seq, oogd.gds_cd, oogd.gds_nm
                            from ez_or.or_ord_b oob
                            join ez_or.dl_dlv_b ddb on ddb.ord_no = oob.ord_no
                            join ez_or.dl_dlv_gds_d ddgd on ddb.dlv_no = ddgd.dlv_no
                            join ez_or.or_ord_gds_d oogd on oogd.ord_no = ddgd.ord_no and oogd.ord_gds_seq = ddgd.ord_gds_seq
                           where ddb.clm_no is null
                             and ddb.dlv_obnd_typ_cd = 'ORD'
                             and ddb.dlv_st_cd = 'CMPT'
                             and ddb.dlv_cmpt_dtm between to_char(now() - interval '1 day', 'YYYYMMDD') || '120000' and to_char(now(), 'YYYYMMDD') || '120000'
                             and ddgd.dlv_gds_qty - ddgd.dlv_cncl_gds_qty > 0
                             and oogd.gdsrvw_wrt_gd_nttk_snd_obj_yn = 'N'
                             and oogd.mndr_choc_opt_yn = 'Y'
                             and oob.fam_mem_no is null
                           union all
                          select oob.user_key, oob.ord_no, dosgd.ord_gds_seq, oogd.gds_cd, oogd.gds_nm
                            from ez_or.or_ord_b oob
                            join ez_or.dl_ord_snd_gds_d dosgd on dosgd.ord_no = oob.ord_no
                            join ez_or.or_ord_gds_d oogd on oogd.ord_no = dosgd.ord_no and oogd.ord_gds_seq = dosgd.ord_gds_seq
                           where oob.fam_mem_no is null
                             and dosgd.ord_snd_st_cd = '02'
                             and dosgd.ord_snd_gds_use_st_cd = '03'
                             and dosgd.use_dtm between to_char(now() - interval '1 day', 'YYYYMMDD') || '120000' and to_char(now(), 'YYYYMMDD') || '120000'
                             and oogd.gdsrvw_wrt_gd_nttk_snd_obj_yn = 'N'
                           group by oob.user_key, oob.ord_no, dosgd.ord_gds_seq, oogd.gds_cd, oogd.gds_nm /* 발송상품은 수량별로 들어감 */
                      ) t
                 where not exists (select 1 from ez_pd.pd_gdsrvw_b where gds_cd = t.gds_cd and ord_no = t.ord_no)
                 group by t.user_key
             ) ord
         join ez_ct.ct_usr_b cub on cub.user_key = ord.user_key
         join ez_ct.ct_usr_d cud on cud.user_key = ord.user_key
         join ez_ct.ct_user_dvc_b cudb on cudb.user_key = ord.user_key
        where (cub.use_yn = 'Y' or cub.test_usr_yn = 'Y')
          and cud.vip_knd_cd <![CDATA[<>]]> '1005' /* 블랙리스트 제외 */
          and cudb.bnft_inf_push_rcv_agr_yn = 'Y'
          and cudb.dvc_tkn_cval is not null
          and cudb.app_ver_nm is not null
          and cudb.mbl_last_lgin_dtm <![CDATA[>]]> to_char(now() - interval '12 month', 'YYYYMMDD')
          and cudb.use_yn = 'Y'
    </select>

    <select id="selectEzCheckinAppPushTarget" resultType="com.ezwelesp.batch.hims.order.review.domain.GoodsReviewWriteRequestDto">
        select cub.user_key
              , ezwel_order_num
              , client_cd
              , case when cudb.app_pckg_nm is null then 'bokji'
                    else case when cudb.app_pckg_nm = 'com.ezwel.ibene' then 'KT그룹'
                               when cudb.app_pckg_nm = 'com.ezwel.hhig' then '현대중공업그룹'
                               when cudb.app_pckg_nm = 'com.ezwel.ontongdaejeon' then '온통대전'
                               when cudb.app_pckg_nm = 'com.ezwel.hyundai' then '현대자동차그룹'
                               when cudb.app_pckg_nm = 'com.ezwel.youthjobaba' then '경기청년몰'
                               when cudb.app_pckg_nm = 'com.ezwel.nonghyuplogin' then '농협몰'
                               when cudb.app_pckg_nm = 'com.ezwel.milipass' then '밀리패스'
                         else 'bokji' end
                  end as affNm
        from ez_checkin.ezc_reserv_base erb
        join ez_ct.ct_usr_b cub on cub.user_key = erb.user_key::varchar
        join ez_ct.ct_usr_d cud on cud.user_key = cub.user_key
        join ez_ct.ct_user_dvc_b cudb on cudb.user_key = cub.user_key
       where erb.check_out_dd = to_char(now(), 'YYYYMMDD')
         and erb.reserv_status = 'R001OK'
         and (cub.use_yn = 'Y' or cub.test_usr_yn = 'Y')
         and cud.vip_knd_cd <![CDATA[<>]]> '1005' /* 블랙리스트 제외 */
         and cudb.bnft_inf_push_rcv_agr_yn = 'Y'
         and cudb.dvc_tkn_cval is not null
         and cudb.app_ver_nm is not null
         and cudb.mbl_last_lgin_dtm <![CDATA[>]]> to_char(now() - interval '12 month', 'YYYYMMDD')
         and cudb.use_yn = 'Y'
    </select>

</mapper>
