package com.ezwelesp.batch.lgportal.entity;

import lombok.Data;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 상품옵션조합기본(ez_pd.pd_gds_opt_comb_b)
 */
@Data
public class PdGdsOptCombBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 상품코드(gds_cd) not null
     */
    private String gdsCd;

    /**
     * 옵션상품조합순번(opt_gds_comb_seq) not null
     */
    private Long optGdsCombSeq;

    /**
     * 협력사옵션상품번호(csp_opt_gds_no)
     */
    private String cspOptGdsNo;

    /**
     * 옵션추가가격(opt_add_prc)
     */
    private BigDecimal optAddPrc;

    /**
     * 재고수량(stck_qty)
     */
    private Integer stckQty;

    /**
     * 안전재고관리여부(safe_stck_mng_yn)
     */
    private String safeStckMngYn;

    /**
     * 안전재고수량(safe_stck_qty)
     */
    private Integer safeStckQty;

    /**
     * 판매수량(sell_qty)
     */
    private Integer sellQty;

    /**
     * 옵션매입가격(opt_pchs_prc)
     */
    private BigDecimal optPchsPrc;

    /**
     * 인지세발행여부(sttx_pblc_yn)
     */
    private String sttxPblcYn;

    /**
     * 사용여부(use_yn)
     */
    private String useYn;

    /**
     * 상품옵션중량(gds_opt_wgt)
     */
    private BigDecimal gdsOptWgt;

    /**
     * 이미지경로(img_path)
     */
    private String imgPath;

    /**
     * 이미지노출여부(img_exps_yn)
     */
    private String imgExpsYn;

    /**
     * 상품상세이미지경로(gds_dtl_img_path)
     */
    private String gdsDtlImgPath;

    /**
     * 정렬순서(sort_ordg)
     */
    private Integer sortOrdg;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
