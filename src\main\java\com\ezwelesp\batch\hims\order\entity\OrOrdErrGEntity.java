package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 주문오류로그(ez_or.or_ord_err_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrOrdErrGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문오류로그순번(ord_err_log_seq) not null
     */
    private Long ordErrLogSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 주문오류로그코드(ord_err_log_cd)
     */
    private String ordErrLogCd;

    /**
     * 오류메시지내용(err_msg_cntn)
     */
    private String errMsgCntn;

    /**
     * 주문배송내용J(ord_dlv_cntnj)
     */
    private String ordDlvCntnj;

    /**
     * 주문상품내용J(ord_gds_cntnj)
     */
    private String ordGdsCntnj;

    /**
     * 결제파라미터내용J(pymt_para_cntnj)
     */
    private String pymtParaCntnj;

    /**
     * 주문쿠폰내용J(ord_cpn_cntnj)
     */
    private String ordCpnCntnj;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최초등록IP(frst_reg_ip) not null
     */
    private String frstRegIp;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
