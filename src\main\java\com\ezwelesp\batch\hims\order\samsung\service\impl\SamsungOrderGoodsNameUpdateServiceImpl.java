package com.ezwelesp.batch.hims.order.samsung.service.impl;

import com.ezwelesp.batch.hims.order.samsung.mapper.command.SamsungOrderGoodsCommandMapper;
import com.ezwelesp.batch.hims.order.samsung.service.SamsungOrderGoodsNameUpdateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class SamsungOrderGoodsNameUpdateServiceImpl implements SamsungOrderGoodsNameUpdateService {

    private final SamsungOrderGoodsCommandMapper samsungOrderGoodsCommandMapper;

    @Override
    public void replaceSpecialCharsInOrOrdBGoodsName() {
        samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNm40();
        samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNm41();
        samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNmQuot();
        samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNm39();
        samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNm35();
        samsungOrderGoodsCommandMapper.replaceOrdGdsSmryNmAmp();
    }

    @Override
    public void replaceSpecialCharsInOrOrdGdsDGoodsName() {
        samsungOrderGoodsCommandMapper.replaceGdsNm40();
        samsungOrderGoodsCommandMapper.replaceGdsNm41();
        samsungOrderGoodsCommandMapper.replaceGdsNmQuot();
        samsungOrderGoodsCommandMapper.replaceGdsNm39();
        samsungOrderGoodsCommandMapper.replaceGdsNm35();
        samsungOrderGoodsCommandMapper.replaceGdsNmAmp();
    }
}
