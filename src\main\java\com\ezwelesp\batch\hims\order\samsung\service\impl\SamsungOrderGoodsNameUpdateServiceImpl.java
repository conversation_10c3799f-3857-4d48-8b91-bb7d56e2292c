package com.ezwelesp.batch.hims.order.samsung.service.impl;

import com.ezwelesp.batch.hims.order.samsung.service.SamsungOrderGoodsNameUpdateService;
import com.ezwelesp.batch.hims.order.samsung.service.command.SamsungOrderGoodsCommandService;
import com.ezwelesp.batch.hims.order.samsung.service.query.SamsungOrderGoodsQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class SamsungOrderGoodsNameUpdateServiceImpl implements SamsungOrderGoodsNameUpdateService {

    private final SamsungOrderGoodsCommandService samsungOrderGoodsCommandService;
    private final SamsungOrderGoodsQueryService samsungOrderGoodsQueryService;

    @Override
    public void replaceSpecialCharsInOrOrdBGoodsName() {
        // 처리 전 검증
        int beforeCount = samsungOrderGoodsQueryService.getOrdGdsSmryNmWithSpecialCharsCount();
        log.info("주문기본 테이블 처리 전 특수문자 포함 건수: {}", beforeCount);

        if (beforeCount == 0) {
            log.info("주문기본 테이블에 처리할 특수문자가 포함된 데이터가 없습니다.");
            return;
        }

        // Command 실행
        samsungOrderGoodsCommandService.replaceSpecialCharsInOrOrdBGoodsName();

        // 처리 후 검증
        int afterCount = samsungOrderGoodsQueryService.getOrdGdsSmryNmWithSpecialCharsCount();
        log.info("주문기본 테이블 처리 후 특수문자 포함 건수: {}", afterCount);
        log.info("주문기본 테이블 처리 완료 - 처리된 건수: {}", beforeCount - afterCount);
    }

    @Override
    public void replaceSpecialCharsInOrOrdGdsDGoodsName() {
        // 처리 전 검증
        int beforeCount = samsungOrderGoodsQueryService.getGdsNmWithSpecialCharsCount();
        log.info("주문상품상세 테이블 처리 전 특수문자 포함 건수: {}", beforeCount);

        if (beforeCount == 0) {
            log.info("주문상품상세 테이블에 처리할 특수문자가 포함된 데이터가 없습니다.");
            return;
        }

        // Command 실행
        samsungOrderGoodsCommandService.replaceSpecialCharsInOrOrdGdsDGoodsName();

        // 처리 후 검증
        int afterCount = samsungOrderGoodsQueryService.getGdsNmWithSpecialCharsCount();
        log.info("주문상품상세 테이블 처리 후 특수문자 포함 건수: {}", afterCount);
        log.info("주문상품상세 테이블 처리 완료 - 처리된 건수: {}", beforeCount - afterCount);
    }
}
