package com.ezwelesp.batch.hims.order.pgcompare.inicis.job;

import com.ezwelesp.batch.hims.order.pgcompare.inicis.tasklet.InicisPgApiPrsnlPayCompareTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class ApiPgPrsnlPayCompareJobConfig {
    private final CommonJobListener commonJobListener;
    private final InicisPgApiPrsnlPayCompareTasklet inicisPgApiPrsnlPayCompareTasklet;


    @Bean("BA_HIOR00002")
    public Job inicisPgCompareJob(JobRepository jobRepository, @Qualifier("BA_HIOR00002_STEP1") Step inicisPgCompareStep1) {
        return new JobBuilder("BA_HIOR00002", jobRepository)
                .listener(commonJobListener)
                .start(inicisPgCompareStep1)
                .build();
    }

    @Bean("BA_HIOR00002_STEP1")
    public Step inicisPgCompareStep1(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00002_STEP1", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(inicisPgApiPrsnlPayCompareTasklet, transactionManager)
                .build();
    }


}
