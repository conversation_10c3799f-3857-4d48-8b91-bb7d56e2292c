<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.product.mapper.query.ProductManagementQueryMapper">
    <!-- 상품 판매일자 종료된 상품 타겟 조회  -->
    <select id="selectLgPortalProductSaleEndTarget"
            resultType="com.ezwelesp.batch.hims.product.domain.dto.LgPortalProductSaleEndDto">
        SELECT B.TARGET_GOODS_CD
             , B.GOODS_CD
        FROM EZ_PD.pd_gds_c A
                 JOIN EZ_IF.api_batch_goods B ON A.gds_cd = '101' || B.goods_cd::text
        WHERE A.gds_sell_st_cd = '1002'
          AND A.sell_end_dtm <![CDATA[ < ]]> to_char(CURRENT_DATE
            , 'yyyymmddhh24miss')
          AND A.last_mod_dtm <![CDATA[ > ]]> to_char(CURRENT_DATE -60
            , 'yyyymmddhh24miss')
          AND B.target_goods_cd IS NOT NULL;

    </select>

</mapper>
