package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

/**
  * 출고/반품/교환지 저장 요청 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see DeliveryLocListReqDto
  */
@SuperBuilder(toBuilder = true)
@Getter
public class DeliveryLocListReqDto {
    private String apiKey;

    private String deliveryMappingCode;
    private String deliveryAddrTypeCode;
    private String deliveryCostTypeCode;

    @Builder.Default
    private int page = 1;

    @Builder.Default
    private int pageSize = 5;
}
