<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgcompare.inicis.mapper.command.PgCompareCommandMapper">

    <insert id="insertEzEcOrderPayLog">
        insert into ez_if.ez_ec_order_pay_log
        (
         tid
        ,status_cd
        , order_num
        , ezwel_order_num
        , store_id
        , deal_type
        , device_type
        , pay_type
        , deal_status
        , confirm_dd
        , confirm_tm
        , cancel_dd
        , cancel_tm
        , deal_amt
        , cancel_amt
        , cancel_tid
        , card_amt
        , alli_point
        , inst_mm
        , card_type
        , confirm_num
        , simple_pay
        , cancel_remain_amt
        , bank_nm
        , goods_nm
        , pay_dt
        , pg_type
        , pay_cancel_dt
        )
        values (
       #{tid, jdbcType = VARCHAR}
       , #{statusCd, jdbcType = VARCHAR}
       , #{orderNum, jdbcType = VARCHAR}
       , #{ezwelOrderNum, jdbcType = NUMERIC}
       , #{storeId, jdbcType = VARCHAR}
       , #{dealType, jdbcType = CHAR}
       , #{deviceType, jdbcType = VARCHAR}
       , #{payType, jdbcType = VARCHAR}
       , #{dealStatus, jdbcType = VARCHAR}
       , #{confirmDd, jdbcType = VARCHAR}
       , #{confirmTm, jdbcType = VARCHAR}
       , #{cancelDd, jdbcType = VARCHAR}
       , #{cancelTm, jdbcType = VARCHAR}
       , #{dealAmt, jdbcType = NUMERIC}
       , #{cancelAmt, jdbcType = NUMERIC}
       , #{cancelTid, jdbcType = VARCHAR}
       , #{cardAmt, jdbcType = NUMERIC}
       , #{alliPoint, jdbcType = VARCHAR}
       , #{instMm, jdbcType = VARCHAR}
       , #{cardType, jdbcType = VARCHAR}
       , #{confirmNum, jdbcType = VARCHAR}
       , #{simplePay, jdbcType = VARCHAR}
       , #{cancelRemainAmt, jdbcType = NUMERIC}
       , #{bankNm, jdbcType = VARCHAR}
       , #{goodsNm, jdbcType = VARCHAR}
       , #{payDt, jdbcType = VARCHAR}
       , #{pgType, jdbcType = CHAR}
       , #{payCancelDt, jdbcType = VARCHAR}
       )
    </insert>

    <update id="updateEzEcOrderPayLog">
        update ez_if.ez_ec_order_pay_log
        set
          order_num = #{orderNum, jdbcType = VARCHAR}
          , ezwel_order_num = #{ezwelOrderNum, jdbcType = NUMERIC}
          , store_id = #{storeId, jdbcType = VARCHAR}
          , deal_type = #{dealType, jdbcType = CHAR}
          , device_type = #{deviceType, jdbcType = VARCHAR}
          , pay_type = #{payType, jdbcType = VARCHAR}
          , deal_status = #{dealStatus, jdbcType = VARCHAR}
          , confirm_dd = #{confirmDd, jdbcType = VARCHAR}
          , confirm_tm = #{confirmTm, jdbcType = VARCHAR}
          , cancel_dd = #{cancelDd, jdbcType = VARCHAR}
          , cancel_tm = #{cancelTm, jdbcType = VARCHAR}
          , deal_amt = #{dealAmt, jdbcType = NUMERIC}
          , cancel_amt = #{cancelAmt, jdbcType = NUMERIC}
          , cancel_tid = #{cancelTid, jdbcType = VARCHAR}
          , card_amt = #{cardAmt, jdbcType = NUMERIC}
          , alli_point = #{alliPoint, jdbcType = VARCHAR}
          , inst_mm =  #{instMm, jdbcType = VARCHAR}
          , card_type = #{cardType, jdbcType = VARCHAR}
          , confirm_num = #{confirmNum, jdbcType = VARCHAR}
          , simple_pay = #{simplePay, jdbcType = VARCHAR}
          , cancel_remain_amt = #{cancelRemainAmt, jdbcType = NUMERIC}
          , bank_nm = #{bankNm, jdbcType = VARCHAR}
          , goods_nm = #{goodsNm, jdbcType = VARCHAR}
          , pay_dt = #{payDt, jdbcType = VARCHAR}
          , pg_type = #{pgType, jdbcType = CHAR}
          , pay_cancel_dt = #{payCancelDt, jdbcType = VARCHAR}
        where
            tid =  #{tid, jdbcType = VARCHAR}
            and status_cd = #{statusCd, jdbcType = VARCHAR}
    </update>

    <insert id="insertApiPgPrsnlPay">
        insert into ez_if.api_pg_prsnl_pay (status_cd, tid, cancel_tid,
                                        ezwel_order_num, store_id, confirm_num,
                                        pay_type, deal_status, confirm_dt,
                                        cancel_dt, deal_amt, cancel_amt,
                                        card_amt, cancel_remain_amt, alli_point,
                                        inst_mm, card_type, simple_pay,
                                        bank_nm, goods_nm, pay_dt,
                                        pay_cancel_dt, reg_dt, pay_cd,
                                        deposit_dt, deposit_amt, calc_amt
        )
        values (#{statusCd,jdbcType=VARCHAR}, #{tid,jdbcType=VARCHAR}, #{cancelTid,jdbcType=VARCHAR},
                #{ezwelOrderNum }, #{storeId,jdbcType=VARCHAR}, #{confirmNum,jdbcType=VARCHAR},
                #{payType,jdbcType=VARCHAR}, #{dealStatus,jdbcType=VARCHAR}, #{confirmDt,jdbcType=VARCHAR},
                #{cancelDt,jdbcType=VARCHAR}, #{dealAmt,jdbcType=NUMERIC}, #{cancelAmt,jdbcType=NUMERIC},
                #{cardAmt,jdbcType=NUMERIC}, #{cancelRemainAmt,jdbcType=NUMERIC}, #{alliPoint,jdbcType=VARCHAR},
                #{instMm,jdbcType=NUMERIC}, #{cardType,jdbcType=VARCHAR}, #{simplePay,jdbcType=VARCHAR},
                #{bankNm,jdbcType=VARCHAR}, #{goodsNm,jdbcType=VARCHAR}, #{payDt,jdbcType=VARCHAR},
                #{payCancelDt,jdbcType=VARCHAR}, #{regDt,jdbcType=VARCHAR}, #{payCd,jdbcType=VARCHAR},
                #{depositDt,jdbcType=VARCHAR}, #{depositAmt,jdbcType=NUMERIC}, #{calcAmt,jdbcType=NUMERIC}
               )
    </insert>

    <update id="updateApiPgPrsnlPay">
        update ez_if.api_pg_prsnl_pay
        set cancel_tid = #{cancelTid,jdbcType=VARCHAR},
            ezwel_order_num = #{ezwelOrderNum},
            store_id = #{storeId,jdbcType=VARCHAR},
            confirm_num = #{confirmNum,jdbcType=VARCHAR},
            pay_type = #{payType,jdbcType=VARCHAR},
            deal_status = #{dealStatus,jdbcType=VARCHAR},
            confirm_dt = #{confirmDt,jdbcType=VARCHAR},
            cancel_dt = #{cancelDt,jdbcType=VARCHAR},
            deal_amt = #{dealAmt,jdbcType=NUMERIC},
            cancel_amt = #{cancelAmt,jdbcType=NUMERIC},
            card_amt = #{cardAmt,jdbcType=NUMERIC},
            cancel_remain_amt = #{cancelRemainAmt,jdbcType=NUMERIC},
            alli_point = #{alliPoint,jdbcType=VARCHAR},
            inst_mm = #{instMm,jdbcType=NUMERIC},
            card_type = #{cardType,jdbcType=VARCHAR},
            simple_pay = #{simplePay,jdbcType=VARCHAR},
            bank_nm = #{bankNm,jdbcType=VARCHAR},
            goods_nm = #{goodsNm,jdbcType=VARCHAR},
            pay_dt = #{payDt,jdbcType=VARCHAR},
            pay_cancel_dt = #{payCancelDt,jdbcType=VARCHAR},
            reg_dt = #{regDt,jdbcType=VARCHAR},
            pay_cd = #{payCd,jdbcType=VARCHAR},
            deposit_dt = #{depositDt,jdbcType=VARCHAR},
            deposit_amt = #{depositAmt,jdbcType=NUMERIC},
            calc_amt = #{calcAmt,jdbcType=NUMERIC}
        where pg_type = #{pgType,jdbcType=CHAR}
          and status_cd = #{statusCd,jdbcType=VARCHAR}
          and tid = #{tid,jdbcType=VARCHAR}
    </update>
</mapper>

