package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Data;

/**
  * 배송접수 처리 사용 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see CspDlvrLocDto
  */
@Data
public class CspDlvrLocDto {
    /**
     * 업체코드
     */
    private Integer cspCd;

    /**
     * 출고지ID
     */
    private String cspDlvrId;

    /**
     * 배송지유형코드(01:출고지, 02:반품지, 03:교환지)
     */
    private String dlvrTypeCd;

    /**
     * 배송비유형코드(01:무료배송, 02: 조건부유료배송)
     */
    private String dlvrCostTypeCd;

    /**
     * 배송비
     */
    private Integer dlvrPrice;

    /**
     * 협력사출고위치명
     */
    private String cspDlvrNm;

    /**
     * 출고/반품/교환 위치기본주소
     */
    private String dlvrAddr;

    /**
     * 출고/반품/교환 위치상세주소
     */
    private String dlvrAddrDetail;

    /**
     * 출고/반품/교환 위치전화번호
     */
    private String telno;

    /**
     * 출고/반품/교환 위치우편번호
     */
    private String zipCd;

    /**
     * 대표주소지 여부
     */
    private String baseSettingYn;



}
