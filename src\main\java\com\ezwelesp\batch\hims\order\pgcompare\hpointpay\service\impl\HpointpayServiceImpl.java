package com.ezwelesp.batch.hims.order.pgcompare.hpointpay.service.impl;



import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.amazonaws.util.Base64;
import com.ezwelesp.batch.hims.order.entity.PyPgPymtCmprErrMtrgLEntity;
import com.ezwelesp.batch.hims.order.entity.external.ApiPgPrsnlPayEntity;
import com.ezwelesp.batch.hims.order.entity.external.EzEcHpointpayEntity;
import com.ezwelesp.batch.hims.order.pgcompare.hpointpay.domain.TossTransactionDto;
import com.ezwelesp.batch.hims.order.pgcompare.hpointpay.mapper.command.HpointpayCommandMapper;
import com.ezwelesp.batch.hims.order.pgcompare.hpointpay.mapper.query.HpointpayQueryMapper;
import com.ezwelesp.batch.hims.order.pgcompare.hpointpay.service.HpointpayService;
import com.ezwelesp.batch.hims.order.reseller.service.MonitorJobService;
import com.ezwelesp.batch.hims.order.utils.OrderJsonUtils;
import com.ezwelesp.batch.hims.order.utils.RestClientUtil;
import com.ezwelesp.framework.utils.DateUtils;
import com.ezwelesp.framework.utils.request.dto.Header;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service 
@RequiredArgsConstructor
public class HpointpayServiceImpl implements HpointpayService{

	private final HpointpayQueryMapper hpointpayQueryMapper;
	private final HpointpayCommandMapper hpointpayCommandMapper;
	private final MonitorJobService monitorJobService;
	
	
	public static final String DEV_PAY_SECRET_KEY = "test_sk_ODnyRpQWGrNLyJZXRGy3Kwv1M9EN";
	public static final String REAL_PAY_SECRET_KEY = "live_sk_k6bJXmgo28emGLMk9vM8LAnGKWx4";
	
	/**
	 * 결제 대사
	 */
	@Override
	public void callHpointpayCompareData() {
		
		log.info("======= PG(H pay) 승인 대사 START ==========");
		List<HashMap> errList = new ArrayList<>();
		try {
			// PG 대사 오류 목록 조회(복지관기준)
			List<HashMap> cuserDiffList = this.hpointpayQueryMapper.selectPgApproveAmtCuserDiffList();
			if(!cuserDiffList.isEmpty()) {
				cuserDiffList.stream().forEach(errList::add);
			}
			// PG 대사 오류 목록 조회(이니시스 기준)
			List<HashMap> pgDiffList = this.hpointpayQueryMapper.selectPgApproveAmtInicisDiffList();
			if(!pgDiffList.isEmpty()) {
				pgDiffList.stream().forEach(errList::add);
			}
			
			// PG 승인 대사 오류건 DB 등록(EZ_EC_ORDER_PAY_ERR)
			if(!errList.isEmpty()) {
				errList.stream().distinct().forEach(item -> {
					this.hpointpayCommandMapper.insertPgApproveErrList(PyPgPymtCmprErrMtrgLEntity.builder()
						.pgPymtErrCd(String.valueOf(item.get("errType")))
						.ordNo(String.valueOf(item.get("orderNum")))
						.ordStCd(String.valueOf(item.get("orderStatus")))
						.aspOrdNo(String.valueOf(item.get("aspOrderNum")))
						.ordChNm(String.valueOf(item.get("orderType")))
						.pymtAmt(new BigDecimal(String.valueOf(item.get("recgPrice"))))
						.pgPymtApvAmt(new BigDecimal(String.valueOf(item.get("pgAmt"))))
						.pgKndCd(String.valueOf(item.get("pgType")))
						.pymtMnsNm(String.valueOf(item.get("payType")))
						.pgFrcsNo(String.valueOf(item.get("storeId")))
						.pgApvNo(String.valueOf(item.get("cancelTid")))
						.pgCrdcApvNo(String.valueOf(item.get("cardConfirmNum")))
						.pgFrcsOrdNo(String.valueOf(item.get("shopOrderNum")))
						.acssTeTypNm(String.valueOf(item.get("deviceType")))
						.mgrCnftCmptYn("N")
						.mgrMemo("")
						.build());
				});
			}
			String resEtc = "[PG(H pay)대사] 오류건 발생 : " + errList.size() + "건";
			this.monitorJobService.saveScMntMonitorRes(DateUtils.dateTimeNow(), "INICIS_ORDER_AMOUNT_001", errList.size(), resEtc);
			
		} catch (Exception e) {
			log.error("처리중 에러가 발생햇습니다.", e);
			//모니터잡에 던져야함.. TOBE에서 어떻게 하는지 확인 필요.
			this.monitorJobService.saveScMntMonitorRes(DateUtils.dateTimeNow(), "PG_DAILY_ERROR_REPORT_001", errList.size(), "[PG(H pay)대사] 배치 오류 발생<br>" + e);
		}
		
		log.info("======= PG(H pay) 승인 대사 END ==========");
	}
	
	/**
	 * 결제 수집
	 * @throws Exception 
	 */
	@Override
	public void callHpointpayGatherData() throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		params.put("startDate", DateUtils.dateTimeNow("yyyy-MM-dd'T'HH") + ":00:00.000");
		params.put("endDate", DateUtils.dateTimeNow("yyyy-MM-dd'T'HH") + ":59:59.999");
//		params.put("startDate", "2024-06-18T13:00:00.000");
//		params.put("endDate", "2024-12-18T13:59:59.999");
		params.put("limit", 500);
		
		
		log.info("=============== H.Point pay 거래 조회 시작 ===============");
		String lastTransactionKey = "";
		boolean isLoop = true;
		while(isLoop) {
			if(!StringUtils.isEmpty(lastTransactionKey)) {
				params.put("startingAfter", lastTransactionKey);
			}
			lastTransactionKey = this.insertData(params);
			if(StringUtils.isEmpty(lastTransactionKey)) isLoop = false;
		}
		log.info("=============== H.Point pay 거래 조회 종료 ===============");	
	}

	
	private String insertData(Map<String, Object> params) throws Exception {
		String lastTransactionKey = "";
		List<TossTransactionDto> responseData = this.request(params);
		//데이터 호출
		if(!responseData.isEmpty()) {
			for(TossTransactionDto item : responseData) {
				try {
					String payType =null;
					String dealStatus = "";
					boolean checkPayStatus = false;
					String confirmDt = convertIso8601ToString(item.getTransactionAt());
					if("DONE".equals(item.getStatus())){
						payType = "A";
						dealStatus = "승인";
					}else if("CANCELED".equals(item.getStatus())){
						payType = "C";
						dealStatus = "취소";
						checkPayStatus = true;
					}else if("PARTIAL_CANCELED".equals(item.getStatus())){
						payType = "P";
						checkPayStatus = false;
						dealStatus = "부분취소";
					}
					
					if(StringUtils.isEmpty(payType)) throw new IllegalArgumentException("payType 정의 안됨");
					
					String payMethod = null;
					boolean checkPayCard = false;
					if ("카드".equals(item.getMethod())) {
						payMethod = "CARD";
						checkPayCard = true;
					} else if ("계좌이체".equals(item.getMethod())) {
						payMethod = "ACCOUNT";
						checkPayCard = false;
					}
					
					if (StringUtils.isEmpty(payMethod)) {
						throw new IllegalArgumentException("payMethod 정의 안됨");
					}
					
					/**
					 * 현대포인트페이 저장
					 */
					this.saveTossTransaction(EzEcHpointpayEntity.builder()
							.tossKey(item.getTransactionKey())
							.authcode(item.getPaymentKey())
							.storeId(item.getMId())
							.tossOrderNum(item.getOrderId())
							.payType(payType)
							.payMethod(payMethod)
							.payAmt(item.getAmount())
							.confirmDt(confirmDt)
							.build());
				
					/**
					 * 거래대사PG거래내역 사용을 위한 저장
					 */
					this.saveApiPgPrsnlPay(ApiPgPrsnlPayEntity.builder()
							.pgType("H")
							.statusCd(checkPayStatus ? "0001" : "0002")
							.tid(item.getTransactionKey())
							.cancelTid(item.getPaymentKey())
							.ezwelOrderNum(Long.parseLong(item.getOrderId()))
							.storeId(item.getMId())
							.confirmNum("")
							.payType("현대포인트페이")
							.dealStatus(dealStatus)
							.confirmDt(confirmDt)
							.dealAmt(item.getAmount())
							.cancelDt(!checkPayStatus ? confirmDt : "")
							.cancelAmt(!checkPayStatus ? item.getAmount() : BigDecimal.ZERO)
							.cardAmt(checkPayCard ? item.getAmount() : BigDecimal.ZERO)
							.payCd("1021")
							.depositDt(!checkPayCard ? confirmDt : "")
							.depositAmt(!checkPayCard ? item.getAmount() : BigDecimal.ZERO)
							.build());
					
				} catch (Exception e) {
					e.printStackTrace();
				}
				lastTransactionKey = item.getTransactionKey();
			}
			
			// 요청 겟수랑 다르면 종료를 위해 last key 미전달
			if (responseData.size() != Integer.parseInt(params.get("limit").toString())) {
				lastTransactionKey = "";
			}
		}
		
		return lastTransactionKey;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	private void saveApiPgPrsnlPay(ApiPgPrsnlPayEntity apiPgPrsnlPayEntity) {
		this.hpointpayCommandMapper.insertApiPgPrsnlPay(apiPgPrsnlPayEntity);
		
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	private void saveTossTransaction(EzEcHpointpayEntity ezEcHpointpayEntity) {
		this.hpointpayCommandMapper.insertTossTransaction(ezEcHpointpayEntity);
	}

	/**
	 * API 요청
	 * @param params
	 * @param string
	 * @return
	 * @throws Exception 
	 */
	@SuppressWarnings("deprecation")
	private List<TossTransactionDto> request(Map<String, Object> params) throws Exception {
		List<Header> headerList = new ArrayList<>();
		headerList.add(new Header("Authorization", "Basic " + this.encodeBase64(DEV_PAY_SECRET_KEY+ ":")));
	
		ResponseEntity<String> result = RestClientUtil.requestApiGet("https://api.tosspayments.com/v1/transactions", 30000, headerList, params);
		
		if(result.getStatusCodeValue() != 200) {
			log.info("Remote Server Receive Failed(Internal Error Http Status Code:[" +result.getStatusCodeValue() + "])");
			throw new Exception("[5002]서버 수신오류(Server Internal Error)");	
		}
		return OrderJsonUtils.deserializeCamelArray(result.getBody(), TossTransactionDto.class);
	}

	private String encodeBase64(String value) {
		return Base64.encodeAsString(value.getBytes());
	}
	
	
	public String convertIso8601ToString(String date) {
		try {
			SimpleDateFormat inFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ", Locale.GERMANY);
			date = date.replaceAll("\\+0([0-9]){1}\\:00", "+0$100");
			SimpleDateFormat outFormat = new SimpleDateFormat("yyyyMMddHHmmss");
			return outFormat.format(inFormat.parse(date));
		} catch(Exception e) {
			return date;
		}
	}
	

}
