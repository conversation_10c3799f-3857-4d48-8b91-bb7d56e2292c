package com.ezwelesp.batch.hims.order.delivery.tasklet;

import com.ezwelesp.batch.hims.order.delivery.domain.DeliveryDelayDto;
import com.ezwelesp.batch.hims.order.delivery.service.DeliveryDelayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeliveryDueDelayTasklet implements Tasklet {
    private final DeliveryDelayService deliveryDelayService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        List<DeliveryDelayDto> targetList = deliveryDelayService.getDeliveryDueDelayTargetList();

        for (DeliveryDelayDto target : targetList) {
            try {
                deliveryDelayService.updateDeliveryDueDelayDays(target);
            } catch (Exception e) {
                log.error("DeliveryDueDelayTasklet Failed: {}", e.getMessage(), e);
            }
        }

        log.debug("DeliveryDueDelayTasklet finished");

        return RepeatStatus.FINISHED;
    }
}
