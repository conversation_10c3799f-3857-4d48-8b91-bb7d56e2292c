package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 숙박주문관리자취소상세(ez_or.or_ldg_ord_mgr_cncl_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrLdgOrdMgrCnclDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 숙박주문관리자취소상세순번(ldg_ord_mgr_cncl_dtl_seq) not null
     */
    private Long ldgOrdMgrCnclDtlSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 숙박주문지원차수(ldg_ord_afe_nos) not null
     */
    private Long ldgOrdAfeNos;

    /**
     * 숙박예약번호(ldg_rsv_no) not null
     */
    private String ldgRsvNo;

    /**
     * 요청파라미터내용(req_para_cntn)
     */
    private String reqParaCntn;

    /**
     * 취소여부(cncl_yn) not null
     */
    private String cnclYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
