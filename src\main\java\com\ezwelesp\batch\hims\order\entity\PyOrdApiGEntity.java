package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 주문API로그(ez_or.py_ord_api_g)
 */
@Jacksonized
@Getter
@SuperBuilder(toBuilder = true)
public class PyOrdApiGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문API로그순번(ord_api_log_seq) not null
     */
    private Long ordApiLogSeq;

    /**
     * 주문API종류코드(ord_api_knd_cd) not null
     */
    private String ordApiKndCd;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * PG가맹점번호(pg_frcs_no)
     */
    private String pgFrcsNo;

    /**
     * PG승인번호(pg_apv_no)
     */
    private String pgApvNo;

    /**
     * 결제수단코드(pymt_mns_cd)
     */
    private String pymtMnsCd;

    /**
     * PG종류코드(pg_knd_cd)
     */
    private String pgKndCd;

    /**
     * 주문토큰문자값(ord_tkn_cval)
     */
    private String ordTknCval;

    /**
     * API처리성공여부(api_prcs_succ_yn) not null
     */
    private String apiPrcsSuccYn;

    /**
     * API요청일시(api_req_dtm)
     */
    private String apiReqDtm;

    /**
     * API응답일시(api_resp_dtm)
     */
    private String apiRespDtm;

    /**
     * 결과메시지내용(rslt_msg_cntn)
     */
    private String rsltMsgCntn;

    /**
     * 요청파라미터내용J(req_para_cntnj)
     */
    private String reqParaCntnj;

    /**
     * 응답파라미터내용J(resp_para_cntnj)
     */
    private String respParaCntnj;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
