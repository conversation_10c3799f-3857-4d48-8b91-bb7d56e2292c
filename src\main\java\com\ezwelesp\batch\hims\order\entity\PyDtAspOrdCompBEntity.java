package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 일자별제휴사주문비교기본(ez_or.py_dt_asp_ord_comp_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyDtAspOrdCompBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 제휴사주문비교번호(asp_ord_comp_no) not null
     */
    private Long aspOrdCompNo;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 주문비교건수(ord_comp_cnt)
     */
    private Integer ordCompCnt;

    /**
     * 배치처리완료여부(bat_prcs_cmpt_yn) not null
     */
    private String batPrcsCmptYn;

    /**
     * 배치처리완료일시(bat_prcs_cmpt_dtm)
     */
    private String batPrcsCmptDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
