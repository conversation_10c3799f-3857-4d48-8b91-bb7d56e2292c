package com.ezwelesp.batch.hims.order.goodsflow.mapper.command;

import com.ezwelesp.batch.hims.order.entity.DlGoodsfDlvIntlRsltDEntity;
import com.ezwelesp.batch.hims.order.entity.external.GikimidetailEntity;
import com.ezwelesp.batch.hims.order.entity.external.GikimimastEntity;
import com.ezwelesp.batch.hims.order.entity.external.GikimiresultEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GoodsFlowTraceCommandMapper {
    void updateIntlChckCd(@Param("list") List<String> dlvIntlInntNoList, @Param("intlChckCd") String intlChckCd);
    void insertGikimiMaster(GikimimastEntity entity);
    void insertGikimiDetail(List<GikimidetailEntity> list);
    void mergeDlGoodsfDlvIntlRslt(DlGoodsfDlvIntlRsltDEntity entity);
    void updateGikimiResultStatus(GikimiresultEntity entity);

    void updateDeliveryFinishStatus(String dlvNo, String invcNo);
    void updateDeliveryResultCode(String dlvNo, String invcNo, String dlvIntCoTrmsRsltCd);

    void updateSendTraceRequestResult(List<GikimimastEntity> list);
    void mergeGikimiresult(GikimiresultEntity entity);

    void deleteGikimimast();
    void deleteGikimidetail();
    void deleteGikimiresult();
}

