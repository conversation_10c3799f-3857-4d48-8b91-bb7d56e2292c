package com.ezwelesp.batch.lgportal.entity;

import lombok.Data;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 상품코드(ez_pd.pd_gds_c)
 */
@Data
public class PdGdsCEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 상품코드(gds_cd) not null
     */
    private String gdsCd;

    /**
     * 상품명(gds_nm) not null
     */
    private String gdsNm;

    /**
     * 채널코드(ch_cd) not null
     */
    private String chCd;

    /**
     * 상품유형코드(gds_typ_cd) not null
     */
    private String gdsTypCd;

    /**
     * 상품유형상세코드(gds_typ_dtl_cd) not null
     */
    private String gdsTypDtlCd;

    /**
     * 과거상품코드(pst_gds_cd)
     */
    private String pstGdsCd;

    /**
     * 협력사상품코드(csp_gds_cd)
     */
    private String cspGdsCd;

    /**
     * 상품매입유형코드(gds_pchs_typ_cd) not null
     */
    private String gdsPchsTypCd;

    /**
     * 과세종류코드(taxn_knd_cd) not null
     */
    private String taxnKndCd;

    /**
     * 상품판매상태코드(gds_sell_st_cd) not null
     */
    private String gdsSellStCd;

    /**
     * 상품승인상태코드(gds_apv_st_cd)
     */
    private String gdsApvStCd;

    /**
     * 상품심의상태코드(gds_deli_st_cd)
     */
    private String gdsDeliStCd;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 상품모델명(gds_mdl_nm)
     */
    private String gdsMdlNm;

    /**
     * 브랜드코드(brnd_cd)
     */
    private String brndCd;

    /**
     * 제조회사명(mnfc_co_nm)
     */
    private String mnfcCoNm;

    /**
     * 서비스제공회사명(srv_offr_co_nm)
     */
    private String srvOffrCoNm;

    /**
     * 상품간편설명(gds_simp_desc)
     */
    private String gdsSimpDesc;

    /**
     * 관리표준카테고리코드(mng_std_ctgr_cd) not null
     */
    private String mngStdCtgrCd;

    /**
     * 판매시작일시(sell_strt_dtm)
     */
    private String sellStrtDtm;

    /**
     * 판매종료일시(sell_end_dtm)
     */
    private String sellEndDtm;

    /**
     * 원산지명(orgn_nm)
     */
    private String orgnNm;

    /**
     * 가격노출여부(prc_exps_yn) not null
     */
    private String prcExpsYn;

    /**
     * 판매기간노출여부(sell_term_exps_yn) not null
     */
    private String sellTermExpsYn;

    /**
     * 판매수량노출여부(sell_qty_exps_yn) not null
     */
    private String sellQtyExpsYn;

    /**
     * 상품검색노출여부(gds_srch_exps_yn) not null
     */
    private String gdsSrchExpsYn;

    /**
     * 상품검색어(gds_scwd)
     */
    private String gdsScwd;

    /**
     * 개인화적용대상여부(psnz_aply_obj_yn) not null
     */
    private String psnzAplyObjYn;

    /**
     * 고객신청형태상품여부(cst_apl_shap_gds_yn) not null
     */
    private String cstAplShapGdsYn;

    /**
     * 현금형태상품유형코드(cash_shap_gds_typ_cd) not null
     */
    private String cashShapGdsTypCd;

    /**
     * 정기배송상품여부(fxtrm_dlv_gds_yn) not null
     */
    private String fxtrmDlvGdsYn;

    /**
     * 최저가비교가격(lwpr_comp_prc)
     */
    private BigDecimal lwprCompPrc;

    /**
     * 최저가비교단독판매가격여부(lwpr_comp_ipd_sell_prc_yn)
     */
    private String lwprCompIpdSellPrcYn;

    /**
     * 상품카테고리명노출여부(gds_ctgr_nm_exps_yn) not null
     */
    private String gdsCtgrNmExpsYn;

    /**
     * 1회구매가능최대수량사용여부(t1_buy_poss_max_qty_use_yn)
     */
    private String t1BuyPossMaxQtyUseYn;

    /**
     * 1회구매가능최대수량(t1_buy_poss_max_qty)
     */
    private Integer t1BuyPossMaxQty;

    /**
     * 1인구매가능최대수량사용여부(psn1_buy_poss_max_qty_use_yn)
     */
    private String psn1BuyPossMaxQtyUseYn;

    /**
     * 1인구매가능최대수량(psn1_buy_poss_max_qty)
     */
    private Integer psn1BuyPossMaxQty;

    /**
     * 1인구매가능기간조건코드(psn1_buy_poss_term_cond_cd)
     */
    private String psn1BuyPossTermCondCd;

    /**
     * 1인구매가능조건시작일자(psn1_buy_poss_cond_strt_dt)
     */
    private String psn1BuyPossCondStrtDt;

    /**
     * 1인구매가능조건종료일자(psn1_buy_poss_cond_end_dt)
     */
    private String psn1BuyPossCondEndDt;

    /**
     * 다중배송가능여부(mti_dlv_poss_yn) not null
     */
    private String mtiDlvPossYn;

    /**
     * 재입고신청가능여부(rstk_apl_poss_yn) not null
     */
    private String rstkAplPossYn;

    /**
     * 선물하기사용여부(gvgft_use_yn) not null
     */
    private String gvgftUseYn;

    /**
     * 정기구독주문가능여부(sto_ord_poss_yn) not null
     */
    private String stoOrdPossYn;

    /**
     * 정기구독주문전용상품여부(sto_ord_only_gds_yn)
     */
    private String stoOrdOnlyGdsYn;

    /**
     * 정기구독약관번호(sto_tnc_no)
     */
    private Long stoTncNo;

    /**
     * 상품옵션사용여부(gds_opt_use_yn)
     */
    private String gdsOptUseYn;

    /**
     * 상품옵션유형코드(gds_opt_typ_cd)
     */
    private String gdsOptTypCd;

    /**
     * 상품선택옵션사용여부(gds_choc_opt_use_yn) not null
     */
    private String gdsChocOptUseYn;

    /**
     * 재고수량(stck_qty)
     */
    private Integer stckQty;

    /**
     * 판매수량(sell_qty)
     */
    private Integer sellQty;

    /**
     * 상품수불관리여부(gds_rap_mng_yn) not null
     */
    private String gdsRapMngYn;

    /**
     * 상품창고코드(gds_sth_cd)
     */
    private String gdsSthCd;

    /**
     * 상품권종코드(gds_bntp_cd)
     */
    private String gdsBntpCd;

    /**
     * 판매단위수량(sell_unit_qty)
     */
    private Integer sellUnitQty;

    /**
     * 안전재고관리여부(safe_stck_mng_yn)
     */
    private String safeStckMngYn;

    /**
     * 안전재고수량(safe_stck_qty)
     */
    private Integer safeStckQty;

    /**
     * 현금영수증발행여부(csrc_pblc_yn)
     */
    private String csrcPblcYn;

    /**
     * 해외배송가능여부(abrd_dlv_poss_yn)
     */
    private String abrdDlvPossYn;

    /**
     * 본인인증필요여부(self_crtf_need_yn)
     */
    private String selfCrtfNeedYn;

    /**
     * 크로스셀링상품여부(crssl_gds_yn)
     */
    private String crsslGdsYn;

    /**
     * 경도(lon)
     */
    private BigDecimal lon;

    /**
     * 위도(lat)
     */
    private BigDecimal lat;

    /**
     * 상품바코드번호(gds_bcd_no)
     */
    private String gdsBcdNo;

    /**
     * 상품수익률(gds_dfrt_rt)
     */
    private Double gdsDfrtRt;

    /**
     * 정상판매가격(nrml_sell_prc) not null
     */
    private BigDecimal nrmlSellPrc;

    /**
     * 실제판매가격(real_sell_prc) not null
     */
    private BigDecimal realSellPrc;

    /**
     * 상품매입가격(gds_pchs_prc) not null
     */
    private BigDecimal gdsPchsPrc;

    /**
     * 액면가격(fval_prc)
     */
    private BigDecimal fvalPrc;

    /**
     * 수수료금액(cms_amt)
     */
    private BigDecimal cmsAmt;

    /**
     * 최저가확인사용여부(lwpr_cnft_use_yn)
     */
    private String lwprCnftUseYn;

    /**
     * 최저가가격(lwpr_prc)
     */
    private BigDecimal lwprPrc;

    /**
     * 최저가기준일시(lwpr_bsic_dtm)
     */
    private String lwprBsicDtm;

    /**
     * 안전인증대상여부(safe_crtf_obj_yn)
     */
    private String safeCrtfObjYn;

    /**
     * 성인인증필요여부(adlt_crtf_need_yn) not null
     */
    private String adltCrtfNeedYn;

    /**
     * 합배송제외상품여부(cdlv_excld_gds_yn)
     */
    private String cdlvExcldGdsYn;

    /**
     * 동일포장가능상품수(same_pack_poss_gds_cnt)
     */
    private Integer samePackPossGdsCnt;

    /**
     * 배송정책순번(dlv_plcy_seq) not null
     */
    private Long dlvPlcySeq;

    /**
     * 배송템플릿순번(dlv_tmpl_seq) not null
     */
    private Long dlvTmplSeq;

    /**
     * 협력사출고위치번호(csp_obnd_loc_no)
     */
    private String cspObndLocNo;

    /**
     * 배송비용(dlv_exp)
     */
    private BigDecimal dlvExp;

    /**
     * 제주도추가배송비용(jeju_add_dlv_exp)
     */
    private BigDecimal jejuAddDlvExp;

    /**
     * 도서산간추가배송비용(ismt_add_dlv_exp)
     */
    private BigDecimal ismtAddDlvExp;

    /**
     * 반품상품회수대상여부(rtp_gds_wtdw_obj_yn)
     */
    private String rtpGdsWtdwObjYn;

    /**
     * 교환반품배송비용부담방법코드(exch_rtp_dlv_exp_budn_mthd_cd)
     */
    private String exchRtpDlvExpBudnMthdCd;

    /**
     * 배송비용결제방법코드(dlv_exp_pymt_mthd_cd) not null
     */
    private String dlvExpPymtMthdCd;

    /**
     * 조건부무료배송여부(cndl_nchg_dlv_yn)
     */
    private String cndlNchgDlvYn;

    /**
     * 조건부무료배송비용(cndl_nchg_dlv_exp)
     */
    private BigDecimal cndlNchgDlvExp;

    /**
     * 금요일배송가능여부(fri_dlv_poss_yn)
     */
    private String friDlvPossYn;

    /**
     * 교환반품묶음배송불가여부(exch_rtp_bndl_ndmt_yn)
     */
    private String exchRtpBndlNdmtYn;

    /**
     * 교환배송비용(exch_dlv_exp)
     */
    private BigDecimal exchDlvExp;

    /**
     * 반품배송비용(rtp_dlv_exp)
     */
    private BigDecimal rtpDlvExp;

    /**
     * 배송희망일사용여부(dlv_hope_dd_use_yn)
     */
    private String dlvHopeDdUseYn;

    /**
     * 배송희망일조건시작일수(dlv_hope_dd_cond_strt_dcnt)
     */
    private Integer dlvHopeDdCondStrtDcnt;

    /**
     * 배송희망일조건종료일수(dlv_hope_dd_cond_end_dcnt)
     */
    private Integer dlvHopeDdCondEndDcnt;

    /**
     * 배송지역그룹코드(dlv_area_grp_cd)
     */
    private String dlvAreaGrpCd;

    /**
     * 배송지역그룹설정코드(dlv_area_grp_stup_cd)
     */
    private String dlvAreaGrpStupCd;

    /**
     * 난수발행사용여부(rdno_pblc_use_yn) not null
     */
    private String rdnoPblcUseYn;

    /**
     * 취소환불가능여부(cncl_rfnd_poss_yn) not null
     */
    private String cnclRfndPossYn;

    /**
     * 상품문의전화번호(gds_inq_telno)
     */
    private String gdsInqTelno;

    /**
     * 낙전수입주체코드(lstinc_magn_cd) not null
     */
    private String lstincMagnCd;

    /**
     * E쿠폰가맹점번호(ecpn_frcs_no)
     */
    private String ecpnFrcsNo;

    /**
     * 인지세발행여부(sttx_pblc_yn)
     */
    private String sttxPblcYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
