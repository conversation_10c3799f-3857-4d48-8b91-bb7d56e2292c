package com.ezwelesp.batch.hims.order.goodsflow.domain.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 배송결과 수신 response (ReceiveTraceResult API)
 */
@Getter
@Setter
@ToString
public class ReceiveTraceResultResponse {
    private boolean success;
    private Object context;
    private Error error;
    private Data data;

    @Getter
    @Setter
    @ToString
    public static class Error {
        private int status;
        private String message;
        private String detailMessage;
    }

    @Getter
    @Setter
    @ToString
    public static class Data {
        private int totalItems;
        private List<Item> items;
    }

    @Getter
    @Setter
    @ToString
    public static class Item {
        private String transUniqueCode;
        private int seq;
        private String sectionCode;
        private String logisticsCode;
        private String invoiceNo;
        private String dlvStatType;
        private String procDateTime;
        private String taker;
        private String branchName;
        private String branchTel;
        private String employeeName;
        private String employeeTel;
        private String employeeMsg;
        private String exceptionCode;
        private String exceptionName;
        private String errorCode;
        private String errorName;
        private String defCode1;
        private String defCode2;
        private String createDateTime;
    }
}
