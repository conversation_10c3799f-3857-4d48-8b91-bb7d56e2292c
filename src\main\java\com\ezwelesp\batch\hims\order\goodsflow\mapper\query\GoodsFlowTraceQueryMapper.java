package com.ezwelesp.batch.hims.order.goodsflow.mapper.query;

import com.ezwelesp.batch.hims.order.entity.DlDlvBEntity;
import com.ezwelesp.batch.hims.order.entity.DlGoodsfDlvIntlBEntity;
import com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowDeliveryFinishDto;
import com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowSendTraceRequestDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface GoodsFlowTraceQueryMapper {

    long selectSequence(String seqName);
    List<DlGoodsfDlvIntlBEntity> selectDeliveryTraceTargetList();
    List<DlGoodsfDlvIntlBEntity> selectDeliveryTraceDetailByInntDlvNo(String goodsfInntDlvNo);
    List<GoodsFlowDeliveryFinishDto> selectDeliveryFinishList();

    List<DlGoodsfDlvIntlBEntity> selectInterlockBaseByGoodsfInntDlvNo(String goodsfInntDlvNo);
    DlDlvBEntity selectDeliveryBaseInfo(String dlvNo);

    List<GoodsFlowSendTraceRequestDto> selectSendTraceRequestTarget();
}


