package com.ezwelesp.batch.hims.order.pgconfirm.dongbaek.tasklet;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.order.pgconfirm.dongbaek.service.PgTransactionErrorService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class PgTransactionErrorTasklet implements Tasklet{
	
	private final PgTransactionErrorService pgTransactionErrorService;
	
	@Override	
	public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
		
		log.info("############## 동백몰 결제대사 START ##############" + contribution.getStepExecution().getStepName());
		this.pgTransactionErrorService.callBatch();
		log.info("############## 동백몰 결제대사 START ##############" + contribution.getStepExecution().getStepName());
		return RepeatStatus.FINISHED;
	}



}
