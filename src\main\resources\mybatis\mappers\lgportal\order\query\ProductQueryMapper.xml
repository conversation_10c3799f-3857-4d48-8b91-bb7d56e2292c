<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.lgportal.order.mapper.query.ProductQueryMapper">

    <select id="selectGoods">
        select pgc.gds_cd
             , pgc.gds_nm
             , pgc.ch_cd
             , pgc.gds_typ_cd
             , pgc.gds_typ_dtl_cd
             , pgc.taxn_knd_cd
             , pgc.gds_sell_st_cd
             , pgc.csp_cd
             , pgc.mng_std_ctgr_cd
             , pgc.sell_strt_dtm
             , pgc.sell_end_dtm
             , pgc.cst_apl_shap_gds_yn
             , pgc.fxtrm_dlv_gds_yn
             , pgc.t1_buy_poss_max_qty_use_yn
             , pgc.t1_buy_poss_max_qty
             , pgc.psn1_buy_poss_max_qty_use_yn
             , pgc.psn1_buy_poss_max_qty
             , pgc.psn1_buy_poss_term_cond_cd
             , pgc.psn1_buy_poss_cond_strt_dt
             , pgc.psn1_buy_poss_cond_end_dt
             , pgc.mti_dlv_poss_yn
             , pgc.gvgft_use_yn
             , pgc.sto_ord_poss_yn
             , pgc.sto_ord_only_gds_yn
             , pgc.sto_tnc_no
             , pgc.gds_opt_use_yn
             , pgc.gds_opt_typ_cd
             , pgc.gds_choc_opt_use_yn
             , coalesce(pgc.stck_qty, 0)      as stck_qty
             , coalesce(pgc.sell_qty, 0)      as sell_qty
             , coalesce(pgc.sell_unit_qty, 0) as sell_unit_qty
             , pgc.safe_stck_mng_yn
             , pgc.safe_stck_qty
             , pgc.abrd_dlv_poss_yn
             , pgc.self_crtf_need_yn
             , coalesce(pgc.nrml_sell_prc, 0) as nrml_sell_prc
             , coalesce(pgc.real_sell_prc, 0) as real_sell_prc
             , coalesce(pgc.gds_pchs_prc, 0)  as gds_pchs_prc
             , coalesce(pgc.fval_prc, 0)      as fval_prc
             , coalesce(pgc.cms_amt, 0)       as cms_amt
             , pgc.adlt_crtf_need_yn
             , pgc.cdlv_excld_gds_yn
             , pgc.same_pack_poss_gds_cnt
             , pgc.dlv_plcy_seq
             , pgc.dlv_tmpl_seq
             , pgc.csp_obnd_loc_no
             , pgc.dlv_exp
             , pgc.jeju_add_dlv_exp
             , pgc.ismt_add_dlv_exp
             , pgc.rtp_gds_wtdw_obj_yn
             , pgc.exch_rtp_dlv_exp_budn_mthd_cd
             , pgc.dlv_exp_pymt_mthd_cd
             , pgc.cndl_nchg_dlv_yn
             , pgc.cndl_nchg_dlv_exp
             , pgc.fri_dlv_poss_yn
             , pgc.exch_rtp_bndl_ndmt_yn
             , pgc.exch_dlv_exp
             , pgc.rtp_dlv_exp
             , pgc.dlv_hope_dd_use_yn
             , pgc.dlv_hope_dd_cond_strt_dcnt
             , pgc.dlv_hope_dd_cond_end_dcnt
             , pgc.dlv_area_grp_cd
             , pgc.dlv_area_grp_stup_cd
             , pgc.rdno_pblc_use_yn
             , pgc.cncl_rfnd_poss_yn
             , pgc.sttx_pblc_yn
        from ez_pd.pd_gds_c pgc
        where pgc.gds_cd = #{gdsCd}
    </select>

     <select id="selectGoodsOptionBase">
          select count(gds_cd)
          from ez_pd.pd_gds_opt_b opt
          where opt.gds_cd = #{gdsCd}
            and opt.mndr_choc_yn = 'Y'
            and opt.use_yn = 'Y'
     </select>

    <select id="selectGoodsOption" parameterType="map">
        select gds_cd
             , opt_gds_comb_seq
             , csp_opt_gds_no
             , opt_add_prc
             , safe_stck_mng_yn
             , coalesce(stck_qty, 0)      as stck_qty
             , coalesce(safe_stck_qty, 0) as safe_stck_qty
             , coalesce(sell_qty, 0)      as sell_qty
             , opt_pchs_prc
             , sttx_pblc_yn
             , use_yn
        from ez_pd.pd_gds_opt_comb_b pgocb
        where pgocb.gds_cd = #{gdsCd}
          and pgocb.opt_gds_comb_seq = #{optGdsCombSeq}
    </select>

</mapper>
