package com.ezwelesp.batch.hims.product.domain.dto;

import com.ezwelesp.batch.lgportal.product.domain.enums.lgPortalSendKey;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("UpdateProductStatusDto")
@Getter
@SuperBuilder
public class LgPortalProductSaleEndDto {

    @Builder.Default()
    private Integer providerNo = 419;
    private String certKey;
    private String productNo;
    private lgPortalSendKey sendKey;
    private String sendYn;
    private String regDt;
    private String regId;
    private String deliveryNo;

    private String targetGoodsCd;
    private Integer goodsCd;

}
