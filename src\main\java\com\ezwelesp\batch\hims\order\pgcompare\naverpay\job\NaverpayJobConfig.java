package com.ezwelesp.batch.hims.order.pgcompare.naverpay.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.order.pgcompare.naverpay.tasklet.NaverpayTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 네이버페이 결제대사
 */

@Slf4j
@Configuration
@RequiredArgsConstructor
public class NaverpayJobConfig {
	private final CommonJobListener commonJobListener;
	private final NaverpayTasklet naverpayTasklet;
	
	/**
	 * 네이버페이 결제대사 API 호출 및 데이터 적재
	 * @param jobRepository
	 * @param step
	 * @return
	 */
	@Bean("BA_HIOR00007")
	public Job BA_HIOR00007(JobRepository jobRepository,  @Qualifier("BA_HIOR00007_STEP") Step step) {
		return new JobBuilder("BA_HIOR00007", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(step)
                .build();
	}
	
	
	
	@Bean("BA_HIOR00007_STEP")
	public Step BA_HIOR00007_STEP(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
		  return new StepBuilder("BA_HIOR00007_STEP", jobRepository)
	                .allowStartIfComplete(true)
	                .tasklet(naverpayTasklet, transactionManager)
	                .build();
		
	}
	
	/**
	 * 네이버페이의 정산 대사 내역을 API 호출을 통해 전달받아 수정
	 * @param jobRepository
	 * @param step
	 * @return
	 */
	@Bean("BA_HIOR00006")
	public Job BA_HIOR00006(JobRepository jobRepository,  @Qualifier("BA_HIOR00006_STEP") Step step) {
		return new JobBuilder("BA_HIOR00006", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(step)
                .build();
	}
	
	@Bean("BA_HIOR00006_STEP")
	public Step BA_HIOR00006_STEP(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
		  return new StepBuilder("BA_HIOR00006_STEP", jobRepository)
	                .allowStartIfComplete(true)
	                .tasklet(naverpayTasklet, transactionManager)
	                .build();
		
	}
	
}
