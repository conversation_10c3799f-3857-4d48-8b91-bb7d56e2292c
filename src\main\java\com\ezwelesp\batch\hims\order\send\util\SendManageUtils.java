package com.ezwelesp.batch.hims.order.send.util;

import com.ezwelesp.framework.apiresponse.BaseEspHttpStatus;
import com.ezwelesp.framework.exception.ServiceException;
import com.ezwelesp.framework.utils.StringUtils;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.oned.Code128Writer;
import com.google.zxing.oned.Code39Writer;
import com.google.zxing.oned.EAN13Writer;
import com.google.zxing.oned.ITFWriter;
import com.google.zxing.qrcode.QRCodeWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.EnumMap;
import java.util.Map;

import static com.ezwelesp.batch.hims.order.config.EcpnConstants.*;

@Slf4j
@Component
public class SendManageUtils {
    private static final int BARCODE_WIDTH = 464;
    private static final int BARCODE_HEIGHT = 80;
    private static final int QRCODE_WIDTH = 150;
    private static final int QRCODE_HEIGHT = 150;

    private static final int MERGED_IMAGE_WIDTH = 480;
    private static final int MERGED_IMAGE_BARCODE_HEIGHT = 640;
    private static final int MERGED_IMAGE_QRCODE_HEIGHT = 735;

    // 해상도 높임에 따른 이미지 크기값 추가
    private static final int MMS_IMAGE_WIDTH = 480;
    private static final int MMS_IMAGE_HEIGHT = 480;

    /**
     * 첨부파일 생성 (상품이미지, 바코드이미지)
     */
    public Resource getImageResource(String intgGdsRdnoNo, String gdsImg, String bcdExpsYn, String bcdTypCd) {
        if (StringUtils.isBlank(gdsImg) && !Y_STR.equals(bcdExpsYn)) {
            return null;
        }

        BufferedImage goodsImage = null;
        BufferedImage barcodeImage = null;

        // 상품이미지
        if (StringUtils.isNotBlank(gdsImg)) {
            goodsImage = getGoodsImage(gdsImg);
        }

        // 바코드이미지
        if (Y_STR.equals(bcdExpsYn)) {
            barcodeImage = makeBarcode(bcdTypCd, intgGdsRdnoNo);
        }

        // 상품이미지 + 바코드이미지
        if (goodsImage != null && barcodeImage != null) {
            return getByteArrayResource(merge(goodsImage, barcodeImage, intgGdsRdnoNo, bcdTypCd));
        }

        return getByteArrayResource(goodsImage == null ? barcodeImage : goodsImage);
    }

    /**
     * 상품 이미지
     */
    private BufferedImage getGoodsImage(String gdsImg) {
        // TODO 상품이미지 관리방법에 따른 경로 처리방법 확인필요
        BufferedImage goodsImage = null;
        try {
            URL url = new URL("http://img.ezwelfare.net/ezwel-image/ecoupon/files/goods/G_30000037_1499412701252_%EC%95%84%EB%AA%A8%EB%A5%B4%EC%97%AC%EC%84%B1_500.png");
            goodsImage = ImageIO.read(url);
        } catch (Exception e) {
            log.error("-------> {}", e.getMessage());
        }

        return goodsImage;
    }


    /**
     * 바코드 생성
     */
    private BufferedImage makeBarcode(String bcdTypCd, String intgGdsRdnoNo) {
        Map<EncodeHintType, Object> hints = new EnumMap<>(EncodeHintType.class);
        hints.put(EncodeHintType.MARGIN, 1);

        BufferedImage bufferedImage = null;
        try {
            BitMatrix bitMatrix;

            if (BcdTypCdEnum.CODE_128.getCode().equals(bcdTypCd)) {
                bitMatrix = new Code128Writer().encode(intgGdsRdnoNo, BarcodeFormat.CODE_128, BARCODE_WIDTH, BARCODE_HEIGHT, hints);
            } else if (BcdTypCdEnum.CODE_39.getCode().equals(bcdTypCd)) {
                bitMatrix = new Code39Writer().encode(intgGdsRdnoNo, BarcodeFormat.CODE_39, BARCODE_WIDTH, BARCODE_HEIGHT, hints);
            } else if (BcdTypCdEnum.EAN_13.getCode().equals(bcdTypCd)) {
                bitMatrix = new EAN13Writer().encode(intgGdsRdnoNo, BarcodeFormat.EAN_13, BARCODE_WIDTH, BARCODE_HEIGHT, hints);
            } else if (BcdTypCdEnum.ITF.getCode().equals(bcdTypCd)) {
                bitMatrix = new ITFWriter().encode(intgGdsRdnoNo, BarcodeFormat.ITF, BARCODE_WIDTH, BARCODE_HEIGHT, hints);
            } else if (BcdTypCdEnum.QRCODE.getCode().equals(bcdTypCd)) {
                bitMatrix = new QRCodeWriter().encode(intgGdsRdnoNo, BarcodeFormat.QR_CODE, QRCODE_WIDTH, QRCODE_HEIGHT, hints);
            } else {
                throw new ServiceException(BaseEspHttpStatus.valueOf("바코드 유형코드가 존재하지 않습니다."));
            }

            if (bitMatrix != null) {
                bufferedImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
            }
        } catch (Exception e) {
            log.error("바코드 생성 실패 type: {}, no: {}, msg: {}", bcdTypCd, intgGdsRdnoNo, e.getMessage());
            throw new ServiceException(BaseEspHttpStatus.valueOf("바코드 생성 실패"));
        }

        return bufferedImage;
    }

    /**
     * 상품이미지 + 바코드이미지 병합
     */
    private BufferedImage merge(BufferedImage goodsImage, BufferedImage barcodeImage, String intgGdsRdnoNo, String bcdTypCd) {
        int width = MERGED_IMAGE_WIDTH;
        int height = BcdTypCdEnum.QRCODE.getCode().equals(bcdTypCd) ? MERGED_IMAGE_QRCODE_HEIGHT : MERGED_IMAGE_BARCODE_HEIGHT;
        int space = 10;

        if (goodsImage.getWidth() != MMS_IMAGE_WIDTH) {
            goodsImage = createImgResized(goodsImage,  MMS_IMAGE_WIDTH,  MMS_IMAGE_HEIGHT, true);
        }

        BufferedImage mergedImage = new BufferedImage(width, height, BufferedImage.SCALE_SMOOTH);
        Graphics2D g2d = mergedImage.createGraphics();
        Font currentFont = g2d.getFont();
        Font newFont = currentFont.deriveFont(currentFont.getSize() * 2F);

        g2d.setFont(newFont); //이미지 사이즈 확대에 따른 폰트사이즈 변경

        FontMetrics fm = g2d.getFontMetrics();
        Rectangle2D r = fm.getStringBounds(intgGdsRdnoNo, g2d);

        int goodsImageX = (width - goodsImage.getWidth()) / 2;
        int goodsImageY = space;
        int barcodeImageX = (width - barcodeImage.getWidth()) / 2;
        int barcodeImageY = goodsImageY + goodsImage.getHeight() + space;
        int barcodeStringX = (width - (int) r.getWidth()) / 2;
        int barcodeStringY = barcodeImageY + barcodeImage.getHeight() + fm.getAscent();

        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, mergedImage.getWidth(), mergedImage.getHeight());
        g2d.drawImage(goodsImage, goodsImageX, goodsImageY, null);
        g2d.drawImage(barcodeImage, barcodeImageX, barcodeImageY, null);
        g2d.setColor(Color.BLACK);
        g2d.drawString(intgGdsRdnoNo, barcodeStringX, barcodeStringY);

        return mergedImage;
    }

    private BufferedImage createImgResized(Image orImg, int width, int height, boolean alpha){
        int imgType = alpha ? BufferedImage.TYPE_INT_RGB : BufferedImage.TYPE_INT_ARGB;
        BufferedImage scale = new BufferedImage(width, height, imgType);
        Graphics2D g = scale.createGraphics();
        if (alpha) {
            g.setComposite(AlphaComposite.Src);
        }
        g.drawImage(orImg, 0, 0, width, height, null);
        g.dispose();
        return scale;
    }

    private ByteArrayResource getByteArrayResource(BufferedImage image) {
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            ImageIO.write(image, "jpg", output);

            // 로컬 이미지 테스트
            // ImageIO.write(image, "jpg", new File(String.format("C:\\ezwel\\temp\\%s.jpg", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")))));

            return new ByteArrayResource(output.toByteArray()) {
                @Override
                public String getFilename() { //이 부분 없으면 안됨
                    return "image.jpg";
                }
            };
        } catch (IOException e) {
            throw new ServiceException(BaseEspHttpStatus.valueOf("이미지 파일 ByteArrayResource 변환 실패"));
        }
    }
}
