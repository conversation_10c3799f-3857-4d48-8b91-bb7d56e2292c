package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 가족회원기본(ez_ct.ct_fam_mem_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class CtFamMemBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 가족회원번호(fam_mem_no) not null
     */
    private String famMemNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 가족회원ID(fam_mem_id) not null
     */
    private String famMemId;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 가족회원명(fam_mem_nm) not null
     */
    private String famMemNm;

    /**
     * 가족회원관계코드(fam_mem_rel_cd)
     */
    private String famMemRelCd;

    /**
     * 성별코드(gndr_cd)
     */
    private String gndrCd;

    /**
     * 본인인증생년월일일자(self_crtf_bymd_dt)
     */
    private String selfCrtfBymdDt;

    /**
     * 음력양력구분코드(lnar_slar_div_cd)
     */
    private String lnarSlarDivCd;

    /**
     * 가족회원신청상태코드(fam_mem_apl_st_cd)
     */
    private String famMemAplStCd;

    /**
     * 생년월일성별(bymdg)
     */
    private String bymdg;

    /**
     * 로그인가능여부(lgin_poss_yn) not null
     */
    private String lginPossYn;

    /**
     * 최초로그인여부(frst_lgin_yn) not null
     */
    private String frstLginYn;

    /**
     * 임시비밀번호여부(tmp_pwd_yn) not null
     */
    private String tmpPwdYn;

    /**
     * 가족회원포인트사용여부(fam_mem_pnt_use_yn) not null
     */
    private String famMemPntUseYn;

    /**
     * 선택적복지포인트사용대상여부(wfp_use_obj_yn) not null
     */
    private String wfpUseObjYn;

    /**
     * 특별포인트사용대상여부(spp_use_obj_yn) not null
     */
    private String sppUseObjYn;

    /**
     * 이메일수신동의여부(eml_rcv_agr_yn) not null
     */
    private String emlRcvAgrYn;

    /**
     * 문자메시지수신동의여부(tmsg_rcv_agr_yn) not null
     */
    private String tmsgRcvAgrYn;

    /**
     * 본인인증종류코드(self_crtf_knd_cd)
     */
    private String selfCrtfKndCd;

    /**
     * 본인인증예외허용일자(self_crtf_excp_allw_dt)
     */
    private String selfCrtfExcpAllwDt;

    /**
     * 본인인증일시(self_crtf_dtm)
     */
    private String selfCrtfDtm;

    /**
     * 가입일시(join_dtm)
     */
    private String joinDtm;

    /**
     * 승인일시(apv_dtm)
     */
    private String apvDtm;

    /**
     * 탈퇴유형코드(drot_typ_cd)
     */
    private String drotTypCd;

    /**
     * 탈퇴일시(drot_dtm)
     */
    private String drotDtm;

    /**
     * 사용자암호화CI(usr_enc_ci)
     */
    private String usrEncCi;

    /**
     * 사용자암호화DI(usr_enc_di)
     */
    private String usrEncDi;

    /**
     * 사용자암호화비밀번호(usr_enc_pwd) not null
     */
    private String usrEncPwd;

    /**
     * 비밀번호최종변경일시(pwd_last_chg_dtm)
     */
    private String pwdLastChgDtm;

    /**
     * 로그인실패제한수(lgin_fail_limt_cnt)
     */
    private Integer lginFailLimtCnt;

    /**
     * 회원가입경로코드(mem_join_path_cd)
     */
    private String memJoinPathCd;

    /**
     * 해외배송대행약관동의여부(abrd_dlv_actg_tnc_agr_yn) not null
     */
    private String abrdDlvActgTncAgrYn;

    /**
     * 개인정보수집활용동의여부(accpi_yn) not null
     */
    private String accpiYn;

    /**
     * 만14세미만여부(age14_belo_yn) not null
     */
    private String age14BeloYn;

    /**
     * 로그인성공일시(lgin_succ_dtm)
     */
    private String lginSuccDtm;

    /**
     * 로그인성공수(lgin_succ_cnt)
     */
    private Integer lginSuccCnt;

    /**
     * 로그인실패일시(lgin_fail_dtm)
     */
    private String lginFailDtm;

    /**
     * 로그인실패수(lgin_fail_cnt)
     */
    private Integer lginFailCnt;

    /**
     * 외국인여부(frnr_yn) not null
     */
    private String frnrYn;

    /**
     * 관리자메모(mgr_memo)
     */
    private String mgrMemo;

    /**
     * 최초등록일시(frst_reg_dtm)
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

    /**
     * 생년월일일자(bymd_dt)
     */
    private String bymdDt;

    /**
     * 테스트사용자여부(test_usr_yn)
     */
    private String testUsrYn;

}
