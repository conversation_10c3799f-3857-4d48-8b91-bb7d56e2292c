<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.receipt.mapper.query.CashReceiptQueryMapper">

    <select id="selectOrderCashReceiptTarget" resultType="com.ezwelesp.batch.hims.order.receipt.domain.OrderClaimDto" flushCache="true">
        select
            a.ord_no
        from
            ez_or.or_ord_b a
        where not exists (select 1 from ez_or.py_csrc_b where clm_no = '0' and ord_no = a.ord_no)
           and exists (select 1 from ez_or.or_ord_gds_d where ord_no = a.ord_no)
           and a.ord_dtm <![CDATA[>]]> '20250510000000'
        order by a.ord_no asc
        limit 1000
    </select>

    <select id="selectClaimCashReceiptTarget" resultType="com.ezwelesp.batch.hims.order.receipt.domain.OrderClaimDto" flushCache="true">
        with v_result as (select a.ord_no,
                                 b.clm_no,
                                 case
                                     when b.clm_st_cd = 'CMPT' then (select clm_no
                                                                     from ez_or.cl_clm_b
                                                                     where ord_no = b.ord_no
                                                                       and clm_no != b.clm_no and clm_st_cd = 'CMPT'
                                     and (clm_cmpt_dtm <![CDATA[<]]> b.clm_cmpt_dtm or (clm_cmpt_dtm = b.clm_cmpt_dtm and clm_no <![CDATA[<]]> b.clm_no )) order by clm_cmpt_dtm asc limit 1
                              ) else null end as prev_clm_no /* 추후 완료일자로 변경해야함 현재 클레임이 완료일시 이전 완료 클레임 번호 */
            from
                ez_or.or_ord_b a join ez_or.cl_clm_b b on a.ord_no = b.ord_no
            where
                b.clm_cmpt_dtm is not null /* 추후 완료일자로 변경해야함 */
              and b.clm_st_cd = 'CMPT'
              and not exists (select 1 from ez_or.py_csrc_b where clm_no = b.clm_no and ord_no = b.ord_no)
              and a.ord_dtm <![CDATA[>]]> '20250510000000'
            order by b.clm_cmpt_dtm asc
            limit 1000
        )
        select
            *
        from v_result a
        where not exists (select 1 from ez_or.py_csrc_b where clm_no = a.prev_clm_no and ord_no = a.ord_no and csrc_pblc_cncl_dt is not null)
    </select>



    <resultMap id="OrderInfoResultMap" type="com.ezwelesp.batch.hims.order.receipt.domain.OrderInfoDto">
        <id column="ord_no" jdbcType="VARCHAR" property="ordNo"/>
        <id column="clm_no" jdbcType="VARCHAR" property="clmNo"/>
        <result column="prev_clm_no" jdbcType="VARCHAR" property="prevClmNo"/>
        <result column="prcs_nos" jdbcType="VARCHAR" property="prcsNos"/>
        <result column="ord" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.OrOrdBEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectSnakeTypeHandler"
                property="ord"/>
        <result column="cpnList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.OrOrdCpnDEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="cpnList"/>
        <result column="cpnGdsList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.OrOrdCpnGdsDEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="cpnGdsList"/>
        <result column="dlvExpList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.receipt.domain.DeliveryExpenseDto"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="dlvExpList"/>
        <result column="dlvGdsList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.DlDlvGdsDEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="dlvGdsList"/>
        <result column="ordPayList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.PyPymtBEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="ordPayList"/>
        <result column="ordGdsList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.receipt.domain.OrdGdsDto"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="ordGdsList"/>
        <result column="exOrdGdsList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.receipt.domain.OrdGdsDto"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="exOrdGdsList"/>
        <result column="clm" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.ClClmBEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectSnakeTypeHandler"
                property="clm"/>
        <result column="clmGdsList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.ClClmGdsDEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="clmGdsList"/>
        <result column="clmCpnList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.OrOrdCpnDEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="clmCpnList"/>
        <result column="clmCpnGdsList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.OrOrdCpnGdsDEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="clmCpnGdsList"/>
        <result column="clmDlvExpList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.receipt.domain.DeliveryExpenseDto"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="clmDlvExpList"/>
        <result column="clmPayList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.PyPymtBEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="clmPayList"/>
        <result column="clmDlvGdsList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.entity.DlDlvGdsDEntity"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="clmDlvGdsList"/>
    </resultMap>

    <select id="selectOrderInfo" resultMap="OrderInfoResultMap" flushCache="true">
        with v_result as (
            select
                ord_no
                 ,'' as clm_no /* 정렬문제로 null대신 */
                 ,'' as prev_clm_no
                 ,'' as clm_cmpt_dtm
            from
                ez_or.or_ord_b a
            where a.ord_no = #{ordNo}
            union all
            select
                a.ord_no, b.clm_no,
                case when b.clm_st_cd = 'CMPT'
                    then (select clm_no
                        from ez_or.cl_clm_b
                        where ord_no = b.ord_no
                            and clm_no != b.clm_no and clm_st_cd = 'CMPT'
                            and (clm_cmpt_dtm <![CDATA[<]]> b.clm_cmpt_dtm or (clm_cmpt_dtm = b.clm_cmpt_dtm and clm_no <![CDATA[<]]> b.clm_no )) order by clm_cmpt_dtm asc limit 1
                          )  else null end as prev_clm_no /* 추후 완료일자로 변경해야함 현재 클레임이 완료일시 이전 완료 클레임 번호 */
                ,b.clm_cmpt_dtm
            from
                ez_or.or_ord_b a join ez_or.cl_clm_b b on a.ord_no = b.ord_no
            where a.ord_no = #{ordNo}
        and b.clm_st_cd = 'CMPT'
        and b.clm_cmpt_dtm is not null /* 추후 완료일자로 변경해야함 */
        )
        select
            vb.ord_no
             ,vb.clm_no
             ,vb.prev_clm_no
             ,oob.ordJson as ord
             ,cpn.cpnJson as cpnList
             ,cpnGds.cpnGdsJson as cpnGdsList
             ,dlvExp.dlvExpjson as dlvExpList
             ,dlvGds.dlvGdsJson AS dlvGdsList
             ,cash.cashPayJson as ordPayList
             ,oogd.ordGdsJson as ordGdsList
             ,exoogd.ordGdsJson as exOrdGdsList
             ,ccb.clmJson as clm
             ,ccgd.clmGdsJson as clmGdsList
             ,clmCpn.clmCpnJson as clmCpnList
             ,clmCpnGds.clmCpnGdsJson as clmCpnGdsList
             ,clmCash.clmCashPayJson as clmPayList
             ,clmExpdlv.clmExpDlvJson as clmDlvExpList
             ,clmDlvGds.clmDlvGdsJson AS clmDLvGdsList
        from v_result vb
        left join lateral (
            select a.ord_no , to_json(a) as ordjson from ez_or.or_ord_b a where a.ord_no = vb.ord_no group by a.ord_no
        ) oob on oob.ord_no = vb.ord_no
        left join lateral (
            select a.ord_no , json_agg(a) as cpnJson from ez_or.or_ord_cpn_d a where a.ord_no = vb.ord_no and a.clm_no is null group by a.ord_no
        ) cpn on cpn.ord_no = vb.ord_no
        left join lateral (
            select
                a.ord_no
                 ,json_agg(a) as cpnGdsJson
            from
                ez_or.or_ord_cpn_gds_d a
                    join ez_or.or_ord_cpn_d b on a.ord_cpn_no = b.ord_cpn_no
            where b.clm_no is null
            group by a.ord_no
        ) cpnGds on cpnGds.ord_no = vb.ord_no
        left join lateral (
            select
                z.ord_no
                 ,json_agg(z) as dlvExpjson
            from (
                 select
                     b.csp_cd
                      ,a.*
                 from
                     ez_or.dl_dlv_exp_b a
                         join ez_or.dl_dlv_b b on a.dlv_exp_no = b.dlv_exp_no
                 where b.ord_no = vb.ord_no and b.clm_no is null
             ) z group by z.ord_no
        ) dlvExp on dlvExp.ord_no = vb.ord_no
        left join lateral (
            select
                a.ord_no
                 ,json_agg(a) as dlvGdsJson
            from
                ez_or.dl_dlv_gds_d a
                    JOIN ez_or.dl_dlv_b b ON a.dlv_no = b.dlv_no and b.ord_no = vb.ord_no and b.clm_no IS null
            group by a.ord_no
        ) dlvGds on dlvGds.ord_no = vb.ord_no
        left join lateral (
            select a.ord_no , json_agg(a) as cashPayJson from (
                                                                  select
                                                                      a.pymt_no
                                                                       ,a.orgl_pymt_no
                                                                       ,a.ord_no
                                                                       ,a.clm_no
                                                                       ,a.pg_frcs_no
                                                                       ,a.pg_apv_no
                                                                       ,a.dlv_exp_pymt_yn
                                                                       ,a.pymt_div_cd
                                                                       ,a.pymt_nos
                                                                       ,a.pymt_amt
                                                                       ,a.pymt_st_cd
                                                                       ,a.pymt_mns_cd
                                                                       ,a.pymt_dtm
                                                                       ,a.pymt_cncl_rfnd_dtm
                                                                       ,a.wsp_div_cd
                                                                       ,a.csrc_pblc_obj_amt
                                                                  from ez_or.py_pymt_b a where a.ord_no = vb.ord_no and a.clm_no is null
                                                              ) a
            group by a.ord_no
        ) cash on cash.ord_no = vb.ord_no
        left join lateral (
            select a.ord_no , json_agg(a order by a.ord_gds_seq asc) as ordGdsJson from ez_or.or_ord_gds_d a where a.ord_no = vb.ord_no and a.orgl_ord_gds_seq is null  group by a.ord_no
        ) oogd on oogd.ord_no = vb.ord_no
        left join lateral (
            select b.ord_no , json_agg(a order by a.ord_gds_seq asc) as ordGdsJson
            from ez_or.or_ord_gds_d a
                     join ez_or.cl_clm_gds_d b on a.ord_no = b.ord_no and a.ord_gds_seq = b.exch_ord_gds_seq
            where b.ord_no = vb.ord_no and b.clm_no = vb.clm_no and a.orgl_ord_gds_seq is not null
            group by b.ord_no,b.clm_no
       ) exoogd on exoogd.ord_no = vb.ord_no and exoogd.ord_no = vb.ord_no
       left join lateral (
            select a.ord_no , a.clm_no, to_json(a) as clmJson from ez_or.cl_clm_b a where a.ord_no = vb.ord_no  group by a.ord_no, a.clm_no
       ) ccb on ccb.ord_no = vb.ord_no and ccb.clm_no = vb.clm_no
       left join lateral (
            select a.ord_no , a.clm_no, json_agg(a) as clmGdsJson from ez_or.cl_clm_gds_d a where a.ord_no = vb.ord_no  group by a.ord_no, a.clm_no
       ) ccgd on ccgd.ord_no = vb.ord_no and ccgd.clm_no = vb.clm_no
       left join lateral (
            select a.ord_no, a.clm_no , json_agg(a) as clmCpnJson from ez_or.or_ord_cpn_d a where a.ord_no = vb.ord_no and a.clm_no = vb.clm_no group by a.ord_no,a.clm_no
       ) clmCpn on clmCpn.ord_no = vb.ord_no and clmCpn.clm_no = vb.clm_no
       left join lateral (
            select
                a.ord_no
                 ,b.clm_no
                 ,json_agg(a) as clmCpnGdsJson
            from
                ez_or.or_ord_cpn_gds_d a
                    join ez_or.or_ord_cpn_d b on a.ord_cpn_no = b.ord_cpn_no
            where b.clm_no = vb.clm_no
            group by a.ord_no,b.clm_no
       ) clmCpnGds on clmCpnGds.ord_no = vb.ord_no and clmCpnGds.clm_no = vb.clm_no
       left join lateral (
            select a.ord_no , a.clm_no, json_agg(a) as clmCashPayJson from (
                                                                               select
                                                                                   a.pymt_no
                                                                                    ,a.orgl_pymt_no
                                                                                    ,a.ord_no
                                                                                    ,a.clm_no
                                                                                    ,a.pg_frcs_no
                                                                                    ,a.pg_apv_no
                                                                                    ,a.dlv_exp_pymt_yn
                                                                                    ,a.pymt_div_cd
                                                                                    ,a.pymt_nos
                                                                                    ,a.pymt_amt
                                                                                    ,a.pymt_st_cd
                                                                                    ,a.pymt_mns_cd
                                                                                    ,a.pymt_dtm
                                                                                    ,a.pymt_cncl_rfnd_dtm
                                                                                    ,a.wsp_div_cd
                                                                                    ,a.csrc_pblc_obj_amt
                                                                               from ez_or.py_pymt_b a where a.ord_no = vb.ord_no and a.clm_no = vb.clm_no
                                                                           ) a
            group by a.ord_no,a.clm_no
       ) clmCash on clmCash.ord_no = vb.ord_no and clmCash.clm_no = vb.clm_no
       left join lateral (
            select
                z.ord_no
                 ,z.clm_no
                 ,json_agg(z) as clmExpDlvJson
            from (
                 select
                     b.csp_cd
                      ,b.clm_no
                      ,a.*
                 from
                     ez_or.dl_dlv_exp_b a
                         join ez_or.dl_dlv_b b on a.dlv_exp_no = b.dlv_exp_no
                 where b.ord_no = vb.ord_no and b.clm_no = vb.clm_no
             ) z group by z.ord_no,z.clm_no
       ) clmExpDlv on clmExpDlv.ord_no = vb.ord_no and clmExpDlv.clm_no = vb.clm_no
       left join lateral (
            select
                a.ord_no
                 ,vb.clm_no as clm_no
                 ,json_agg(a) as clmDlvGdsJson
            from
                ez_or.dl_dlv_gds_d a
                    JOIN ez_or.dl_dlv_b b ON a.dlv_no = b.dlv_no and b.ord_no = vb.ord_no and b.clm_no = vb.clm_no
            group by a.ord_no,vb.clm_no
       ) clmDlvGds on clmDlvGds.ord_no = vb.ord_no and clmDlvGds.clm_no = vb.clm_no
       order by vb.clm_cmpt_dtm asc,vb.clm_no asc
    </select>


    <select id="selectCountCashReceipt" resultType="int" flushCache="true">
        select
        count(1)
        from ez_or.py_csrc_b
        where  ord_no = #{ordNo}
        <choose>
            <when test="clmNo != null and clmNo != ''">
                and clm_no = #{clmNo}
            </when>
            <otherwise>
                and clm_no = '0'
            </otherwise>
        </choose>
    </select>

    <select id="selectCashReceiptPublicationGatherCount" resultType="int">
        select
            count(1)
        from
        ez_or.py_csrc_b
        where
        1 = 1
        <choose>
            <when test="publicationDate == 'ALL'">
                and 1 = 1
            </when>
            <when test="publicationDate != null and publicationDate != ''">
                and (csrc_pblc_obj_dt = #{publicationDate} and csrc_pg_apv_no is null and csrc_apv_no is null) or (csrc_pblc_cncl_dt = #{publicationDate} and csrc_pg_apv_no is not null and cncl_csrc_pg_apv_no is null)
            </when>
            <otherwise>
                and 1 = 0
            </otherwise>
        </choose>
    </select>

    <select id="selectCashReceiptPublicationGather" resultType="com.ezwelesp.batch.hims.order.receipt.domain.CashPublicationGatherDto">
        select
            ord_no
            ,ord_gds_seq
            ,csrc_hndw_prcs_nos
            ,clm_no
            ,asp_ord_no
            ,all_pymt_amt
            ,csrc_pblc_obj_dt
            ,csrc_pblc_usg_cd
            ,enc_csrc_pblc_no
            ,pg_frcs_no
            ,csrc_pblc_typ_cd
            ,user_key
            ,ordr_nm
            ,ordr_eml_adr
            ,ordr_mbl_telno
            ,csrc_pg_apv_no
            ,csrc_apv_no
            ,csrc_apv_dtm
            ,csrc_pblc_cncl_dt
            ,cncl_csrc_pg_apv_no
            ,csrc_cncl_apv_dtm
            ,gds_cd
            ,gds_nm
            ,csp_cd
            ,csp_nm
            ,gds_amt_rt
            ,gds_spl_prc
            ,csrc_gds_pblc_amt
            ,csrc_spl_prc
            ,csrc_vat_amt
            ,gds_vat_amt
            ,csp_csrc_pblc_yn
            ,csp_csrc_pblc_magn_cd
            ,csrc_bzrn_obj_cd
            ,csrc_pblc_knd_cd
            ,csrc_pblc_obj_gds_yn
            ,csp_bzrn
            ,taxn_knd_cd
            ,std_menu_cd
            ,rgm_clsg_cncl_yn
            ,csrc_pblc_yn
            ,rmrk
            ,wfp_csrc_pblc_amt
            ,wgs_sbtr_csrc_pblc_amt
            ,spp_csrc_pblc_amt
            ,acnt_trns_csrc_pblc_amt
            ,vtac_csrc_pblc_amt
            ,lbv_csrc_pblc_amt
            ,kcci_pnt_csrc_pblc_amt
            ,vndp_csrc_pblc_amt
            ,vnpp_csrc_pblc_amt
            ,hpnt_csrc_pblc_amt
            ,nvpay_csrc_pblc_amt
            ,nhpnt_csrc_pblc_amt
            ,wfp_yn
            ,spp_yn
            ,clxp_itd_obj_yn
            ,frst_reg_dtm
        from
            ez_or.py_csrc_b
        where
            1 = 1
            <choose>
                <when test="publicationDate == 'ALL'">
                    and 1 = 1
                </when>
                <when test="publicationDate != null and publicationDate != ''">
                    and (csrc_pblc_obj_dt = #{publicationDate} and csrc_pg_apv_no is null and csrc_apv_no is null) or (csrc_pblc_cncl_dt = #{publicationDate} and csrc_pg_apv_no is not null and cncl_csrc_pg_apv_no is null)
                </when>
                <otherwise>
                    and 1 = 0
                </otherwise>
            </choose>
            <if test="ordNo != null and ordNo != ''">
                and ord_no <![CDATA[>=]]> #{ordNo}
                and ord_gds_seq <![CDATA[>=]]> #{ordGdsSeq}
                and clm_no <![CDATA[>=]]> #{clmNo}
                and csrc_hndw_prcs_nos <![CDATA[>]]> #{csrcHndwPrcsNos}
            </if>
        limit 100
    </select>


    <select id="selectUnitedContentServiceProviderInfo" resultType="com.ezwelesp.batch.hims.order.receipt.domain.ContentServiceProviderDto" >
        select
            a.csp_cd
            ,a.csp_nm
            ,b.bzrn
        from
            ez_co.co_csp_b a
            join ez_co.co_untd_cust_b b on a.untd_cust_cd = b.untd_cust_cd
        where a.csp_cd = #{cspCd}
    </select>

</mapper>
