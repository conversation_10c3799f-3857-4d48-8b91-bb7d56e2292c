package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import com.ezwelesp.batch.lgportal.product.mapper.query.ProductLGPortalQueryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

import java.util.Collections;

import static com.ezwelesp.batch.lgportal.product.service.LgProductUtils.mapToProductAnnouncement;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalEditProductAnnouncementHandler implements LgPortalEditProductProcessorHandler {

    private final LgApiClient lgApiClient;
    private final ProductLGPortalQueryMapper productLGPortalQueryMapper;

    @Override
    public LgProductApiResponseDto handle(LgEditProductTargetVo item) {
        val productAnnouncementList = productLGPortalQueryMapper.selectLgProductAnnouncementByGdsCd(
                Collections.singletonList(item.getGdsCd())
        );

        val requestDto = mapToProductAnnouncement(productAnnouncementList);

        val responseDto = lgApiClient.sendLgAddProductAnnouncementApi(requestDto, item.getApiSeq());


        if (responseDto != null && !"200".equals(responseDto.getResult())) {
            log.debug("sendLgAddProductAnnouncementApi Fail : {},{}", item.getGdsCd(), responseDto.getResult());
        }

        return responseDto;
    }

}
