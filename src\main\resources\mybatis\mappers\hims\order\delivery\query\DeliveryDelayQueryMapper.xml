<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.delivery.mapper.query.DeliveryDelayQueryMapper">

    <resultMap id="DeliveryDelayResultMap" type="com.ezwelesp.batch.hims.order.delivery.domain.DeliveryDelayDto">
        <id column="ord_no" jdbcType="VARCHAR" property="ordNo"/>
        <id column="dlv_no" jdbcType="VARCHAR" property="dlvNo"/>
        <result column="csp_cd" jdbcType="VARCHAR" property="cspCd"/>
        <result column="sat_dlv_poss_yn" jdbcType="VARCHAR" property="satDlvPossYn"/>
        <result column="dlv_strt_clsg_dt" jdbcType="VARCHAR" property="dlvStrtClsgDt"/>
        <result column="dlv_dlay_dcnt" jdbcType="NUMERIC" property="dlvDlayDcnt"/>
        <result column="dlv_due_dt" jdbcType="VARCHAR" property="dlvDueDt"/>
        <result column="dlv_due_dlay_dcnt" jdbcType="NUMERIC" property="dlvDueDlayDcnt"/>
        <result column="gdsInfoList" jdbcType="VARCHAR"
                javaType="com.ezwelesp.batch.hims.order.delivery.domain.DeliveryDelayGoodsInfoDto"
                typeHandler="com.ezwelesp.batch.hims.order.handler.ObjectListSnakeTypeHandler"
                property="gdsInfoList"/>
    </resultMap>

    <select id="selectDeliveryDelayTargetList" resultMap="DeliveryDelayResultMap">
        select oob.ord_no
              , ddb.dlv_no
              , ddb.csp_cd
              , ddb.dlv_strt_clsg_dt
              , ddb.sat_dlv_poss_yn
              , ddb.dlv_dlay_dcnt
              , gds.gdsInfoJson as gdsInfoList
          from ez_or.or_ord_b oob
          join ez_or.dl_dlv_b ddb on ddb.ord_no = oob.ord_no
          left join lateral (
              select ddgd.dlv_no
                    , json_agg(json_build_object(
                          'fri_dlv_poss_yn', coalesce(oogd.fri_dlv_poss_yn, 'N')
                        , 'gds_typ_dtl_cd', pgc.gds_typ_dtl_cd)
                      ) as gdsInfoJson
                from ez_or.dl_dlv_gds_d ddgd
                join ez_or.or_ord_gds_d oogd on oogd.ord_no = ddgd.ord_no and oogd.ord_gds_seq = ddgd.ord_gds_seq
                left join ez_pd.pd_gds_c pgc on pgc.gds_cd = oogd.gds_cd
               where ddgd.dlv_no = ddb.dlv_no
               group by ddgd.dlv_no) gds on gds.dlv_no = ddb.dlv_no
        where oob.ord_dlv_knd_cd = 'DLV'
        and oob.ord_dtm <![CDATA[>=]]> to_char(now() - interval '100 days', 'YYYYMMDD') || '000000'
        and oob.ord_dtm <![CDATA[<=]]> to_char(now() - interval '1 days', 'YYYYMMDD') || '235959'
        and ddb.dlv_st_cd in ('STBY', 'RDY', 'OBND', 'DLV')
        and oob.cncl_dtm is null
        and ddb.dlv_cmpt_dtm is null
        and ddb.clm_no is null
        and ddb.dlv_strt_clsg_dt <![CDATA[<=]]> to_char(now() - interval '1 days', 'YYYYMMDD')
        and ddb.dlv_plcy_cd in ('1001', '1002', '1003') /* TODO 배송정책 확인 1001:당일, 1002:익일, 1003:순차, 1004:해외, 1005:주문제작, 1006:설치예약 */
    </select>

    <select id="selectDeliveryDueDelayTargetList" resultMap="DeliveryDelayResultMap">
        select oob.ord_no
              , ddb.dlv_no
              , ddb.csp_cd
              , ddb.dlv_due_dt
              , ddb.sat_dlv_poss_yn
              , ddb.dlv_due_dlay_dcnt
              , gds.gdsInfoJson as gdsInfoList
          from ez_or.or_ord_b oob
          join ez_or.dl_dlv_b ddb on ddb.ord_no = oob.ord_no
          left join lateral (
              select ddgd.dlv_no
                   , json_agg(json_build_object(
                      'fri_dlv_poss_yn', coalesce(oogd.fri_dlv_poss_yn, 'N')
                  , 'gds_typ_dtl_cd', pgc.gds_typ_dtl_cd)
                     ) as gdsInfoJson
              from ez_or.dl_dlv_gds_d ddgd
                       join ez_or.or_ord_gds_d oogd on oogd.ord_no = ddgd.ord_no and oogd.ord_gds_seq = ddgd.ord_gds_seq
                       left join ez_pd.pd_gds_c pgc on pgc.gds_cd = oogd.gds_cd
              where ddgd.dlv_no = ddb.dlv_no
              group by ddgd.dlv_no) gds on gds.dlv_no = ddb.dlv_no
         where oob.cncl_dtm is null
            /* and oob.ord_st_cd in ('ORD_CMPT', 'GDS_RDY', 'OBND')  주문완료, 상품준비중, 출고진행 *배송이 하나라도 바뀌면 주문상태가 업데이트 되므로 배송상태만 보고 처리*/
           and ddb.dlv_st_cd in ('STBY', 'RDY', 'OBND') /* 배송대기, 배송준비중, 출고진행 */
           and ddb.dlv_obnd_typ_cd = 'ORD' /* 주문출고 */
           and ddb.clm_no is null
           and oob.ord_dtm <![CDATA[>=]]> to_char(now() - interval '100 days', 'YYYYMMDD') || '000000'
           and oob.ord_dtm <![CDATA[<=]]> to_char(now() - interval '1 days', 'YYYYMMDD') || '235959'
           and ddb.dlv_due_dt <![CDATA[<=]]> to_char(now() - interval '1 days', 'YYYYMMDD')
    </select>

    <select id="selectBusinessDayCnt" resultType="int">
        WITH converted_holidays AS (
            SELECT TO_DATE(chb2.strt_dtm, 'YYYYMMDD')::DATE AS holiday_start_date
                 , TO_DATE(chb2.end_dtm, 'YYYYMMDD')::DATE AS holiday_end_date
            FROM ez_cm.cm_hldy_b chb2
            where chb2.end_dtm >= #{startDate}
        ) /* TODO dtm -> dt 바뀔수도있음  */
        SELECT COUNT (*) AS business_days
        FROM (
                SELECT GENERATE_SERIES(TO_DATE(#{startDate}, 'YYYYMMDD') + '1 DAY'::INTERVAL, NOW() - '1 DAY'::INTERVAL, '1 DAY')::DATE AS target_date
            ) d
        WHERE EXTRACT(DOW FROM d.target_date) NOT IN (0 /* 일요일 제외 */
            <if test="isFriHoliday">
                , 5 /* 금요일배송 안하면 제외 (신선/냉장/냉동) */
            </if>
            <if test='satDlvPossYn == "N"'>
                , 6 /* 토요일배송 안하면 제외 */
            </if>)
          AND NOT EXISTS (
            SELECT 1
            FROM converted_holidays h
            WHERE d.target_date BETWEEN h.holiday_start_date AND h.holiday_end_date)
          and not exists ( /* 협력사 휴일 제외 */
            select 1
              from ez_co.co_hldy_b
             where csp_cd = #{cspCd}
               and use_yn = 'Y'
               and hldy_aply_obj_cd = 'CSP'
               and hldy_dt = to_char(now(), 'YYYYMMDD'))
    </select>
</mapper>
