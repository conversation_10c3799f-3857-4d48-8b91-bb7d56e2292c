package com.ezwelesp.batch.lgportal.order.service.impl;

import com.ezwelesp.batch.lgportal.entity.*;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.DeliveryAcceptReqDto;
import com.ezwelesp.batch.lgportal.order.dto.OrderListReqDto;
import com.ezwelesp.batch.lgportal.order.dto.OrderListResDto;
import com.ezwelesp.batch.lgportal.order.mapper.command.ApiBatchCommandMapper;
import com.ezwelesp.batch.lgportal.order.mapper.query.ApiBatchQueryMapper;
import com.ezwelesp.batch.lgportal.order.mapper.query.OrderQueryMapper;
import com.ezwelesp.batch.lgportal.order.mapper.query.ProductQueryMapper;
import com.ezwelesp.batch.lgportal.order.service.OrderListService;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.DateUtils;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
  * LG Portal 주문 조회
  *
  * <AUTHOR>
  * @since 2025.03.31
  * @see com.ezwelesp.batch.lgportal.order.service.impl
  */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderListServiceImpl implements OrderListService {

    private final OrderQueryMapper orderQueryMapper;
    private final ProductQueryMapper productQueryMapper;
    private final ApiBatchCommandMapper apiBatchCommandMapper;
    private final ApiBatchQueryMapper apiBatchQueryMapper;

    @Value("${lgportal.server.host}")
    private String serverHost;

    @Value("${lgportal.server.timeout}")
    private int timeout;

    @Value("${lgportal.api-key}")
    private String apiKey;

    @Value("${lgportal.cert-key}")
    private String certKey;

    @Override
    public void orderList(String beforeTime, String nowTime) {
        OrderListReqDto orderListReqDto = OrderListReqDto.builder().build();

        try {
            AjaxResult
                    result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_GET_DELIVERYSLIP, this.timeout, JsonObjectConverter.serialize(
                    orderListReqDto.toBuilder()
                            .apiKey(apiKey)
                            .deliveryStatusCodeChanged_above(beforeTime)
                            .deliveryStatusCodeChanged_below(nowTime)
                            .build()
            ));

            if (StringUtils.equals(String.valueOf(HttpStatus.OK.value()), result.get("result").toString())) {

                ObjectMapper objectMapper = this.configMapper();
                List<OrderListResDto> resDtoList = objectMapper.convertValue(result.get("items"),
                        new TypeReference<>() {
                        });
                log.debug("size {}" , resDtoList.size());

                resDtoList.forEach(this::setOrderTmpEntity);
            }

        } catch (Exception e) {
            log.error("LG portal 'orderList' failed", e);
        }
    }

    private void setOrderTmpEntity(OrderListResDto resDto) {

        String salesOrderNo = String.valueOf(resDto.getSalesOrderNo());
        List<OrderGdsTmpEntity> gdsTmpEntityList = new ArrayList<>();

        // 이지웰 주문데이터 미생성된 경우
        if (this.orderQueryMapper.selectOrderCountByClientOrderNum(salesOrderNo) == 0) {

            List<OrderListResDto.DeliveryItem> deliveryItems = resDto.getDeliveryItems();
            OrderListResDto.DeliveryItem deliveryItem = deliveryItems.get(0);

            // 주문일시
            String ordDtm = deliveryItem.getCreated();

            if (StringUtils.isNotEmpty(ordDtm)) {
                ordDtm = DateUtils.parseDateToStr(DateUtils.YYYYMMDDHHMMSS,
                        DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, ordDtm));
            }

            // 주문상품 정보
            gdsTmpEntityList = this.setOrderGdsTmpEntityList(deliveryItems, resDto);

            // 배송비, 묶음배송처리, 배송비는 1개만 생성 'deliverAmt' 리스트 중에서 주문상문의 '주문항목번호'(orderItemNo)와 비교해서 처리
            OrderListResDto.DlvExp dlvExp = new OrderListResDto.DlvExp();
            List<OrderListResDto.DeliveryAmt> deliveryAmtList = resDto.getDeliverAmt();

            deliveryAmtList.stream().findFirst()
                    .filter(deliveryAmt -> deliveryItems.stream().anyMatch(
                            item -> item.getDeliveryAmtNo().equals(deliveryAmt.getDeliveryAmtNo())))
                    .map(OrderListResDto.DeliveryAmt::getPay)
                    .ifPresent(dlvExp::setDlvExp);

            // 배송주소 정보
            OrderDlvTmpEntity orderDlvTmpEntity = OrderDlvTmpEntity.builder()
                    .dlvStCd(LgConstants.DlvStCd.findEzwelCode("ACC1022"))// '배송준비중'
                    .rcvrNm(resDto.getRecvName())
                    .rcvrMblTelno(resDto.getRecvMobilePhone())
                    .rcvrZipcd(resDto.getZipcode())
                    .rcvrBasAdr(resDto.getAddr())
                    .rcvrDtlAdr(resDto.getAddr2())
                    .dlvReqCntn(deliveryItem.getDeliveryMemo())
                    .build();

            // TODO-JSKIM27 협력사출고위치번호, 배송정책순번 등은 fo_order_api 호출 시 확인
            // 배송비용 기본
            DlDlvExpBTmpEntity dlDlvExpBTmpEntity = DlDlvExpBTmpEntity.builder()
                    .dlvExp(dlvExp.getDlvExp())
                    .build();

            // 결제정보
            PyPymtTmpEntity pyPymtTmpEntity = PyPymtTmpEntity.builder()
                    .pymtDivCd("PYMT")      // 결제구분코드 '결제'
                    .pymtStCd("CMPT")       // 결제상태코드 '완료'
                    .pymtMnsCd("1001")      // 결제수단 '포인트'
                    .pymtPrcsMemo("lgcns-api연동주문")
                    .build();

            // 주문정보
            OrderTmpEntity.builder()
                    .clntOrdNo(salesOrderNo)
                    .ordStCd(LgConstants.OrdStCd.findEzwelCode(resDto.getOrderStatusCode()))
                    .ordTypCd("GNRL")
                    .userKey("1000000000")
                    .clntCd("lgapi")
                    .aspOrdCspCd("10000811")
                    .acssDvcCd("PCWB")
                    .stdMenuCd("10001942")
                    .ordDtm(ordDtm)
                    .gdsTmpEntityList(gdsTmpEntityList)
                    .dlvTmpEntity(orderDlvTmpEntity)
                    .pyPymtTmpEntity(pyPymtTmpEntity)
                    .dlDlvExpTmpEntity(dlDlvExpBTmpEntity)
                    .build();

            // TODO-JSKIM27 주문생성(fo_order_api) 호출,
            //  - 주문 생성 시 주문상품 옵션별 재고 차감 확인 필요
            
        }
        else {
            // 주문은 존재하지만, 출고지가 다른 경우(주문상품 누락)
            //this.odOrdQueryMapper.selectApiDlvrDetailNoCount(deliveryItem.)
        }

        // [배송접수] API 호출
        List<DeliveryAcceptReqDto.DeliveryItem> deliveryAccptList = gdsTmpEntityList.stream()
                .map(OrderGdsTmpEntity::getLgpApiDlvNo)
                .distinct()
                .map(apiDlvNo -> DeliveryAcceptReqDto.DeliveryItem.builder()
                        .deliveryCmdNo(Long.valueOf(apiDlvNo))
                        .build())
                .toList();

        this.deliveryAccept(deliveryAccptList);

    }

    private List<OrderGdsTmpEntity> setOrderGdsTmpEntityList(List<OrderListResDto.DeliveryItem> deliveryItems, OrderListResDto resDto) {
        return deliveryItems.stream().flatMap(deliveryItem ->
                this.setOrderGdsTmpEntitys(deliveryItem, resDto).stream()).collect(Collectors.toList());
    }

    private List<OrderGdsTmpEntity> setOrderGdsTmpEntitys(OrderListResDto.DeliveryItem deliveryItem, OrderListResDto resDto) {

        // TODO-JSKIM27 상품코드에 '채널코드' 3자리가 추가됨에 따라 이전상품과 신규 상품에 대한 구분방법이 필요함.아직 현업에서 정리안된 상태임

        List<OrderGdsTmpEntity> gdsTmpEntityList = new ArrayList<>();
        PdGdsCEntity pdGdsCEntity = this.productQueryMapper.selectGoods(deliveryItem.getProductCode());

        if (this.productQueryMapper.selectGoodsOptionBase(deliveryItem.getProductCode()) > 0) {

            String optTypeCd = pdGdsCEntity.getGdsOptTypCd(); // 상품옵션유형코드

            if (StringUtils.equals(LgConstants.GoodsOptionType.SINGLE.getCode(), optTypeCd)) {
                // 단독형
                if (String.valueOf(deliveryItem.getSkuCode()).indexOf("_") > -1) {

                    String[] optionMappingCode = deliveryItem.getSkuCode().split("_");

                    for (int h = 0; h < optionMappingCode.length; h++) {
                        String[] optionMapping = optionMappingCode[h].split("-");
                        gdsTmpEntityList.add(this.orderGdsTmpEntity(deliveryItem, pdGdsCEntity, optionMapping[1]));
                        
                        // 해당옵션 재고체크 및 API데이터(SKU품절) 생성
                        this.insertApiBatchSendSoldout(deliveryItem.getProductCode(), optionMapping[1], deliveryItem.getProductNo(),
                                LgConstants.SendKey.SKU_SOLD_OUT.getSendKey());

                    }
                }
                else {
                    String[] optionMappingCode = deliveryItem.getSkuCode().split("-");

                    gdsTmpEntityList.add(this.orderGdsTmpEntity(deliveryItem, pdGdsCEntity, optionMappingCode[1]));

                    // 해당옵션 재고체크 및 API데이터(SKU품절) 생성
                    this.insertApiBatchSendSoldout(deliveryItem.getProductCode(), optionMappingCode[1], deliveryItem.getProductNo(),
                            LgConstants.SendKey.SKU_SOLD_OUT.getSendKey());
                }
            }
            else {
                // 조합형
                String[] optionMappingCode = deliveryItem.getSkuCode().split("_");
                for (int h = 0; h < optionMappingCode.length; h++) {
                    String[] optionMappingCodeTemp = optionMappingCode[h].split("-");

                    gdsTmpEntityList.add(this.orderGdsTmpEntity(deliveryItem, pdGdsCEntity, optionMappingCodeTemp[1]));

                    // 해당옵션 재고체크 및 API데이터(SKU품절) 생성
                    this.insertApiBatchSendSoldout(deliveryItem.getProductCode(), optionMappingCodeTemp[1], deliveryItem.getProductNo(),
                            LgConstants.SendKey.SKU_SOLD_OUT.getSendKey());

                }
            }
        }
        else {
            gdsTmpEntityList.add(this.orderGdsTmpEntity(deliveryItem, pdGdsCEntity, null));

            /*
             * productCode : 이지웰 상품번호, productNo : LG포털 상품번호
             * ez_if.api_batch_goods.target_goods_cd : LG포털 상품번호
             */
            this.insertApiBatchSend(this.apiBatchQueryMapper.selectApiBatchGoods(deliveryItem.getProductCode()),
                    LgConstants.SendKey.SOLD_OUT.getSendKey());
        }

        // 'deliveryItems' 로그 데이터 생성
        try {
            this.insertDeliveryItemsLog(String.valueOf(resDto.getSalesOrderNo())
                , LgConstants.OrdStCd.findEzwelCode(resDto.getOrderStatusCode())
                , String.valueOf(deliveryItem.getDeliveryCmdNo())
                , JsonObjectConverter.serialize(deliveryItem));
        }
        catch (Exception e) {
            log.error("insertDeliveryItemsLog failed", e);
        }

        return gdsTmpEntityList;
    }

    private void insertDeliveryItemsLog(String clientOrderNum, String orderStatus, String deliveryNo, String deliveryItemsLog) {

        this.apiBatchCommandMapper.insertApiBatchLog(ApiBatchLogEntity.builder()
                .clientOrderNum(clientOrderNum)
                .sendKey(orderStatus)
                .deliveryNo(deliveryNo)
                .deliveryItem(Long.valueOf(deliveryNo))
                .returnData(deliveryItemsLog)
                .returnCode(LgConstants.EMPTY_STR)
                .returnMessage(LgConstants.EMPTY_STR)
                .regId("Batch")
                .build());
    }

    private void insertApiBatchSendSoldout(String gdsCd, String optGdsCombSeq, String productNo, int sendKey) {

        PdGdsOptCombBEntity pdGdsOptCombBEntity = this.productQueryMapper.selectGoodsOption(gdsCd, Long.valueOf(optGdsCombSeq));

        if (ObjectUtils.isNotEmpty(pdGdsOptCombBEntity) && pdGdsOptCombBEntity.getStckQty() == 0) {
            this.apiBatchCommandMapper.insertApiBatchSend(ApiBatchSendEntity.builder()
                    .providerNo(Long.valueOf(LgConstants.LGPORTAL_PROVIDER_NO))
                    .certkey(this.certKey)
                    .productNo(Long.valueOf(productNo))
                    .sendKey(sendKey)
                    .sendYn("N")
                    .regId("batch")
                    .build());
        }
    }

    private void insertApiBatchSend(String productNo, int sendKey) {

        this.apiBatchCommandMapper.insertApiBatchSendNoSku(ApiBatchSendEntity.builder()
                .providerNo(Long.valueOf(LgConstants.LGPORTAL_PROVIDER_NO))
                .certkey(this.certKey)
                .productNo(Long.valueOf(productNo))
                .sendKey(sendKey)
                .sendYn("N")
                .regId("batch")
                .build());
    }

    private ObjectMapper configMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        return mapper;
    }

    private OrderGdsTmpEntity orderGdsTmpEntity(OrderListResDto.DeliveryItem deliveryItem, PdGdsCEntity pdGdsCEntity, String optGdsCombSeq) {
        return OrderGdsTmpEntity.builder()
                .chCd("100")                                           // '복지샵'
                .gdsNm(deliveryItem.getProductName())
                .gdsCd(deliveryItem.getProductCode())
                .ordGdsQty(deliveryItem.getRealOrderCnt())              // 주문상품수량
                .cspCd(pdGdsCEntity.getCspCd())
                .hezoStdCtgrCd(deliveryItem.getStandardCategoryNo())    // 복지몰표준카테소리
                .lgpApiDlvNo(String.valueOf(deliveryItem.getDeliveryCmdNo()))
                .lgpApiDlvDtlNo(String.valueOf(deliveryItem.getOrderItemNo()))
                .productNo(deliveryItem.getProductNo())
                .optGdsCombSeq(StringUtils.isNotEmpty(optGdsCombSeq) ? Long.valueOf(optGdsCombSeq) : null)
                .cspObndLocNo(pdGdsCEntity.getCspObndLocNo())           // 협력사출고지번호
                .dlvPlcySeq(pdGdsCEntity.getDlvPlcySeq())               // 배송정책순번
                .build();
    }

    private void deliveryAccept(List<DeliveryAcceptReqDto.DeliveryItem> deliveryAccptList ) {
        if (ObjectUtils.isEmpty(deliveryAccptList)) {
            return;
        }

        DeliveryAcceptReqDto deliveryAcceptReqDto = new DeliveryAcceptReqDto(this.apiKey);

        deliveryAcceptReqDto.setDeliveryItems(deliveryAccptList);

        try {
            AjaxResult result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_SET_DELIVERY_ACCEPT, this.timeout, JsonObjectConverter.serialize(deliveryAcceptReqDto));

            ApiBatchDlvrEntity entity = new ApiBatchDlvrEntity();
            entity.setSendKey(1010);

            if (com.ezwelesp.framework.utils.StringUtils.equals("200", result.get("result").toString())) {
                entity.setSendYn("Y");
                entity.setReturnCode(result.get("result").toString());
                entity.setReturnMessage(result.get("resultMessage").toString());
            } else {
                entity.setSendYn("F");
                entity.setReturnCode(result.get("result").toString());
                entity.setReturnMessage(result.get("resultMessage").toString());
            }

            this.apiBatchCommandMapper.insertApiBatchDlvr(entity);

        } catch (JsonProcessingException e) {
            throw new RuntimeException("LG portal api request failed");
        }
    }
}
