package com.ezwelesp.batch.lgportal.product.chunk.writer;

import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisBatchItemWriter;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@StepScope
@RequiredArgsConstructor
public class LgPortalEditProductWriter extends MyBatisBatchItemWriter<LgProductApiResponseDto> {

    public LgPortalEditProductWriter(
            @Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory sqlSessionFactory
    ) {
        this.setSqlSessionFactory(sqlSessionFactory);
        this.setStatementId(
                "com.ezwelesp.batch.lgportal.product.mapper.command.ProductLGPortalCommandMapper.updateListApiBatchSendStatus");
    }
}
