package com.ezwelesp.batch.hims.order.goodsflow.domain.request;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 배송결과수신 응답 request (SendTraceResultResponse API)
 */
@Getter
@Setter
@Builder
@ToString
public class SendTraceResultRequest {
    private SendTraceResultRequestItem data;

    @Getter
    @Setter
    @Builder
    @ToString
    public static class SendTraceResultRequestItem {
        private List<Item> items;
    }

    @Getter
    @Setter
    @Builder
    @ToString
    public static class Item {
        private String transUniqueCode;
        private int seq;
    }
}
