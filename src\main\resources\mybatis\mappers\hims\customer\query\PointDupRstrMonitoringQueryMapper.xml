<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.customer.mapper.query.PointDupRstrMonitoringQueryMapper">

    <select id="selectUserPointDupRstr">
        SELECT count(*)
        FROM (SELECT clnt_cd, user_key, count(ord_no)
              FROM ez_ct.cp_usr_wfp_b
              WHERE wfp_wsp_cd = '9001'
                AND wsp_amt > 0
                AND frst_reg_dtm >= to_char(current_date - 6, 'yyyymmdd') || '000000'
              GROUP BY clnt_cd, user_key, ord_no, wfp_wsp_cd
              HAVING COUNT(ord_no) > 1)
    </select>

    <select id="selectSpecialPointDupRstr">
        SELECT count(*)
        FROM (SELECT ord_no, count(ord_no)
              FROM ez_ct.cp_spp_use_d
              WHERE spp_use_typ_cd = 'C'
                AND frst_reg_dtm >= to_char(current_date - 7, 'yyyymmdd') || '000000'
              GROUP BY ord_no, spp_use_typ_cd
              HAVING COUNT(ord_no) > 1)
    </select>
</mapper>
