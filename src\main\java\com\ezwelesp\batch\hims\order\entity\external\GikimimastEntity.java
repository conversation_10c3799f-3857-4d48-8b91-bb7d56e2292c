package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * [인터페이스] 배송지키미 요청 마스터 테이블
 * gikimimast
 */
@Getter
@SuperBuilder
public class GikimimastEntity implements Serializable {
    /**
     * 마스터ID(자동증가 ID필드)
     */
    private Long mid;

    /**
     * 배송번호*(배송에 대한 고유번호 (굿스플로 전송단위) )
     */
    private String transUniqueCode;

    /**
     * 관리구분코드
     */
    private String sectionCode;

    /**
     * 회원사코드
     */
    private String memberCode;

    /**
     * 판매자ID
     */
    private String sellerCode;

    /**
     * 판매자명
     */
    private String sellerName;

    /**
     * 보내는분 명
     */
    private String fromName;

    /**
     * 받는분 명
     */
    private String toName;

    /**
     * 받는분 휴대폰 연락처
     */
    private String toMobile;

    /**
     * 배송사 코드(굿스플로에서 지정한 배송사 코드)
     */
    private String logisticsCode;

    /**
     * 운송장번호
     */
    private String invoiceNo;

    /**
     * 배송구분
     */
    private String dlvretType;

    /**
     * 운송장등록일시
     */
    private String invoicePrintDate;

    /**
     * 오류코드
     */
    private String errCode;

    /**
     * 오류설명
     */
    private String errDesc;

    /**
     * 전문전송일시
     */
    private String ediDatetime;

    /**
     * 생성일시
     */
    private String createDatetime;

    /**
     * 업체관리코드1
     */
    private String defCode1;

    /**
     * 업체관리코드2
     */
    private String defCode2;

    private static final long serialVersionUID = 1L;
}
