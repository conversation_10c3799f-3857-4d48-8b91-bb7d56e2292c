<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hcas.mapper.query.PolMemberStoreExpireInfmQueryMapper">
    <select id="getMemberStoreList">
                
			SELECT 
				A.frcs_nm AS frcsNm
				, C.comm_dtl_cd_nm AS commDtlCdNm
				, A.mgr_nm AS mgrNm
				, A.cc_br_cd AS ccBrCd
				, (SELECT comm_dtl_cd_nm FROM ez_ct.ct_cc_dtl_c where clnt_cd='polbokji'and cc_cd='1101' and cc_dtl_cd=A.cc_br_cd) AS branchNm    
				, A.cc_dept_cd AS ccDeptCd
				, (SELECT comm_dtl_cd_nm FROM ez_ct.ct_cc_dtl_c where clnt_cd='polbokji'and cc_cd='1102' and cc_dtl_cd=A.cc_br_cd) AS branch2Nm
				, A.bnft_aply_strt_dt AS bnftAplyStrtDt
				, A.bnft_aply_end_dt AS bnftAplyEndDt
				, (SELECT eml_adr FROM ez_cm.cm_mgr_b E WHERE E.clnt_cd='polbokji' AND COALESCE(A.frst_reg_usr_id,A.last_mod_usr_id)=E.mgr_id) AS mailReceiver
				, A.frcs_biztp_cd
			FROM ez_ct.ct_npa_frcs_b A 
			     LEFT JOIN ez_cm.bb_ezmbrs_area_b B ON A.ezmbrs_area_no = B.ezmbrs_area_no
			     LEFT JOIN ez_ct.ct_cc_dtl_c C ON C.cc_dtl_cd = A.frcs_biztp_cd
			     LEFT JOIN ez_cm.bb_ezmbrs_area_b D ON D.ezmbrs_area_no = B.high_ezmbrs_area_no
			WHERE C.clnt_cd = 'polbokji'
			  AND C.cc_cd = '1501'
			  AND C.use_yn = 'Y'
			  AND A.bnft_aply_end_dt = to_char(CURRENT_DATE+1, 'YYYYMMDD')
			ORDER BY A.bnft_aply_end_Dt DESC
    </select>
    
</mapper>