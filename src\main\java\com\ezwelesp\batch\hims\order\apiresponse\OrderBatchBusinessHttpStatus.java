package com.ezwelesp.batch.hims.order.apiresponse;

import com.ezwelesp.framework.apiresponse.EspHttpStatus;

/**
 * 주문 배치 오류 코드
 */
public enum OrderBatchBusinessHttpStatus implements EspHttpStatus {

    /**
     * 코드 범위 확정되면 수정
     */
    FAIL_ORDER_BATCH_ERROR_TEST(12900, "오류 메시지 테스트.");

    private final int code;
    private final String message;

    OrderBatchBusinessHttpStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage(String... args) {
        return String.format(message, (Object[]) args);
    }

}
