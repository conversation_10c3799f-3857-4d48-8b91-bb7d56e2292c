package com.ezwelesp.batch.lgportal.product.domain.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgUpdateProductPriceRequestDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgUpdateProductPriceRequestDto {

    private String productMappingCode;  // 이지웰 상품번호
    private Integer productNo;          // lg 상품번호
    private Long consumerPrice;         // 소비자가
    private Long salesPrice;            // 판매가격(회원가)
    private Long vendorPrice;           // 공급가
}
