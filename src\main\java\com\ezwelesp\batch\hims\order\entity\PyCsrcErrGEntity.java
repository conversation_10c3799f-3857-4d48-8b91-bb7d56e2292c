package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 현금영수증오류로그(ez_or.py_csrc_err_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyCsrcErrGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 현금영수증오류로그순번(csrc_err_log_seq) not null
     */
    private Long csrcErrLogSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 현금영수증수기처리차수(csrc_hndw_prcs_nos) not null
     */
    private Long csrcHndwPrcsNos;

    /**
     * 결제차수(pymt_nos)
     */
    private Long pymtNos;

    /**
     * 현금영수증오류코드(csrc_err_cd)
     */
    private String csrcErrCd;

    /**
     * 현금영수증오류내용J(csrc_err_cntnj)
     */
    private String csrcErrCntnj;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
