package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.List;

/**
 * 교환 접수시 Dto
 *
 * <AUTHOR> rebecca
 * @date : 2025. 02. 25
 */
@Getter
@SuperBuilder
public class ClaimExchangetReqDto {

    // 주문번호
    private String orderNumber;

    // 협력사코드
    private String cspCd;

    // 협력사명
    private String cspNm;

    // 협력사출고위치번호
    private String cspObndLocNo;

    // 클레임종류코드
    private String clmKndCd;

    // 주문상품 리스트
    private List<ClaimSaveGoodsDto> ordGdsList;

    // 배송지순번
    private String dlvAdrNo;

    // 클레임배송비용
    private String clmDlvExp;

    // 클레임추가비용결제수단코드
    private String clmAddExpPrcsPymtMnsCd;

    // 배송정보
    private List<ClaimDeliveryAddressDto> deliveryAddressList;

}
