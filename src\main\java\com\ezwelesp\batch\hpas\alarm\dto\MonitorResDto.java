package com.ezwelesp.batch.hpas.alarm.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class MonitorResDto {
    private String resSeq;
    private String jobId;
    private String jobStartDt;
    private String jobEndDt;
    private String alramYn;
    private String alramCntMaxYn;
    private String alramCntMax;
    private String alramUser;
    private String criticalValueYn;
    private String criticalValue;
    private String criticalValueUnit;
    private String resStatus;
    private int resValue;
    private String resEtc;
    private String resEtc2;
    private String resIp;
    private String regDt;
    private String modiDt;
    private String sendStatus;
    private String sendStartDt;
    private String sendEndDt;
    private String sendDesc;
}
