package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 주문상품상세(ez_or.or_ord_gds_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrOrdGdsDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 무형상품판매차수(intg_gds_sell_nos)
     */
    private Long intgGdsSellNos;

    /**
     * 채널코드(ch_cd) not null
     */
    private String chCd;

    /**
     * 상품코드(gds_cd)
     */
    private String gdsCd;

    /**
     * 협력사상품코드(csp_gds_cd)
     */
    private String cspGdsCd;

    /**
     * 상품명(gds_nm)
     */
    private String gdsNm;

    /**
     * 과세종류코드(taxn_knd_cd) not null
     */
    private String taxnKndCd;

    /**
     * 정산템플릿순번(stl_tmpl_seq)
     */
    private Long stlTmplSeq;

    /**
     * 고속버스예약구분코드(exbs_rsv_div_cd)
     */
    private String exbsRsvDivCd;

    /**
     * 고속버스좌석번호(exbs_seat_no)
     */
    private String exbsSeatNo;

    /**
     * 복지몰표준카테고리코드(hezo_std_ctgr_cd)
     */
    private String hezoStdCtgrCd;

    /**
     * 복지몰메뉴코드(hezo_menu_cd)
     */
    private String hezoMenuCd;

    /**
     * 복지몰번호(hezo_no)
     */
    private Long hezoNo;

    /**
     * HIFI부서코드(hifi_dept_cd)
     */
    private String hifiDeptCd;

    /**
     * 협력사코드(csp_cd)
     */
    private String cspCd;

    /**
     * 협력사출고위치번호(csp_obnd_loc_no)
     */
    private String cspObndLocNo;

    /**
     * 배송정책순번(dlv_plcy_seq)
     */
    private Long dlvPlcySeq;

    /**
     * 합배송제외상품여부(cdlv_excld_gds_yn) not null
     */
    private String cdlvExcldGdsYn;

    /**
     * 동일포장가능상품수(same_pack_poss_gds_cnt)
     */
    private Integer samePackPossGdsCnt;

    /**
     * 설정배송비용(stup_dlv_exp)
     */
    private BigDecimal stupDlvExp;

    /**
     * 설정교환배송비용(stup_exch_dlv_exp)
     */
    private BigDecimal stupExchDlvExp;

    /**
     * 설정반품배송비용(stup_rtp_dlv_exp)
     */
    private BigDecimal stupRtpDlvExp;

    /**
     * 설정제주도추가배송비용(stup_jeju_add_dlv_exp)
     */
    private BigDecimal stupJejuAddDlvExp;

    /**
     * 설정도서산간추가배송비용(stup_ismt_add_dlv_exp)
     */
    private BigDecimal stupIsmtAddDlvExp;

    /**
     * 반품상품회수대상여부(rtp_gds_wtdw_obj_yn)
     */
    private String rtpGdsWtdwObjYn;

    /**
     * 교환반품배송비용부담방법코드(exch_rtp_dlv_exp_budn_mthd_cd)
     */
    private String exchRtpDlvExpBudnMthdCd;

    /**
     * 배송비용결제방법코드(dlv_exp_pymt_mthd_cd) not null
     */
    private String dlvExpPymtMthdCd;

    /**
     * 조건부무료배송여부(cndl_nchg_dlv_yn)
     */
    private String cndlNchgDlvYn;

    /**
     * 조건부무료배송비용(cndl_nchg_dlv_exp)
     */
    private BigDecimal cndlNchgDlvExp;

    /**
     * 금요일배송가능여부(fri_dlv_poss_yn)
     */
    private String friDlvPossYn;

    /**
     * 교환반품묶음배송불가여부(exch_rtp_bndl_ndmt_yn)
     */
    private String exchRtpBndlNdmtYn;

    /**
     * 주문상품수량(ord_gds_qty) not null
     */
    private Integer ordGdsQty;

    /**
     * 취소상품수량(cncl_gds_qty) not null
     */
    private Integer cnclGdsQty;

    /**
     * 주문상품매입가격(ord_gds_pchs_prc) not null
     */
    private BigDecimal ordGdsPchsPrc;

    /**
     * 상품매입가격(gds_pchs_prc) not null
     */
    private BigDecimal gdsPchsPrc;

    /**
     * 옵션매입가격(opt_pchs_prc) not null
     */
    private BigDecimal optPchsPrc;

    /**
     * 주문상품판매가격(ord_gds_sell_prc) not null
     */
    private BigDecimal ordGdsSellPrc;

    /**
     * 상품판매가격(gds_sell_prc) not null
     */
    private BigDecimal gdsSellPrc;

    /**
     * 옵션판매가격(opt_sell_prc) not null
     */
    private BigDecimal optSellPrc;

    /**
     * 주문상품정상가격(ord_gds_nrml_prc) not null
     */
    private BigDecimal ordGdsNrmlPrc;

    /**
     * 정상판매가격(nrml_sell_prc) not null
     */
    private BigDecimal nrmlSellPrc;

    /**
     * 액면가격(fval_prc) not null
     */
    private BigDecimal fvalPrc;

    /**
     * 제휴사상품할인금액(asp_gds_dc_amt)
     */
    private BigDecimal aspGdsDcAmt;

    /**
     * 제휴사쿠폰할인금액(asp_cpn_dc_amt)
     */
    private BigDecimal aspCpnDcAmt;

    /**
     * 주문상품수수료율(ord_gds_cms_rt)
     */
    private Double ordGdsCmsRt;

    /**
     * 약정상품추가수수료금액(stip_gds_add_cms_amt) not null
     */
    private BigDecimal stipGdsAddCmsAmt;

    /**
     * 협력사상품수수료율(csp_gds_cms_rt)
     */
    private Double cspGdsCmsRt;

    /**
     * 사용가능결제수단내용(use_poss_pymt_mns_cntn)
     */
    private String usePossPymtMnsCntn;

    /**
     * 원본주문상품순번(orgl_ord_gds_seq)
     */
    private Long orglOrdGdsSeq;

    /**
     * 취소수수료발생상품여부(cncl_cms_ocrn_gds_yn) not null
     */
    private String cnclCmsOcrnGdsYn;

    /**
     * 옵션교환상품여부(opt_exch_gds_yn) not null
     */
    private String optExchGdsYn;

    /**
     * 최초주문상품순번(frst_ord_gds_seq)
     */
    private Long frstOrdGdsSeq;

    /**
     * 이벤트코드(evnt_cd)
     */
    private String evntCd;

    /**
     * 이벤트코너상세순번(evnt_conr_dtl_seq)
     */
    private Long evntConrDtlSeq;

    /**
     * EZ라이브번호(ezlive_no)
     */
    private String ezliveNo;

    /**
     * 인지세발행번호(sttx_pblc_no)
     */
    private String sttxPblcNo;

    /**
     * 딜상품번호(dlgds_no)
     */
    private String dlgdsNo;

    /**
     * 딜상품명(dlgds_nm)
     */
    private String dlgdsNm;

    /**
     * 대표상품여부(rps_gds_yn) not null
     */
    private String rpsGdsYn;

    /**
     * 적립금부담주체코드(mlg_budn_magn_cd)
     */
    private String mlgBudnMagnCd;

    /**
     * 복지몰표준카테고리복지제도포인트적용코드(hezo_std_ctgr_wsp_aply_cd)
     */
    private String hezoStdCtgrWspAplyCd;

    /**
     * 상품평작성안내알림톡발송대상여부(gdsrvw_wrt_gd_nttk_snd_obj_yn) not null
     */
    private String gdsrvwWrtGdNttkSndObjYn;

    /**
     * 옵션상품조합순번(opt_gds_comb_seq)
     */
    private Long optGdsCombSeq;

    /**
     * 주문상품옵션내용(ord_gds_opt_cntn)
     */
    private String ordGdsOptCntn;

    /**
     * 주문상품옵션수량(ord_gds_opt_qty) not null
     */
    private Integer ordGdsOptQty;

    /**
     * 옵션우선순위(opt_prr) not null
     */
    private Integer optPrr;

    /**
     * 필수선택옵션여부(mndr_choc_opt_yn) not null
     */
    private String mndrChocOptYn;

    /**
     * 옵션추가가격(opt_add_prc)
     */
    private BigDecimal optAddPrc;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
