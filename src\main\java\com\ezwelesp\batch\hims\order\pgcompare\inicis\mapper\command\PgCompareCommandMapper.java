package com.ezwelesp.batch.hims.order.pgcompare.inicis.mapper.command;

import com.ezwelesp.batch.hims.order.entity.external.ApiPgPrsnlPayEntity;
import com.ezwelesp.batch.hims.order.entity.external.EzEcOrderPayLogEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PgCompareCommandMapper {

    int insertEzEcOrderPayLog(EzEcOrderPayLogEntity orderPayLog);

    int updateEzEcOrderPayLog(EzEcOrderPayLogEntity orderPayLog);

    int insertApiPgPrsnlPay(ApiPgPrsnlPayEntity apiPgPayLog);

    int updateApiPgPrsnlPay(ApiPgPrsnlPayEntity apiPgPayLog);

}

