package com.ezwelesp.batch.lgportal.product.chunk.processor;

import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaAnswerRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaAutoAnswerListDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgQnaAutoAnswerTargetVo;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@StepScope
@RequiredArgsConstructor
public class LgPortalProductQnaAutoAnswerProcessor
        implements ItemProcessor<LgQnaAutoAnswerTargetVo, LgProductQnaAutoAnswerListDto> {
    private final LgApiClient lgApiClient;


    @Override
    public LgProductQnaAutoAnswerListDto process(@NonNull LgQnaAutoAnswerTargetVo item) {
        try {


            val title = "문의 답변드립니다.";
            val content = "고객님 안녕하십니까. 본 게시판은[상품에 관련된 문의]만 처리가 가능하므로,<br/>"
                    + "주문/교환/반품/환불 관련하여 불편사항이나 도움이 필요하시다면 LG CNS 고객센터로 문의 부탁드립니다.";


            val requestDto = LgProductQnaAnswerRequestDto.builder()
                    .productInquiryNo(item.getLgpBbsNoInteger())
                    .answerTitle(title)
                    .answerContents(content)
                    .build();

            val responseDto = lgApiClient.sendLgUpdateProductQnaAutoAnswerApi(
                    requestDto,
                    item.getGdsCd()
            );

            if (responseDto == null) {
                log.debug("sendLgUpdateProductQnaAnswerApi response null : {},{}", item.getGdsCd(),
                        responseDto.getResult());
                return null;
            }

            if (!"200".equals(responseDto.getResult())) {
                log.debug("sendLgUpdateProductQnaAnswerApi Fail : {},{}", item.getGdsCd(), responseDto.getResult());
                return null;
            }

            return LgProductQnaAutoAnswerListDto.builder()
                    .productNo(Integer.valueOf(item.getTargetGoodsCd()))
                    .titleArr(title)
                    .valueArr(content)
                    .boardNo(Integer.valueOf(item.getLgpBbsNo()))
                    .result(responseDto.getResult())
                    .resultMessage(responseDto.getResultMessage())
                    .build();

        } catch (Exception e) {
            log.error("LgPortalEditProductProcessor Fail : {},{}", item.getGdsCd(), e.getMessage());

            return null;
        }
    }

}

