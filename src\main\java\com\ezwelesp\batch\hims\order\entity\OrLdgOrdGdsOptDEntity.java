package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 숙박주문상품옵션상세(ez_or.or_ldg_ord_gds_opt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrLdgOrdGdsOptDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 숙박주문지원차수(ldg_ord_afe_nos) not null
     */
    private Long ldgOrdAfeNos;

    /**
     * 객실옵션순번1(room_opt_seq1) not null
     */
    private BigDecimal roomOptSeq1;

    /**
     * 객실옵션순번2(room_opt_seq2) not null
     */
    private BigDecimal roomOptSeq2;

    /**
     * 주문상품옵션내용(ord_gds_opt_cntn) not null
     */
    private String ordGdsOptCntn;

    /**
     * 객실옵션추가가격(room_opt_add_prc) not null
     */
    private BigDecimal roomOptAddPrc;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
