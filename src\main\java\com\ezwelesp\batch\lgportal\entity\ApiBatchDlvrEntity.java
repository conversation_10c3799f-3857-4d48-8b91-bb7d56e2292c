package com.ezwelesp.batch.lgportal.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * [인터페이스] API 배치 배송
 * api_batch_dlvr
 */
@Data
public class ApiBatchDlvrEntity implements Serializable {
    /**
     * API 시퀀스
     */
    private long apiSeq;

    /**
     * 전송 키
     */
    private int sendKey;

    /**
     * 전송여부 Y:전송   N:미전송
     */
    private String sendYn;

    /**
     * 배송전표번호
     */
    private String deliveryNo;

    /**
     * 배송전표항목번호
     */
    private Long deliveryItem;

    /**
     * 송장번호
     */
    private String invoiceNo;

    /**
     * 택배사 번호
     */
    private String logisticsNo;

    /**
     * 리턴 코드
     */
    private String returnCode;

    /**
     * 리턴 메시지
     */
    private String returnMessage;

    /**
     * 등록일자
     */
    private String regDt;

    /**
     * 등록자ID
     */
    private String regId;

    private static final long serialVersionUID = 1L;
}
