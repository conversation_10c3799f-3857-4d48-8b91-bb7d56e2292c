spring:
  datasource:
    hikari:
      primary:
        command:
          driver-class-name: org.postgresql.Driver
          jdbc-url: ******************************************************************************************************************************************
          username: ezwel_bat_app
          password: ezwel_bat_app#was34
          max-lifetime: 175
          connection-timeout: 30000
          maximum-pool-size: 10
        query:
          driver-class-name: org.postgresql.Driver
          jdbc-url: *********************************************************************************************************************************************
          username: ezwel_bat_sel
          password: ezwel_bat_sel#was34
          max-lifetime: 175
          connection-timeout: 30000
          maximum-pool-size: 10

# AWS Account Credentials (AWS 접근 키)
cloud:
  aws:
    credentials:
      use-default-aws-credentials-chain: true
    region:
      static: ap-northeast-2 #(S3 버킷 지역)
    stack:
      auto: false
    kms:
      key-arn: arn:aws:kms:ap-northeast-2:************:alias/enc-dev

# ezwel config
ezwel:
  apim:
    host: https://apim.ezwel.com

# lg portal interface
lgportal:
  chunk-size: 1000
  server:
    host: https://v2api.lglifecare.com
    port: 443
    timeout: 60
  api-key: prdEzwelApiUser
  cert-key: u6;ShVow;kgR5=$

kyobo:
  chunk-size: 1000
  server:
    host: https://openapi.kyobobook.co.kr/corpm/api/v1/kko/ordr
    port: 443
    timeout: 10
  auth-session-key: eyJhbGciOiJSUzUxMiJ9.******************************************************************************************************************************************************.Zac-gjLQ-GxElGAupw0ZrSDELz0u_Ddfh_92tpWZmPBRe8niXKdY6PGFxX1dyrs2g2KJrACG9JAiej1lxmZ_YcdKTqVOJNs4KHEFnc477hKsSBKWCQgHMc6pO7yakaKtKn5HwYI-zpbQ755IxQvr9-cLqhaCCaAxFU5S5-aWW7f6R8KVQULlgJTLk-3rdquItxFbHZYY1tbvf095HwuXz0wb3Z2RhfyDEqSEzyE6Rsz3CsE-lcrkhFKCOAofh-ThL-VlkXQqKwtKkSffDAGQl4IA2konJK_7eP3pplyNpEnrocEr3EXUH9HnJAMimFxfPG_lSrR_PlYWMfHXoActlA

hyundai-futurenet:
  host: *************
  port: 12351

goodsflow:
  server:
    zkm:
      host: https://test3.goodsflow.com
      timeout: 60
      api-key: 28bd4794-3528-4966-9dca-3758f61cfa17
    return:
      host: https://test.goodsflow.com
      timeout: 60
      api-key: 1ff603e3-953d-404e-abfd-d66eeffd615c

naverpay:
  api:
    history: https://apis.naver.com/%s/naverpay/payments/v2.2/list/history
    settlement: https://apis.naver.com/naverpaysettle-payment/naverpaysettle/v1/settlements/by-case
    timeout: 25000

pinpay:
  api:
    history: https://pinpayapi.bluewalnut.co.kr/pay/transactions
    timeout: 5000
    mchId: ezwelpin01
    limit: 100