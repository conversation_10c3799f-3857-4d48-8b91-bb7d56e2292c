package com.ezwelesp.batch.lgportal.order.job;

import com.ezwelesp.batch.lgportal.order.chunk.reader.ExchRtpWtdrRfsItemReader;
import com.ezwelesp.batch.lgportal.order.chunk.writer.ExchRtpWtdrRfsItemWriter;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.ClaimWtdrRfsReqDto;
import com.ezwelesp.batch.listener.CommonJobListener;
import com.ezwelesp.batch.listener.CommonStepListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class ExchRtpWtdrRfsJobConfig {
    private final String WTDR_RFS_EXCH_RTP_STEP = LgConstants.JobName.JOB_WTDR_RFS_EXCH_RTP + "_STEP";

    private final CommonJobListener commonJobListener;

    @Value("${lgportal.chunk-size}")
    private int chunkSize;


    @Bean(LgConstants.JobName.JOB_WTDR_RFS_EXCH_RTP)
    public Job regInvoiceNoJob(JobRepository jobRepository, @Qualifier(WTDR_RFS_EXCH_RTP_STEP) Step regInvoiceNoStep) {
        return new JobBuilder(LgConstants.JobName.JOB_WTDR_RFS_EXCH_RTP, jobRepository)
                .listener(commonJobListener)
                .start(regInvoiceNoStep)
                .build();
    }

    @Bean(name = WTDR_RFS_EXCH_RTP_STEP)
    public Step exchRtpWtdrRfsStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager,
            ExchRtpWtdrRfsItemReader exchRtpWtdrRfsItemReader,
            ExchRtpWtdrRfsItemWriter exchRtpWtdrRfsItemWriter, CommonStepListener commonStepListener) {

        return new StepBuilder(WTDR_RFS_EXCH_RTP_STEP, jobRepository)
                .allowStartIfComplete(true)
                .<ClaimWtdrRfsReqDto, ClaimWtdrRfsReqDto>chunk(chunkSize, transactionManager)
                .transactionManager(transactionManager)
                .reader(exchRtpWtdrRfsItemReader)
                .writer(exchRtpWtdrRfsItemWriter)
                .listener(commonStepListener)
                .build();

    }
}
