package com.ezwelesp.batch.hims.order.entity.external;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.experimental.SuperBuilder;

/**
 * PG개인결제대사내역
 */
@Data
@SuperBuilder(toBuilder=true)
public class ApiPgPrsnlPayEntity implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * PG사 타입
	 */
	private String pgType;
	
	/**
	 * 상태코드 
	 */
	private String statusCd;
	
	/**
	 * TID
	 */
	private String tid;
	
	/**
	 * 취소TID(원TID)
	 */
	private String cancelTid;
	
	/**
	 * 이지웰주문번호
	 */
	private Long ezwelOrderNum;
	
	/**
	 * 상점아이디(MID)
	 */
	private String storeId;
	
	/**
	 * 승인번호
	 */
	private String confirmNum;
	
	/**
	 * 결제유형
	 */
	private String payType;
	
	/**
	 * 거래상태
	 */
	private String dealStatus;
	
	/**
	 * 승인일
	 */
	private String confirmDt;
	
	/**
	 * 취소일
	 */
	private String cancelDt;
	
	/**
	 * 거래금액
	 */
	private BigDecimal dealAmt;
	
	/**
	 * 취소금액
	 */
	private BigDecimal cancelAmt;
	
	/**
	 * 신용카드금액
	 */
	private BigDecimal cardAmt;
	
	/**
	 * 취소잔액
	 */
	private BigDecimal cancelRemainAmt;
	
	/**
	 * 제휴포인트
	 */
	private String alliPoint;
	
	/**
	 * 할부개월수
	 */
	private int instMm;
	
	/**
	 * 카드유형
	 */
	private String cardType;
	
	/**
	 * 간편결제
	 */
	private String simplePay;
	
	/**
	 * 은행명
	 */
	private String bankNm;
	
	/**
	 * 상품명
	 */
	private String goodsNm;
	
	/**
	 * 지불일시
	 */
	private String payDt;
	
	/**
	 * 취소매입 완료일시
	 */
	private String payCancelDt;
	
	/**
	 * 등록일시
	 */
	private String regDt;
	
	/**
	 * 결제유형1
	 */
	private String payCd;
	
	/**
	 * 입금일
	 */
	private String depositDt;
	
	/**
	 * 입금액
	 */
	private BigDecimal depositAmt;
	
	/**
	 * 정산금액
	 */
	private BigDecimal calcAmt;
}
