<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.delivery.mapper.query.PenddingDeliveryQueryMapper">

    <select id="selectPenddingDeliveryList" resultType="com.ezwelesp.batch.hims.order.delivery.domain.PenddingDeliveryDto">
        select cmmb.mgr_id
              , cmmb.md_nm
              , sum(gds.stby_cnt) as stby_cnt
              , sum(gds.rdy_cnt) as rdy_cnt
              , sum(gds.obnd_cnt) as obnd_cnt
              , sum(gds.dlv_cnt) as dlv_cnt
          from (select oogd.gds_cd
                      , sum(case when ddb.dlv_st_cd = 'STBY' then 1 else 0 end) as stby_cnt
                      , sum(case when ddb.dlv_st_cd = 'RDY' then 1 else 0 end) as rdy_cnt
                      , sum(case when ddb.dlv_st_cd = 'OBND' then 1 else 0 end) as obnd_cnt
                      , sum(case when ddb.dlv_st_cd = 'DLV' then 1 else 0 end) as dlv_cnt
                  from ez_or.or_ord_b oob
                  join ez_or.dl_dlv_b ddb on ddb.ord_no = oob.ord_no
                  join ez_or.dl_dlv_gds_d ddgd on ddgd.dlv_no = ddb.dlv_no
                  join ez_or.or_ord_gds_d oogd on oogd.ord_no = ddgd.ord_no and oogd.ord_gds_seq = ddgd.ord_gds_seq
                  left join lateral (select ccgd.ord_no
                                             , ccgd.ord_gds_seq
                                             , sum(ccgd.clm_gds_qty - ccgd.cncl_qty) as clm_gds_qty
                                         from ez_or.cl_clm_gds_d ccgd
                                         join ez_or.cl_clm_b ccb on ccb.clm_no = ccgd.clm_no
                                        where ccgd.ord_no = oob.ord_no
                                          and ccgd.ord_gds_seq = oogd.ord_gds_seq
                                          and ((ccb.clm_knd_cd = 'CNCL' and ccb.clm_st_cd = 'APL') or ccb.clm_knd_cd = 'RTP')
                                        group by ccgd.ord_no, ccgd.ord_gds_seq) clm on clm.ord_no = oob.ord_no and clm.ord_gds_seq = oogd.ord_gds_seq
                 where oob.ord_dtm between #{startDate} || '000000' and #{endDate} || '235959'
                   and oob.ord_st_cd in ('ORD_CMPT', 'GDS_RDY', 'OBND', 'DLV')
                   and ddb.dlv_obnd_typ_cd = 'ORD'
                   and ddb.dlv_st_cd in ('STBY', 'RDY', 'OBND', 'DLV')
                   and ddgd.dlv_gds_qty - ddgd.dlv_cncl_gds_qty > 0
                   and oogd.ord_gds_qty - oogd.cncl_gds_qty - coalesce(clm.clm_gds_qty, 0) > 0
                 group by oogd.gds_cd) gds
         join ez_pd.pd_gds_c pgc on pgc.gds_cd = gds.gds_cd
         left join ez_dp.dp_std_ctgr_b dscb on dscb.std_ctgr_cd = pgc.mng_std_ctgr_cd
         left join ez_cm.cm_mgr_md_b cmmb on cmmb.mgr_md_no = dscb.mgr_md_no
        where cmmb.mgr_md_no is not null
           /* asis md조건 AND MGR.mgr_id IN ('20191044','20131052','2300204','20131046','20191015','20161116','20141062','20201078','2211461','20201025','2407297','20201039','20161120','20151008','20201054','20191069','20211028','20211049','20201062', '20131008', '2428565', '20181036', '2224185' ) */
        group by cmmb.mgr_id, cmmb.md_nm
    </select>
</mapper>
