package com.ezwelesp.batch.hims.order.send.service.impl;

import com.ezwelesp.batch.hims.order.send.domain.EzwelSendDto;
import com.ezwelesp.batch.hims.order.send.domain.SendCallCenterInfoDto;
import com.ezwelesp.batch.hims.order.send.domain.SendResponse;
import com.ezwelesp.batch.hims.order.send.mapper.query.SendManageQueryMapper;
import com.ezwelesp.batch.hims.order.send.service.EzwelSendManageService;
import com.ezwelesp.batch.hims.order.send.util.SendManageUtils;
import com.ezwelesp.framework.apiresponse.BaseEspHttpStatus;
import com.ezwelesp.framework.exception.ServiceException;
import com.ezwelesp.framework.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class EzwelSendManageServiceImpl implements EzwelSendManageService {
    private final SendManageUtils sendManageUtils;
    private final SendManageQueryMapper sendManageQueryMapper;

    @Override
    public SendResponse send(EzwelSendDto ezwelSendDto) {
        // 유효성 체크
        this.validate(ezwelSendDto);

        if ("MMS".equals(ezwelSendDto.getTmsgSndKndCd())) {
            this.sendMms(ezwelSendDto);
        } else if (List.of("SMS", "LMS").contains(ezwelSendDto.getTmsgSndKndCd())) {
            this.sendSms(ezwelSendDto);
        } else {
            throw new ServiceException(BaseEspHttpStatus.valueOf("발송종류코드가 지정되지 않았습니다."));
        }

        // TODO 발송요청시 응답값에 따라 response 세팅 필요
        return SendResponse.builder()
                .success(true)
                .result("")
                .build();
    }

    private void sendSms(EzwelSendDto param) {
        Map<String, Object> result = new HashMap<>();

        MultiValueMap<String, Object> request = new LinkedMultiValueMap<>();
        request.add("callTo", param.getRcvrMblTelno());
        request.add("callFrom", this.getCallCenter(param.getClntCd()));
        request.add("msgType", "SMS");
        request.add("smsTxt", param.getIntgGdsInfmSndTypCntn()); // TODO 발송 메시지 확인필요 (쿠폰번호 넣어줘야함)
        request.add("svcType", "1021");
        request.add("__ezwel_framework_view_type__", "json");

        // TODO SMS, LMS 발송 요청
    }

    private void sendMms(EzwelSendDto param) {
        // 첨부할 이미지가 있으면 이미지 생성 (상품이미지, 바코드이미지)
        Resource resource = sendManageUtils.getImageResource(param.getIntgGdsRdnoNo(), param.getGdsImg(), param.getBcdExpsYn(), param.getBcdTypCd());

        //발송 데이터 생성
        MultiValueMap<String, Object> request = new LinkedMultiValueMap<>();
        request.add("callTo", param.getRcvrMblTelno());
        request.add("callFrom",  this.getCallCenter(param.getClntCd()));
        request.add("smsTxt", param.getIntgGdsInfmSndTypCntn()); // TODO 발송 메시지 확인필요 (쿠폰번호 넣어줘야함)
        request.add("svcType", "1021");
        request.add("__ezwel_framework_view_type__", "json");
        request.add("mmsSubject", "");
        if (resource != null)  {
            request.add("attachedFile", resource);
        }

        // TODO MMS 발송 요청
    }

    /**
     * --   @param 사업분류 (bsns_cate) chb.cst_cntr_telno_aply_bsic_cd
     * -- 	 @param 고객사분류(promo_gu) ccb.clnt_cls_cd
     * -- 	 @param 콜센터사업분류기준 (callCenterType) ccb.cst_cntr_telno_typ_cd
     * -- 	 @param 콜센터전화번호(callCenter) chb.clct_telno
     */
    private String getCallCenter(String clntCd) {
        SendCallCenterInfoDto dto = sendManageQueryMapper.selectSendCallCenterInfo(clntCd);
        String defaultTel = "02-3282-0579";

        if (dto == null) {
            return defaultTel;
        }

        if ("02".equals(dto.getCstCntrTelnoTypCd())) { // 예외적용
            return dto.getClctTelno();
        }

        // 선택적복지 & 민간기업(엘지)
        if ("1001".equals(dto.getCstCntrTelnoAplyBsicCd()) && "1006".equals(dto.getClntClsCd())) {
            return "02-3282-0556";
        }
        
        // 공통코드 매핑이안됨
//        case "복지패키지"        :resCalTel = "02-3282-0505";break;
//        case "사회서비스"        :resCalTel = "02-3282-0567";break;
//        case "회원복지"            :resCalTel = "02-3282-0586";break;

        return defaultTel;
    }

    private void validate(EzwelSendDto param) {
        if (StringUtils.isEmpty(param.getRcvrMblTelno())) {
            throw new ServiceException(BaseEspHttpStatus.valueOf("수신자전화번호는 필수입니다."));
        }
    }
}
