package com.ezwelesp.batch.listener;

import org.springframework.batch.core.ItemReadListener;

import com.ezwelesp.batch.aasample.test.domain.User;

public class UserItemReadListener implements ItemReadListener<User> {
    @Override
    public void beforeRead() {}

    @Override
    public void afterRead(User user) {
        System.out.println("Thread : " + Thread.currentThread().getName() + ", Read item: " + user.getId());
    }

    @Override
    public void onReadError(Exception e) {}
}
