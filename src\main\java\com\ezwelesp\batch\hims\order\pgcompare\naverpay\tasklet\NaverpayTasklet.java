package com.ezwelesp.batch.hims.order.pgcompare.naverpay.tasklet;

import java.util.Optional;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.order.pgcompare.naverpay.service.NaverpayService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class NaverpayTasklet implements Tasklet{
	
	private final NaverpayService naverpayService;
	
	@Override
	public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
		log.info("############## 네이버 결제대사 START ##############" + contribution.getStepExecution().getStepName());
		String searchDtm = Optional.ofNullable(chunkContext.getStepContext().getJobParameters().get("searchDtm"))
                .map(Object::toString)
                .filter(s -> !s.isBlank())
                .orElse(null);
		if("BA_HIOR00007_STEP".equals(contribution.getStepExecution().getStepName())) {
			this.naverpayService.callNaverpayApi(searchDtm);
		}else if("BA_HIOR00006_STEP".equals(contribution.getStepExecution().getStepName())) {
			this.naverpayService.callNaverpaySettlementApi(searchDtm);
		}
		log.info("############## 네이버 결제대사 END ##############" + contribution.getStepExecution().getStepName());
	
		return RepeatStatus.FINISHED;
	}

}
