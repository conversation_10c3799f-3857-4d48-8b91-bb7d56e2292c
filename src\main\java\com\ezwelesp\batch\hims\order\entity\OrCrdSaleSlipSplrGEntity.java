package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 카드매출전표공급자로그(ez_or.or_crd_sale_slip_splr_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrCrdSaleSlipSplrGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 카드매출전표공급자로그순번(crd_sale_slip_splr_log_seq) not null
     */
    private Long crdSaleSlipSplrLogSeq;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 카드매출전표연동결과코드(crd_sale_slip_intl_rslt_cd)
     */
    private String crdSaleSlipIntlRsltCd;

    /**
     * 결과메시지내용(rslt_msg_cntn)
     */
    private String rsltMsgCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
