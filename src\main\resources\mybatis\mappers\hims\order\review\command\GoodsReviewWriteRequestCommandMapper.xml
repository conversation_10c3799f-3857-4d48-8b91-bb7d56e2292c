<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.review.mapper.command.GoodsReviewWriteRequestCommandMapper">
    <update id="updateOrderGoodsPushYn">
        with v_target as (
            select t.ord_no
                  , t.ord_gds_seq
              from <foreach collection='list' item="item" separator="," open="(values " close=")">
                    ( #{item.ordNo}
                    , #{item.ordGdsSeq})
                    </foreach> as t(ord_no, ord_gds_seq)
            )
        update ez_or.or_ord_gds_d oogd
           set gdsrvw_wrt_gd_nttk_snd_obj_yn = 'Y'
              , last_mod_dtm = TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
              , last_mod_usr_id = 'BATCH'
              , last_mod_pgm_id = 'BATCH'
          from v_target vt
         where oogd.ord_no = vt.ord_no
           and oogd.ord_gds_seq = vt.ord_gds_seq
    </update>
</mapper>
