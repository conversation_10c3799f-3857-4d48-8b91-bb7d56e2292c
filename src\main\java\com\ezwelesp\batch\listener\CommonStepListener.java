package com.ezwelesp.batch.listener;

import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CommonStepListener implements StepExecutionListener {
    @Override
    public void beforeStep(StepExecution stepExecution) {
        String param1 = stepExecution.getJobParameters().getString("status");
        String param2 = stepExecution.getJobParameters().getString("param2");
        String ntcTitl = stepExecution.getJobParameters().getString("ntcTitl");
//        System.out.println("==========================Parameter Check: param1: " + param1 + ", param2: " + param2);
        log.debug("==========================Parameter ntcTitl: {}", ntcTitl);
    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        System.out.println("=================Step Complete============");
        return ExitStatus.COMPLETED;
    }
}
