package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 주문상품금액상세(ez_or.or_ord_gds_amt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrOrdGdsAmtDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 결제차수(pymt_nos) not null
     */
    private Long pymtNos;

    /**
     * 매출취소구분코드(sale_cncl_div_cd) not null
     */
    private String saleCnclDivCd;

    /**
     * 배송번호(dlv_no) not null
     */
    private String dlvNo;

    /**
     * 배송상품순번(dlv_gds_seq) not null
     */
    private Long dlvGdsSeq;

    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * 주문수량(ord_qty) not null
     */
    private Integer ordQty;

    /**
     * 주문상품판매가격(ord_gds_sell_prc) not null
     */
    private BigDecimal ordGdsSellPrc;

    /**
     * 주문상품매입가격(ord_gds_pchs_prc) not null
     */
    private BigDecimal ordGdsPchsPrc;

    /**
     * 거래일자(trd_dt)
     */
    private String trdDt;

    /**
     * 협력사정산처리시작일시(csp_stl_prcs_strt_dtm)
     */
    private String cspStlPrcsStrtDtm;

    /**
     * 고객사정산처리시작일시(clnt_stl_prcs_strt_dtm)
     */
    private String clntStlPrcsStrtDtm;

    /**
     * 협력사정산확정여부(csp_stl_cnfm_yn) not null
     */
    private String cspStlCnfmYn;

    /**
     * 고객사정산확정여부(clnt_stl_cnfm_yn) not null
     */
    private String clntStlCnfmYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
