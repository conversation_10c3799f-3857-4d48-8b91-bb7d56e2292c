package com.ezwelesp.batch.hims.order.reseller.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.reseller.domain
 * @since 2025.05.19
 */
@Data
public class SuspectedResellerOrderDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 7805518530479584698L;

    private String clntCd;
    private String userKey;
    private String clntNm;
    private String usrNm;
    private Long ordGdsSkuQty;
    private Long ordGdsQty;
    private Long ordCnt;
    private BigDecimal ordAmt;
}
