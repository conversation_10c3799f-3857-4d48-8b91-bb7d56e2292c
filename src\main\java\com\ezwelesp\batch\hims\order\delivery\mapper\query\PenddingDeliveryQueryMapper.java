package com.ezwelesp.batch.hims.order.delivery.mapper.query;

import com.ezwelesp.batch.hims.order.delivery.domain.PenddingDeliveryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PenddingDeliveryQueryMapper {
    List<PenddingDeliveryDto> selectPenddingDeliveryList(@Param("startDate") String startDate, @Param("endDate") String endDate);
}


