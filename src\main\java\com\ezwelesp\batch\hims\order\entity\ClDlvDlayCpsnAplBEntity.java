package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 배송지연보상신청기본(ez_or.cl_dlv_dlay_cpsn_apl_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClDlvDlayCpsnAplBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송번호(dlv_no) not null
     */
    private String dlvNo;

    /**
     * 배송상품순번(dlv_gds_seq) not null
     */
    private Long dlvGdsSeq;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 사용자명(usr_nm) not null
     */
    private String usrNm;

    /**
     * 사용자모바일전화번호(usr_mbl_telno) not null
     */
    private String usrMblTelno;

    /**
     * 사용자이메일주소(usr_eml_adr) not null
     */
    private String usrEmlAdr;

    /**
     * 배송지연보상신청내용(dlv_dlay_cpsn_apl_cntn)
     */
    private String dlvDlayCpsnAplCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
