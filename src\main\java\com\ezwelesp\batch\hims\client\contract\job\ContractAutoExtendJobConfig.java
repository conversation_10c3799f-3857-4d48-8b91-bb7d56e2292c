package com.ezwelesp.batch.hims.client.contract.job;

import com.ezwelesp.batch.hims.client.contract.tasklet.ContractAutoExtendTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobScope;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * <AUTHOR> 임종갑 플그림
 * @see : com.ezwelesp.batch.hims.client.contract.job
 * @since : 2025-06-16
 */
@Configuration
@RequiredArgsConstructor
public class ContractAutoExtendJobConfig {
    private final CommonJobListener commonJobListener;
    private final ContractAutoExtendTasklet contractAutoExtendTasklet;

    @Bean("CONTRACT_AUTOEXTEND_JOB")
    public Job salesledgerContractAutoExtendJob(JobRepository jobRepository,
            @Qualifier("CONTRACT_AUTOEXTEND_JOB_STEP") Step step){
        return new JobBuilder("CONTRACT_AUTOEXTEND_JOB",jobRepository)
                .listener(commonJobListener)
                .start(step)
                .build();

    }

    @Bean("CONTRACT_AUTOEXTEND_JOB_STEP")
    @JobScope
    public Step step(
            JobRepository jobRepository, DataSourceTransactionManager transactionManager
    ){
        return new StepBuilder("CONTRACT_AUTOEXTEND_JOB_STEP", jobRepository)
                .tasklet(contractAutoExtendTasklet, transactionManager)
                .build();
    }
}
