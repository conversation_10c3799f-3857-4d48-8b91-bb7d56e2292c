package com.ezwelesp.batch.hpas.alarm.service.impl;

import java.util.List;
import org.springframework.stereotype.Service;
import com.ezwelesp.batch.hpas.alarm.dto.LowPriceSmsMainDto;
import com.ezwelesp.batch.hpas.alarm.mapper.query.LowPriceSmsMainQueryMapper;
import com.ezwelesp.batch.hpas.alarm.service.LowPriceSmsMainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class LowPriceSmsMainServiceImpl implements LowPriceSmsMainService {
    private final LowPriceSmsMainQueryMapper lowPriceSmsMainQueryMapper;

    @Override
    public List<LowPriceSmsMainDto> getLowPriceSmsMainList() {
        return lowPriceSmsMainQueryMapper.selectLowPriceSmsMainList();
    }
}
