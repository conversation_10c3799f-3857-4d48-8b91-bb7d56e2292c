package com.ezwelesp.batch.hims.order.pgconfirm.nicepg.service.impl;


import com.ezwelesp.batch.hims.order.pgconfirm.nicepg.domain.NicePgErrorDto;
import com.ezwelesp.batch.hims.order.pgconfirm.nicepg.mapper.query.NicePgConfirmErrorQueryMapper;
import com.ezwelesp.batch.hims.order.pgconfirm.nicepg.service.NicePgConfirmErrorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class NicePgConfirmErrorServiceImpl implements NicePgConfirmErrorService {

    private final NicePgConfirmErrorQueryMapper nicePgConfirmErrorQueryMapper;

    @Override
    public List<NicePgErrorDto> getNicePgConfirmErrorList() {
        return nicePgConfirmErrorQueryMapper.selectNicePgConfirmErrorList();
    }
}
