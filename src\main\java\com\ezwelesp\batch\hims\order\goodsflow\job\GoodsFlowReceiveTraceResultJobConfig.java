package com.ezwelesp.batch.hims.order.goodsflow.job;

import com.ezwelesp.batch.hims.order.goodsflow.tasklet.GoodsFlowReceiveTraceResultTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 굿스플로 배송결과 수신 [BA_HIOR00141]
 *  - 굿스플로 배송결과 수신 API 호출
 *  - 수신결과 ez_if.gikimiresult insert
 *  - 굿스플로 배송결과수신 응답처리 API 호출
 */
@Configuration
@RequiredArgsConstructor
public class GoodsFlowReceiveTraceResultJobConfig {
    private final CommonJobListener commonJobListener;
    private final GoodsFlowReceiveTraceResultTasklet goodsFlowReceiveTraceResultTasklet;

    @Bean("BA_HIOR00141_1")
    public Job goodsFlowReceiveTraceResultJob(JobRepository jobRepository, @Qualifier("BA_HIOR00141_1_STEP") Step goodsFlowReceiveTraceResultStep) {
        return new JobBuilder("BA_HIOR00141_1", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(goodsFlowReceiveTraceResultStep)
                .build();
    }

    @Bean("BA_HIOR00141_1_STEP")
    public Step goodsFlowReceiveTraceResultStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00141_1_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(goodsFlowReceiveTraceResultTasklet, transactionManager)
                .build();
    }
}
