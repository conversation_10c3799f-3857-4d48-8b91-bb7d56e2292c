package com.ezwelesp.batch.hims.order.giftorder.mapper.query;

import com.ezwelesp.batch.hims.order.giftorder.domain.GiftOrderDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.mapper
 * @since 2025.05.15
 */
@Mapper
public interface GiftOrderQueryMapper {

    List<GiftOrderDto> selectGiftOrderAfterFourDaysList();

    List<Long> selectGiftOrderAutoCancelTargetOrdNoList();
}
