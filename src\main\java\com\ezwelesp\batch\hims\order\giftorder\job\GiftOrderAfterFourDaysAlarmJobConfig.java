package com.ezwelesp.batch.hims.order.giftorder.job;

import com.ezwelesp.batch.hims.order.giftorder.tasklet.GiftOrderAfterFourDaysAlarmTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 선물하기 주문 후 4일 경과된 알림톡 대상 주문 리스트 조회 및 알림톡을 발송한다.[BA_HIOR00034]
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.job
 * @since 2025.05.15
 */
@Configuration
@RequiredArgsConstructor
public class GiftOrderAfterFourDaysAlarmJobConfig {
    private final CommonJobListener commonJobListener;
    private final GiftOrderAfterFourDaysAlarmTasklet giftOrderAfterFourDaysAlarmTasklet;

    @Bean("BA_HIOR00034")
    public Job giftOrderAfterFourDaysAlarmJob(JobRepository jobRepository, @Qualifier("BA_HIOR00034_STEP") Step giftOrderAfterFourDaysAlarmStep) {
        return new JobBuilder("BA_HIOR00034", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(giftOrderAfterFourDaysAlarmStep)
                .build();
    }

    @Bean("BA_HIOR00034_STEP")
    public Step giftOrderAfterFourDaysAlarmStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00034_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(giftOrderAfterFourDaysAlarmTasklet, transactionManager)
                .build();
    }
}
