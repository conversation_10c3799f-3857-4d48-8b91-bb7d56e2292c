package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 주문발송기본(ez_or.dl_ord_snd_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlOrdSndBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문발송순번(ord_snd_seq) not null
     */
    private Long ordSndSeq;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 난수발송주체코드(rdno_snd_magn_cd)
     */
    private String rdnoSndMagnCd;

    /**
     * 문자메시지발송종류코드(tmsg_snd_knd_cd)
     */
    private String tmsgSndKndCd;

    /**
     * 수신자명(rcvr_nm)
     */
    private String rcvrNm;

    /**
     * 수신자모바일전화번호(rcvr_mbl_telno) not null
     */
    private String rcvrMblTelno;

    /**
     * 발송예약일자(snd_rsv_dt)
     */
    private String sndRsvDt;

    /**
     * 주문발송메시지코드(ord_snd_msg_cd)
     */
    private String ordSndMsgCd;

    /**
     * 발송메시지내용(snd_msg_cntn)
     */
    private String sndMsgCntn;

    /**
     * 발송요청내용(snd_req_cntn)
     */
    private String sndReqCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
