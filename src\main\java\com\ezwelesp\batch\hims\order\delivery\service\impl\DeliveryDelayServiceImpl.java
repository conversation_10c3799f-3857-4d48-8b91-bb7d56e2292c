package com.ezwelesp.batch.hims.order.delivery.service.impl;

import com.ezwelesp.batch.hims.order.delivery.domain.DeliveryDelayDto;
import com.ezwelesp.batch.hims.order.delivery.mapper.command.DeliveryDelayCommandMapper;
import com.ezwelesp.batch.hims.order.delivery.mapper.query.DeliveryDelayQueryMapper;
import com.ezwelesp.batch.hims.order.delivery.service.DeliveryDelayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryDelayServiceImpl implements DeliveryDelayService {
    private final DeliveryDelayQueryMapper deliveryDelayQueryMapper;
    private final DeliveryDelayCommandMapper deliveryDelayCommandMapper;

    @Override
    public List<DeliveryDelayDto> getDeliveryDelayTargetList() {
        return deliveryDelayQueryMapper.selectDeliveryDelayTargetList();
    }

    @Override
    public List<DeliveryDelayDto> getDeliveryDueDelayTargetList() {
        return deliveryDelayQueryMapper.selectDeliveryDueDelayTargetList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateDeliveryDelayDays(DeliveryDelayDto dto) {
        // 지연일 계산 (기준일: 배송시작마감일자)
        int delayDays = getBusinessDayCnt(dto, dto.getDlvStrtClsgDt());

        if (dto.getDlvDlayDcnt() == delayDays) {
            return;
        }

        // 배송지연일수 업데이트
        deliveryDelayCommandMapper.updateDeliveryDelayDays(dto.getDlvNo(), delayDays);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateDeliveryDueDelayDays(DeliveryDelayDto dto) {
        // 지연일 계산 (기준일: 배송예정일자)
        int delayDays = getBusinessDayCnt(dto, dto.getDlvDueDt());

        if (dto.getDlvDueDlayDcnt() == delayDays) {
            return;
        }

        // 배송예정지연일수 업데이트
        deliveryDelayCommandMapper.updateDeliveryDueDelayDays(dto.getDlvNo(), delayDays);
    }

    /**
     * 기준일자(startDate) ~ 오늘날짜 사이의 영업일 조회 (휴일, 협력사휴일 제외)
     */
    private int getBusinessDayCnt(DeliveryDelayDto dto, String startDate) {
        // 1007: 신선/냉장/냉동식품 && 금요일배송가능여부 N -> 금요일 휴일로 처리
        boolean isFriHoliday = dto.getGdsInfoList().stream()
                .anyMatch(e -> "1007".equals(e.getGdsTypDtlCd()) && "N".equals(e.getFriDlvPossYn()));

        return deliveryDelayQueryMapper.selectBusinessDayCnt(dto.getCspCd(), startDate, dto.getSatDlvPossYn(), isFriHoliday);
    }
}
