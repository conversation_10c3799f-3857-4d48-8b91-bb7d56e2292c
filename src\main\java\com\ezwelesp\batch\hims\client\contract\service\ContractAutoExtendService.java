package com.ezwelesp.batch.hims.client.contract.service;

import com.ezwelesp.batch.hims.client.contract.domain.CtSlsContBAutoExtendDto;
import com.ezwelesp.batch.hims.client.contract.mapper.command.ContractAutoExtendCommandMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> 임종갑 플그림
 * @see : com.ezwelesp.batch.hims.client.contract.service
 * @since : 2025-06-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractAutoExtendService {
    private final ContractAutoExtendCommandMapper contractAutoExtendCommandMapper;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void modifyContractExtend(CtSlsContBAutoExtendDto ctSlsContBAutoExtendDto) {
        try {
            // 계약 종료일자 연장 1년
            contractAutoExtendCommandMapper.modifyContractExtend(ctSlsContBAutoExtendDto);
            // 계약기본 히스토리 저장
            contractAutoExtendCommandMapper.insertCtSlsContBHistory(ctSlsContBAutoExtendDto);
        } catch (Exception e) {
            log.error("modifyContractExtend Failed: {}", e.getMessage(), e);
        }
    }
}
