<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.client.contract.mapper.command.ContractAutoExtendCommandMapper">

    <update id="modifyContractExtend">
        update ez_ct.ct_sls_cont_b
        set cont_end_dt  = to_char(to_date(cont_end_dt,'yyyyMMdd') + interval '1 year','yyyyMMdd'),
            last_mod_dtm = TO_CHAR(statement_timestamp(),'yyyyMMddhh24miss'),
            last_mod_usr_id = 'BATCH',
            last_mod_pgm_id = 'CONTRACT_AUTOEXTEND_JOB'
        where sls_cont_no = #{slsContNo}
    </update>


    <insert id="insertCtSlsContBHistory">
        insert into ez_ct.ct_sls_cont_b_h(sls_cont_no, sls_cont_bas_his_seq, clnt_cd, bz_fld_cd,
                                          offr_srv_cd, offr_srv_dtl_cd, sls_chrg_mgr_id,
                                          oper_chrg_mgr_id, sls_cont_reg_dt, cont_strt_dt,
                                          cont_end_dt, frst_cont_dt, cont_term_auto_ext_yn,
                                          rsct_doc_no, sls_cont_bz_nm, sls_cont_lbv_nm,
                                          atch_file_grp_seq, sls_rprt_no, sls_cont_apv_st_cd,
                                          athz_chrg_usr_id, rmrk, frst_reg_dtm,
                                          frst_reg_usr_id, frst_reg_pgm_id, last_mod_dtm,
                                          last_mod_usr_id, last_mod_pgm_id)
        select
            sls_cont_no ,
            (
                select coalesce(max(sls_cont_bas_his_seq)+1,1) from ez_ct.ct_sls_cont_b_h
                where sls_cont_no =  #{slsContNo}
            ) as sls_cont_bas_his_seq,
            clnt_cd ,
            bz_fld_cd ,
            offr_srv_cd ,
            offr_srv_dtl_cd ,
            sls_chrg_mgr_id ,
            oper_chrg_mgr_id ,
            sls_cont_reg_dt ,
            cont_strt_dt ,
            cont_end_dt ,
            frst_cont_dt ,
            cont_term_auto_ext_yn ,
            rsct_doc_no ,
            sls_cont_bz_nm ,
            sls_cont_lbv_nm ,
            atch_file_grp_seq ,
            sls_rprt_no ,
            sls_cont_apv_st_cd ,
            athz_chrg_usr_id ,
            rmrk ,
            frst_reg_dtm ,
            frst_reg_usr_id ,
            frst_reg_pgm_id ,
            last_mod_dtm ,
            last_mod_usr_id ,
            last_mod_pgm_id
        from  ez_ct.ct_sls_cont_b cscb
        where cscb.sls_cont_no = #{slsContNo}
          and cscb.clnt_cd = #{clntCd}
    </insert>
</mapper>
