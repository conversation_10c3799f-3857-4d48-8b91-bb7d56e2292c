<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.samsung.mapper.query.SamsungOrderGoodsQueryMapper">

    <!-- 주문기본 - 특수문자가 포함된 주문상품요약명 건수 조회 -->
    <select id="countOrdGdsSmryNmWithSpecialChars40" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_b
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&#40;%'
        ]]>
    </select>

    <select id="countOrdGdsSmryNmWithSpecialChars41" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_b
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&#41;%'
        ]]>
    </select>

    <select id="countOrdGdsSmryNmWithSpecialCharsQuot" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_b
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&quot;%'
        ]]>
    </select>

    <select id="countOrdGdsSmryNmWithSpecialChars39" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_b
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&#39;%'
        ]]>
    </select>

    <select id="countOrdGdsSmryNmWithSpecialChars35" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_b
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&#35;%'
        ]]>
    </select>

    <select id="countOrdGdsSmryNmWithSpecialCharsAmp" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_b
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ord_gds_smry_nm like '%&amp;%'
        ]]>
    </select>

    <!-- 주문상품상세 - 특수문자가 포함된 상품명 건수 조회 -->
    <select id="countGdsNmWithSpecialChars40" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_gds_d ogd
        inner join ez_or.or_ord_b oob on oob.ord_no = ogd.ord_no
        where oob.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ogd.gds_nm like '%&#40;%'
        ]]>
    </select>

    <select id="countGdsNmWithSpecialChars41" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_gds_d ogd
        inner join ez_or.or_ord_b oob on oob.ord_no = ogd.ord_no
        where oob.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ogd.gds_nm like '%&#41;%'
        ]]>
    </select>

    <select id="countGdsNmWithSpecialCharsQuot" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_gds_d ogd
        inner join ez_or.or_ord_b oob on oob.ord_no = ogd.ord_no
        where oob.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ogd.gds_nm like '%&quot;%'
        ]]>
    </select>

    <select id="countGdsNmWithSpecialChars39" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_gds_d ogd
        inner join ez_or.or_ord_b oob on oob.ord_no = ogd.ord_no
        where oob.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ogd.gds_nm like '%&#39;%'
        ]]>
    </select>

    <select id="countGdsNmWithSpecialChars35" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_gds_d ogd
        inner join ez_or.or_ord_b oob on oob.ord_no = ogd.ord_no
        where oob.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ogd.gds_nm like '%&#35;%'
        ]]>
    </select>

    <select id="countGdsNmWithSpecialCharsAmp" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_gds_d ogd
        inner join ez_or.or_ord_b oob on oob.ord_no = ogd.ord_no
        where oob.ord_no > '5000000000'
          and oob.ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
          and ogd.gds_nm like '%&amp;%'
        ]]>
    </select>

    <!-- 처리 대상 주문 건수 조회 -->
    <select id="countTargetOrders" resultType="int">
        <![CDATA[
        select count(*)
        from ez_or.or_ord_b
        where ord_no > '5000000000'
          and ord_dtm between to_char(current_date - interval '2 days', 'YYYYMMDD') || '000000' and to_char(current_date, 'YYYYMMDD') || '235959'
        ]]>
    </select>

    <!-- 특정 주문번호의 주문상품요약명 조회 -->
    <select id="selectOrdGdsSmryNmByOrdNo" resultType="string">
        <![CDATA[
        select ord_gds_smry_nm
        from ez_or.or_ord_b
        where ord_no = #{ordNo}
        ]]>
    </select>

    <!-- 특정 주문번호의 상품명 조회 -->
    <select id="selectGdsNmByOrdNo" resultType="string">
        <![CDATA[
        select gds_nm
        from ez_or.or_ord_gds_d
        where ord_no = #{ordNo}
        limit 1
        ]]>
    </select>
</mapper>
