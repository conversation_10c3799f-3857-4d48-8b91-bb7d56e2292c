<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.aasample.test.mapper.query.UserQueryMapper">
    <select id="selectNtcStatByNtcId" resultType="String">
        SELECT ntc_stat
        FROM tble_ntc
        WHERE ntc_id = #{ntcId}
    </select>

    <select id="selectNtcByNtcTitl" resultType="com.ezwelesp.batch.aasample.test.domain.Test">
        SELECT *
        FROM tble_ntc
        WHERE ntc_titl LIKE '%'||#{ntcTitl}||'%'
        LIMIT #{_pagesize} OFFSET #{_skiprows}
    </select>
    
    <select id="selectMaxId" resultType="Long">
        SELECT MAX(ntc_id)
        FROM tble_ntc
    </select>

    <select id="selectNtcByStatus" resultType="com.ezwelesp.batch.aasample.test.domain.Test">
        SELECT *
        FROM tble_ntc
        WHERE ntc_stat = #{ntcStat}
        ORDER BY ntc_id
        LIMIT #{_pagesize} OFFSET #{_skiprows}
    </select>
</mapper>
