package com.ezwelesp.batch.lgportal.product.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgProductGetQnaListRequestDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgProductGetQnaListRequestDto {

    @Builder.Default
    private Integer page = 1;              // (필수) 페이지 번호
    @Builder.Default
    private Integer pageCount = 100000;         // (필수) 페이지 사이즈
    private String createdStartDate;   // (필수) 등록일 시작일
    private String createdEndDate;     // (필수) 등록일 종료일
    private String siteIDMatching;     // 사이트 아이디

    @Builder.Default
    private String statusCode = "02";         // 처리 현황
    private String memberName;         // 질문자명
    private String memberID;           // 질문자 ID
    private String title;              // 문의 제목
    private String answerer;           // 답변자명

}


