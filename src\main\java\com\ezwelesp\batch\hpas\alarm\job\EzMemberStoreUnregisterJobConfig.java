package com.ezwelesp.batch.hpas.alarm.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import com.ezwelesp.batch.hpas.alarm.tasklet.EzMemberStoreUnregisterTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class EzMemberStoreUnregisterJobConfig {
    private final EzMemberStoreUnregisterTasklet ezMemberStoreUnregisterTasklet;
    private final CommonJobListener commonJobListener;

    @Bean("ezMembersStoreUnregisterJob")
    public Job ezMembersStoreUnregisterJob(JobRepository jobRepository,
            @Qualifier("ezMembersStoreUnregisterStep") Step ezMembersStoreUnregisterStep) {
        return new JobBuilder("EzMembersStoreUnregisterJob", jobRepository)
                .listener(commonJobListener)
                .start(ezMembersStoreUnregisterStep)
                .build();
    }

    @Bean("ezMembersStoreUnregisterStep")
    public Step EzMembersStoreUnregisterStep(JobRepository jobRepository,
            DataSourceTransactionManager transactionManager) {
        return new StepBuilder("ezMembersStoreUnregisterStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(ezMemberStoreUnregisterTasklet, transactionManager)
                .build();

    }
}
