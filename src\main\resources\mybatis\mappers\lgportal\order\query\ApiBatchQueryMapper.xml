<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">

<mapper namespace="com.ezwelesp.batch.lgportal.order.mapper.query.ApiBatchQueryMapper">

    <!-- 교환/반품 완료 목록 조회 -->
    <select id="selectExchRtpCompleteList">
        SELECT
            api_no
            , api_chg_return_no
        FROM ez_if.api_batch_chg_return
        WHERE send_yn = 'N'
          AND send_key = '1018'
          AND reg_dt > TO_CHAR(NOW() - interval '3 days', 'YYYYMMDDHH24MISS')
        ORDER BY api_no
    </select>

    <!--
            상품판매상태코드(gds_sell_st_cd) 1001:등록대기, 1002:판매, 1003:변경대기, 1004:품절
            상품유형상세코드(gds_typ_dtl_cd) 1001:일반배송, 1010:확인안됨- WELFARRE.EZ_GOODS.DLV_FORM 배송형태 (공통코드 : 1047)
            채널코드(CH_CD = '100') '복지샵'
        -->
    <select id="selectRegCspDlvrList">
        SELECT
            csp_cd
             , csp_obnd_loc_no
             , dlvr_type_cd
             , dlvr_cost_type_cd
             , csp_dlvr_nm
             , tel_no
             , zip_cd
             , dlvr_addr
             , dlvr_addr_detail
             , base_setting_yn
             , dlvr_price
        FROM (SELECT gds.csp_cd
                   , gds.csp_obnd_loc_no
                   , '01' AS dlvr_type_cd
                   , (CASE WHEN gds.dlv_exp > 0 THEN '02' ELSE '01' END) AS dlvr_cost_type_cd
                   , rtp.csp_obnd_loc_nm AS csp_dlvr_nm
                   , rtp.obnd_loc_telno AS tel_no
                   , rtp.obnd_loc_zipcd AS zip_cd
                   , rtp.obnd_loc_bas_adr AS dlvr_addr
                   , rtp.obnd_loc_dtl_adr AS dlvr_addr_detail
                   , rtp.rps_adr_yn AS base_setting_yn
                   , gds.dlv_exp AS dlvr_price
              FROM ez_co.co_csp_b csp
                       INNER JOIN ez_co.co_csp_obnd_rtp_loc_b rtp ON (rtp.csp_cd = csp.csp_cd)
                       INNER JOIN ez_pd.pd_gds_c gds ON (gds.csp_obnd_loc_no = rtp.csp_obnd_loc_no
                  AND gds.gds_sell_st_cd IN ('1001', '1002', '1003', '1004')
                  AND gds.gds_typ_dtl_cd IN ('1001', '1010'))
              WHERE 1 = 1
                AND csp.lgp_api_use_yn = 'Y'
                AND csp.csp_st_cd = 'APV'
                AND csp.ch_cd = '100'
                AND NOT EXISTS (SELECT 'X'
                                FROM ez_if.api_batch_csp_dlvr api
                                WHERE api.csp_cd = gds.csp_cd::NUMERIC
                                  AND api.csp_dlvr_id = gds.csp_obnd_loc_no
                                  AND api.dlvr_price = gds.dlv_exp
                                  AND api.dlvr_type_cd = '01'
                                  AND api.send_yn = 'Y')
              GROUP BY gds.csp_cd
                     , gds.csp_obnd_loc_no
                     , gds.dlv_exp
                     , rtp.csp_obnd_loc_nm
                     , rtp.obnd_loc_telno
                     , rtp.obnd_loc_zipcd
                     , rtp.obnd_loc_bas_adr
                     , rtp.obnd_loc_dtl_adr
                     , rtp.rps_adr_yn
              UNION
              SELECT gds.csp_cd
                   , gds.csp_obnd_loc_no
                   , '02' AS dlvr_type_cd
                   , (CASE WHEN gds.rtp_dlv_exp > 0 THEN '02' ELSE '01' END) AS dlvr_cost_type_cd
                   , rtp.csp_obnd_loc_nm AS csp_dlvr_nm
                   , rtp.rtp_loc_telno AS tel_no
                   , rtp.rtp_loc_zipcd AS zip_cd
                   , rtp.rtp_loc_bas_adr AS dlvr_addr
                   , rtp.rtp_loc_dtl_adr AS dlvr_addr_detail
                   , rtp.rps_adr_yn AS base_setting_yn
                   , COALESCE(gds.rtp_dlv_exp, 0) * 2 AS dlvr_price
              FROM ez_co.co_csp_b csp
                       INNER JOIN ez_co.co_csp_obnd_rtp_loc_b rtp ON (rtp.csp_cd = csp.csp_cd)
                       INNER JOIN ez_pd.pd_gds_c gds ON (gds.csp_obnd_loc_no = rtp.csp_obnd_loc_no
                  AND gds.gds_sell_st_cd IN ('1001', '1002', '1003', '1004')
                  AND gds.gds_typ_dtl_cd IN ('1001', '1010'))
              WHERE 1 = 1
                AND csp.lgp_api_use_yn = 'Y'
                AND csp.csp_st_cd = 'APV'
                AND csp.ch_cd = '100'
                AND NOT EXISTS (SELECT 'X'
                                FROM ez_if.api_batch_csp_dlvr api
                                WHERE api.csp_cd = gds.csp_cd::NUMERIC
                                  AND api.csp_dlvr_id = gds.csp_obnd_loc_no
                                  AND api.dlvr_price = COALESCE(gds.rtp_dlv_exp, 0) * 2
                                  AND api.dlvr_type_cd = '02'
                                  AND api.send_yn = 'Y')
              GROUP BY gds.csp_cd
                     , gds.csp_obnd_loc_no
                     , gds.rtp_dlv_exp
                     , rtp.csp_obnd_loc_nm
                     , rtp.rtp_loc_telno
                     , rtp.rtp_loc_zipcd
                     , rtp.rtp_loc_bas_adr
                     , rtp.rtp_loc_dtl_adr
                     , rtp.rps_adr_yn
              UNION
              SELECT gds.csp_cd
                   , gds.csp_obnd_loc_no
                   , '03' AS dlvr_type_cd
                   , (CASE WHEN gds.exch_dlv_exp > 0 THEN '02' ELSE '01' END) AS dlvr_cost_type_cd
                   , rtp.csp_obnd_loc_nm AS csp_dlvr_nm
                   , rtp.rtp_loc_telno AS tel_no
                   , rtp.rtp_loc_zipcd AS zip_cd
                   , rtp.rtp_loc_bas_adr AS dlvr_addr
                   , rtp.rtp_loc_dtl_adr AS dlvr_addr_detail
                   , rtp.rps_adr_yn AS base_setting_yn
                   , COALESCE(gds.exch_dlv_exp, 0) * 2 AS dlvr_price
              FROM ez_co.co_csp_b csp
                       INNER JOIN ez_co.co_csp_obnd_rtp_loc_b rtp ON (rtp.csp_cd = csp.csp_cd)
                       INNER JOIN ez_pd.pd_gds_c gds ON (gds.csp_obnd_loc_no = rtp.csp_obnd_loc_no
                  AND gds.gds_sell_st_cd IN ('1001', '1002', '1003', '1004')
                  AND gds.gds_typ_dtl_cd IN ('1001', '1010'))
              WHERE 1 = 1
                AND csp.lgp_api_use_yn = 'Y'
                AND csp.csp_st_cd = 'APV'
                AND csp.ch_cd = '100'
                AND NOT EXISTS (SELECT 'X'
                                FROM ez_if.api_batch_csp_dlvr api
                                WHERE api.csp_cd = gds.csp_cd::NUMERIC
                                  AND api.csp_dlvr_id = gds.csp_obnd_loc_no
                                  AND api.dlvr_price = COALESCE(gds.exch_dlv_exp, 0) * 2
                                  AND api.dlvr_type_cd = '03'
                                  AND api.send_yn = 'Y')
              GROUP BY gds.csp_cd
                     , gds.csp_obnd_loc_no
                     , gds.exch_dlv_exp
                     , rtp.csp_obnd_loc_nm
                     , rtp.rtp_loc_telno
                     , rtp.rtp_loc_zipcd
                     , rtp.rtp_loc_bas_adr
                     , rtp.rtp_loc_dtl_adr
                     , rtp.rps_adr_yn
             )
    </select>
    <!--
    <select id="selectTargetCspDlvrList">
        SELECT
            csp_cd
             , csp_obnd_loc_no
        FROM (SELECT gds.csp_cd
                   , gds.csp_obnd_loc_no
              FROM ez_co.co_csp_b csp
                       INNER JOIN ez_co.co_csp_obnd_rtp_loc_b rtp ON (rtp.csp_cd = csp.csp_cd)
                       INNER JOIN ez_pd.pd_gds_c gds ON (gds.csp_obnd_loc_no = rtp.csp_obnd_loc_no
                  AND gds.gds_sell_st_cd IN ('1001', '1002', '1003', '1004')
                  AND gds.gds_typ_dtl_cd IN ('1001', '1010'))
              WHERE 1 = 1
                AND csp.lgp_api_use_yn = 'Y'
                AND csp.csp_st_cd = 'STBY'
                AND csp.ch_cd = 'HEWS'
                AND NOT EXISTS (SELECT 'X'
                                FROM ez_if.api_batch_csp_dlvr api
                                WHERE api.csp_cd = gds.csp_cd::NUMERIC
                                  AND api.csp_dlvr_id = gds.csp_obnd_loc_no
                                  AND api.dlvr_price = gds.dlv_exp
                                  AND api.dlvr_type_cd = '01'
                                  AND api.send_yn = 'Y')
              GROUP BY gds.csp_cd, gds.csp_obnd_loc_no
              UNION
              SELECT gds.csp_cd
                   , gds.csp_obnd_loc_no
              FROM ez_co.co_csp_b csp
                       INNER JOIN ez_co.co_csp_obnd_rtp_loc_b rtp ON (rtp.csp_cd = csp.csp_cd)
                       INNER JOIN ez_pd.pd_gds_c gds ON (gds.csp_obnd_loc_no = rtp.csp_obnd_loc_no
                  AND gds.gds_sell_st_cd IN ('1001', '1002', '1003', '1004')
                  AND gds.gds_typ_dtl_cd IN ('1001', '1010'))
              WHERE 1 = 1
                AND csp.lgp_api_use_yn = 'Y'
                AND csp.csp_st_cd = 'STBY'
                AND csp.ch_cd = 'HEWS'
                AND NOT EXISTS (SELECT 'X'
                                FROM ez_if.api_batch_csp_dlvr api
                                WHERE api.csp_cd = gds.csp_cd::NUMERIC
                                  AND api.csp_dlvr_id = gds.csp_obnd_loc_no
                                  AND api.dlvr_price = COALESCE(gds.rtp_dlv_exp, 0) * 2
                                  AND api.dlvr_type_cd = '02'
                                  AND api.send_yn = 'Y')
              GROUP BY gds.csp_cd, gds.csp_obnd_loc_no
              UNION
              SELECT gds.csp_cd
                   , gds.csp_obnd_loc_no
              FROM ez_co.co_csp_b csp
                       INNER JOIN ez_co.co_csp_obnd_rtp_loc_b rtp ON (rtp.csp_cd = csp.csp_cd)
                       INNER JOIN ez_pd.pd_gds_c gds ON (gds.csp_obnd_loc_no = rtp.csp_obnd_loc_no
                  AND gds.gds_sell_st_cd IN ('1001', '1002', '1003', '1004')
                  AND gds.gds_typ_dtl_cd IN ('1001', '1010'))
              WHERE 1 = 1
                AND csp.lgp_api_use_yn = 'Y'
                AND csp.csp_st_cd = 'STBY'
                AND csp.ch_cd = 'HEWS'
                AND NOT EXISTS (SELECT 'X'
                                FROM ez_if.api_batch_csp_dlvr api
                                WHERE api.csp_cd = gds.csp_cd::NUMERIC
                                  AND api.csp_dlvr_id = gds.csp_obnd_loc_no
                                  AND api.dlvr_price = COALESCE(gds.exch_dlv_exp, 0) * 2
                                  AND api.dlvr_type_cd = '03'
                                  AND api.send_yn = 'Y')
              GROUP BY gds.csp_cd, gds.csp_obnd_loc_no)
        LIMIT 1000
    </select>-->

    <!-- 배송완료 처리 대상 리스트 조회 -->
    <select id="selectDeliveryCompleteList">
        SELECT
            dlvr.api_seq
             ,dlvr.delivery_no
             ,dlvr.delivery_item
        FROM ez_if.api_batch_dlvr dlvr
                 INNER JOIN ( SELECT api_dlvr_cd FROM ez_if.api_dlvr_mapping WHERE dlvr_trace_yn = 'N' GROUP BY api_dlvr_cd) mapp on (mapp.api_dlvr_cd = dlvr.logistics_no)
                 INNER JOIN ez_or.dl_dlv_gds_d dlvGds on (dlvGds.lgp_api_dlv_dtl_no = CAST(dlvr.delivery_item AS VARCHAR))
                 INNER JOIN ez_or.dl_dlv_b dlvb on (dlvb.dlv_no = dlvGds.dlv_no
            AND dlvb.dlv_st_cd = 'CMPT')
                 INNER JOIN ez_or.or_ord_b ord ON (dlvb.ord_no = ord.ord_no
            AND ord.ord_dtm >= TO_CHAR(NOW() - interval '90 days', 'YYYYMMDDHH24MISS'))
        WHERE dlvr.send_key = '1009'
          AND NOT EXISTS (
            SELECT 'X' from ez_if.api_batch_dlvr sub
            WHERE sub.delivery_item = dlvr.delivery_item
              AND sub.SEND_KEY = '1010'
        )
    </select>

    <!-- 송장정보 등록 대상 리스트 조회 -->
    <select id="selectRegInvoiceNoList">
        SELECT api_seq
             , send_key
             , send_yn
             , delivery_no
             , delivery_item
             , invoice_no
             , logistics_no
             , return_code
             , return_message
             , reg_dt
             , reg_id
        FROM
            ez_if.api_batch_dlvr
        WHERE send_yn = 'N'
          AND send_key = '1009'
          and substring(reg_dt from 1 for 8) >= TO_CHAR(NOW() - interval '7 days', 'yyyymmdd')
    </select>

    <select id="selectApiBatchGoods">
        SELECT
            target_goods_cd
        FROM ez_if.api_batch_goods
        WHERE 1 = 1
          AND target_div = 'LGCNS'
          AND goods_cd = #{gdsCd}
    </select>

    <!--
        품절 취소 처리 목록 조회
         - AS-IS 에서는 주문상품의 LG포털API배송번호(lgp_api_dlv_no), LG포털API배송상세번호(lgp_api_dlv_dtl_no)와 조인해서 조회하지만
           품절처리된 전체 상품에 대해서 LG포털에서 주문취소해햐할 것으로 판단되어 변경
    -->
    <select id="selectOrderCancelGoodsOstkList" flushCache="true">
        SELECT
            api_seq
             , delivery_no
             , delivery_item_no
             , cancel_qty
        FROM ez_if.api_batch_send
        WHERE reg_dt >= (TO_CHAR(NOW() - interval '7 days', 'YYYYMMDD') ||'000000')
          AND send_yn = 'N'
          AND send_key = '1016'
    </select>

</mapper>
