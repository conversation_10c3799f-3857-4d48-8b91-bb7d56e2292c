<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.send.mapper.query.SendGoodsProcessQueryMapper">

    <select id="selectSendGoodsTarget" resultType="com.ezwelesp.batch.hims.order.send.domain.SendGoodsBaseDto" flushCache="true">
        select dosb.rcvr_nm
              , dosb.rcvr_mbl_telno
              , dosgd.ord_no
              , dosgd.ord_snd_seq
              , dosgd.ord_gds_seq
              , dosgd.ord_snd_gds_dtl_seq
              , dosgd.csp_cd
              , dosgd.rdno_snd_magn_cd
              , dosgd.tmsg_snd_knd_cd
              , dosgd.intg_gds_rdno_no
              , oogd.gds_cd
              , oogd.gds_nm
              , oogd.ord_gds_opt_cntn
              , oogd.csp_gds_cd
              , oogd.opt_gds_comb_seq
              , oob.ordr_nm
              , oob.clnt_cd
              , piistb.intg_gds_infm_snd_typ_cntn
              , pirb.bcd_exps_yn
              , pirb.bcd_typ_cd
              , case when dosgd.rdno_snd_magn_cd = '01' then 0
                      else (select count(1) from ez_or.dl_ord_snd_gds_d where ord_no = dosgd.ord_no and csp_cd = dosgd.csp_cd and ord_snd_st_cd not in ('07', '09'))
                  end as api_call_count
          from ez_or.dl_ord_snd_gds_d dosgd
          join ez_or.or_ord_b oob on oob.ord_no = dosgd.ord_no
          join ez_or.dl_ord_snd_b dosb on dosb.ord_no = dosgd.ord_no and dosb.ord_snd_seq = dosgd.ord_snd_seq
          join ez_or.or_ord_gds_d oogd on oogd.ord_no = dosgd.ord_no and oogd.ord_gds_seq = dosgd.ord_gds_seq
          join ez_pd.pd_gds_c pgc on pgc.gds_cd = oogd.gds_cd
          join ez_pd.pd_ingd_b pib on pib.gds_cd = pgc.gds_cd
          left join ez_pd.pd_ingd_rdno_b pirb on pirb.gds_cd = oogd.gds_cd and pirb.intg_gds_rdno_pblc_nos = dosgd.intg_gds_rdno_pblc_nos
          left join ez_pd.pd_ingd_infm_snd_typ_b piistb on piistb.intg_gds_infm_snd_typ_seq = dosgd.intg_gds_infm_snd_typ_seq
         where dosgd.ord_snd_st_cd = '01' /* 발송대기 */
           and oob.ord_st_cd = 'ORD_CMPT'
           and ((pib.intg_gds_typ_cd = 'RSV' and pgc.gds_typ_dtl_cd = '2002') or (pib.intg_gds_typ_cd = 'SND' and pgc.gds_typ_dtl_cd in ('2001', '2002', '2003')))
        -- and dosgd.rdno_snd_mthd_cd = 'AT' /* 자동 */
        -- and dosgd.rdno_crt_yn = 'Y' /* 난수생성여부 */
          and dosgd.intg_gds_rdno_no is not null
          and dosgd.snd_dtm is null
          and dosgd.snd_rsv_dtm <![CDATA[<]]> to_char(now(), 'YYYYMMDDHH24MISS')
        limit 1000
    </select>
</mapper>
