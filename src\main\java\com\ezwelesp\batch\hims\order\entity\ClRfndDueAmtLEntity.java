package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 환불예정금액내역(ez_or.cl_rfnd_due_amt_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClRfndDueAmtLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 환불대기결제수단내역순번(rfnd_stby_pymt_mns_lst_seq) not null
     */
    private Long rfndStbyPymtMnsLstSeq;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 결제수단코드(pymt_mns_cd)
     */
    private String pymtMnsCd;

    /**
     * 환불금액(rfnd_amt) not null
     */
    private BigDecimal rfndAmt;

    /**
     * 복지제도포인트구분코드(wsp_div_cd)
     */
    private String wspDivCd;

    /**
     * 복지제도포인트코드(wsp_cd)
     */
    private String wspCd;

    /**
     * 주문쿠폰번호(ord_cpn_no)
     */
    private Long ordCpnNo;

    /**
     * 쿠폰사용금액(cpn_use_amt) not null
     */
    private BigDecimal cpnUseAmt;

    /**
     * 배송비용환불대상코드(dlv_exp_rfnd_obj_cd)
     */
    private String dlvExpRfndObjCd;

    /**
     * 배송비용(dlv_exp) not null
     */
    private BigDecimal dlvExp;

    /**
     * 클레임환불완료일시(clm_rfnd_cmpt_dtm)
     */
    private String clmRfndCmptDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
