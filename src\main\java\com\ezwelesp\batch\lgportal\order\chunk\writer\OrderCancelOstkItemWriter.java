package com.ezwelesp.batch.lgportal.order.chunk.writer;

import com.ezwelesp.batch.lgportal.entity.ApiBatchSendEntity;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.OrderCancelOstkReqDto;
import com.ezwelesp.batch.lgportal.order.mapper.command.ApiBatchCommandMapper;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 품절로 인한 주문취소 처리
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order
 * @since 2025.03.05
 */
@Component
public class OrderCancelOstkItemWriter implements ItemWriter<ApiBatchSendEntity> {

    @Value("${lgportal.server.host}")
    private String serverHost;

    @Value("${lgportal.server.timeout}")
    private int timeout;

    @Value("${lgportal.api-key}")
    private String apiKey;



    private final SqlSessionTemplate sqlSessionTemplate;

    /**
     * Instantiates a new Delivery complete item writer.
     *
     * @param sqlSessionFactory the sql session factory
     */
    public OrderCancelOstkItemWriter(@Qualifier("primaryCommandSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory);
    }

    @Override
    public void write(Chunk<? extends ApiBatchSendEntity> chunks) {
        chunks.forEach(apiBatchSendEntity ->
                this.sqlSessionTemplate.update(ApiBatchCommandMapper.class.getName() + ".updateApiBatchSend",
                        this.orderCancelOstk(apiBatchSendEntity)));
    }

    private ApiBatchSendEntity orderCancelOstk(ApiBatchSendEntity entity) {

        OrderCancelOstkReqDto orderCancelOstkReqDto = new OrderCancelOstkReqDto(this.apiKey);
        orderCancelOstkReqDto.setSaleOrderNo(entity.getDeliveryNo());

        orderCancelOstkReqDto.setProducts(Collections.singletonList(OrderCancelOstkReqDto.OrderGoods.builder()
                .orderItemNo(entity.getDeliveryItemNo())
                .requestCnt(entity.getCancelQty())
                .build()));

        try {
            AjaxResult result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_SOLDOUT_CANCEL,
                    this.timeout, JsonObjectConverter.serialize(orderCancelOstkReqDto));

            return ApiBatchSendEntity.builder()
                    .apiSeq(entity.getApiSeq())
                    .sendYn(StringUtils.equals("200", result.get("result").toString()) ? "Y" : "N")
                    .errorMessage(result.get("resultMessage").toString())
                    .build();

        } catch (JsonProcessingException e) {
            throw new RuntimeException("LG portal api request failed");
        }
    }
}
