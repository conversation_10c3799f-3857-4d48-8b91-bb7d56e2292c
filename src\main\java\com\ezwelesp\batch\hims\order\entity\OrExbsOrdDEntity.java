package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 고속버스주문상세(ez_or.or_exbs_ord_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrExbsOrdDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 제휴사주문번호(asp_ord_no) not null
     */
    private String aspOrdNo;

    /**
     * 고속버스예약구분코드(exbs_rsv_div_cd) not null
     */
    private String exbsRsvDivCd;

    /**
     * 고속버스배차예매번호(exbs_aoc_advtc_no)
     */
    private String exbsAocAdvtcNo;

    /**
     * 정상가격(nrml_prc) not null
     */
    private BigDecimal nrmlPrc;

    /**
     * 할인금액(dc_amt) not null
     */
    private BigDecimal dcAmt;

    /**
     * 판매가격(sell_prc) not null
     */
    private BigDecimal sellPrc;

    /**
     * 소요시간분수(rqtm_micnt)
     */
    private Integer rqtmMicnt;

    /**
     * 출발일자(dptr_dt)
     */
    private String dptrDt;

    /**
     * 출발시각(dptr_time)
     */
    private String dptrTime;

    /**
     * 고속버스배차출발일자(exbs_aoc_dptr_dt)
     */
    private String exbsAocDptrDt;

    /**
     * 고속버스배차출발시각(exbs_aoc_dptr_time)
     */
    private String exbsAocDptrTime;

    /**
     * 출발고속버스터미널코드(dptr_exbstm_cd)
     */
    private String dptrExbstmCd;

    /**
     * 도착고속버스터미널코드(arvl_exbstm_cd)
     */
    private String arvlExbstmCd;

    /**
     * 배차출발고속버스터미널코드(aoc_dptr_exbstm_cd)
     */
    private String aocDptrExbstmCd;

    /**
     * 배차도착고속버스터미널코드(aoc_arvl_exbstm_cd)
     */
    private String aocArvlExbstmCd;

    /**
     * 고속버스등급코드(exbs_grd_cd)
     */
    private String exbsGrdCd;

    /**
     * 고속버스회사코드(exbs_co_cd)
     */
    private String exbsCoCd;

    /**
     * 승차홈번호목록값(bplfm_no_lval)
     */
    private String bplfmNoLval;

    /**
     * QR코드생성여부(qrcode_crt_yn) not null
     */
    private String qrcodeCrtYn;

    /**
     * 고속버스주문할인여부(exbs_ord_dc_yn) not null
     */
    private String exbsOrdDcYn;

    /**
     * 고속버스배차구분코드(exbs_aoc_div_cd)
     */
    private String exbsAocDivCd;

    /**
     * 고속버스경유여부(exbs_thrgh_yn) not null
     */
    private String exbsThrghYn;

    /**
     * 고속버스배차노선번호(exbs_aoc_rout_no)
     */
    private String exbsAocRoutNo;

    /**
     * 고속버스배차예약구분코드(exbs_aoc_rsv_div_cd)
     */
    private String exbsAocRsvDivCd;

    /**
     * 고속버스할인구분코드(exbs_dc_div_cd)
     */
    private String exbsDcDivCd;

    /**
     * 고속버스할인종류코드(exbs_dc_knd_cd)
     */
    private String exbsDcKndCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
