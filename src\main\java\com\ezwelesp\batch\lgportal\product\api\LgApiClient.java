package com.ezwelesp.batch.lgportal.product.api;

import com.ezwelesp.batch.lgportal.product.domain.dto.*;
import com.ezwelesp.batch.lgportal.product.service.LgPortalJsonHelper;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import static com.ezwelesp.batch.lgportal.product.domain.enums.LgApiEndpoint.*;


@Slf4j
@Component
@RequiredArgsConstructor
public class LgApiClient {



    private final RestTemplate restTemplate;
    private final LgPortalJsonHelper lgPortalJsonHelper;

    @Value("${lgportal.server.host}")
    private String serverHost;

    @Value("${lgportal.server.timeout}")
    private int timeout;

    @Value("${lgportal.api-key}")
    private String apiKey;

    @Value("${lgportal.cert-key}")
    private String certKey;


    @Configuration
    public static class RestTemplateConfig {
        @Bean
        public RestTemplate restTemplate() {
            return new RestTemplate();
        }
    }

    // 상품 등록 API
    public LgProductApiResponseDto sendLgAddProductApi(
            @NonNull LgAddProductRequestDto request
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_ADD_PRODUCT.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                request.getProductMappingCode(),
                null
        );
    }

    // 상품 고시정보 등록/수정 API
    public LgProductApiResponseDto sendLgAddProductAnnouncementApi(
            @NonNull LgProductAnnouncementDto request
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_ADD_PRODUCT_GOSI_INFO.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                request.getProductMappingCode(),
                null
        );


    }

    public LgProductApiResponseDto sendLgAddProductAnnouncementApi(
            @NonNull LgProductAnnouncementDto request,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_ADD_PRODUCT_GOSI_INFO.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                request.getProductMappingCode(),
                apiSeq
        );
    }

    // 상품 삭제 API
    public LgProductApiResponseDto sendLgDeleteProductApi(
            @NonNull LgProductApiResponseDto request
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_DELETE_PRODUCT.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                request.getGdsCd(),
                null
        );
    }

    // 상품 기본정보 수정 API
    public LgProductApiResponseDto sendLgUpdateProductBasicApi(
            @NonNull LgUpdateProductBasicRequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_UPDATE_BASIC_PRODUCT.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );

    }

    // 상품 콘텐츠 수정 API
    public LgProductApiResponseDto sendLgUpdateProductContentsApi(
            @NonNull LgUpdateProductContentsRequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_UPDATE_PRODUCT_CONTENTS.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }

    // 상품 배송 및 교환반품 정보 수정 API
    public LgProductApiResponseDto sendLgUpdateProductDeliveryApi(
            @NonNull LgUpdateProductDeliveryRequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq

    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_UPDATE_PRODUCT_DELIVERY.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }

    // 상품 Image 수정 API
    public LgProductApiResponseDto sendLgUpdateProductImageApi(
            @NonNull LgUpdateProductImageRequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq

    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_UPDATE_PRODUCT_IMAGE.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }

    //  상품 QnA 답변 등록
    public LgProductApiResponseDto sendLgUpdateProductQnaAnswerApi(
            @NonNull LgProductQnaAnswerRequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_SAVE_QNA_ANSWER.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }

    //  상품 QnA 자동 답변 등록
    public LgProductApiResponseDto sendLgUpdateProductQnaAutoAnswerApi(
            @NonNull LgProductQnaAnswerRequestDto request,
            @NonNull String gdsCd
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_SAVE_QNA_ANSWER.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                null
        );
    }


    //  상품 가격 수정
    public LgProductApiResponseDto sendLgUpdateProductPriceApi(
            @NonNull LgUpdateProductPriceRequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_UPDATE_PRODUCT_PRICE.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }

    //  상품 품절 상태로 변경
    public LgProductApiResponseDto sendLgUpdateProductSoldOutApi(
            @NonNull LgUpdateProductSoldOutRequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_UPDATE_PRODUCT_SOLDOUT.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }

    //  상품 판매 상태로 변경
    // soldOut 하고 동일한 RequestDto 사용
    public LgProductApiResponseDto sendLgUpdateProductSaleApi(
            @NonNull LgUpdateProductSoldOutRequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_UPDATE_PRODUCT_SALE.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }


    //  상품 SKU 초기화
    public LgProductApiResponseDto sendLgProductSKUResetApi(
            @NonNull LgUpdateProductSKUResetRequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_SKU_STOCK_RESET.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }

    //  상품 SKU 등록
    public LgProductApiResponseDto sendLgProductSKUAddApi(
            @NonNull LgProductSKURequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_ADD_SKU.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }


    //  상품 SKU 수정
    public LgProductApiResponseDto sendLgProductSKUEditApi(
            @NonNull LgProductSKURequestDto request,
            @NonNull String gdsCd,
            @NonNull Integer apiSeq
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_UPDATE_SKU.getUrl(),
                request
        );

        return lgPortalJsonHelper.parseLgApiProductResponse(
                responseSting,
                gdsCd,
                apiSeq
        );
    }

    //  상품 QnA 리스트 가지고 오기
    public LgProductQnaResponseDto sendLgProductGetQnaListApi(
            @NonNull LgProductGetQnaListRequestDto request
    ) {
        val responseSting = externalLGPortalProductCall(
                LG_API_GET_QNA_LIST.getUrl(),
                request
        );
        return lgPortalJsonHelper.parseLgProductQnaResponse(
                responseSting
        );
    }


    // POST 요청 공통 로직
    // QnA 전용 호출: gdsCd, apiSeq 없이 호출 가능
    private <T> String externalLGPortalProductCall(
            String targetUrl,
            T requestObject
    ) throws RuntimeException {

        val requestJsonString = lgPortalJsonHelper.addLgApiKeysToString(apiKey, requestObject);

        try {

            // 주문팀과 동일한 API 타도록 수정
            val result = RestClientUtil.requestApi(
                    this.serverHost + targetUrl
                    , this.timeout
                    , requestJsonString
            );

            return result.toString();

        } catch (RuntimeException e) {
            log.error("externalLGPortalProductCall RuntimeException {}: {}", requestJsonString, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("externalLGPortalProductCall failed {}: {}", requestJsonString, e.getMessage());
            throw new IllegalArgumentException("externalLGPortalProductCall failed", e);
        }
    }

}
