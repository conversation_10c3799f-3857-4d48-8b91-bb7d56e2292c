package com.ezwelesp.batch.hims.order.goodsflow.domain;

import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class GoodsFlowSendTraceRequestDetailDto {
    private String orderNo;
    private int orderLine;
    private String itemCode;
    private String itemName;
    private String itemOption;
    private String itemQty;
    private String itemPrice;
    private String orderDate;
    private String paymentDate;
}
