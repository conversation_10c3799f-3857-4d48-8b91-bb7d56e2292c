package com.ezwelesp.batch.hims.order.receipt.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class DeliveryExpenseDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;


    private String cspCd;

    private String clmNo;

    /**
     * 배송비용번호(dlv_exp_no) not null
     */
    private Long dlvExpNo;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 협력사출고위치번호(csp_obnd_loc_no)
     */
    private String cspObndLocNo;

    /**
     * 배송정책순번(dlv_plcy_seq)
     */
    private Long dlvPlcySeq;

    /**
     * 원본배송비용번호(orgl_dlv_exp_no)
     */
    private Long orglDlvExpNo;

    /**
     * 배송비용변경대상클레임번호(dlv_exp_chg_obj_clm_no)
     */
    private String dlvExpChgObjClmNo;

    /**
     * 조건부무료배송여부(cndl_nchg_dlv_yn) not null
     */
    private String cndlNchgDlvYn;

    /**
     * 무료배송적용결제금액(nchg_dlv_aply_pymt_amt) not null
     */
    private BigDecimal nchgDlvAplyPymtAmt;

    /**
     * 설정배송비용(stup_dlv_exp) not null
     */
    private BigDecimal stupDlvExp;

    /**
     * 배송비용(dlv_exp) not null
     */
    private BigDecimal dlvExp;

    /**
     * 할인쿠폰배송비용(dc_cpn_dlv_exp) not null
     */
    private BigDecimal dcCpnDlvExp;

    /**
     * 취소할인쿠폰배송비용(cncl_dc_cpn_dlv_exp) not null
     */
    private BigDecimal cnclDcCpnDlvExp;

    /**
     * 제주도추가배송비용(jeju_add_dlv_exp) not null
     */
    private BigDecimal jejuAddDlvExp;

    /**
     * 도서산간추가배송비용(ismt_add_dlv_exp) not null
     */
    private BigDecimal ismtAddDlvExp;

    /**
     * 취소이후발생배송비용(cncl_aft_ocrn_dlv_exp) not null
     */
    private BigDecimal cnclAftOcrnDlvExp;

    /**
     * 착불송금배송비용(arpay_rmtt_dlv_exp) not null
     */
    private BigDecimal arpayRmttDlvExp;

    /**
     * 현대이지웰부담배송비용(ezwl_budn_dlv_exp) not null
     */
    private BigDecimal ezwlBudnDlvExp;

    /**
     * 배송비용부담주체코드(dlv_exp_budn_magn_cd)
     */
    private String dlvExpBudnMagnCd;

    /**
     * 쿠폰번호(cpn_no)
     */
    private String cpnNo;

    /**
     * 쿠폰사용상세번호(usr_cpn_no)
     */
    private Long usrCpnNo;

    /**
     * 쿠폰사용취소일시(cpn_use_cncl_dtm)
     */
    private String cpnUseCnclDtm;


}
