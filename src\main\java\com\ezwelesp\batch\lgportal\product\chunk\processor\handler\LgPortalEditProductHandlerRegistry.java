package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import com.ezwelesp.batch.lgportal.product.domain.enums.lgPortalSendKey;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class LgPortalEditProductHandlerRegistry {

    private final Map<lgPortalSendKey, LgPortalEditProductProcessorHandler> handlerMap = new HashMap<>();

    public void registerHandler(lgPortalSendKey sendKey, LgPortalEditProductProcessorHandler handler) {
        handlerMap.put(sendKey, handler);
    }

    public LgPortalEditProductProcessorHandler getHandler(lgPortalSendKey sendKey) {
        return handlerMap.get(sendKey);
    }
}
