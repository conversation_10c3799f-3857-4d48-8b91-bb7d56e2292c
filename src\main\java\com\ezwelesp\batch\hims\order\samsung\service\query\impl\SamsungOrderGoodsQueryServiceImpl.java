package com.ezwelesp.batch.hims.order.samsung.service.query.impl;

import com.ezwelesp.batch.hims.order.samsung.mapper.query.SamsungOrderGoodsQueryMapper;
import com.ezwelesp.batch.hims.order.samsung.service.query.SamsungOrderGoodsQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@RequiredArgsConstructor
@Service
public class SamsungOrderGoodsQueryServiceImpl implements SamsungOrderGoodsQueryService {

    private final SamsungOrderGoodsQueryMapper samsungOrderGoodsQueryMapper;

    @Override
    @Transactional(value = "primaryQueryTransactionManager", readOnly = true)
    public int getOrdGdsSmryNmWithSpecialCharsCount() {
        int count40 = samsungOrderGoodsQueryMapper.countOrdGdsSmryNmWithSpecialChars40();
        int count41 = samsungOrderGoodsQueryMapper.countOrdGdsSmryNmWithSpecialChars41();
        int countQuot = samsungOrderGoodsQueryMapper.countOrdGdsSmryNmWithSpecialCharsQuot();
        int count39 = samsungOrderGoodsQueryMapper.countOrdGdsSmryNmWithSpecialChars39();
        int count35 = samsungOrderGoodsQueryMapper.countOrdGdsSmryNmWithSpecialChars35();
        int countAmp = samsungOrderGoodsQueryMapper.countOrdGdsSmryNmWithSpecialCharsAmp();
        
        int totalCount = count40 + count41 + countQuot + count39 + count35 + countAmp;
        
        log.info("주문기본 특수문자 포함 건수 - &#40;: {}, &#41;: {}, &quot;: {}, &#39;: {}, &#35;: {}, &amp;: {}, 총합: {}", 
                count40, count41, countQuot, count39, count35, countAmp, totalCount);
        
        return totalCount;
    }

    @Override
    @Transactional(value = "primaryQueryTransactionManager", readOnly = true)
    public int getGdsNmWithSpecialCharsCount() {
        int count40 = samsungOrderGoodsQueryMapper.countGdsNmWithSpecialChars40();
        int count41 = samsungOrderGoodsQueryMapper.countGdsNmWithSpecialChars41();
        int countQuot = samsungOrderGoodsQueryMapper.countGdsNmWithSpecialCharsQuot();
        int count39 = samsungOrderGoodsQueryMapper.countGdsNmWithSpecialChars39();
        int count35 = samsungOrderGoodsQueryMapper.countGdsNmWithSpecialChars35();
        int countAmp = samsungOrderGoodsQueryMapper.countGdsNmWithSpecialCharsAmp();
        
        int totalCount = count40 + count41 + countQuot + count39 + count35 + countAmp;
        
        log.info("주문상품상세 특수문자 포함 건수 - &#40;: {}, &#41;: {}, &quot;: {}, &#39;: {}, &#35;: {}, &amp;: {}, 총합: {}", 
                count40, count41, countQuot, count39, count35, countAmp, totalCount);
        
        return totalCount;
    }

    @Override
    @Transactional(value = "primaryQueryTransactionManager", readOnly = true)
    public int getTargetOrdersCount() {
        int count = samsungOrderGoodsQueryMapper.countTargetOrders();
        log.info("처리 대상 주문 건수 (최근 2일): {}", count);
        return count;
    }

    @Override
    @Transactional(value = "primaryQueryTransactionManager", readOnly = true)
    public String getOrdGdsSmryNmByOrdNo(String ordNo) {
        return samsungOrderGoodsQueryMapper.selectOrdGdsSmryNmByOrdNo(ordNo);
    }

    @Override
    @Transactional(value = "primaryQueryTransactionManager", readOnly = true)
    public String getGdsNmByOrdNo(String ordNo) {
        return samsungOrderGoodsQueryMapper.selectGdsNmByOrdNo(ordNo);
    }

    @Override
    @Transactional(value = "primaryQueryTransactionManager", readOnly = true)
    public boolean hasDataToProcess() {
        int ordGdsSmryNmCount = getOrdGdsSmryNmWithSpecialCharsCount();
        int gdsNmCount = getGdsNmWithSpecialCharsCount();
        
        boolean hasData = ordGdsSmryNmCount > 0 || gdsNmCount > 0;
        log.info("처리할 데이터 존재 여부: {}", hasData);
        
        return hasData;
    }
}
