package com.ezwelesp.batch.lgportal.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * 결제기본
 * py_pymt_b
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyPymtTmpEntity implements Serializable {
    /**
     * 결제번호
     */
    private String pymtNo;

    /**
     * 원본결제번호
     */
    private String orglPymtNo;

    /**
     * 주문번호
     */
    private String ordNo;

    /**
     * 클레임번호
     */
    private String clmNo;

    /**
     * PG가맹점번호
     */
    private String pgFrcsNo;

    /**
     * PG승인번호
     */
    private String pgApvNo;

    /**
     * 배송비용결제여부
     */
    private String dlvExpPymtYn;

    /**
     * 결제구분코드
     */
    private String pymtDivCd;

    /**
     * 결제차수
     */
    private Integer pymtNos;

    /**
     * 결제금액
     */
    private Long pymtAmt;

    /**
     * 결제상태코드
     */
    private String pymtStCd;

    /**
     * 결제수단코드
     */
    private String pymtMnsCd;

    /**
     * 결제일시
     */
    private String pymtDtm;

    /**
     * 결제취소환불일시
     */
    private String pymtCnclRfndDtm;

    /**
     * 복지제도포인트구분코드
     */
    private String wspDivCd;

    /**
     * 특별포인트복지제도포인트코드
     */
    private String sppWspCd;

    /**
     * 현금영수증발행대상여부
     */
    private String csrcPblcObjYn;

    /**
     * 결제파라미터내용J
     */
    private Object pymtParaCntnj;

    /**
     * 결제처리메모
     */
    private String pymtPrcsMemo;

    /**
     * 배송비용결제취소메모
     */
    private String dlvExpPymtCnclMemo;

    /**
     * 배송비용결제메모
     */
    private String dlvExpPymtMemo;

    /**
     * 우리모아포인트승인번호
     */
    private Long wmpntApvNo;

    /**
     * 카드사명
     */
    private String crdcNm;

    /**
     * 카드종류코드
     */
    private String crdKndCd;

    /**
     * 암호화카드번호
     */
    private String encCrdNo;

    /**
     * 카드사승인번호
     */
    private String crdcApvNo;

    /**
     * 카드결제무이자할부여부
     */
    private String crdPymtWintInstYn;

    /**
     * 카드할부개월수
     */
    private Integer crdInstMcnt;

    /**
     * 카드결제부분취소가능여부
     */
    private String crdPymtInpaPossYn;

    /**
     * 결제카드대체번호
     */
    private String pymtCrdRplcNo;

    /**
     * 간편결제종류코드
     */
    private String smpyKndCd;

    /**
     * 네이버페이승인번호
     */
    private String nvpayApvNo;

    /**
     * 금융기관코드
     */
    private String fnnsCd;

    /**
     * 배송비용환불대상코드
     */
    private String dlvExpRfndObjCd;

    /**
     * 코나아이주문번호
     */
    private String konaiOrdNo;

    /**
     * 코나아이거래일시
     */
    private String konaiTrdDtm;

    /**
     * 포인트제도마감일자
     */
    private String pntRgmClsgDt;

    /**
     * 제도마감이후포인트소멸금액
     */
    private BigDecimal rgmClsgAftPntExtnAmt;

    /**
     * E쿠폰협력사연동결과코드
     */
    private String ecpnCspIntlRsltCd;

    /**
     * E쿠폰복지제도포인트우선차감일시
     */
    private String ecpnWspPfrSbtrDtm;

    /**
     * E쿠폰복지제도포인트확정차감일시
     */
    private String ecpnWspCnfmSbtrDtm;

    /**
     * 최초등록일시
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID
     */
    private String lastModPgmId;

    private static final long serialVersionUID = 1L;
}
