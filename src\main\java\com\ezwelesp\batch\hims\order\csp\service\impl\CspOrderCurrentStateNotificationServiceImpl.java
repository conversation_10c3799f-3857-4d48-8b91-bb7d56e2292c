package com.ezwelesp.batch.hims.order.csp.service.impl;

import com.ezwelesp.batch.hims.order.csp.domain.CspOrderCurrentStateDto;
import com.ezwelesp.batch.hims.order.csp.mapper.query.CspOrderCurrentStateNotificationQueryMapper;
import com.ezwelesp.batch.hims.order.csp.service.CspOrderCurrentStateNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class CspOrderCurrentStateNotificationServiceImpl implements CspOrderCurrentStateNotificationService {
    private final CspOrderCurrentStateNotificationQueryMapper cspOrderCurrentStateNotificationQueryMapper;

    @Override
    public List<CspOrderCurrentStateDto> getCspOrderCurrentSateList() {
        return this.cspOrderCurrentStateNotificationQueryMapper.selectCspOrderCurrentSateList();
    }
}
