package com.ezwelesp.batch.hims.order.send.domain;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@XmlRootElement(name = "ResponseEzwel")
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
@NoArgsConstructor
@ToString
public class CspSendResponse {
    @XmlElement
    private String result;
    @XmlElement
    private String message;
    @XmlElement
    private String returnValue;
    @XmlElement
    private String cspCd;
    @XmlElement
    private String cspGoodsCd;
    @XmlElement
    private String ecpnSeq;	//CSP사 쿠폰번호 혹은 고유일련번호
    @XmlElement
    private String ecpnRn;
    /**
     * 2017.11.18 CSP 연동 규격서 업데이트로 추가
     * 전문번호(TRID) or CSP 쿠폰 고유일련번호( 쿠폰발송 요청에서 리턴해준 업체와 통신을 위한 Key)
     *
     * 주문발송상품상세 - ecpnCspReqInntNo 컬럼으로 관리
     */
    @XmlElement
    private String ecpnTrid;
}
