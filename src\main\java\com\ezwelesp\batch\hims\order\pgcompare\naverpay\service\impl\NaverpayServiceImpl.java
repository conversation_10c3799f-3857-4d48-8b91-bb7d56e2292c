package com.ezwelesp.batch.hims.order.pgcompare.naverpay.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.ezwelesp.batch.hims.order.entity.external.ApiPgPrsnlPayEntity;
import com.ezwelesp.batch.hims.order.entity.external.EzEcOrderPayLogEntity;
import com.ezwelesp.batch.hims.order.pgcompare.naverpay.mapper.command.NaverpayCommandMapper;
import com.ezwelesp.batch.hims.order.pgcompare.naverpay.mapper.query.NaverpayQueryMapper;
import com.ezwelesp.batch.hims.order.pgcompare.naverpay.service.NaverpayService;
import com.ezwelesp.batch.hims.order.utils.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.ezwelesp.framework.utils.StringUtils;
import com.ezwelesp.framework.utils.request.dto.Header;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service 
@RequiredArgsConstructor
public class NaverpayServiceImpl implements NaverpayService {
	
	private final NaverpayQueryMapper naverpayQueryMapper;
	private final NaverpayCommandMapper naverpayCommandMapper;
	
	@Value(("${naverpay.api.timeout}"))
	private int timeout;
	
	@Value(("${naverpay.api.history}"))
	private String apiUrlHistory;
	
	@Value(("${naverpay.api.settlement}"))
	private String apiUrlSettlement;
	
	private final String[] naverpayMidList = {"NAVER_PARTNER_ID", "NAVER_PARTNER_TRAVEL", "NAVER_PARTNER_LIMIT", "NAVER_PARTNER_DEDUCT", "NAVER_PARTNER_MK_B2E", "NAVER_PARTNER_MK_B2C"};
	
	@Override
	public void callNaverpayApi(String searchDtm) {
		try {
			this.orderProcess(searchDtm);
		} catch (Exception e) {
			log.error("======= [네이버페이 결제 수집] ERROR " + e.toString());
		}
	}
	
	@Override
	public void callNaverpaySettlementApi(String searchDtm) {
		
		try {
			this.settlementProcess(searchDtm);
		} catch (Exception e) {
			log.error("======= [네이버페이 정산 수집] ERROR " + e.toString());
		}
	}
	
	/**
	 * 결제 대사 영역
	 */
	private void orderProcess(String searchDtm) {
		// 날짜가 없는 경우 이전일 
		String searchDate = StringUtils.isNotEmpty(searchDtm) ? searchDtm : this.getDateTime();
		
		log.info("Naverpay Batch process SEARCH_DATE " + searchDtm + ", searchDate " + searchDate);
		
		try {
			for(String naverMid : naverpayMidList) {
				for(int index = 1; index <= this.getNaverpayPageNum(naverMid, searchDate) ; index++) {
					this.getNaverpayOrderList(index, naverMid, searchDate);
				}
			}
		}catch(Exception e) {
			log.error("Naverpay Batch process ERROR");
			log.error(e.getMessage());
			e.printStackTrace();
		}finally{
			log.info("===Naverpay Batch process FINISHED===");
		}
	}
	

	/**
	 * 정산 대사 영역
	 */
	private void settlementProcess(String searchDtm) {
		// 날짜가 없는 경우 이전일 
		String searchDate = StringUtils.isNotEmpty(searchDtm) ? searchDtm : this.getDate();
		
		try {
			for(String naverMid : naverpayMidList) {
				for(int index = 1; index <= this.getNaverpayPageNumSettlement(naverMid, searchDate) ; index++) {
					this.getNaverpayOrderListSettlement(index, naverMid, searchDate);
				}
			}
		}catch(Exception e) {
			log.error("Naverpay Batch process ERROR");
			log.error(e.getMessage());
			e.printStackTrace();
		}finally{
			log.info("===Naverpay Batch process FINISHED===");
		}
	}
	

	/**
	 * 결제
	 * @param mid
	 * @param searchDate
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	private int getNaverpayPageNum(String mid, String searchDate) throws Exception {
		int returnPageNum = 0;
		log.info("getNaverpayPageNum time [" + searchDate + "]");

		JSONObject sendObject = new JSONObject();
		sendObject.put("startTime", searchDate + "0000");
		sendObject.put("endTime", searchDate + "5959");
		sendObject.put("pageNumber", 1);
		sendObject.put("rowsPerPage", 100);

		Map<String, Object> resultMap = this.naverpayRequest(sendObject.toString(), mid);
		log.info("getNaverpayPageNum sendObject:" + sendObject.toString());

		String resultCode = String.valueOf(resultMap.get("code"));
		String resultMessage = String.valueOf(resultMap.get("message"));
		Map<String, Object> resBody = (Map<String, Object>) resultMap.get("body");


		log.info("getNaverpayPageNum resultCode [" + resultCode + "]" + resultMessage);
		if("Success".equals(resultCode) ) {
			int totalPageCount = objectReturnInteger(resBody.get("totalPageCount"));
			returnPageNum = totalPageCount;
		}
		log.info("getNaverpayPageNum returnPageNum " + returnPageNum);

		return returnPageNum;

	}
	
	/**
	 * 정산
	 * @param mid
	 * @param searchDate
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	private int getNaverpayPageNumSettlement(String mid, String searchDate) throws Exception {
		int returnPageNum = 0;
		log.info("getNaverpayPageNumSettlement time [" + searchDate + "]");

		Map<String,Object> sendObject = new HashMap<>();
		sendObject.put("startDate", searchDate );
		sendObject.put("endDate", searchDate);
		sendObject.put("pageNumber", 1);
		sendObject.put("periodType", "SETTLE_COMPLETE_DATE");

		Map<String, Object> resultMap = this.naverpayRequestSettlement(sendObject, mid);
		log.info("getNaverpayPageNumSettlement sendObject:" + sendObject.toString());

		String resultCode = String.valueOf(resultMap.get("code"));
		String resultMessage = String.valueOf(resultMap.get("message"));
		Map<String, Object> resBody = (Map<String, Object>) resultMap.get("body");


		log.info("getNaverpayPageNumSettlement resultCode [" + resultCode + "]" + resultMessage);
		if("Success".equals(resultCode) && resBody != null) {
				int totalPageCount = objectReturnInteger(resBody.get("totalPageCount"));
				returnPageNum = totalPageCount;	
			}
		
		log.info("getNaverpayPageNumSettlement returnPageNum " + returnPageNum);

		return returnPageNum;
	}


	@SuppressWarnings("unchecked")
	private void getNaverpayOrderList(int page, String mid, String searchDate) throws Exception {

		log.info("getNaverpayOrderList page [" + page + "]");
		log.info("getNaverpayOrderList time [" + searchDate + "]");

		JSONObject sendObject = new JSONObject();
		sendObject.put("startTime", searchDate + "0000");
		sendObject.put("endTime", searchDate + "5959");
		sendObject.put("pageNumber", page);
		sendObject.put("rowsPerPage", 100);

		Map<String, Object> resultMap = this.naverpayRequest(sendObject.toString(), mid);

		log.info("getNaverpayOrderList sendObject:" + sendObject.toString());


		String resultCode = String.valueOf(resultMap.get("code"));
		String resultMessage = String.valueOf(resultMap.get("message"));
		Map<String, Object> resBody = (Map<String, Object>) resultMap.get("body");

		log.info("getNaverpayOrderList resultCode [" + resultCode + "]" + resultMessage);

		if("Success".equals(resultCode) ) {
			int totalPageCount = objectReturnInteger(resBody.get("totalPageCount"));
			int currentPageNumber = objectReturnInteger(resBody.get("currentPageNumber"));

			log.info("getNaverpayOrderList resultCode:" + resultCode + ", totalPageCount:" + totalPageCount + ", currentPageNumber:" + currentPageNumber);

			List<Map<String, Object>> resList = (List<Map<String, Object>>) resBody.get("list");
			log.info("getNaverpayOrderList resList.size(): " + resList.size());

			try {
				HashMap<String, Object> map = this.naverpayQueryMapper.selectNaverPayMidInfo(mid);
				mid = String.valueOf(map.get("np_mid"));
				
				this.saveNaverpayDataLog(resList, mid);
				this.saveNaverpayDataOrder(resList, mid);
				
			} catch (Exception e) {
				log.error("insertNaverpayData insertOrderNaverpay catch " + e.getMessage());
				e.printStackTrace();
			}
		}
	}
	
	@SuppressWarnings("unchecked")
	private void getNaverpayOrderListSettlement(int page, String mid, String searchDate) throws Exception {
		log.info("getNaverpayOrderListSettlement page [" + page + "]");
		log.info("getNaverpayOrderListSettlement time [" + searchDate + "]");

		Map<String,Object> sendObject = new HashMap<>();
		sendObject.put("startDate", searchDate );
		sendObject.put("endDate", searchDate);
		sendObject.put("pageNumber", 1);
		sendObject.put("periodType", "SETTLE_COMPLETE_DATE");

		Map<String, Object> resultMap = this.naverpayRequestSettlement(sendObject, mid);

		log.info("getNaverpayOrderListSettlement sendObject:" + sendObject.toString());


		String resultCode = String.valueOf(resultMap.get("code"));
		String resultMessage = String.valueOf(resultMap.get("message"));
		Map<String, Object> resBody = (Map<String, Object>) resultMap.get("body");

		log.info("getNaverpayOrderListSettlement resultCode [" + resultCode + "]" + resultMessage);

		if("Success".equals(resultCode) ) {
			int totalPageCount = objectReturnInteger(resBody.get("totalPageCount"));
			int currentPageNumber = objectReturnInteger(resBody.get("currentPageNumber"));

			log.info("getNaverpayOrderListSettlement resultCode:" + resultCode + ", totalPageCount:" + totalPageCount + ", currentPageNumber:" + currentPageNumber);

			List<Map<String, Object>> resList = (List<Map<String, Object>>) resBody.get("list");
			log.info("getNaverpayOrderListSettlement resList.size(): " + resList.size());

			try {
				HashMap<String, Object> map = this.naverpayQueryMapper.selectNaverPayMidInfo(mid);
				mid = String.valueOf(map.get("np_mid"));
				this.modifyNaverpayDataSettement(resList, mid);
			} catch (Exception e) {
				log.error("insertNaverpayData insertOrderNaverpay catch " + e.getMessage());
				e.printStackTrace();
			}
		}
		
	}
	


	private Map<String,Object> naverpayRequest(String sendJson, String mid) throws Exception {
		Map<String,Object> returnString = new HashMap<>();
		String partnerId = "";
		String clientId = "";
		String clientSecret = "";
		String chainId = "";

		HashMap<String, Object> midInfo = this.naverpayQueryMapper.selectNaverPayMidInfo(mid);
		partnerId = String.valueOf(midInfo.get("np_partner_id"));
		clientId = String.valueOf(midInfo.get("np_client_id"));
		clientSecret = String.valueOf(midInfo.get("np_client_secret"));
		chainId = checkNull(midInfo.get("np_chain_id"));

		//Server 타입에 맞춰서 경로 변경
		String naverpayApiUrl = String.format(this.apiUrlHistory, partnerId);
		log.info("##################### NAVER PAY API URL ################ >>> " + naverpayApiUrl);
		

		log.info("naverpayRequest sendJson " + sendJson);

		try {
			log.info("naverpayRequest partnerId " + partnerId);
			returnString = naverpayConnect(naverpayApiUrl, clientId, clientSecret, chainId, sendJson);
		} catch (Exception e) {
			log.error("naverpayRequest ERROR");
			e.printStackTrace();
			log.error("naverpayRequest Reconnect sleep 5000");
			Thread.sleep(5000);
			log.error("naverpayRequest Reconnect");
			log.error("naverpayRequest partnerId " + partnerId);
			returnString = naverpayConnect(naverpayApiUrl, clientId, clientSecret, chainId, sendJson);
		}

		return returnString;
	}
	
	private Map<String, Object> naverpayRequestSettlement(Map<String,Object> sendJson, String mid) throws Exception {
		Map<String, Object> returnObject = new HashMap<>();
		String partnerId = "";
		String apiKey = "";

		HashMap<String, Object> midInfo = this.naverpayQueryMapper.selectNaverPayMidInfo(mid);
		partnerId = String.valueOf(midInfo.get("np_partner_id"));
		apiKey = String.valueOf(midInfo.get("api_key")); 
		

		if(this.apiUrlSettlement.indexOf("dev-pub") > 0) apiKey= "b33ca90d0f2efc079cd2079bb5d1c88f9ea8702fdd17dce1c497662b7ea864e0";
		
		
		log.info("naverpayRequest sendJson " + sendJson);

		try {
			log.info("naverpayRequest partnerId " + partnerId);
			returnObject = naverpayConnectSettlement(this.apiUrlSettlement, apiKey, sendJson);
		} catch (Exception e) {
			log.error("naverpayRequest ERROR");
			e.printStackTrace();
			log.error("naverpayRequest Reconnect sleep 5000");
			Thread.sleep(5000);
			log.error("naverpayRequest Reconnect");
			log.error("naverpayRequest partnerId " + partnerId);
			returnObject = naverpayConnectSettlement(this.apiUrlSettlement, apiKey, sendJson);
		}

		return returnObject;
	}

	
	

	private String checkNull(Object obj) {
		String str = String.valueOf(obj).trim();
		if (StringUtils.isEmpty(str) || "null".equals(str)) {
			return "";
		}

		return str;
	}
	
	
	private Map<String,Object> naverpayConnect(String naverpayApiUrl, String clientId, String clientSecret, String chainId, String sendJson) throws Exception {
		try {
			
			List<Header> headerList = new ArrayList<>();
			headerList.add(new Header("X-Naver-Client-Id", clientId));
			headerList.add(new Header("X-Naver-Client-Secret", clientSecret));
			headerList.add(new Header("X-NaverPay-Chain-Id", chainId));
			
			AjaxResult result = RestClientUtil.requestApi(naverpayApiUrl, timeout, headerList, sendJson);
			if(!StringUtils.equals("Success", result.get("code").toString())) {
				log.info("Remote Server Receive Failed(Internal Error Http Status Code:[" + result.get("code") + "])");
				throw new Exception("[5002]서버 수신오류(Server Internal Error)");
			}
			return result;
		} catch (Exception ex) {
			log.error("Remote Server Receive Failed");
			ex.printStackTrace();
			throw new Exception("[5004]서버 통신오류");
		} 
	}
	

	private Map<String, Object> naverpayConnectSettlement(String naverpayApiUrl, String apiKey, Map<String,Object> sendJson) throws Exception {
		try {
			
			List<Header> headerList = new ArrayList<>();
			headerList.add(new Header("X-Naver-API-Key", apiKey));
			
			ResponseEntity<String> result = RestClientUtil.requestApiGet(naverpayApiUrl, timeout, headerList, sendJson);
//			if(!StringUtils.equals("Success", result.get("code").toString())) {
//				log.info("Remote Server Receive Failed(Internal Error Http Status Code:[" + result.get("code") + "])");
//				throw new Exception("[5002]서버 수신오류(Server Internal Error)");
//			}
			
			if(result.getStatusCodeValue() != 200) {
				log.info("Remote Server Receive Failed(Internal Error Http Status Code:[" +result.getStatusCodeValue() + "])");
				throw new Exception("[5002]서버 수신오류(Server Internal Error)");	
			}
			
			return (Map<String, Object>) new JsonObjectConverter().deserialize(result.getBody());
		} catch (Exception ex) {
			log.error("Remote Server Receive Failed");
			ex.printStackTrace();
			throw new Exception("[5004]서버 통신오류");
		} 
	}
	
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void saveNaverpayDataLog(List<Map<String, Object>> resList, String mid) throws Exception {

		try {
			log.info("insertNaverpayData resList [" + resList.size() + "]");
			for(Map<String, Object> dataMap : resList) {
				Map<String, Object> insertData = new HashMap<>();
				boolean checkPayStatus = false;
				String dealStatus = "";

				// 01:원결제 승인건, 03:전체취소 건, 04:부분취소 건
				if("01".equals(dataMap.get("admissionTypeCode"))) {
					dealStatus = "승인";
					checkPayStatus = true;
				}else if("03".equals(dataMap.get("admissionTypeCode"))) {
					dealStatus = "취소";
					checkPayStatus = false;
				}else if("04".equals(dataMap.get("admissionTypeCode"))) {
					dealStatus = "부분취소";
					checkPayStatus = false;
				}else {
					continue;
				}

				String confirmDd = dataMap.get("tradeConfirmYmdt") != null ? String.valueOf(dataMap.get("tradeConfirmYmdt")).substring(0, 8) : "";
				String confirmTm = dataMap.get("tradeConfirmYmdt") != null ? String.valueOf(dataMap.get("tradeConfirmYmdt")).substring(8, 14) : "";
				String storeId = mid;
				String paymentId = String.valueOf(dataMap.get("paymentId"));
				int payAmt = objectReturnInteger(dataMap.get("totalPayAmount"));
				
				EzEcOrderPayLogEntity ezEcOrderPayLogEntity = EzEcOrderPayLogEntity.builder()
						.tid(paymentId)
						.statusCd(checkPayStatus ? "0001" : "0002")
						.orderNum(dataMap.get("tradeConfirmYmdt") + "_" + dataMap.get("merchantPayKey"))
						.ezwelOrderNum(Long.parseLong(String.valueOf(dataMap.get("merchantPayKey")).replace("MK_", "")))
						.storeId(storeId)
						.dealType("A")
						.deviceType("구분없음")
						.payType("네이버페이")
						.dealStatus(dealStatus)
						.confirmDd(confirmDd)
						.confirmTm(confirmTm)
						.payDt(confirmDd + confirmTm)
						.dealAmt(new BigDecimal(payAmt))
						.cancelTid(paymentId) //취소인 경우 원거래번호
						.confirmNum(dataMap.get("cardAuthNo") != null ? String.valueOf(dataMap.get("cardAuthNo")) : "")
						.pgType("B")
						.build();
				

				//취소일때
				if(!checkPayStatus) {
					ezEcOrderPayLogEntity = ezEcOrderPayLogEntity.toBuilder()
							.cancelDd(confirmDd)
							.cancelTm(confirmTm)
							.cancelAmt(new BigDecimal(payAmt))
							.build();
				}

				this.naverpayCommandMapper.insertNaverpayOrderLog(ezEcOrderPayLogEntity);
				log.info("insertNaverpayOrderLog insertData[" + insertData.toString() + "]");
			}

		} catch(DuplicateKeyException e) {
			log.error("insertNaverpayOrderLog catch " + e.getMessage());
			e.printStackTrace();
		} catch (Exception e) {
			log.error("insertNaverpayOrderLog catch " + e.getMessage());
			e.printStackTrace();
		}
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void saveNaverpayDataOrder(List<Map<String, Object>> resList, String mid) throws Exception {

		try {
			log.info("insertNaverpayData resList [" + resList.size() + "]");
			for(Map<String, Object> dataMap : resList) {
				Map<String, Object> insertData = new HashMap<>();
				boolean checkPayStatus = false;
				String dealStatus = "";

				// 01:원결제 승인건, 03:전체취소 건, 04:부분취소 건
				if("01".equals(dataMap.get("admissionTypeCode"))) {
					dealStatus = "승인";
					checkPayStatus = true;
				}else if("03".equals(dataMap.get("admissionTypeCode"))) {
					dealStatus = "취소";
					checkPayStatus = false;
				}else if("04".equals(dataMap.get("admissionTypeCode"))) {
					dealStatus = "부분취소";
					checkPayStatus = false;
				}else {
					continue;
				}

				String confirmDd = dataMap.get("tradeConfirmYmdt") != null ? String.valueOf(dataMap.get("tradeConfirmYmdt")).substring(0, 8) : "";
				String confirmTm = dataMap.get("tradeConfirmYmdt") != null ? String.valueOf(dataMap.get("tradeConfirmYmdt")).substring(8, 14) : "";
				String storeId = mid;
				String paymentId = String.valueOf(dataMap.get("paymentId"));
				int payAmt = objectReturnInteger(dataMap.get("totalPayAmount"));
				
				ApiPgPrsnlPayEntity apiPgPrsnlPayEntity = ApiPgPrsnlPayEntity.builder()
						.tid(paymentId)
						.statusCd(checkPayStatus ? "0001" : "0002")
						.ezwelOrderNum(Long.parseLong(String.valueOf(dataMap.get("merchantPayKey")).replace("MK_", "")))
						.storeId(storeId)
						.payType("네이버페이")
						.dealStatus(dealStatus)
						.confirmDt(confirmDd + confirmTm)
						.payDt(confirmDd + confirmTm)
						.dealAmt(new BigDecimal(payAmt))
						.cancelTid(paymentId) //취소인 경우 원거래번호
						.confirmNum(dataMap.get("cardAuthNo") != null ? String.valueOf(dataMap.get("cardAuthNo")) : "")
						.pgType("B")
						.payCd("1022")
						.build();
				
				//취소일때
				if(!checkPayStatus) {
					apiPgPrsnlPayEntity = apiPgPrsnlPayEntity.toBuilder()
							.cancelDt(confirmDd + confirmTm)
							.cancelAmt(new BigDecimal(payAmt))
							.build();
				}
				this.naverpayCommandMapper.insertNaverpayOrder(apiPgPrsnlPayEntity);
				log.info("insertNaverpayOrder insertData[" + insertData.toString() + "]");
			}

		} catch(DuplicateKeyException e) {
			log.error("insertNaverpayOrder catch " + e.getMessage());
			e.printStackTrace();
		} catch (Exception e) {
			log.error("insertNaverpayOrder catch " + e.getMessage());
			e.printStackTrace();
		}
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	private void modifyNaverpayDataSettement(List<Map<String, Object>> resList, String mid) {
		try{
			log.info("modifyNaverpayDataSettement resList [" + resList.size() + "]");
			for(Map<String, Object> dataMap : resList) {
				try {
					this.naverpayCommandMapper.updateOrderNaverPay(ApiPgPrsnlPayEntity.builder()
							.tid(String.valueOf(dataMap.get("productOrderId")))
							.cancelTid(String.valueOf(dataMap.get("orderId")))
							.depositDt(String.valueOf(dataMap.get("settleCompleteDate")))
							.depositAmt(new BigDecimal(String.valueOf(dataMap.get("settleExpectAmount"))))
							.calcAmt(new BigDecimal(String.valueOf(dataMap.get("paySettleAmount"))))
							.pgType("B")
							.build());
				}catch(Exception e) {
					log.error("modifyNaverpayDataSettement updateOrderNaverPay catch " + e.getMessage());
					e.printStackTrace();
				}
			}
		}catch(Exception e) {
			log.error("modifyNaverpayDataSettement catch " + e.getMessage());
			e.printStackTrace();
		}
		
	}
	
	private String getDateTime(){
		SimpleDateFormat dateform = new SimpleDateFormat("yyyyMMddHH"); //yyyyMMddHHmmss
		Date time = new Date();
		Calendar cal = Calendar.getInstance();
		cal.setTime(time);
		cal.add(Calendar.HOUR, -1);
		return dateform.format(cal.getTime()).toString();
	}
	
	private String getDate(){
		SimpleDateFormat dateform = new SimpleDateFormat("yyyyMMdd"); //yyyyMMdd
		Date time = new Date();
		Calendar cal = Calendar.getInstance();
		cal.setTime(time);
		cal.add(Calendar.DATE, -1);
		return dateform.format(cal.getTime()).toString();
	}
	
	private int objectReturnInteger(Object obj) {
		String str = "";
		try {
			str = String.valueOf(obj).trim();
			if (StringUtils.isEmpty(str) || "null".equals(str)) {
				return 0;
			}
		} catch (Exception e) {
			return 0;
		}

		return (int) Double.parseDouble(str);
	}
}
