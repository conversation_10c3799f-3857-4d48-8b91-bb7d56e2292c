package com.ezwelesp.batch.lgportal.product.service;


import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductSKURequestDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgUpdateProductSKUResetRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgProductOptionCombVo;
import com.ezwelesp.batch.lgportal.product.mapper.command.ProductLGPortalCommandMapper;
import com.ezwelesp.batch.lgportal.product.mapper.query.ProductLGPortalQueryMapper;
import com.ezwelesp.framework.apiresponse.BaseEspHttpStatus;
import com.ezwelesp.framework.exception.ServiceException;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.ezwelesp.batch.lgportal.product.service.LgProductUtils.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class LgEditProductSKUService {

    private final ProductLGPortalQueryMapper productLGPortalQueryMapper;
    private final ProductLGPortalCommandMapper productLGPortalCommandMapper;
    private final LgApiClient lgApiClient;

    public void lgEditProductSKU() {

        try {
            // 수정된 SKU 내역 조회
            val lgEditProductSKUListRaw = productLGPortalQueryMapper.selectLgEditProductSKUTarget();

            // 옵션의 종류가 5개 이하인 건만 처리
            val lgEditProductSKUList = lgEditProductTargetOptCountCheck(lgEditProductSKUListRaw);

            // 상품번호 추출
            val editProductSKUGdsCdList = lgEditProductSKUList.stream()
                    .map(LgEditProductTargetVo::getGdsCd)
                    .toList();

            // 해당 상품번호로 SKU에 해당하는 모든 옵션정보 불러오기
            val editProductSKUOptionsList = productLGPortalQueryMapper.selectLgProductOptionByGdsCds(
                    editProductSKUGdsCdList
            );

            // SKU 상품번호 별 map 으로 전환
            val editProductSKUMap = editProductSKUOptionsList.stream()
                    .collect(Collectors.groupingBy(LgProductOptionCombVo::getGdsCd));

            // api 통신작업
            val lgEditProductSKUResultDtoList = lgEditProductSKUList.stream()
                    .map(target -> {
                        // 해당 상품의 옵션 가지고오기
                        val targetOptionList = editProductSKUMap.getOrDefault(
                                target.getGdsCd(),
                                Collections.emptyList()
                        );

                        return processLgProductSKU(target, targetOptionList);
                    }).toList();

            log.info("AddProductResultList: {}", lgEditProductSKUResultDtoList);

        } catch (Exception e) {
            log.error("LGPortal EditProduct Error: {}", e.getMessage(), e);
            throw new ServiceException(BaseEspHttpStatus.INTERNAL_SERVER_ERROR, "EditProduct Error: " + e.getMessage());

        }
    }

    private LgProductApiResponseDto processLgProductSKU(
            @NonNull LgEditProductTargetVo target,
            @NonNull List<LgProductOptionCombVo> productOptionListRaw
    ) {
        try {

            //SKU 초기화 RequestDto 생성
            val lgUpdateProductSoldOutRequestDto = LgUpdateProductSKUResetRequestDto.builder()
                    .productNo(target.getProductNo())
                    .sKuAttrs(getSKUAttrs(productOptionListRaw))
                    .sKUTypeCode(getSKUTypeCode(productOptionListRaw))
                    .build();

            // sku 초기화 API 통신
            val lgAddProductResult = lgApiClient.sendLgProductSKUResetApi(
                    lgUpdateProductSoldOutRequestDto,
                    target.getGdsCd(),
                    target.getApiSeq()
            );

            // 초기화 실패시 내역 저장 (api_batch_send)
            if (isApiFailure(lgAddProductResult)) {
                return handleLgProductBatchSendFailure(
                        lgAddProductResult,
                        "sendLgProductSKUResetApi Fail"
                );
            }

            // productOptionListRaw 에서 옵션금액 마이너스 제외
            val productOptionList = productOptionListRaw.stream()
                    .filter(row -> row.getOptAddPrc() >= 0)
                    .toList();

            // 옵션이 없는 경우 실패는 아니나, 더이상 진행 안함으로 로그 남기기
            if (productOptionList.isEmpty()) {
                return handleLgProductBatchSendFailure(
                        lgAddProductResult,
                        "productOptions is Empty"
                );
            }

            // 옵션을 RequestDto 형태로 변경
            val lgProductSKURequestDtoList = productOptionList.stream()
                    .map(option ->
                            lgProductSKURequestDtoBuilder(target, option)
                    ).toList();

            // 옵션을 RequestDto 형태로 변경한 건별로 add 및 edit
            val lgProductAddSKUApiResponseList = lgProductSKURequestDtoList.stream()
                    .map(lgProductSKURequestDto ->
                            sendProcessLgProductSKU(
                                    target,
                                    lgProductSKURequestDto
                            )
                    ).toList();


            // 실패한 내역 리스트
            val failedApiResponsesList = lgProductAddSKUApiResponseList.stream()
                    .filter(LgProductUtils::isApiFailure) // 200이 아닌 응답만 필터링
                    .toList();

            // 옵션 추가에서 실패내역 존재시 요청 테이블의 내역도 오류로 처리하고 내역 테이블에 남김.
            if (!failedApiResponsesList.isEmpty()) {
                val failedList = failedApiResponsesList.stream()
                        .map(responses -> responses.getProductNo() + ":" + responses.getResultMessage())
                        .collect(Collectors.joining(" / "));

                val failedApiResponsesExists = LgProductApiResponseDto.builder()
                        .result("500")
                        .resultMessage("failedApiResponsesExists:  " + failedList)
                        .productNo(null)
                        .gdsCd(target.getGdsCd())
                        .build();

                return handleLgProductBatchSendFailure(
                        failedApiResponsesExists,
                        "failedApiResponsesExists"
                );
            }

            // 모든게 성공이니, 성공처리
            val successApiResponses = LgProductApiResponseDto.builder()
                    .result("200")
                    .resultMessage("success")
                    .productNo(null)
                    .gdsCd(target.getGdsCd())
                    .build();

            productLGPortalCommandMapper.updateApiBatchSendStatus(successApiResponses);

            return successApiResponses;


        } catch (Exception e) {
            // 한 건이 오류가 나더라도 실패건으로 반환
            // 그래서 debug 로 로깅
            log.debug("processEditProductSKU Exception: {} {}", e.getMessage(), e);
            val editProductSKUApiException = LgProductApiResponseDto.builder()
                    .result("500")
                    .resultMessage(e.getMessage())
                    .productNo(null)
                    .gdsCd(target.getGdsCd())
                    .build();

            return handleLgProductBatchSendFailure(
                    editProductSKUApiException,
                    "processEditProductSKU Exception"
            );
        }
    }

    private LgProductApiResponseDto sendProcessLgProductSKU(
            @NonNull LgEditProductTargetVo target,
            @NonNull LgProductSKURequestDto lgProductSKURequestDto
    ) {
        // 상품 추가 api 전송
        val sendLgProductSKUAddRespons = lgApiClient.sendLgProductSKUAddApi(
                lgProductSKURequestDto,
                target.getGdsCd(),
                target.getApiSeq()
        );

        // 실패시 실패내역 전송
        if (isApiFailure(sendLgProductSKUAddRespons)) {
            if ("421".equals(sendLgProductSKUAddRespons.getResult())
                    && "중복된 SKU맵핑코드입니다.".equals(
                    sendLgProductSKUAddRespons.getResultMessage())
            ) {
                return sendLgProductSKUEdit(target, lgProductSKURequestDto);
            }
            else {
                return handleLgProductBatchSKUFailure(
                        lgProductSKURequestDto,
                        sendLgProductSKUAddRespons,
                        "sendLgProductSKUAddApi Fail"
                );
            }
        }

        // 상품 SKU ADD api 성공시 db 저장
        productLGPortalCommandMapper.insertApiBatchSKUStatus(lgProductSKURequestDto, sendLgProductSKUAddRespons);

        return sendLgProductSKUAddRespons;
    }

    // 상품 수정 api 전송
    private LgProductApiResponseDto sendLgProductSKUEdit(
            @NonNull LgEditProductTargetVo target,
            @NonNull LgProductSKURequestDto lgProductSKURequestDto
    ) {
        // 상품 추가 api 전송
        val sendLgProductSKUEditResponse = lgApiClient.sendLgProductSKUEditApi(
                lgProductSKURequestDto,
                target.getGdsCd(),
                target.getApiSeq()
        );

        if (isApiFailure(sendLgProductSKUEditResponse)) {
            return handleLgProductBatchSKUFailure(
                    lgProductSKURequestDto,
                    sendLgProductSKUEditResponse,
                    "sendLgProductSKUEditApi Fail"
            );
        }

        // 상품 SKU 수정 api 성공시 db 저장
        productLGPortalCommandMapper.insertApiBatchSKUStatus(lgProductSKURequestDto, sendLgProductSKUEditResponse);

        return sendLgProductSKUEditResponse;
    }

    private List<LgEditProductTargetVo> lgEditProductTargetOptCountCheck(
            List<LgEditProductTargetVo> lgEditProductSKUListRaw
    ) {
        // 타겟 리스트가 5개 초과인 건 로깅
        val targetOptCountOver5List = lgEditProductSKUListRaw.stream()
                .filter(target -> target.getOptCnt() > 5)
                .toList();

        if (!targetOptCountOver5List.isEmpty()) {
            log.debug("LgEditProductSKUService targetOptCountOver5List: {}",
                    targetOptCountOver5List.stream()
                            .map(LgEditProductTargetVo::getGdsCd) // 상품 ID만 출력
                            .toList());
        }

        return lgEditProductSKUListRaw.stream()
                .filter(target -> target.getOptCnt() <= 5)
                .toList();
    }


    // Sku api 실패시 BatchSend 테이블 처리로직
    private LgProductApiResponseDto handleLgProductBatchSendFailure(
            LgProductApiResponseDto apiResult,
            String logMessage
    ) {
        log.debug("{}: {}", logMessage, apiResult);
        val failureResponse = createFailureResponse(apiResult, logMessage);
        // 실패 내역 db 업데이트
        productLGPortalCommandMapper.updateApiBatchSendStatus(failureResponse);
        return failureResponse;

    }

    // Sku api 실패시 BatchSKU 테이블 처리로직
    private LgProductApiResponseDto handleLgProductBatchSKUFailure(
            LgProductSKURequestDto lgProductSKURequestDto,
            LgProductApiResponseDto apiResult,
            String logMessage
    ) {
        log.debug("{}: {}", logMessage, apiResult);
        val failureResponse = createFailureResponse(apiResult, logMessage);
        // 상품 SKU 실패 내역 db 업데이트
        productLGPortalCommandMapper.insertApiBatchSKUStatus(lgProductSKURequestDto, failureResponse);
        return failureResponse;

    }

    private static LgProductSKURequestDto lgProductSKURequestDtoBuilder(
            @NonNull LgEditProductTargetVo target,
            @NonNull LgProductOptionCombVo option
    ) {
        return LgProductSKURequestDto.builder()
                .productMappingCode(target.getGdsCd())
                .productNo(target.getProductNo())
                .sKUMappingCode(option.getOptGdsSeq())
                .sKUCode(option.getOptGdsSeq())
                .sKUVal(option.getOptVal())
                .controlSalesPrice(option.getOptAddPrc())
                .stockCnt(option.getStckQty())
                .exposeYN(option.getUseYn())
                .sortSeq(option.getSortOrdg())
                .build();
    }


}

