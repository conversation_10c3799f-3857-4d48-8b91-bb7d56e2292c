<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hpas.alarm.mapper.query.EntryCspMainQueryMapper">

	<sql id="entryCspMainLoginList">
        select ccb.csp_cd
             , ccb.csp_nm
             , cmb.mgr_lgin_id
             , cccd.chrgr_nm
		     , cccd.chrgr_telno
		     , cccd.chrgr_eml_adr
		     , cmbOper.mgr_nm as chrgr_nm_oper
		     , cmbOper.telno as chrgr_mbl_telno_oper
		     , cmbOper.eml_adr as chrgr_eml_adr_oper
		     , cmbBein.mgr_nm as chrgr_nm_bein
		     , cmbBein.telno as chrgr_mbl_telno_bein
		     , cmbBein.eml_adr as chrgr_eml_adr_bein
		     , ccb.bein_apv_dtm
		     , case when ccb.sat_dlv_poss_yn = 'Y' then ccb.sat_dlv_poss_yn
		            else 'N'
		        end sat_dlv_poss_yn
		     , cmb.lgin_dtm
          from ez_cm.cm_mgr_b cmb                -- 관리자기본
         inner join ez_co.co_csp_b ccb           -- 협력사기본
            on ccb.untd_cust_cd = cmb.untd_cust_cd
         inner join ez_co.co_csp_chrgr_d cccd    -- 협력사담당자상세
            on cccd.csp_cd = ccb.csp_cd
           and cccd.csp_chrgr_tsk_cd = 'OPER'    -- 운영담당
         inner join ez_cm.cm_mgr_b cmbOper       -- 관리자기본-이지웰운영담당
            on cmbOper.mgr_id = ccb.oper_chrg_mgr_id
         inner join ez_cm.cm_mgr_b cmbBein       -- 관리자기본-이지웰입점담당
            on cmbBein.mgr_id = ccb.bein_chrg_mgr_id
         inner join ez_cm.cm_menu_aprc_d cmad    -- 메뉴접근상세
            on cmad.csp_cd = ccb.csp_cd
         where cmb.sys_knd_cd = 'HPAS'
           and cmb.mgr_chrg_tsk_cd = 'HPAS_MSTR' -- 관리자담당업무코드: 협력사 마스터ID
           and ccb.csp_st_cd = 'APVC'            -- 협력사상태코드:승인완료
           and ccb.ch_cd in ('100', '101')       -- ch_cd: 복지샵, 브랜드몰
           and ccb.csp_cd != '10072082'          -- 220706 현업요청으로 10072082 업체 제외
           and ccb.bein_apv_dtm is not null
           and cmb.lgin_dtm is null
           and current_date - to_date(ccb.bein_apv_dtm, 'yyyymmddhhmi24') = 4
	</sql>

    <select id="selectEntryCspMainLoginCount" resultType="int">
    	select count(*)
    	  from (
			    <include refid="entryCspMainLoginList" />
		)
    </select>

    <select id="selectEntryCspMainLoginList" parameterType="int" resultType="com.ezwelesp.batch.hpas.alarm.dto.EntryCspMainDto">
    	select *
    	  from (
			    select row_number() over (order by csp_cd) as num
			         , a.*
			      from (
					    <include refid="entryCspMainLoginList" />
				) a
		)
    	 where num <![CDATA[<=]]> 10000 * #{pageInt}
    	   and num <![CDATA[>=]]> (#{pageInt} - 1)* 10000 + 1
    </select>

	<sql id="entryCspMainGoodsList">
        select ccb.csp_cd
             , ccb.csp_nm
             , cmb.mgr_lgin_id
             , cccd.chrgr_nm
		     , cccd.chrgr_telno
		     , cccd.chrgr_eml_adr
		     , cmbOper.mgr_nm as chrgr_nm_oper
		     , cmbOper.telno as chrgr_mbl_telno_oper
		     , cmbOper.eml_adr as chrgr_eml_adr_oper
		     , cmbBein.mgr_nm as chrgr_nm_bein
		     , cmbBein.telno as chrgr_mbl_telno_bein
		     , cmbBein.eml_adr as chrgr_eml_adr_bein
		     , ccb.bein_apv_dtm
		     , case when ccb.sat_dlv_poss_yn = 'Y' then ccb.sat_dlv_poss_yn
		            else 'N'
		        end sat_dlv_poss_yn
		     , cmb.lgin_dtm
          from ez_cm.cm_mgr_b cmb                -- 관리자기본
         inner join ez_co.co_csp_b ccb           -- 협력사기본
            on ccb.untd_cust_cd = cmb.untd_cust_cd
         inner join ez_co.co_csp_chrgr_d cccd    -- 협력사담당자상세
            on cccd.csp_cd = ccb.csp_cd
           and cccd.csp_chrgr_tsk_cd = 'OPER'    -- 운영담당
         inner join ez_cm.cm_mgr_b cmbOper       -- 관리자기본-이지웰운영담당
            on cmbOper.mgr_id = ccb.oper_chrg_mgr_id
         inner join ez_cm.cm_mgr_b cmbBein       -- 관리자기본-이지웰입점담당
            on cmbBein.mgr_id = ccb.bein_chrg_mgr_id
         inner join ez_cm.cm_menu_aprc_d cmad    -- 메뉴접근상세
            on cmad.csp_cd = ccb.csp_cd
         where cmb.sys_knd_cd = 'HPAS'
           and cmb.mgr_chrg_tsk_cd = 'HPAS_MSTR' -- 관리자담당업무코드: 협력사 마스터ID
           and ccb.csp_st_cd = 'APVC'            -- 협력사상태코드:승인완료
           and ccb.ch_cd in ('100', '101')       -- ch_cd: 복지샵, 브랜드몰
           and ccb.csp_cd != '10072082'          -- 220706 현업요청으로 10072082 업체 제외
           and ccb.bein_apv_dtm is not null
           and cmb.lgin_dtm is not null
           and current_date - to_date(ccb.bein_apv_dtm, 'yyyymmddhhmi24') = 8
           and (select count(*) from ez_pd.pd_gds_c where csp_cd = ccb.csp_cd) = 0
	</sql>

    <select id="selectEntryCspMainGoodsCount" resultType="int">
    	select count(*)
    	  from (
			    <include refid="entryCspMainGoodsList" />
		)
    </select>

    <select id="selectEntryCspMainGoodsList" parameterType="int" resultType="com.ezwelesp.batch.hpas.alarm.dto.EntryCspMainDto">
    	select *
    	  from (
			    select row_number() over (order by csp_cd) as num
			         , a.*
			      from (
					    <include refid="entryCspMainGoodsList" />
				) a
		)
    	 where num <![CDATA[<=]]> 10000 * #{pageInt}
    	   and num <![CDATA[>=]]> (#{pageInt} - 1)* 10000 + 1
    </select>

</mapper>
