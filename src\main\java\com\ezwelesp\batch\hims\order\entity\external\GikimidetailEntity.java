package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * [인터페이스] 배송지키미 요청 디테일 테이블
 * gikimidetail
 */
@Getter
@SuperBuilder
public class GikimidetailEntity implements Serializable {
    /**
     * 마스터ID
     */
    private Long mid;

    /**
     * ID 순번
     */
    private int idseq;

    /**
     * 주문번호
     */
    private String orderNo;

    /**
     * 주문행 번호
     */
    private long orderLine;

    /**
     * 주문행 상품코드
     */
    private String itemCode;

    /**
     * 주문행 상품명
     */
    private String itemName;

    /**
     * 주문행 상품옵션
     */
    private String itemOption;

    /**
     * 주문행 상품수량
     */
    private int itemQty;

    /**
     * 주문행 상품단가
     */
    private BigDecimal itemPrice;

    /**
     * 주문일시(YYYYMMDDHHMMSS)
     */
    private String orderDate;

    /**
     * 입금일시(YYYYMMDDHHMMSS)
     */
    private String paymentDate;

    private static final long serialVersionUID = 1L;
}
