package com.ezwelesp.batch.lgportal.entity;

import lombok.Data;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * [인터페이스] API배치 업체 배송지
 * api_batch_csp_dlvr
 */
@Jacksonized
@Getter
@SuperBuilder(toBuilder = true)
public class ApiBatchCspDlvrEntity implements Serializable {
    /**
     * 업체코드
     */
    private Integer cspCd;

    /**
     * 출고지ID
     */
    private String cspDlvrId;

    /**
     * 배송지유형코드(01:출고지, 02:반품지, 03:교환지)
     */
    private String dlvrTypeCd;

    /**
     * 배송비유형코드(01:무료배송, 02: 조건부유료배송)
     */
    private String dlvrCostTypeCd;

    /**
     * 배송비
     */
    private Integer dlvrPrice;

    /**
     * 요청파라메터
     */
    private String reqParam;

    /**
     * LG배송지번호
     */
    private Integer lgDlvrNo;

    /**
     * 등록일자
     */
    private String regDt;

    /**
     * 전송여부
     */
    private String sendYn;

    /**
     * 결과코드
     */
    private String resultCd;

    /**
     * 결과메세지
     */
    private String resultMsg;

    private static final long serialVersionUID = 1L;
}
