package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * [인터페이스] 배송결과 테이블
 * gikimiresult
 */
@Getter
@SuperBuilder
public class GikimiresultEntity implements Serializable {
    /**
     * 고객사용번호*(상품에 대한 고유번호, 결과보고 단위)
     */
    private String transUniqueCode;

    /**
     * 일련번호(상품에 대한 결과처리 일련번호)
     */
    private int seq;

    /**
     * 관리구분코드
     */
    private String sectionCode;

    /**
     * 배송사코드
     */
    private String logisticsCode;

    /**
     * 운송장번호
     */
    private String invoiceNo;

    /**
     * 배송상태
     */
    private String dlvStatType;

    /**
     * 처리일시
     */
    private String procDateTime;

    /**
     * 예외코드 (배송상태가 29, 59, 69인 경우)
     */
    private String exceptionCode;

    /**
     * 예외코드명
     */
    private String exceptionName;

    /**
     * 지점정보
     */
    private String branchName;

    /**
     * 지점연락처
     */
    private String branchTel;

    /**
     * 배달기사이름
     */
    private String employeeName;

    /**
     * 배달기사연락처
     */
    private String employeeTel;

    /**
     * 배달예정시간
     */
    private String employeeMsg;

    /**
     * 수령인 정보
     */
    private String taker;

    /**
     * 오류코드
     */
    private String errorCode;

    /**
     * 오류코드명
     */
    private String errorName;

    /**
     * 업체관리코드1
     */
    private String defCode1;

    /**
     * 업체관리코드2
     */
    private String defCode2;

    /**
     * 생성일시
     */
    private String createDateTime;

    /**
     * 처리여부
     */
    private String procYn;

    /**
     * 이지웰처리일시(이지웰 배송결과 처리일시)
     */
    private String ezwelProcDate;

    private static final long serialVersionUID = 1L;
}
