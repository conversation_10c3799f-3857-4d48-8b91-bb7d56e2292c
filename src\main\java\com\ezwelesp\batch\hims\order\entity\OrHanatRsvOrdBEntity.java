package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 하나투어예약주문기본(ez_or.or_hanat_rsv_ord_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrHanatRsvOrdBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 주문자여부(ordr_yn) not null
     */
    private String ordrYn;

    /**
     * 주문자명(ordr_nm)
     */
    private String ordrNm;

    /**
     * 주문자영문성씨명(ordr_enlst_nm)
     */
    private String ordrEnlstNm;

    /**
     * 주문자영문이름명(ordr_enm_nm)
     */
    private String ordrEnmNm;

    /**
     * 생년월일일자(bymd_dt)
     */
    private String bymdDt;

    /**
     * 성별코드(gndr_cd)
     */
    private String gndrCd;

    /**
     * 여행상품나이구분코드(trip_gds_age_div_cd)
     */
    private String tripGdsAgeDivCd;

    /**
     * 주문자전화번호(ordr_telno)
     */
    private String ordrTelno;

    /**
     * 주문자이메일주소(ordr_eml_adr)
     */
    private String ordrEmlAdr;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
