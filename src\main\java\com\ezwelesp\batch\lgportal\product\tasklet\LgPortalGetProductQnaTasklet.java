package com.ezwelesp.batch.lgportal.product.tasklet;


import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductGetQnaListRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaInsertListDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaListDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgQnaProductInfoVo;
import com.ezwelesp.batch.lgportal.product.mapper.command.ProductLGPortalCommandMapper;
import com.ezwelesp.batch.lgportal.product.mapper.query.ProductLGPortalQueryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ezwelesp.batch.lgportal.product.service.LgProductUtils.isApiFailure;
import static com.ezwelesp.batch.lgportal.product.service.ServerTimeUtil.getServerTimeHour;
import static com.ezwelesp.batch.lgportal.product.service.ServerTimeUtil.getTimeToString;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalGetProductQnaTasklet implements Tasklet {


    private final LgApiClient lgApiClient;
    private final ProductLGPortalQueryMapper productLGPortalQueryMapper;
    private final ProductLGPortalCommandMapper productLGPortalCommandMapper;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) {
        try {

            // request 형태 만들기
            val getProductQnaListRequestDto = setLgProductGetQnaListRequestDto();

            // api 통신
            val productQnaListApiResult = lgApiClient.sendLgProductGetQnaListApi(getProductQnaListRequestDto);

            if (isApiFailure(productQnaListApiResult.getResult())) {
                log.error("sendLgProductGetQnaListApi: {}", productQnaListApiResult.getResultMessage());
                throw new IllegalStateException("API 호출 실패: " + productQnaListApiResult.getResultMessage());
            }

            // 각 건별로 db 인서트 시작
            // raw 데이터 정리
            val lgProductQnaList = setLgProductQnaList(productQnaListApiResult);

            // 리스트에서 상품 정보 가지고 오려고
            // 사실 협력사 정보만 가지고오는거면 쿼리서 insert할때 Select 하는것도 방법이긴한데..
            // 운영하기 용이하도록 분리
            // 상품번호 및 정보 로 map 으로 변환
            val lgQnaProductMap = setLgQnaProductInfoMap(lgProductQnaList);

            // 해당 상품 번호 가지고와서 처리.
            lgProductQnaList.forEach(item -> {
                try {
                    // 매칭된 LG QnA Product 정보 가져오기
                    //  todo 테이블 마다 lg 상품번호 형이 달라서 맞추는 상황. 테이블 구조 바뀌면 내용 다 바뀌어야되서 내역 확인 필요
                    //   나중에 버프에게 해당 내용 관련 수정 요청 해야되며 수정 확정나면 작업 진행 필요
                    // 현재 ProductNo를 키로 사용하여 매칭 정보 조회
                    val lgQnaProductInfo = lgQnaProductMap.get(String.valueOf(item.getProductNo()));

                    if (lgQnaProductInfo == null) {
                        log.error("LgPortalGetProductQnaTasklet lgQnaProductInfo is null : {}", item);
                        return;
                    }

                    // 건별로 처리
                    processAddProductQna(item, lgQnaProductInfo);
                } catch (Exception e) {
                    // 개별 예외를 로깅하고 루프는 계속 진행
                    log.error("processAddProductQna: {}. Error: {}", item.getProductNo(), e.getMessage(), e);
                }
            });

        } catch (Exception e) {
            log.error("LgPortalGetProductQnaTasklet Failed: {}", e.getMessage(), e);
        }

        return RepeatStatus.FINISHED;
    }

    private void processAddProductQna(
            LgProductQnaListDto item,
            LgQnaProductInfoVo lgQnaProductInfo
    ) {

        // 인서트 dto 만들어서.
        val lgProductQnaInsertListDto = LgProductQnaInsertListDto.builder()
                .ttl(item.getTitle())
                .inqCntn(item.getContents())
                .gdsCd(lgQnaProductInfo.getGoodsCdString())// 이지웰 상품번호
                .gdsNm(item.getProductName())
                .cspCd(lgQnaProductInfo.getCspCdString())
                .lgpBbsNo(item.getProductInquiryNoString())
                .build();

        productLGPortalCommandMapper.insertApiProductQna(lgProductQnaInsertListDto);

    }


    private Map<String, LgQnaProductInfoVo> setLgQnaProductInfoMap(
            List<LgProductQnaListDto> lgProductQnaList
    ) {
        val lgQnaProductNoList = lgProductQnaList.stream()
                .map(dto -> String.valueOf(dto.getProductNo()))
                .toList();

        val lgQnaProductList = productLGPortalQueryMapper.selectLgQnaProductInfo(lgQnaProductNoList);

        return lgQnaProductList.stream()
                .collect(Collectors.toMap(LgQnaProductInfoVo::getTargetGoodsCd, product -> product));
    }



    private List<LgProductQnaListDto> setLgProductQnaList(
            LgProductQnaResponseDto lgProductQnaResponseDto
    ) {

        val lgProductQnaListRaw = lgProductQnaResponseDto.getItems();

        // 리스트 조회를 위해 String으로 가지고와서 조회
        val lgProductQnaInquiryNoList = lgProductQnaListRaw.stream()
                .map(dto -> String.valueOf(dto.getProductInquiryNo())) // String으로 변환
                .toList();

        // 이미 존재하는 리스트 가지고오기.
        val existingInquiryNumbers = productLGPortalQueryMapper.selectLgProductQnaCnt(lgProductQnaInquiryNoList);

        // 이미 존재하는 리스트 제외
        return lgProductQnaListRaw.stream()
                .filter(dto ->
                        !existingInquiryNumbers.containsKey(
                                String.valueOf(dto.getProductInquiryNo())
                        )
                )
                .toList();
    }


    private LgProductGetQnaListRequestDto setLgProductGetQnaListRequestDto() {

        // 시간 설정 현재 시간 위주로 가지고오는 로직 / 분 초 밀리초 = 0
        val nowTime = getServerTimeHour();
        // 24시간 전부터 1시간 후까지 전송
        val startTime = getTimeToString(nowTime.minusDays(1));
        val endTime = getTimeToString(nowTime.plusHours(1));

        // 시간 제외한 값은 DTO에 기본값 걸정함.
        return LgProductGetQnaListRequestDto.builder()
                .createdStartDate(startTime)
                .createdEndDate(endTime)
                .build();
    }
}

