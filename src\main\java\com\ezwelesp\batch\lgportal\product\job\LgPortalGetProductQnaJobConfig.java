package com.ezwelesp.batch.lgportal.product.job;


import com.ezwelesp.batch.lgportal.product.tasklet.LgPortalGetProductQnaTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;



/**
 * LG_API 상품 QnA DB 입력
 */
//Scheduled(cron = "00 00 */1 * * *") 매 1시간 마다
@Configuration
public class LgPortalGetProductQnaJobConfig {
    private final LgPortalGetProductQnaTasklet lgPortalGetProductQnaTasklet;

    public LgPortalGetProductQnaJobConfig(
            @Qualifier("lgPortalGetProductQnaTasklet") LgPortalGetProductQnaTasklet lgPortalGetProductQnaTasklet
    ) {
        this.lgPortalGetProductQnaTasklet = lgPortalGetProductQnaTasklet;
    }

    @Bean("BA_HIPO00035")
    public Job lgPortalGetProductQnaJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPO00035_STEP") Step lgPortalGetProductQnaStep
    ) {
        return new JobBuilder("BA_HIPO00035", jobRepository)
                .start(lgPortalGetProductQnaStep)
                .build();
    }

    @Bean("BA_HIPO00035_STEP")
    public Step lgPortalGetProductQnaStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
    ) {
        return new StepBuilder("BA_HIPO00035_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(lgPortalGetProductQnaTasklet, transactionManager)
                .build();
    }
}
