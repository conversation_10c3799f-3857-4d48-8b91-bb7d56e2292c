package com.ezwelesp.batch.lgportal.order.service.impl;

import com.ezwelesp.batch.hims.order.entity.ClClmRsnLEntity;
import com.ezwelesp.batch.hims.order.entity.external.CoCspObndRtpLocBEntity;
import com.ezwelesp.batch.lgportal.entity.ApiBatchChgReturnEntity;
import com.ezwelesp.batch.lgportal.entity.OrOrdBEntity;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.*;
import com.ezwelesp.batch.lgportal.order.mapper.command.ApiBatchCommandMapper;
import com.ezwelesp.batch.lgportal.order.mapper.query.ClaimQueryMapper;
import com.ezwelesp.batch.lgportal.order.mapper.query.OrderQueryMapper;
import com.ezwelesp.batch.lgportal.order.service.ClaimService;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.DateUtils;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
  * LG Portal 교환/반품 조회 및 클레임 생성
  *
  * <AUTHOR>
  * @since 2025.03.31
  * @see com.ezwelesp.batch.lgportal.order.service.impl
  */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClaimServiceImpl implements ClaimService {

    private final ClaimQueryMapper claimQueryMapper;
    private final OrderQueryMapper orderQueryMapper;
    private final ApiBatchCommandMapper apiBatchCommandMapper;

    @Value("${lgportal.server.host}")
    private String serverHost;

    @Value("${lgportal.server.timeout}")
    private int timeout;

    @Value("${lgportal.api-key}")
    private String apiKey;

    @Value("${lgportal.cert-key}")
    private String certKey;

    @Value(("${ezwel.apim.host}"))
    private String apimApiHost;

    @Transactional
    @Override
    public void regExchRtp(String beforeTime, String nowTime) {

        ClaimListReqDto claimListReqDto = ClaimListReqDto.builder().build();

        try {
            AjaxResult
                    result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_EXCEPTION_LIST, this.timeout, JsonObjectConverter.serialize(
                    claimListReqDto.toBuilder()
                            .apiKey(apiKey)
                            .exceptStatusCode(LgConstants.ClaimStatusCd.APPLY.getCode())    // 교환/반품/취소 상태코드(신청)
                            .exceptStatusUpdateDate_above(beforeTime)
                            .exceptStatusUpdateDate_below(nowTime)
                            .build()
            ));

            if (StringUtils.equals(String.valueOf(HttpStatus.OK.value()), result.get("result").toString())) {

                ObjectMapper objectMapper = this.configMapper();
                List<ClaimListResDto> resDtoList = objectMapper.convertValue(result.get("items"),
                        new TypeReference<>() {
                        });
                log.debug("Order exception(exchange, return) list size {}" , resDtoList.size());

                resDtoList.forEach(this::exchRtpProcess);
            }

        } catch (Exception e) {
            log.error("LG portal 'regExchRtp' failed", e);
        }

    }

    @Transactional
    @Override
    public void regOrderCancel(String beforeTime, String nowTime) {
        ClaimListReqDto claimListReqDto = ClaimListReqDto.builder().build();

        try {
            AjaxResult
                    result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_EXCEPTION_LIST, this.timeout, JsonObjectConverter.serialize(
                    claimListReqDto.toBuilder()
                            .apiKey(apiKey)
                            .exceptStatusCode(LgConstants.ClaimStatusCd.COMPLETION.getCode())                //교환반품취소 상태코드(신청:ORD1021, 접수:ORD1022, 완료:ORD1023)
                            .exceptStatusUpdateDate_above(beforeTime)
                            .exceptStatusUpdateDate_below(nowTime)
                            .build()
            ));

            if (StringUtils.equals(String.valueOf(HttpStatus.OK.value()), result.get("result").toString())) {

                ObjectMapper objectMapper = this.configMapper();
                List<ClaimListResDto> resDtoList = objectMapper.convertValue(result.get("items"),
                        new TypeReference<>() {
                        });
                log.debug("order cancel-exception list size {}" , resDtoList.size());

                /*
                 * AS-IS에서는 취소인경우(사용자취소:ORD1011,관리자취소:ORD1012,반품취소:ORD1013) 3가지 취소 처리했으나,
                 * TO-BE에서 '반품취소'는 '반품'으로 처리됨
                 */
                resDtoList = resDtoList.stream().filter(claimResDto ->
                        (StringUtils.equals(claimResDto.getExceptTypeCode(), LgConstants.ClaimTypeCd.CANCEL_PERSON.getCode()) ||
                            StringUtils.equals(claimResDto.getExceptTypeCode(), LgConstants.ClaimTypeCd.CANCEL_MANAGER.getCode()))
                ).toList();

                log.debug("order cancel-exception list size after filtering {}" , resDtoList.size());

                resDtoList.forEach(claimResDto -> this.setOrderCancel(claimResDto, beforeTime, nowTime));
                
                // TODO-JSKIM27, '반품후 취소'(ACCEPT_RTP("ORD1013", "접수반품") 시에 '반품완료' 처리 필요
            }

        } catch (Exception e) {
            log.error("LG portal 'orderList' failed", e);
        }

    }

    private void setOrderCancel(ClaimListResDto resDto, String beforeTime, String nowTime) {

        String clientOrderNum = String.valueOf(resDto.getSalesOrderNo());
        OrderListReqDto orderListReqDto = OrderListReqDto.builder().build();

        try {
            AjaxResult
                    result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_GET_DELIVERYSLIP, this.timeout, JsonObjectConverter.serialize(
                    orderListReqDto.toBuilder()
                            .apiKey(apiKey)
                            .salesOrderNo(clientOrderNum)
                            .orderStatusCode("ORD1036")             // 판매주문상태코드, 주문취소
                            .deliveryStatusCode(LgConstants.EMPTY_STR)
                            .deliveryStatusCodeChanged_above(beforeTime)
                            .deliveryStatusCodeChanged_below(nowTime)
                            .build()
            ));

            if (StringUtils.equals(String.valueOf(HttpStatus.OK.value()), result.get("result").toString())) {

                ObjectMapper objectMapper = this.configMapper();
                List<OrderListResDto> resDtoList = objectMapper.convertValue(result.get("items"),
                        new TypeReference<>() {
                        });

                log.debug("order cancel size {}" , resDtoList.size());

                resDtoList.forEach(orderListResDto ->
                        this.orderCancelProcess(orderListResDto, resDto.getExceptTypeCode()));
            }

        } catch (Exception e) {
            log.error("LG portal 'setOrderCancel' failed", e);
        }
    }

    private void orderCancelProcess(OrderListResDto resDto, String exeptTypeCode) {

        // 1. 취소 대상 주문 확인, 대상 주문이 존재하는 경우에 진행
        OrOrdBEntity ordBEntity = this.orderQueryMapper.selectOrderInfoByClientOrderNum((String.valueOf(resDto.getSalesOrderNo())));

        if (ObjectUtils.isNotEmpty(ordBEntity)) {

            List<OrderListResDto.DeliveryItem> deliveryItems = resDto.getDeliveryItems();

            ClaimCancelReqDto claimCancelReqDto = ClaimCancelReqDto.builder()
                    .ordNo(ordBEntity.getOrdNo())
                    .gdsList(this.setClaimApplyGoods(deliveryItems, ordBEntity))
                    .clmAtbrMagnCd(StringUtils.equals(exeptTypeCode, LgConstants.ClaimTypeCd.CANCEL_PERSON.getCode()) ?
                            LgConstants.ClaimReasonsMainAgentCode.USER.getCode() : LgConstants.ClaimReasonsMainAgentCode.CSP.getCode())
                    .build();

            // 3. '주문취소' 클레임 API 호출
            try {
                AjaxResult result = RestClientUtil.requestApi(this.apimApiHost + LgConstants.CLAIM_API_CANCEL, 10,
                        JsonObjectConverter.serialize(claimCancelReqDto));

            } catch (JsonProcessingException e) {
                log.error("LG portal 'orderCancelProcess' failed", e);
            }

        }

    }

    private List<ClaimApplyGoodsInfoDto> setClaimApplyGoods(List<OrderListResDto.DeliveryItem> deliveryItems, OrOrdBEntity ordBEntity) {

        List<ClaimApplyGoodsInfoDto> claimApplyGoodsInfoDtoList = new ArrayList<>();

        deliveryItems.stream().forEach(deliveryItem -> {
            String lgpApiDlvNo = String.valueOf(deliveryItem.getDeliveryCmdNo());
            String lgpApiDlvDtlNo = String.valueOf(deliveryItem.getOrderItemNo());

            // 2. 취소 대상 상품 확인, 이미 취소된 데이터 존재여부 확인
            if (this.claimQueryMapper.selectCancelOrderGoodsCount(ordBEntity.getOrdNo(), lgpApiDlvNo, lgpApiDlvDtlNo) == 0) {
                // 취소대상 상품 정보 조회
                claimApplyGoodsInfoDtoList.add(this.claimQueryMapper.selectCancelOrderGoodsInfo(ordBEntity.getOrdNo(),
                        lgpApiDlvNo, lgpApiDlvDtlNo));
            }
        });

        return claimApplyGoodsInfoDtoList;
    }

    private void exchRtpProcess(ClaimListResDto resDto) {

        // ORD1013(반품), ORD1014(교환), ORD1011(사용자취소), ORD1012(관리자취소)
        String exceptTypeCode = resDto.getExceptTypeCode();
        String clientOrderNum = String.valueOf(resDto.getSalesOrderNo());
        String salesOrderExceptNo = String.valueOf(resDto.getSalesOrderExceptNo());

        if (this.claimQueryMapper.selectWtdwGdsCount(salesOrderExceptNo) > 0) {
            log.debug("A exchange or return goods has already been registered.[{}::{}::{}]",
                    exceptTypeCode, clientOrderNum, salesOrderExceptNo);
            return;
        }

        // 주문정보 조회
        ExchRtpOrderInfoDto exchRtpOrderInfoDto = this.claimQueryMapper.selectExchRtpOrderInfo(clientOrderNum);

        if (ObjectUtils.isNotEmpty(exchRtpOrderInfoDto)) {

            // 교환, 반품 상세목록
            List<ClaimListResDto.OrderItemExcept> orderItemExceptList = resDto.getOrderItemExcepts();

            ClaimListResDto.OrderItemExcept orderItemExcept = orderItemExceptList.get(0);

            // 등록일시
            String regDtm = orderItemExcept.getChanged();

            if (StringUtils.isNotEmpty(regDtm)) {
                regDtm = DateUtils.parseDateToStr(DateUtils.YYYYMMDDHHMMSS,
                        DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, regDtm));
            }

            // 첫번째 주문상품정보 조회
            ExchRtpGoodsInfoDto firstExchRtpGoodsInfoDto = this.claimQueryMapper.selectExchRtpOrderGoodsInfo(clientOrderNum, exchRtpOrderInfoDto.getDlvCoCd(),
                    StringUtils.equals(exceptTypeCode, LgConstants.ClaimTypeCd.ACCEPT_RTP.getCode()) ? String.valueOf(orderItemExcept.getOrderItemNo()) :
                            String.valueOf(orderItemExcept.getOrgOrderItemNo()));

            // 협력사출고반품위치기본 정보 조회
            CoCspObndRtpLocBEntity cspObndRtpLoc = claimQueryMapper.selectCspObndRtpLocInfo(firstExchRtpGoodsInfoDto.getCspCd(), firstExchRtpGoodsInfoDto.getCspObndLocNo());

            // API[배송정보 조회]를 통한 배송비 조회
            OrderListResDto.DlvExp dlvExp = this.setDlvrPrice(clientOrderNum);

            // 클레임사유
            ClClmRsnLEntity clClmRsnLEntity = this.setClmRsnCntn(exceptTypeCode, resDto);

            // 반품
            if (StringUtils.equals(exceptTypeCode, LgConstants.ClaimTypeCd.ACCEPT_RTP.getCode())) {

                List<ClaimReturnApplyGoodsDto> rtnGdsList = new ArrayList<>();

                orderItemExceptList.stream().forEach(rtnGoods -> {
                    // 주문상품정보 조회
                    ExchRtpGoodsInfoDto exchRtpGoodsInfoDto = this.claimQueryMapper.selectExchRtpOrderGoodsInfo(clientOrderNum, exchRtpOrderInfoDto.getDlvCoCd(),
                            String.valueOf(orderItemExcept.getOrderItemNo()));

                    rtnGdsList.add(ClaimReturnApplyGoodsDto.builder()
                                    .ordGdsSeq(exchRtpGoodsInfoDto.getOrdGdsSeq())
                                    .clmGdsQty(rtnGoods.getCancelCnt())
                                    .clmRsnCd(clClmRsnLEntity.getClmRsnCd())
                                    .clmRsnCntn(clClmRsnLEntity.getClmRsnCntn())
                                    .lgpApiDlvDtlNo(String.valueOf(rtnGoods.getOrderItemExceptNo()))    // API 교환/반품 상세번호
                            .build());
                });

               /* // 클레임 배송주소 기본
                ClaimDeliveryAddressDto claimDeliveryAddressDto = ClaimDeliveryAddressDto.builder()
                        .rcvrNm(exchRtpOrderInfoDto.getRcvrNm())
                        .realRcvrNm(exchRtpOrderInfoDto.getRcvrNm())
                        .rcvrTelno(cspObndRtpLoc.getRtpLocTelno())
                        .rcvrMblTelno(cspObndRtpLoc.getRtpLocTelno())
                        .rcvrZipcd(cspObndRtpLoc.getRtpLocZipcd())
                        .rcvrBasAdr(cspObndRtpLoc.getRtpLocBasAdr())
                        .rcvrDtlAdr(cspObndRtpLoc.getRtpLocDtlAdr())
                        .build();

                // 클레임 배송 기본
                ClaimDeliveryBaseInfoDto claimDeliveryBaseInfoDto = ClaimDeliveryBaseInfoDto.builder()
                        .cspCd(firstExchRtpGoodsInfoDto.getCspCd())
                        .dlvCoCd(exchRtpOrderInfoDto.getDlvCoCd())
                        .build();

                ClaimSaveDeliveryDto claimSaveDeliveryDto = ClaimSaveDeliveryDto.builder()
                        .cspObndLocNo(cspObndRtpLoc.getCspObndLocNo())
                        .dlvExp(new BigDecimal(dlvExp.getDlvExp()))             // 배송비용
                        .deliveryAddressList(List.of(claimDeliveryAddressDto))
                        .deliveryBaseInfoList(List.of(claimDeliveryBaseInfoDto))
                        .build();

                ClaimReturnReqDto claimReturnReqDto = ClaimReturnReqDto.builder()
                        .clmDtm(regDtm)
                        .ordNo(exchRtpOrderInfoDto.getOrdNo())
                        .pkupMthdCd(StringUtils.equals("Y", firstExchRtpGoodsInfoDto.getReturnDlvrUseYn()) ? "H" : "")
                        .rcvrNm(exchRtpOrderInfoDto.getRcvrNm())
                        .rcvrMblTelno(cspObndRtpLoc.getRtpLocTelno())
                        .rcvrbasAdr(cspObndRtpLoc.getObndLocBasAdr())
                        .rcvrDtlAdr(cspObndRtpLoc.getObndLocDtlAdr())
                        .paymentMethod("RFND")
                        .ordGdsList(rtnGdsList)
                        .claimDelivery(claimSaveDeliveryDto)
                        .build();*/

                // 클레임 배송 기본
                ClaimDeliveryBaseInfoDto claimDeliveryBaseInfoDto = ClaimDeliveryBaseInfoDto.builder()
                        .cspCd(firstExchRtpGoodsInfoDto.getCspCd())
                        .dlvCoCd(exchRtpOrderInfoDto.getDlvCoCd())
                        .pkupMthdCd(StringUtils.equals("Y", firstExchRtpGoodsInfoDto.getReturnDlvrUseYn()) ?
                                LgConstants.PickUpMethodCodeEnum.VISIT.getCode() : LgConstants.PickUpMethodCodeEnum.REQUEST_RETURN.getCode())
                        .invcNo(StringUtils.equals("Y", firstExchRtpGoodsInfoDto.getReturnDlvrUseYn()) ?
                                exchRtpOrderInfoDto.getInvcNo() : LgConstants.EMPTY_STR)
                        .build();

                ClaimReturnApplyConditionDto returnApplyConditionDto = ClaimReturnApplyConditionDto.builder()
                        .dlvAdrNo(firstExchRtpGoodsInfoDto.getDlvAdrNo())
                        .ordGdsList(rtnGdsList)
                        .pkupMthdCd(StringUtils.equals("Y", firstExchRtpGoodsInfoDto.getReturnDlvrUseYn()) ?
                                LgConstants.PickUpMethodCodeEnum.VISIT.getCode() : LgConstants.PickUpMethodCodeEnum.REQUEST_RETURN.getCode())
                        .rcvrNm(exchRtpOrderInfoDto.getRcvrNm())
                        .rcvrMblTelno(cspObndRtpLoc.getRtpLocTelno())
                        .rcvrZipcd(cspObndRtpLoc.getObndLocZipcd())
                        .rcvrBasAdr(cspObndRtpLoc.getObndLocBasAdr())
                        .rcvrDtlAdr(cspObndRtpLoc.getObndLocDtlAdr())
                        .paymentMethodCd(LgConstants.ClaimAddExpenseProcessMethodCodeEnum.REFUND.getCode())
                        .claimDeliveryBase(claimDeliveryBaseInfoDto)
                        .dlvExp(new BigDecimal(dlvExp.getDlvExp()))
                        .clmDtm(regDtm)
                        .build();

                // '반품 신청' 클레임 API 호출
                try {
                    AjaxResult result = RestClientUtil.requestApi(this.apimApiHost + LgConstants.CLAIM_API_RETURN + firstExchRtpGoodsInfoDto.getOrdNo(), 10,
                            JsonObjectConverter.serialize(returnApplyConditionDto));

                } catch (JsonProcessingException e) {
                    log.error("LG portal 'orderCancelProcess' failed", e);
                }

            }
            else if (StringUtils.equals(exceptTypeCode, LgConstants.ClaimTypeCd.ACCEPT_EXCH.getCode())) {
                // 교환

                List<ClaimSaveGoodsDto> exchGdsList = new ArrayList<>();

                orderItemExceptList.stream().forEach(exchGoods -> {

                    // TODO-JSKIM27 옵션조합순번은 LG포털, 상품연동 참고 필요

                    // 주문상품정보 조회
                    ExchRtpGoodsInfoDto exchRtpGoodsInfoDto = this.claimQueryMapper.selectExchRtpOrderGoodsInfo(clientOrderNum, exchRtpOrderInfoDto.getDlvCoCd(),
                            String.valueOf(exchGoods.getOrderItemNo()));

                    exchGdsList.add(ClaimSaveGoodsDto.builder()
                            .ordNo(exchRtpGoodsInfoDto.getOrdNo())
                            .ordGdsSeq(exchRtpGoodsInfoDto.getOrdGdsSeq())
                            .exchangeGdsCd(exchGoods.getProductCode())
                            .clmAtbrMagnCd(clClmRsnLEntity.getClmAtbrMagnCd())
                            .clmGdsQty(exchGoods.getCancelCnt())
                            .clmRsnCd(clClmRsnLEntity.getClmRsnCd())
                            .clmRsnCntn(clClmRsnLEntity.getClmRsnCntn())
                            .optGdsCombSeq("O")
                            .lgpApiDlvNo(salesOrderExceptNo)
                            .lgpApiDlvDtlNo(String.valueOf(exchGoods.getOrderItemExceptNo())) // API 교환/반품 상세번호
                            .build());
                });

                ClaimExchangeApplyRequestDto exchangeApplyRequestDto = ClaimExchangeApplyRequestDto.builder()
                        .orderNumber(exchRtpOrderInfoDto.getOrdNo())
                        .cspCd(firstExchRtpGoodsInfoDto.getCspCd())
                        .cspObndLocNo(cspObndRtpLoc.getCspObndLocNo())
                        .clmKndCd(LgConstants.ClaimKindCodeEnum.EXCHANGE.getCode())
                        .ordGdsList(exchGdsList)
                        .clmDlvExp(String.valueOf(dlvExp.getDlvExp()))
                        .clmAddExpPrcsPymtMnsCd(LgConstants.ClaimAddExpenseProcessMethodCodeEnum.REFUND.getCode())
                        .deliveryAddressList(List.of(ClaimDeliveryAddressDto.builder()
                                .rcvrNm(exchRtpOrderInfoDto.getRcvrNm())
                                .realRcvrNm(exchRtpOrderInfoDto.getRcvrNm())
                                .rcvrTelno(cspObndRtpLoc.getRtpLocTelno())
                                .rcvrMblTelno(cspObndRtpLoc.getRtpLocTelno())
                                .rcvrZipcd(cspObndRtpLoc.getRtpLocZipcd())
                                .rcvrBasAdr(cspObndRtpLoc.getRtpLocBasAdr())
                                .rcvrDtlAdr(cspObndRtpLoc.getRtpLocDtlAdr())
                                .withdrawAddress(true)
                                .build()))
                        .pickupMthd(StringUtils.equals("Y", firstExchRtpGoodsInfoDto.getReturnDlvrUseYn()) ?
                                LgConstants.PickUpMethodCodeEnum.VISIT.getCode() : LgConstants.PickUpMethodCodeEnum.REQUEST_RETURN.getCode())
                        .clmDtm(regDtm)
                        .build();

                // '교환 신청' 클레임 API 호출
                try {
                    AjaxResult result = RestClientUtil.requestApi(this.apimApiHost + LgConstants.CLAIM_API_EXCHANGE, 10,
                            JsonObjectConverter.serialize(exchangeApplyRequestDto));

                } catch (JsonProcessingException e) {
                    log.error("LG portal 'exchRtpProcess' failed", e);
                }
            }

            // [교환/반품 접수] LG포털 API 호출
            this.exchRtpAccept(exceptTypeCode, salesOrderExceptNo);

        }
    }



    private ClClmRsnLEntity setClmRsnCntn(String exceptTypeCode, ClaimListResDto resDto) {
        String reasonCd = null;
        String dlvrPayAgent = null;
        String exceptDetailTypeCode = resDto.getExceptDetailTypeCode();
        if (StringUtils.equals(exceptTypeCode, LgConstants.ClaimTypeCd.ACCEPT_RTP.getCode())) {
            // 반품
            if (StringUtils.equals("ORD101M", exceptDetailTypeCode) ||
                    StringUtils.equals("ORD101O", exceptDetailTypeCode)) {
                reasonCd = LgConstants.ClaimReasonCode.CHANGE_OF_MIND.getCode();        // 단순변심
                dlvrPayAgent = LgConstants.ClaimReasonsMainAgentCode.USER.getCode();    // 사용자귀책
            }
            else if (StringUtils.equals("ORD101N", exceptDetailTypeCode)) {
                reasonCd = LgConstants.ClaimReasonCode.GOODS_FAULT.getCode();           // 상품 파손 및 불량
                dlvrPayAgent = LgConstants.ClaimReasonsMainAgentCode.CSP.getCode();     // CP귀책
            }
            else if (StringUtils.equals("ORD101P", exceptDetailTypeCode)) {
                reasonCd = LgConstants.ClaimReasonCode.DELIVERY_DELAY.getCode();        // 배송지연
                dlvrPayAgent = LgConstants.ClaimReasonsMainAgentCode.CSP.getCode();     // CP귀책
            }
        }
        else {
            // 교환
            if (StringUtils.equals("ORD101R", exceptDetailTypeCode) ||
                    StringUtils.equals("ORD101T", exceptDetailTypeCode)) {
                reasonCd = LgConstants.ClaimReasonCode.CHANGE_OF_MIND.getCode();        // 단순변심
                dlvrPayAgent = LgConstants.ClaimReasonsMainAgentCode.USER.getCode();    // 사용자귀책
            } else if("ORD101S".equals(exceptDetailTypeCode)) {
                reasonCd = LgConstants.ClaimReasonCode.GOODS_FAULT.getCode();           // 상품 파손 및 불량
                dlvrPayAgent = LgConstants.ClaimReasonsMainAgentCode.CSP.getCode();     // CP귀책
            }
        }

        return ClClmRsnLEntity.builder()
                .clmRsnCd(reasonCd)
                .clmAtbrMagnCd(dlvrPayAgent)
                .clmRsnCntn(resDto.getMemo())
                .build();

    }

    private OrderListResDto.DlvExp setDlvrPrice(String clientOrderNum) {
        // 배송정보API 호출하여 배송비 조회
        OrderListResDto.DlvExp dlvExp = new OrderListResDto.DlvExp();

        OrderListReqDto orderListReqDto = OrderListReqDto.builder().build();

        try {
            AjaxResult
                    result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_GET_DELIVERYSLIP, this.timeout, JsonObjectConverter.serialize(
                    orderListReqDto.toBuilder()
                            .apiKey(apiKey)
                            .salesOrderNo(clientOrderNum)
                            .deliveryStatusCode(LgConstants.EMPTY_STR)
                            .build()
            ));

            if (StringUtils.equals(String.valueOf(HttpStatus.OK.value()), result.get("result").toString())) {

                ObjectMapper objectMapper = this.configMapper();
                List<OrderListResDto> resDtoList = objectMapper.convertValue(result.get("items"),
                        new TypeReference<>() {
                        });
                log.debug("size {}" , resDtoList.size());

                resDtoList.forEach(resDto -> {
                    List<OrderListResDto.DeliveryAmt> deliveryAmtList = resDto.getDeliverAmt();

                    // '배송지시구분코드'(deliveryCmdTypeCode) 이용 체크
                    deliveryAmtList.stream()
                            .filter(deliveryAmt -> StringUtils.equals("ORD1132", deliveryAmt.getDeliveryCmdTypeCode()))
                            .findFirst()
                            .map(OrderListResDto.DeliveryAmt::getPay)
                            .ifPresent(dlvExp::setDlvExp);
                });
            }

        } catch (Exception e) {
            log.error("LG portal 'setDlvrPrice' failed", e);
        }

        return dlvExp;
    }

    private void exchRtpAccept(String exceptTypeCode, String salesOrderExceptNo) {

        try {
            AjaxResult result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_SET_EXCEPTION_ACCEPT, this.timeout,
                    JsonObjectConverter.serialize(Map.of("apiKey", this.apiKey,
                            "salesOrderExceptNo", salesOrderExceptNo)));

            ApiBatchChgReturnEntity entity = ApiBatchChgReturnEntity.builder()
                    .chgReturnType(exceptTypeCode)
                    .apiChgReturnNo(salesOrderExceptNo)
                    .sendKey(LgConstants.SendKey.EXCH_RTP_ACCEPT.getSendKey())
                    .returnCd(result.get("result").toString())
                    .returnMsg(result.get("resultMessage").toString())
                    .sendYn(StringUtils.equals("200", result.get("result").toString()) ? "Y" : "N")
                    .build();

            this.apiBatchCommandMapper.insertApiChgReturn(entity);

        } catch (JsonProcessingException e) {
            throw new RuntimeException("LG portal api request failed");
        }
    }

    private ObjectMapper configMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        return mapper;
    }
}
