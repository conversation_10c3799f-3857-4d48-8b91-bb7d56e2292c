<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">

<mapper namespace="com.ezwelesp.batch.lgportal.order.mapper.command.ApiBatchCommandMapper">

    <!-- 교환/반품 접수 등록 -->
    <insert id="insertApiChgReturn">
        INSERT INTO ez_if.api_batch_chg_return (
            api_no
            , send_key
            , send_yn
            , chg_return_type
            , api_chg_return_no
            , return_cd
            , return_msg
            , reg_dt
            , reg_id
        )
        VALUES (
            NEXTVAL('ez_if.sq_api_batch_chg_return')
            , #{sendKey}
            , #{sendYn}
            , #{chgReturnType}
            , #{apiChgReturnNo}
            , #{returnCd}
            , #{returnMsg}
            , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
            , 'batch'
        )
    </insert>

    <!-- 교환/반품 완료 전달 결과 등록 -->
    <update id="updateApiChgReturn">
        UPDATE  ez_if.api_batch_chg_return
        SET
            send_yn = #{sendYn}
            , return_cd = #{returnCd}
            , return_msg = #{returnMsg}
        WHERE api_no = #{apiNo}
    </update>

    <!-- LG API 주문로그 생성 -->
    <insert id="insertApiBatchCspDlvr">
        MERGE INTO ez_if.api_batch_csp_dlvr AS dlvr
        USING (VALUES ( #{cspCd}
                      , #{cspDlvrId}
                      , #{dlvrTypeCd}
                      , #{dlvrCostTypeCd}
                      , #{dlvrPrice})) AS source (csp_cd, csp_dlvr_id, dlvr_type_cd, dlvr_cost_type_cd, dlvr_price)
            ON dlvr.csp_cd = source.csp_cd AND dlvr.csp_dlvr_id = csp_dlvr_id.csp_cd AND dlvr.dlvr_type_cd = source.dlvr_type_cd
            AND dlvr.dlvr_cost_type_cd = source.dlvr_cost_type_cd AND dlvr.dlvr_price = source.dlvr_price
            WHEN MATCHED THEN
        UPDATE SET
            send_yn = 'N'
            , reg_dt = TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
        WHERE send_yn != 'Y'
            WHEN NOT MATCHED THEN
        INSERT (csp_cd
               , csp_dlvr_id
               , dlvr_type_cd
               , dlvr_cost_type_cd
               , dlvr_price
               , req_param
               , send_yn
               , result_cd
               , result_msg
               , lg_dlvr_no
               , reg_dt
               , reg_id)
        VALUES ( #{cspCd}
               , #{cspDlvrId}
               , #{dlvrTypeCd}
               , #{dlvrCostTypeCd}
               , #{dlvrPrice}
               , #{reqParam}
               , #{sendYn}
               , #{resultCd}
               , #{resultMsg}
               , #{lgDlvrNo}
               , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
               , 'batch')
    </insert>

    <insert id="insertApiBatchDlvr">
        INSERT INTO ez_if.api_batch_dlvr (
        api_seq
        , send_key
        , send_yn
        , delivery_no
        <if test="deliveryItem != null and deliveryItem != ''">
            ,delivery_item
        </if>
        , return_code
        , return_message
        , reg_dt
        , reg_id
        )
        VALUES (
        NEXTVAL('ez_if.sq_api_batch_dlvr')
        , #{sendKey}
        , #{sendYn}
        , #{deliveryNo}
        <if test="deliveryItem != null and deliveryItem != ''">
            , #{deliveryItem}
        </if>
        , #{returnCode}
        , #{returnMessage}
        , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
        , 'batch'
        )
    </insert>

    <update id="updateApiBatchDlvr">
        UPDATE ez_if.api_batch_dlvr
        SET  return_code = #{returnCode}
        , return_message = #{returnMessage}
        <if test="sendYn != null">
            , send_yn = #{sendYn}
        </if>
        WHERE api_seq = #{apiSeq}
    </update>

    <!-- LG API 주문로그 생성 -->
    <insert id="insertApiBatchLog">
        MERGE INTO ez_if.api_batch_log AS blog
        USING (VALUES ( #{clientOrderNum}
                      , #{sendKey}
                      , #{deliveryItem})) AS source (client_order_num, send_key, delivery_item)
            ON blog.client_order_num = source.client_order_num AND blog.send_key = source.send_key
            AND blog.delivery_item = source.delivery_item
            WHEN NOT MATCHED THEN
        INSERT (client_order_num, send_key, delivery_item, delivery_no,
                return_data, return_code, return_message, reg_dt, reg_id)
        VALUES ( #{clientOrderNum}
               , #{sendKey}
               , #{deliveryItem}
               , #{deliveryNo}
               , #{returnData}
               , #{returnCode}
               , #{returnMessage}
               , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
               , #{regId})
    </insert>

    <!-- 품절-->
    <insert id="insertApiBatchSend">
        MERGE INTO ez_if.api_batch_send AS send
        USING (VALUES ( #{productNo}
                      , #{sendKey}
                      , 'N')) AS source (product_no, send_key, send_yn)
            ON (send.product_no = source.product_no
            AND send.send_key = source.send_key
            AND send.send_yn = source.send_yn)
            WHEN MATCHED THEN
        UPDATE SET
            send_yn = 'N'
            , reg_dt = TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
            WHEN NOT MATCHED THEN
        INSERT ( api_seq
               , provider_no
               , certkey
               , product_no
               , send_key
               , send_yn
               , reg_dt
               , reg_id
        )
        VALUES ( NEXTVAL('ez_if.sq_api_batch_send')
               , #{providerNo}
               , #{certkey}
               , #{productNo}
               , #{sendKey}
               , #{sendYn}
               , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
               , 'ADMIN'
               )
    </insert>

    <insert id="insertApiBatchSendNoSku">
        INSERT INTO  ez_if.api_batch_send ( api_seq
                                          ,provider_no
                                          ,certkey
                                          ,product_no
                                          ,send_key
                                          ,send_yn
                                          ,reg_dt
                                          ,reg_id
        )
        VALUES ( NEXTVAL('ez_if.sq_api_batch_send')
               , #{providerNo}
               , #{certkey}
               , #{productNo}
               , #{sendKey}
               , #{sendYn}
               , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
               , 'ADMIN'
               )
    </insert>

    <update id="updateApiBatchSend">
        UPDATE ez_if.api_batch_send
        SET
        error_messege = #{errorMessage}
        <if test="sendYn != null and sendYn != ''">
            , send_yn = #{sendYn}
        </if>
        WHERE api_seq = #{apiSeq}
    </update>

</mapper>
