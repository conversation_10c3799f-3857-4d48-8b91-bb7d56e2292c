package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 배송주소기본(ez_or.dl_dlv_adr_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlDlvAdrBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송주소번호(dlv_adr_no) not null
     */
    private Long dlvAdrNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 수신자명(rcvr_nm) not null
     */
    private String rcvrNm;

    /**
     * 실제수신자명(real_rcvr_nm)
     */
    private String realRcvrNm;

    /**
     * 수신자전화번호(rcvr_telno)
     */
    private String rcvrTelno;

    /**
     * 수신자이메일주소(rcvr_eml_adr)
     */
    private String rcvrEmlAdr;

    /**
     * 수신자모바일전화번호(rcvr_mbl_telno) not null
     */
    private String rcvrMblTelno;

    /**
     * 수신자우편번호(rcvr_zipcd)
     */
    private String rcvrZipcd;

    /**
     * 수신자기본주소(rcvr_bas_adr)
     */
    private String rcvrBasAdr;

    /**
     * 수신자상세주소(rcvr_dtl_adr)
     */
    private String rcvrDtlAdr;

    /**
     * 배송요청내용(dlv_req_cntn)
     */
    private String dlvReqCntn;

    /**
     * 선물주문경조카드메시지내용(prsn_ord_cccrd_msg_cntn)
     */
    private String prsnOrdCccrdMsgCntn;

    /**
     * 선물하기배송주소입력일시(gvgft_dlv_adr_insr_dtm)
     */
    private String gvgftDlvAdrInsrDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
