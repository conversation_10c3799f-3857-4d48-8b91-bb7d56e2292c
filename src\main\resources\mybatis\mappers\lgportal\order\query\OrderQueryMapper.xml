<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">

<mapper namespace="com.ezwelesp.batch.lgportal.order.mapper.query.OrderQueryMapper">

    <!-- 주문완료 상태의 주문건수 조회-->
    <select id="selectOrderCountByClientOrderNum">
        SELECT
            COUNT(ord_no)
        FROM ez_or.or_ord_b
        WHERE clnt_ord_no = #{clientOrderNum}
        AND ord_st_cd = 'ORD_CMPT'
        AND clnt_cd = 'lgapi'
    </select>

    <select id="selectOrderInfoByClientOrderNum">
        SELECT ord.ord_no
            , ord.asp_ord_no
        FROM ez_or.or_ord_b ord
        WHERE clnt_ord_no = #{clientOrderNum}
          AND ord_st_cd = 'ORD_CMPT'
          AND clnt_cd = 'lgapi'
    </select>

    <select id="selectApiDlvrDetailNoCount">
        SELECT
            COUNT(dlvGds.lgp_api_dlv_dtl_no)
        FROM ez_or.or_ord_gds_d oogd
            INNER JOIN ez_or.dl_dlv_gds_d dlvGds ON (oogd.ord_no = dlvGds.ord_no
                                                     AND oogd.ord_gds_seq = dlvGds.ord_gds_seq)
        WHERE oogd.ord_no = #{orderNum}
            AND dlvGds.lgp_api_dlv_dtl_no = #{deliveryItemNo}
    </select>



</mapper>
