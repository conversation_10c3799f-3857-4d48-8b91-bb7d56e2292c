package com.ezwelesp.batch.lgportal.order.job;

import com.ezwelesp.batch.lgportal.entity.ApiBatchDlvrEntity;
import com.ezwelesp.batch.lgportal.order.chunk.reader.RegDeliveryLocItemReader;
import com.ezwelesp.batch.lgportal.order.chunk.reader.RegInvoiceNoItemReader;
import com.ezwelesp.batch.lgportal.order.chunk.writer.RegDeliveryLocItemWriter;
import com.ezwelesp.batch.lgportal.order.chunk.writer.RegInvoiceNoItemWriter;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.CspDlvrLocDto;
import com.ezwelesp.batch.listener.CommonJobListener;
import com.ezwelesp.batch.listener.CommonStepListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class RegDeliveryLocJobConfig {

    private final String REG_DELIVERY_LOC_STEP = LgConstants.JobName.JOB_REG_DELIVERY_LOC + "_STEP";

    private final CommonJobListener commonJobListener;

    @Value("${lgportal.chunk-size}")
    private int chunkSize;


    @Bean(LgConstants.JobName.JOB_REG_DELIVERY_LOC)
    public Job regInvoiceNoJob(JobRepository jobRepository, @Qualifier(REG_DELIVERY_LOC_STEP) Step regInvoiceNoStep) {
        return new JobBuilder(LgConstants.JobName.JOB_REG_DELIVERY_LOC, jobRepository)
                .listener(commonJobListener)
                .start(regInvoiceNoStep)
                .build();
    }

    @Bean(name = REG_DELIVERY_LOC_STEP)
    public Step regInvoiceNoStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager,
            RegDeliveryLocItemReader regDeliveryLocItemReader,
            RegDeliveryLocItemWriter regDeliveryLocItemWriter, CommonStepListener commonStepListener) {

        return new StepBuilder(REG_DELIVERY_LOC_STEP, jobRepository)
                .allowStartIfComplete(true)
                .<CspDlvrLocDto, CspDlvrLocDto>chunk(chunkSize, transactionManager)
                .transactionManager(transactionManager)
                .reader(regDeliveryLocItemReader)
                .writer(regDeliveryLocItemWriter)
                .listener(commonStepListener)
                .build();

    }
}
