package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 반품 접수 DTO
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto.ClaimReturnReqDto
 * @since 2025.02.17
 */
@Getter
@SuperBuilder
public class ClaimReturnReqDto {

    // 주문번호
    private String ordNo;

    // 반품접수 상품 리스트
    private List<ClaimReturnGoodsDto> ordGdsList;

    // 수거지변경 여부
    private boolean isChangeDeliveryAddress;

    // 수거방법코드
    private String pkupMthdCd;

    // 변경 수신자명
    private String rcvrNm;

    // 변경 수신자 전화번호
    private String rcvrMblTelno;

    // 변경 수신자 우편번호
    private String rcvrZipcd;

    // 변경 수신자 기본주소
    private String rcvrbasAdr;

    // 변경 수신자 상세주소
    private String rcvrDtlAdr;

    // 추가결제방법 코드
    private String paymentMethod;

    // 적립금전환귀책사유주체코드
    private String mlgSwtcAtbrMagnCd;

    // 예금주
    private String dpsrNm;

    // 금융기관 코드
    private String fnnsCd;

    // 계좌번호
    private String actNo;

    // 클레임 일시
    private String clmDtm;

    // 교환반품 배송정보
    private ClaimSaveDeliveryDto claimDelivery;
}
