package com.ezwelesp.batch.hims.client.contract.tasklet;

import com.ezwelesp.batch.hims.client.contract.domain.CtSlsContBAutoExtendDto;
import com.ezwelesp.batch.hims.client.contract.mapper.query.ContractAutoExtendQueryMapper;
import com.ezwelesp.batch.hims.client.contract.service.ContractAutoExtendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 임종갑 플그림
 * @see : com.ezwelesp.batch.hims.client.contract.tasklet
 * @since : 2025-06-16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContractAutoExtendTasklet implements Tasklet {
    private final ContractAutoExtendQueryMapper mapper;
    private final ContractAutoExtendService contractAutoExtendService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try{
            // 1. 계약기간 종료일이 to_date -1일 이고 자동갱신(Y)인 고객사 조회
            List<CtSlsContBAutoExtendDto> list = mapper.selectContractPeriodExpired();
            for (CtSlsContBAutoExtendDto contract : list) {
                // 2. 계약기간 종료일을 1년 후로 연장한다.
                contractAutoExtendService.modifyContractExtend(contract);
            }

        } catch (Exception e) {
            log.error("ContractAutoExtendTasklet Failed: {}", e.getMessage(), e);
        }
        return RepeatStatus.FINISHED;
    }
}
