package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * H포인트결제상세(ez_or.py_hpnt_pymt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyHpntPymtDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 결제번호(pymt_no) not null
     */
    private String pymtNo;

    /**
     * 결제구분코드(pymt_div_cd) not null
     */
    private String pymtDivCd;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * H포인트전문번호(hpnt_tlgm_no) not null
     */
    private String hpntTlgmNo;

    /**
     * H포인트선불충전포인트금액(hpnt_prpy_elctc_pnt_amt) not null
     */
    private BigDecimal hpntPrpyElctcPntAmt;

    /**
     * H포인트일반포인트금액(hpnt_gnrl_pnt_amt) not null
     */
    private BigDecimal hpntGnrlPntAmt;

    /**
     * 부가가치세금액(vat_amt) not null
     */
    private BigDecimal vatAmt;

    /**
     * 부가가치세제외금액(vat_excld_amt) not null
     */
    private BigDecimal vatExcldAmt;

    /**
     * 승인요청일시(apv_req_dtm) not null
     */
    private String apvReqDtm;

    /**
     * 승인일시(apv_dtm) not null
     */
    private String apvDtm;

    /**
     * H포인트승인번호(hpnt_apv_no) not null
     */
    private String hpntApvNo;

    /**
     * 정산일자(stl_dt) not null
     */
    private String stlDt;

    /**
     * H포인트참여회사코드(hpnt_ptcp_co_cd) not null
     */
    private String hpntPtcpCoCd;

    /**
     * H포인트가맹점코드(hpnt_frcs_cd) not null
     */
    private String hpntFrcsCd;

    /**
     * H포인트대리점코드(hpnt_dlsp_cd) not null
     */
    private String hpntDlspCd;

    /**
     * H포인트참여회사채널코드(hpnt_ptcp_co_ch_cd) not null
     */
    private String hpntPtcpCoChCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
