package com.ezwelesp.batch.hims.product.chuck.writer;

import com.ezwelesp.batch.hims.product.domain.dto.LgPortalProductSaleEndDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisBatchItemWriter;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@StepScope
@RequiredArgsConstructor
public class LgPortalProductSaleEndWriter extends MyBatisBatchItemWriter<LgPortalProductSaleEndDto> {

    public LgPortalProductSaleEndWriter(SqlSessionFactory sqlSessionFactory) {
        this.setSqlSessionFactory(sqlSessionFactory);
        this.setStatementId(
                "com.ezwelesp.batch.hims.product.mapper.command.ProductManagementCommandMapper.insertLgPortalProductSaleEnd");
    }
}
