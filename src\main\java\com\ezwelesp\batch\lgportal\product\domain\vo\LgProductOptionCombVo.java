package com.ezwelesp.batch.lgportal.product.domain.vo;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@<PERSON>as("ProductOptionCombVo")
@Getter
@SuperBuilder
public class LgProductOptionCombVo {

    private String gdsCd; // 상품 코드
    private Integer optGdsCombSeq; // 옵션상품 조합 순번
    private String cspOptGdsNo; // 협력사 옵션 상품번호
    private Long optAddPrc; // 옵션추가가격
    private Integer stckQty; //재고수량
    private String safeStckMngYn; //안전재고관리여부
    private Integer safeStckQty; //안전재고수량
    private String sttxPblcYn; //인지세발행여부
    private String useYn; // 사용여부
    private String imgPath; // 이미지경로
    private String gdsDtlImgPath; // 상품 상세 이미지 경로
    private Integer sortOrdg; // 정렬순서

    private String optNm; // 옵션종류 이름 (ex. 색상^|^사이즈)
    private String optVal; // 옵션디테일 이름 (ex. 빨강^|^XL)
    private String optGdsSeq; // 옵션코드(1-1_2-1_3-1)

}


