package com.ezwelesp.batch.hpas.alarm.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import com.ezwelesp.batch.hpas.alarm.tasklet.EzMemberStoreClosedTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class EzMembersStoreClosedJobConfig {
    private final EzMemberStoreClosedTasklet ezMemberStoreClosedTasklet;
    private final CommonJobListener commonJobListener;

    @Bean("ezMembersStoreClosedJob")
    public Job ezMembersStoreClosedJob(JobRepository jobRepository,
            @Qualifier("ezMembersStoreClosedStep") Step ezMembersStoreClosedStep) {
        return new JobBuilder("EzMembersStoreClosedJob", jobRepository)
                .listener(commonJobListener)
                .start(ezMembersStoreClosedStep)
                .build();
    }

    @Bean("ezMembersStoreClosedStep")
    public Step EzMembersStoreClosedStep(JobRepository jobRepository,
            DataSourceTransactionManager transactionManager) {
        return new StepBuilder("ezMembersStoreClosedStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(ezMemberStoreClosedTasklet, transactionManager)
                .build();

    }
}
