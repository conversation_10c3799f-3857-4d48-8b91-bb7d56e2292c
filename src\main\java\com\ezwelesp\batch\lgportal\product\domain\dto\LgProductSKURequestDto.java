package com.ezwelesp.batch.lgportal.product.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@<PERSON>as("LgProductSKUVo")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgProductSKURequestDto {

    private String productMappingCode; // 이지웰 상품코드
    private Integer productNo;         // lg상품코드

    private String sKUMappingCode;     // SKU 관리 코드값 (이지웰)
    private String sKUCode;            // SKU 코드 (Lg)
    private String sKUVal;             // SKU 값 (예: 빨강 ^|^90^|^밥솥)
    private Long controlSalesPrice;    // 상품 판매가에 추가 가격

    @Builder.Default
    private Long controlVendorPrice = 0L; // 상품 공급가에 추가 가격 (이지웰의 경우 고정값 0)

    private Integer stockCnt; // 재고 수량

    private String exposeYN; // 노출 여부 (Y/N)

    private Integer sortSeq; // 정렬 순서
}
