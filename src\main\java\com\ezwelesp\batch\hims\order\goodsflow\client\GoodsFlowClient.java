package com.ezwelesp.batch.hims.order.goodsflow.client;

import com.ezwelesp.framework.exception.ServiceException;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.ezwelesp.framework.utils.request.SyncRequest;
import com.ezwelesp.framework.utils.request.dto.Header;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.client.JdkClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.net.http.HttpClient;
import java.time.Duration;
import java.util.List;

import static com.ezwelesp.batch.hims.order.config.GoodsFlowConstants.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsFlowClient {
    private final GoodsFlowConfig goodsFlowConfig;
    private final ObjectMapper objectMapper;

    public <T, R> R post(GoodsFlowApiEnum api, T body, Class<R> responseType) {
        return this.post(api.getUrl(), getConfig(api.getType()), body, responseType);
    }

    public <T, R> R post(GoodsFlowApiEnum api, String pathVariable, T body, Class<R> responseType) {
        String url = String.format(api.getUrl(), pathVariable);

        return this.post(url, getConfig(api.getType()), body, responseType);
    }

    private <T, R> R post(String url, GoodsFlowConfig.Config config , T body, Class<R> responseType) {
        try {
            log.info("굿스플로 API Request: {}, body: {}", config.getHost() + url, body);
            HttpClient httpClient = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofSeconds(config.getTimeout()))
                    .build();

            RestClient restClient = RestClient.builder()
                    .requestFactory(new JdkClientHttpRequestFactory(httpClient))
                    .build();

            String bodyStr = JsonObjectConverter.serialize(body == null ? EMPTY_STRING : body);

            R response = objectMapper.convertValue(
                    new SyncRequest<String>(restClient, config.getHost())
                            .post(url, this.getHeaders(config), null, bodyStr), responseType);

            log.info("굿스플로 API Response: {}", response);

            return response;
        } catch (Exception e) {
            log.info("api error : {}", e.getMessage());
            throw new RuntimeException("GoodsFlowClient api request failed");
        }
    }

    private GoodsFlowConfig.Config getConfig(String type) {
        GoodsFlowConfig.Config config = goodsFlowConfig.getServer().getOrDefault(type, null);

        if (config == null) {
            throw new ServiceException();
        }

        return config;
    }

    private List<Header> getHeaders(GoodsFlowConfig.Config config) {
        return List.of(
                new Header("Content-Type", "application/json;charset=utf-8"),
                new Header("goodsFLOW-Api-Key", config.getApiKey()));
    }
}
