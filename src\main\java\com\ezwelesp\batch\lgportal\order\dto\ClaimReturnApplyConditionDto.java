package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 반품 접수 DTO
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto
 * @since 2025.05.09
 */
@Getter
@SuperBuilder
@Jacksonized
public class ClaimReturnApplyConditionDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -283787410333447034L;

    // 배송주소번호
    private Long dlvAdrNo;

    // 반품접수 상품 리스트/
    private List<ClaimReturnApplyGoodsDto> ordGdsList;

    // 수거지변경 여부
    private boolean changeDeliveryAddress;

    // 수거방법코드
    private String pkupMthdCd;

    // 수신자명
    private String rcvrNm;

    // 수신자 전화번호
    private String rcvrMblTelno;

    // 수신자 우편번호
    private String rcvrZipcd;

    // 수신자 기본주소
    private String rcvrBasAdr;

    // 수신자 상세주소
    private String rcvrDtlAdr;

    // 추가결제방법 코드
    private String paymentMethodCd;

    // 적립금전환귀책사유주체코드
    private String mlgSwtcAtbrMagnCd;

    // 예금주
    private String dpsrNm;

    // 금융기관 코드
    private String fnnsCd;

    // 계좌번호
    private String actNo;

    /*
     * Batch 에서 추가되는 파라미터
     */

    // 클레임배송기본
    private ClaimDeliveryBaseInfoDto claimDeliveryBase;

    // 클레임 일시
    private String clmDtm;

    // 반품배송비
    private BigDecimal dlvExp;
}
