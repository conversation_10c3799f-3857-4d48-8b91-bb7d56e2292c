package com.ezwelesp.batch.hims.order.giftorder.tasklet;

import com.ezwelesp.batch.hims.order.giftorder.domain.GiftOrderDto;
import com.ezwelesp.batch.hims.order.giftorder.service.GiftOrderAfterFourDaysAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.tasklet
 * @since 2025.05.15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GiftOrderAfterFourDaysAlarmTasklet implements Tasklet {
    private final GiftOrderAfterFourDaysAlarmService giftOrderAfterFourDaysAlarmService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        List<GiftOrderDto> giftOrderList = giftOrderAfterFourDaysAlarmService.getGiftOrderAfterFourDaysList();

        if (giftOrderList.isEmpty()) {
            log.debug("GiftOrderAfterFourDaysAlarmTasklet 대상 없음");
        } else {
            for (GiftOrderDto giftOrder : giftOrderList) {
                try {
                    giftOrderAfterFourDaysAlarmService.sendAlarmAndSaveStatus(giftOrder);
                } catch (Exception e) {
                    log.error("GiftOrderAfterFourDaysAlarmTasklet Failed: {}", e.getMessage(), e);
                }
            }
        }

        return RepeatStatus.FINISHED;
    }
}
