package com.ezwelesp.batch.hims.order.goodsflow.domain.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 굿스플로 반품취소 response
 */
@Getter
@Setter
@ToString
public class ReturnCancelResponse {
    private boolean success;
    private Object data;
    private Error error;

    @Getter
    @Setter
    @ToString
    public static class Error {
        private int status;
        private String message;
        private String detail;
    }
}
