package com.ezwelesp.batch.hims.order.goodsflow.tasklet;

import com.ezwelesp.batch.hims.order.goodsflow.service.GoodsFlowTraceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsFlowDeleteOldDataTasklet implements Tasklet {
    private final GoodsFlowTraceService goodsFlowTraceService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            goodsFlowTraceService.deleteGikimimastRecordBeforeOneYear();

             goodsFlowTraceService.deleteGikimiresultRecordBeforeOneYear();
        } catch (Exception e) {
            log.error("GoodsFlowDeleteOldDataTasklet failed", e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.debug("GoodsFlowDeleteOldDataTasklet finished");

        return RepeatStatus.FINISHED;
    }
}
