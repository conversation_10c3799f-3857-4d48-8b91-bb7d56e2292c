package com.ezwelesp.batch.hims.order.pgcompare.naverpay.mapper.command;

import com.ezwelesp.batch.hims.order.entity.external.ApiPgPrsnlPayEntity;
import com.ezwelesp.batch.hims.order.entity.external.EzEcOrderPayLogEntity;

public interface NaverpayCommandMapper {

	void insertNaverpayOrderLog(EzEcOrderPayLogEntity ezEcOrderPayLogEntity);

	void insertNaverpayOrder(ApiPgPrsnlPayEntity apiPgPrsnlPayEneity);

	void updateOrderNaverPay(ApiPgPrsnlPayEntity apiPgPrsnlPayEntity);

}
