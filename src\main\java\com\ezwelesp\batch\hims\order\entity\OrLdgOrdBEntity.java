package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 숙박주문기본(ez_or.or_ldg_ord_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrLdgOrdBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 숙박주문지원차수(ldg_ord_afe_nos) not null
     */
    private Long ldgOrdAfeNos;

    /**
     * 숙박고객사제도번호(ldg_clnt_rgm_no) not null
     */
    private Long ldgClntRgmNo;

    /**
     * 숙박성수기정책번호(ldg_pksn_plcy_no) not null
     */
    private Long ldgPksnPlcyNo;

    /**
     * 객실코드(room_cd) not null
     */
    private String roomCd;

    /**
     * 숙박시설지점코드(accm_br_cd) not null
     */
    private String accmBrCd;

    /**
     * 숙박그룹코드(ldg_grp_cd) not null
     */
    private String ldgGrpCd;

    /**
     * 시즌구분코드(seas_div_cd) not null
     */
    private String seasDivCd;

    /**
     * 숙박주문신청차수(ldg_ord_apl_nos)
     */
    private Long ldgOrdAplNos;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 사용자고유번호(user_key)
     */
    private String userKey;

    /**
     * 신청자명(aplr_nm)
     */
    private String aplrNm;

    /**
     * 신청자이메일주소(aplr_eml_adr)
     */
    private String aplrEmlAdr;

    /**
     * 신청자전화번호(aplr_telno)
     */
    private String aplrTelno;

    /**
     * 신청자모바일전화번호(aplr_mbl_telno)
     */
    private String aplrMblTelno;

    /**
     * 주문일자(ord_dt)
     */
    private String ordDt;

    /**
     * 예약확정시분(rsv_cnfm_hm)
     */
    private String rsvCnfmHm;

    /**
     * 입실일자(ckin_dt)
     */
    private String ckinDt;

    /**
     * 퇴실일자(ckou_dt)
     */
    private String ckouDt;

    /**
     * 객실수(room_cnt)
     */
    private Integer roomCnt;

    /**
     * 객실사용성인수(room_use_adlt_cnt) not null
     */
    private Integer roomUseAdltCnt;

    /**
     * 객실사용유아수(room_use_infnt_cnt) not null
     */
    private Integer roomUseInfntCnt;

    /**
     * 숙박주문예약상태코드(ldg_ord_rsv_st_cd) not null
     */
    private String ldgOrdRsvStCd;

    /**
     * 숙박주문예약불가사유코드(ldg_ord_rsv_ndmt_rsn_cd)
     */
    private String ldgOrdRsvNdmtRsnCd;

    /**
     * 숙박예약번호(ldg_rsv_no)
     */
    private String ldgRsvNo;

    /**
     * 상품매입가격(gds_pchs_prc) not null
     */
    private BigDecimal gdsPchsPrc;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * 결제요청일자(pymt_req_dt)
     */
    private String pymtReqDt;

    /**
     * 결제완료일자(pymt_cmpt_dt)
     */
    private String pymtCmptDt;

    /**
     * 결제취소요청일자(pymt_cncl_req_dt)
     */
    private String pymtCnclReqDt;

    /**
     * 결제취소완료일자(pymt_cncl_cmpt_dt)
     */
    private String pymtCnclCmptDt;

    /**
     * 숙박주문결제상태코드(ldg_ord_pymt_st_cd)
     */
    private String ldgOrdPymtStCd;

    /**
     * 숙박주문취소결제상태코드(ldg_ord_cncl_pymt_st_cd)
     */
    private String ldgOrdCnclPymtStCd;

    /**
     * 당년사용차감점수(tyr_use_sbtr_scor)
     */
    private Integer tyrUseSbtrScor;

    /**
     * 익년사용차감점수(nyr_use_sbtr_scor)
     */
    private Integer nyrUseSbtrScor;

    /**
     * 취소수수료주문여부(cncl_cms_ord_yn) not null
     */
    private String cnclCmsOrdYn;

    /**
     * 주문요청메모(ord_req_memo)
     */
    private String ordReqMemo;

    /**
     * 관리자처리메모(mgr_prcs_memo)
     */
    private String mgrPrcsMemo;

    /**
     * 예약처리메모(rsv_prcs_memo)
     */
    private String rsvPrcsMemo;

    /**
     * 결제처리메모(pymt_prcs_memo)
     */
    private String pymtPrcsMemo;

    /**
     * 결제이전관리자선등록여부(pymt_bef_mgr_prrg_yn) not null
     */
    private String pymtBefMgrPrrgYn;

    /**
     * 숙박주문당첨여부(ldg_ord_win_yn) not null
     */
    private String ldgOrdWinYn;

    /**
     * 숙박주문확정여부(ldg_ord_cnfm_yn) not null
     */
    private String ldgOrdCnfmYn;

    /**
     * 관리자생성주문여부(mgr_crt_ord_yn) not null
     */
    private String mgrCrtOrdYn;

    /**
     * 숙박주문결제시점코드(ldg_ord_pymt_pntm_cd)
     */
    private String ldgOrdPymtPntmCd;

    /**
     * 웹팩스발송여부(web_fax_snd_yn) not null
     */
    private String webFaxSndYn;

    /**
     * 웹팩스발송일시(web_fax_snd_dtm)
     */
    private String webFaxSndDtm;

    /**
     * 당첨포상숙박여부(win_prz_ldg_yn) not null
     */
    private String winPrzLdgYn;

    /**
     * 부대시설할인쿠폰발송여부(adfc_dc_cpn_snd_yn) not null
     */
    private String adfcDcCpnSndYn;

    /**
     * 부부공무원여부(mcpl_gemp_yn) not null
     */
    private String mcplGempYn;

    /**
     * 수기예약여부(hndw_rsv_yn) not null
     */
    private String hndwRsvYn;

    /**
     * 접속디바이스코드(acss_dvc_cd)
     */
    private String acssDvcCd;

    /**
     * 모바일주문여부(mbl_ord_yn) not null
     */
    private String mblOrdYn;

    /**
     * 문자메시지수신동의여부(tmsg_rcv_agr_yn) not null
     */
    private String tmsgRcvAgrYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
