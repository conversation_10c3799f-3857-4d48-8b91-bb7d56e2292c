package com.ezwelesp.batch.lgportal.product.mapper.query;

import com.ezwelesp.batch.lgportal.product.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ProductLGPortalQueryMapper {

    List<LgAddProductTargetVo> selectLgAddProductTarget();

    List<LgProductOptionCombVo> selectLgProductOptionByGdsCds(List<String> gdsCdList);

    List<LgProductAnnouncementVo> selectLgProductAnnouncementByGdsCd(List<String> gdsCdList);

    List<LgEditProductTargetVo> selectLgEditProductTarget();

    List<LgEditProductDeliveryTargetVo> selectLgEditProductDeliveryTargetByGdsCd(String gdsCd);

    List<LgEditProductTargetVo> selectLgEditProductPriceTarget();

    List<LgEditProductTargetVo> selectLgEditProductSoldOutTarget();

    List<LgEditProductTargetVo> selectLgEditProductSKUTarget();

    LgProductContentsVo selectLgProductContentByGdsCd(String gdsCd);

    Map<String, Integer> selectLgProductQnaCnt(List<String> inquiryNoList);

    List<LgQnaProductInfoVo> selectLgQnaProductInfo(List<String> productNoList);

    List<LgQnaAutoAnswerTargetVo> productQnAAutoAnswerTarget();

}

