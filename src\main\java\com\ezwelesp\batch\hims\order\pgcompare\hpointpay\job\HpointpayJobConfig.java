package com.ezwelesp.batch.hims.order.pgcompare.hpointpay.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.order.pgcompare.hpointpay.tasklet.HpointpayTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 현대포인트 페이 결제대사
 */

@Slf4j
@Configuration
@RequiredArgsConstructor
public class HpointpayJobConfig {
	private final CommonJobListener commonJobListener;
	private final HpointpayTasklet hpointpayTasket;
	
	@Bean("BA_HIOR00017")
	public Job BA_HIOR00017(JobRepository jobRepository
			, @Qualifier("BA_HIOR00017_STEP1") Step step1
			, @Qualifier("BA_HIOR00017_STEP2") Step step2) {
		return new JobBuilder("BA_HIOR00017", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(step1)
                .next(step2)
                .build();
	}
	
	/**
	 * Hpoint 결제수집
	 * @param jobRepository
	 * @param transactionManager
	 * @return
	 */
	@Bean("BA_HIOR00017_STEP1")
	public Step BA_HIOR00017_STEP1(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
		  return new StepBuilder("BA_HIOR00017_STEP1", jobRepository)
	                .allowStartIfComplete(true)
	                .tasklet(hpointpayTasket, transactionManager)
	                .build();
		
	}
	
	/**
	 * Hpoint 결제대사
	 * @param jobRepository
	 * @param transactionManager
	 * @return
	 */
	@Bean("BA_HIOR00017_STEP2")
	public Step BA_HIOR00017_STEP2(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
		  return new StepBuilder("BA_HIOR00017_STEP2", jobRepository)
	                .allowStartIfComplete(true)
	                .tasklet(hpointpayTasket, transactionManager)
	                .build();
		
	}
	
}
