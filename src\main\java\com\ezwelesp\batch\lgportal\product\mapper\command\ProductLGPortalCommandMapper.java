package com.ezwelesp.batch.lgportal.product.mapper.command;

import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaAutoAnswerListDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaInsertListDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductSKURequestDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgAddProductTargetVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ProductLGPortalCommandMapper {

    void insertApiBatchGoodsBeforeSend(LgAddProductTargetVo lgAddProductTargetVo);

    void updateApiBatchGoodsAfterSend(LgProductApiResponseDto item);

    void updateListApiBatchSendStatus(List<LgProductApiResponseDto> items);

    void updateApiBatchSendStatus(LgProductApiResponseDto item);

    void insertApiBatchSKUStatus(LgProductSKURequestDto requestItem, LgProductApiResponseDto responseItem);

    void insertApiProductQna(LgProductQnaInsertListDto item);

    void insertApiProductLgQnaAutoAnswer(LgProductQnaAutoAnswerListDto item);

    void deleteApiBatchSendBefore30Days();

    void deleteApiBatchSkuBefore30Days();


}

