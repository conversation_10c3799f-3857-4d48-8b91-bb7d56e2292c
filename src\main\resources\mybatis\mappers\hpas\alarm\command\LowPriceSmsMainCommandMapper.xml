<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hpas.alarm.mapper.command.LowPriceSmsMainCommandyMapper">

    <insert id="insertLowPriceSmsMainError">
        insert into ez_pd.pd_gds_lwst_prc_bat_err_l (
            gds_lwpr_bat_err_no
          , lwpr_bat_err_typ_cd
          , err_msg_cntn
          , frst_reg_dtm
          , frst_reg_usr_id
          , frst_reg_pgm_id
          , last_mod_dtm
          , last_mod_usr_id
          , last_mod_pgm_id)
        values (
			(select coalesce(max(gds_lwpr_bat_err_no), 1000000000) + 1 from ez_pd.pd_gds_lwst_prc_bat_err_l)
		  , #{lwpr_bat_err_typ_cd}
		  , #{err_msg_cntn}
		  , to_char(now(), 'yyyymmddhh24miss')
		  , 'BATCH'
		  , 'BATCH'
		  , to_char(now(), 'yyyymmddhh24miss')
		  , 'BATCH'
		  , 'BATCH'
		)
    </insert>

</mapper>
