package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * NH멤버스사용취소오류로그(ez_or.py_nhmbrs_use_cncl_err_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyNhmbrsUseCnclErrGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 채널코드(ch_cd) not null
     */
    private String chCd;

    /**
     * NH멤버스거래고유번호(nhmbrs_trd_innt_no) not null
     */
    private String nhmbrsTrdInntNo;

    /**
     * NH멤버스거래결과코드(nhmbrs_trd_rslt_cd) not null
     */
    private String nhmbrsTrdRsltCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최초등록IP(frst_reg_ip) not null
     */
    private String frstRegIp;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
