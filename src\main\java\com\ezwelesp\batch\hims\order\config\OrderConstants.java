package com.ezwelesp.batch.hims.order.config;

import lombok.Getter;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.config
 * @since 2025.05.15
 */
public class OrderConstants {

    /**
     * 선물하기주문상태코드
     *
     * <AUTHOR>
     * @see com.ezwelesp.fo.order.rest.order.config
     * @since 2025.05.09
     */
    @Getter
    public enum GiftStatusCodeEnum {
        GIVE_GIFT("1001", "선물하기"),
        SEND_GIFT("1002", "선물발송(카카오 알림톡)"),
        READ_GIFT("1003", "선물열람"),
        ENTER_ADR("1004", "선물입력(배송지입력)"),
        RESEND_GIFT("1005", "선물URL재발송"),
        EDIT_ADR("1006", "선물입력(배송지변경)"),
        ENCRG_GIFT("1007", "선물하기 독려-배치"),
        CHNG_OPT("1008", "선물입력(옵션변경)"),
        RJT_USR("1010", "선물거절(사용자)"),
        OSTK_GIFT("1011", "선물거절(품절)"),
        CNCL_GIFT("1012", "선물취소"),
        DLV_GIFT("1019", "배송중"),
        CMPT_GIFT("1020", "선물완료"),
        ;

        private final String code;
        private final String name;

        GiftStatusCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
