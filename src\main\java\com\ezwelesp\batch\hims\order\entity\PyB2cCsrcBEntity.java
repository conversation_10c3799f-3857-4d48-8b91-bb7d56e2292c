package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * B2C현금영수증기본(ez_or.py_b2c_csrc_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyB2cCsrcBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 이지웰마켓B2C주문번호(ezmkt_b2c_ord_no) not null
     */
    private String ezmktB2cOrdNo;

    /**
     * 이지웰마켓B2C주문상품순번(ezmkt_b2c_ord_gds_seq) not null
     */
    private Long ezmktB2cOrdGdsSeq;

    /**
     * 결제차수(pymt_nos) not null
     */
    private Long pymtNos;

    /**
     * 현금영수증수기처리차수(csrc_hndw_prcs_nos) not null
     */
    private Long csrcHndwPrcsNos;

    /**
     * 현금영수증발행대상일자(csrc_pblc_obj_dt) not null
     */
    private String csrcPblcObjDt;

    /**
     * 현금영수증PG승인번호(csrc_pg_apv_no)
     */
    private String csrcPgApvNo;

    /**
     * 현금영수증승인번호(csrc_apv_no)
     */
    private String csrcApvNo;

    /**
     * 현금영수증승인일시(csrc_apv_dtm)
     */
    private String csrcApvDtm;

    /**
     * 현금영수증공급가격(csrc_spl_prc) not null
     */
    private BigDecimal csrcSplPrc;

    /**
     * 현금영수증부가가치세금액(csrc_vat_amt) not null
     */
    private BigDecimal csrcVatAmt;

    /**
     * 현금영수증발행취소일자(csrc_pblc_cncl_dt)
     */
    private String csrcPblcCnclDt;

    /**
     * 취소현금영수증PG승인번호(cncl_csrc_pg_apv_no)
     */
    private String cnclCsrcPgApvNo;

    /**
     * 현금영수증취소승인일시(csrc_cncl_apv_dtm)
     */
    private String csrcCnclApvDtm;

    /**
     * 계좌이체결제금액(acnt_trns_pymt_amt) not null
     */
    private BigDecimal acntTrnsPymtAmt;

    /**
     * 전체결제금액(all_pymt_amt) not null
     */
    private BigDecimal allPymtAmt;

    /**
     * 상품금액비율(gds_amt_rt) not null
     */
    private Double gdsAmtRt;

    /**
     * 현금영수증상품발행금액(csrc_gds_pblc_amt) not null
     */
    private BigDecimal csrcGdsPblcAmt;

    /**
     * 상품공급가격(gds_spl_prc) not null
     */
    private BigDecimal gdsSplPrc;

    /**
     * 상품부가가치세금액(gds_vat_amt) not null
     */
    private BigDecimal gdsVatAmt;

    /**
     * 현금영수증발행용도코드(csrc_pblc_usg_cd)
     */
    private String csrcPblcUsgCd;

    /**
     * 현금영수증발행번호(csrc_pblc_no)
     */
    private String csrcPblcNo;

    /**
     * 사용자고유번호(user_key)
     */
    private String userKey;

    /**
     * 주문자명(ordr_nm)
     */
    private String ordrNm;

    /**
     * 주문자이메일주소(ordr_eml_adr)
     */
    private String ordrEmlAdr;

    /**
     * 주문자모바일전화번호(ordr_mbl_telno)
     */
    private String ordrMblTelno;

    /**
     * 협력사코드(csp_cd)
     */
    private String cspCd;

    /**
     * 협력사명(csp_nm)
     */
    private String cspNm;

    /**
     * 협력사사업자등록번호(csp_bzrn)
     */
    private String cspBzrn;

    /**
     * 협력사현금영수증발행여부(csp_csrc_pblc_yn) not null
     */
    private String cspCsrcPblcYn;

    /**
     * 협력사현금영수증발행주체코드(csp_csrc_pblc_magn_cd)
     */
    private String cspCsrcPblcMagnCd;

    /**
     * 현금영수증사업자등록번호대상코드(csrc_bzrn_obj_cd)
     */
    private String csrcBzrnObjCd;

    /**
     * 현금영수증발행대상상품여부(csrc_pblc_obj_gds_yn) not null
     */
    private String csrcPblcObjGdsYn;

    /**
     * 현금영수증발행여부(csrc_pblc_yn) not null
     */
    private String csrcPblcYn;

    /**
     * 과세종류코드(taxn_knd_cd)
     */
    private String taxnKndCd;

    /**
     * 상품코드(gds_cd)
     */
    private String gdsCd;

    /**
     * 상품명(gds_nm)
     */
    private String gdsNm;

    /**
     * 현금영수증발행유형코드(csrc_pblc_typ_cd)
     */
    private String csrcPblcTypCd;

    /**
     * 비고(rmrk)
     */
    private String rmrk;

    /**
     * PG가맹점번호(pg_frcs_no) not null
     */
    private String pgFrcsNo;

    /**
     * 대한상공회의소포인트(kcci_pnt)
     */
    private BigDecimal kcciPnt;

    /**
     * 네이버페이현금영수증발행금액(nvpay_csrc_pblc_amt) not null
     */
    private BigDecimal nvpayCsrcPblcAmt;

    /**
     * 문화비소득공제대상여부(clxp_itd_obj_yn) not null
     */
    private String clxpItdObjYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
