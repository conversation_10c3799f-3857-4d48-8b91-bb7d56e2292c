package com.ezwelesp.batch.aasample.test.chunk.writer;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisBatchItemWriter;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.aasample.test.domain.Test;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@StepScope
public class ChunkSampleItemWriter extends MyBatisBatchItemWriter<Test> {
    public ChunkSampleItemWriter(@Qualifier("primaryCommandSqlSessionFactory") SqlSessionFactory primaryCommandSqlSessionFactory) {
    	this.setSqlSessionFactory(primaryCommandSqlSessionFactory);
        this.setStatementId("com.ezwelesp.batch.aasample.test.mapper.command.UserCommandMapper.updateNtcTitl");
    }

}
