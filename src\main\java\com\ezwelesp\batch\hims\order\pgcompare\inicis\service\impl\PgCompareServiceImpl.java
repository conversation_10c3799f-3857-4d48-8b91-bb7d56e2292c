package com.ezwelesp.batch.hims.order.pgcompare.inicis.service.impl;


import com.ezwelesp.batch.hims.order.config.PgCompareConstants;
import com.ezwelesp.batch.hims.order.entity.external.EzEcOrderPayLogEntity;
import com.ezwelesp.batch.hims.order.pgcompare.inicis.mapper.command.PgCompareCommandMapper;
import com.ezwelesp.batch.hims.order.pgcompare.inicis.mapper.query.PgCompareQueryMapper;
import com.ezwelesp.batch.hims.order.pgcompare.inicis.service.PgCompareService;
import com.ezwelesp.batch.hims.order.utils.OrderJsonUtils;
import com.ezwelesp.framework.exception.ServiceException;
import com.ezwelesp.framework.utils.DateUtils;
import com.ezwelesp.framework.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClient;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Calendar;
import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class PgCompareServiceImpl implements PgCompareService {

    private final PgCompareCommandMapper pgCompareCommandMapper;
    private final PgCompareQueryMapper pgCompareQueryMapper;

    private final String InicisBaseUrl = "https://iniweb.inicis.com";





    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int createEzEcOrderPayLog(EzEcOrderPayLogEntity orderPayLog) {
        log.debug("orderPayLog:{}", OrderJsonUtils.convertToJson(orderPayLog));
        if(pgCompareQueryMapper.selectEzEcOrderPayLogCount(orderPayLog)>0){
            return pgCompareCommandMapper.updateEzEcOrderPayLog(orderPayLog);
        }else{
            return pgCompareCommandMapper.insertEzEcOrderPayLog(orderPayLog);
        }
    }
    //List<EzEcOrderPayLogEntity>

    private String getField(String[] cols, int idx) {
        return getField(cols,idx,0);
    }
    private String getField(String[] cols, int idx, int gap) {
        if (cols[idx+gap] != null)
            return cols[idx+gap].trim();
        else
            return "";
    }

    private String getAddDay(String yyyyMMdd, int dayCnt) {

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Calendar c = Calendar.getInstance();

            Date d = sdf.parse(yyyyMMdd);

            Calendar cal = Calendar.getInstance();
            cal.setTime(d);

            cal.add(Calendar.DATE, dayCnt);

            return sdf.format(cal.getTime());
        }catch (Exception e){
            log.error(e.getMessage(),e);
            throw new ServiceException();
        }
    }

    @Override
    public void connectInicisPgCompareData() {

        String query = "?";
        String urlid = "ezweltsGID";
        String passwd = "1111";
        String gmid = "1";
        String date = getAddDay(DateUtils.dateTimeNow("yyyyMMdd"),-1);
        String stime = "000000";
        String etime = "235959";
        //url조합
        query += "urlid=" + urlid;
        query += "&passwd=" + passwd;
        query += "&date=" + date;
        query += "&stime=" + stime;
        query += "&etime=" + etime;
        query += "&gmid=" + gmid;

        this.getCardCompareData(query, urlid);
        this.getAcctCompareData(query, urlid);
        this.getHppCompareData(query, urlid);

    }

    private void getCardCompareData(String query, String urlid) {
        String responStr = this.connectPgCall(PgCompareConstants.INICIS_PG_COMPARE_CARD_URL, query);

        log.debug("responStr:{}",responStr);

        if (responStr != null && !"".equals(responStr) && responStr.length() > 30) {
            //줄바꿈이 없고 "<br>"로 줄바꿈됨
            //상품명에도 "<br>"이 있어 |<br>urlid 로 줄바꿈함
            String[] rows = ("|<br>" + responStr.trim() + urlid).split("\\|<br>" + urlid);

            if (rows != null && rows.length > 0) {
                int total = rows.length;

                //같은목록에 승인 및 취소가 있을때 취소row가 먼저 나와 최종적으로 '승인'으로 update되어서 루프를 거꾸로 돌림
                //for(int i=0; i<total; i++)
                for (int i = total - 1; i >= 0; i--) {

                    if (rows[i] != null && !"".equals(rows[i])) {

                        log.debug("{}:row:{}",i,rows[i]);
                        String[] cols = rows[i].split("\\|");

                        /*
										if(actCnt < 10)
										{
											for(int j=0; j<cols.length; j++)
												System.out.println("==================================== " + j + " : " + cols[j]);
										}
										*/

										/*
										==================================== 0 :
										==================================== 1 : 일반
										==================================== 2 : ezwel00001
										==================================== 3 : StdpayDBNKezwel0000120191231134011682122

										==================================== 4 : 원거래TID 추가됨

										==================================== 5 : je_1055281472
										==================================== 6 : 20191231
										==================================== 7 : 134012
										==================================== 8 : 20200116
										==================================== 9 : 155804
										==================================== 10 : 취소
										==================================== 11 : 전체취소
										==================================== 12 : 18900
										==================================== 13 : 박금자
										==================================== 14 : 실시간계좌이체
										==================================== 15 :
										==================================== 16 : 박금자
										==================================== 17 : 전북은행
										==================================== 18 :
										==================================== 19 : [오늘만]그린캠프 키모 아동 방한 부츠
										*/

                        //공통
                        String tid = getField(cols, 3,2);
                        String orderNum = getField(cols, 5,2);
                        String ezwelOrderNum = "";
                        String storeId = getField(cols, 2,2);
                        String dealType = "B";
                        String deviceType = getField(cols, 1,2);
                        String payType = getField(cols, 14,2);
                        String dealStatus = getField(cols, 10,2);
                        String confirmDd = getField(cols, 6,2);
                        String confirmTm = getField(cols, 7,2);
                        String cancelDd = getField(cols, 8,2);
                        String cancelTm = getField(cols, 9,2);
                        String dealAmt = getField(cols, 12,2);
                        String cancelAmt = "0";
                        String goodsNm = getField(cols, 19,2);

                        String cancelTid = getField(cols, 4,2);

                        //카드
                        String cardAmt = "0";
                        String alliPoint = "0";
                        String instMm = "";
                        String cardType = "";
                        String confirmNum = "";
                        String simplePay = "";

                        //계좌이체
                        String cancelRemainAmt = getField(cols, 15,2);
                        String bankNm = getField(cols, 17,2);

                        //추가필드
                        String payDt = "";            //지불일시
                        String statusCd = "";        //상태코드

                        //취소일 경우
                        if (dealStatus != null && dealStatus.indexOf("취소") > -1) {
                            cancelAmt = getField(cols, 12,2);
                        }

                        //주문번호가 없거나 뒤 10자리가 숫자가 아닌경우가 있어 처리
                        try {
                            String tmpOrderNum = "";

                            //이지웰 주문번호 : orderNum 뒤 10자리
                            if (orderNum != null && orderNum.length() > 10)
                                tmpOrderNum = orderNum.substring(orderNum.length() - 10, orderNum.length());
                            else
                                tmpOrderNum = orderNum;

                            //ezwelOrderNum 숫자변환
                            ezwelOrderNum = "" + Long.parseLong(tmpOrderNum);
                        } catch (Exception e) {
                        }

                        if (orderNum == null || "".equals(orderNum))
                            orderNum = "EMPTY";
                        if (ezwelOrderNum == null || "".equals(ezwelOrderNum))
                            ezwelOrderNum = "0";

                        //상품명에 '|'가 있어 cols.length > 20 이면 뒤로 붙인다
                        if (cols.length > 20) {
                            for (int k = 20; k < cols.length; k++)
                                goodsNm += "|" + cols[k];
                        }
                        if (goodsNm != null)
                            goodsNm = goodsNm.trim();

                        //상태코드 승인:0001, 취소:0002, 나머지:9999
                        if (dealStatus.indexOf("승인") > -1)
                            statusCd = "0001";
                        else if (dealStatus.indexOf("취소") > -1)
                            statusCd = "0002";
                        else
                            statusCd = "9999";

                        //지불일시
                        if ("0002".equals(statusCd))
                            payDt = cancelDd + "" + cancelTm;
                        else
                            payDt = confirmDd + "" + confirmTm;
                        //cancelTid가 없을경우 승인tid 넣는다
                        if (cancelTid == null || "".equals(cancelTid))
                            cancelTid = tid;

                        EzEcOrderPayLogEntity cardLog =
                                EzEcOrderPayLogEntity.builder().tid(tid).orderNum(orderNum)
                                        .ezwelOrderNum(Long.valueOf(ezwelOrderNum))
                                        .storeId(storeId)
                                        .dealType(dealType)
                                        .payType(payType)
                                        .dealStatus(dealStatus)
                                        .confirmDd(confirmDd)
                                        .confirmTm(confirmTm)
                                        .cancelDd(cancelDd)
                                        .confirmTm(confirmTm)
                                        .cancelDd(cancelDd)
                                        .cancelTm(cancelTm)
                                        .dealAmt(new BigDecimal(dealAmt))
                                        .cancelAmt(new BigDecimal(cancelAmt))
                                        .cancelTid(cancelTid)
                                        .cardAmt(new BigDecimal(cardAmt))
                                        .alliPoint(alliPoint)
                                        .deviceType(deviceType)
                                        .pgType("I")
                                        .instMm(instMm)
                                        .alliPoint(alliPoint)
                                        .cardType(cardType)
                                        .confirmNum(confirmNum)
                                        .simplePay(simplePay)
                                        .cancelRemainAmt(new BigDecimal((StringUtils.isEmpty(cancelRemainAmt)?"0":cancelRemainAmt)))
                                        .bankNm(bankNm)
                                        .goodsNm(goodsNm)
                                        .statusCd(statusCd)
                                        .payDt(payDt).build();



                        //승인일이 현재 일시와 같으면 insert 하지 않는다 (다음 배치때 insret) TODO 해당 로직을 유지하는지 고민
//                                    String confirmDdTm = "";
//                                    if(confirmDd != null)							confirmDdTm += confirmDd;					//승인 일
//                                    if(confirmTm != null && confirmTm.length() > 1) confirmDdTm += confirmTm.substring(0, 2);	//승인 시분초 앞 두자리

//                                    if(batchActionDt.equals(confirmDdTm))
//                                    {
//                                        log.debug("======= [이니시스 카드 대사] " + actionDt + " PASS : " + dataMap);
//                                    }

                        createEzEcOrderPayLog(cardLog);
                    }
                }
            }
        }
    }

    private void getHppCompareData(String query, String urlid) {
        String responStr = connectPgCall(PgCompareConstants.INICIS_PG_COMPARE_HPP_URL, query);
        if (responStr != null && !"".equals(responStr) && responStr.length() > 30) {
            //줄바꿈이 없고 "<br>"로 줄바꿈됨
            //상품명에도 "<br>"이 있어 |<br>urlid 로 줄바꿈함
            String[] rows = ("|<br>" + responStr.trim() + urlid).split("\\|<br>" + urlid);

            if (rows != null && rows.length > 0) {
                int total = rows.length;

                //같은목록에 승인 및 취소가 있을때 취소row가 먼저 나와 최종적으로 '승인'으로 update되어서 루프를 거꾸로 돌림
                //for(int i=0; i<total; i++)
                for (int i = total - 1; i >= 0; i--) {

                    if (rows[i] != null && !"".equals(rows[i])) {

                        String[] cols = rows[i].split("\\|");

                        if (cols != null && cols.length > 0) {
                                    /*
                                    if(actCnt < 10)
                                    {
                                        for(int j=0; j<cols.length; j++)
                                            System.out.println("==================================== " + j + " : " + cols[j]);
                                    }
                                    */

                                    /*
                                    ==================================== 0 :
                                    ==================================== 1 :
                                    ==================================== 2 :
                                    ==================================== 3 : MOBILE
                                    ==================================== 4 : ezwel00001
                                    ==================================== 5 : INIMX_CARDezwel0000120191228123419975167
                                    ==================================== 6 : 1055241216
                                    ==================================== 7 : 20191228
                                    ==================================== 8 : 123420
                                    ==================================== 9 : 20200116
                                    ==================================== 10 : 131948
                                    ==================================== 11 : 매입후취소
                                    ==================================== 12 : 전체취소
                                    ==================================== 13 : 153000
                                    ==================================== 14 : 박태화
                                    ==================================== 15 : 신용카드
                                    ==================================== 16 :
                                    ==================================== 17 : 153000
                                    ==================================== 18 : 0
                                    ==================================== 19 : 00
                                    ==================================== 20 : 롯데카드
                                    ==================================== 21 : 51654337
                                    ==================================== 22 : 일반
                                    ==================================== 23 : 스플라스 리솜(구,리솜스파캐슬 덕산)
                                    */

                            //공통
                            String tid = getField(cols, 5);
                            String orderNum = getField(cols, 6);
                            String ezwelOrderNum = "";
                            String storeId = getField(cols, 4);
                            String dealType = "C";
                            String deviceType = getField(cols, 3);
                            String payType = getField(cols, 15);
                            String dealStatus = getField(cols, 11);
                            String confirmDd = getField(cols, 7);
                            String confirmTm = getField(cols, 8);
                            String cancelDd = getField(cols, 9);
                            String cancelTm = getField(cols, 10);
                            String dealAmt = getField(cols, 13);
                            String cancelAmt = "0";
                            String goodsNm = cols[23];

                            //카드
                            String cancelTid = getField(cols, 16);
                            String cardAmt = getField(cols, 17);
                            String alliPoint = getField(cols, 18);
                            String instMm = getField(cols, 19);
                            String cardType = getField(cols, 20);
                            String confirmNum = getField(cols, 21);
                            String simplePay = getField(cols, 22);

                            //계좌이체
                            String cancelRemainAmt = "";
                            String bankNm = "";

                            //추가필드
                            String payDt = "";            //지불일시
                            String statusCd = "";        //상태코드

                            //2020.06.18 전체취소일 경우도 insert
                                    /*
                                    //cancelTi가 있을경우 기존 승인row를 update하기 위해 tid <-> cancelTid 교체한다
                                    if(cancelTid != null && !"".equals(cancelTid))
                                    {
                                        tid = getField(cols, 16);
                                        cancelTid = getField(cols, 5);
                                    }
                                    */

                            //취소일 경우
                            if (dealStatus != null && dealStatus.indexOf("취소") > -1) {
                                cancelAmt = getField(cols, 13,2);
                            }

                            //주문번호가 없거나 뒤 10자리가 숫자가 아닌경우가 있어 처리
                            try {
                                String tmpOrderNum = "";

                                //이지웰 주문번호 : orderNum 뒤 10자리
                                if (orderNum != null && orderNum.length() > 10)
                                    tmpOrderNum = orderNum.substring(orderNum.length() - 10, orderNum.length());
                                else
                                    tmpOrderNum = orderNum;

                                //ezwelOrderNum 숫자변환
                                ezwelOrderNum = "" + Long.parseLong(tmpOrderNum);
                            } catch (Exception e) {
                            }

                            if (orderNum == null || "".equals(orderNum))
                                orderNum = "EMPTY";
                            if (ezwelOrderNum == null || "".equals(ezwelOrderNum))
                                ezwelOrderNum = "0";

                            //상품명에 '|'가 있어 cols.length > 24 이면 뒤로 붙인다
                            if (cols.length > 24) {
                                for (int k = 24; k < cols.length; k++)
                                    goodsNm += "|" + cols[k];
                            }
                            if (goodsNm != null)
                                goodsNm = goodsNm.trim();

                            //상태코드 승인:0001, 취소:0002, 나머지:9999
                            if (dealStatus.indexOf("승인") > -1)
                                statusCd = "0001";
                            else if (dealStatus.indexOf("취소") > -1)
                                statusCd = "0002";
                            else
                                statusCd = "9999";

                            //지불일시
                            if ("0002".equals(statusCd))
                                payDt = cancelDd + "" + cancelTm;
                            else
                                payDt = confirmDd + "" + confirmTm;

                            //cancelTid가 없을경우 승인tid 넣는다
                            if (cancelTid == null || "".equals(cancelTid))
                                cancelTid = tid;
                            EzEcOrderPayLogEntity acctLog =
                                    EzEcOrderPayLogEntity.builder().tid(tid).orderNum(orderNum)
                                            .ezwelOrderNum(Long.valueOf(ezwelOrderNum))
                                            .storeId(storeId)
                                            .dealType(dealType)
                                            .deviceType(deviceType)
                                            .payType(payType)
                                            .dealStatus(dealStatus)
                                            .confirmDd(confirmDd)
                                            .confirmTm(confirmTm)
                                            .cancelDd(cancelDd)
                                            .cancelTm(cancelTm)
                                            .dealAmt(new BigDecimal(dealAmt))
                                            .cancelAmt(new BigDecimal(cancelAmt))
                                            .cancelTid(cancelTid)
                                            .cardAmt(new BigDecimal(cardAmt))
                                            .alliPoint(alliPoint)

                                            .instMm(instMm)
                                            .alliPoint(alliPoint)
                                            .cardType(cardType)
                                            .confirmNum(confirmNum)
                                            .simplePay(simplePay)
                                            .cancelRemainAmt(new BigDecimal(cancelRemainAmt))
                                            .bankNm(bankNm)
                                            .goodsNm(goodsNm)
                                            .statusCd(statusCd)
                                            .payDt(payDt).build();



                            //승인일이 현재 일시와 같으면 insert 하지 않는다 (다음 배치때 insret) TODO 해당 로직을 유지하는지 고민
//                                    String confirmDdTm = "";
//                                    if(confirmDd != null)							confirmDdTm += confirmDd;					//승인 일
//                                    if(confirmTm != null && confirmTm.length() > 1) confirmDdTm += confirmTm.substring(0, 2);	//승인 시분초 앞 두자리

//                                    if(batchActionDt.equals(confirmDdTm))
//                                    {
//                                        log.debug("======= [이니시스 카드 대사] " + actionDt + " PASS : " + dataMap);
//                                    }

                            createEzEcOrderPayLog(acctLog);
                        }
                    }
                }
            }
        }
    }

    private void getAcctCompareData(String query, String urlid) {
        String responStr = this.connectPgCall(PgCompareConstants.INICIS_PG_COMPARE_ACCT_URL, query);
        if (responStr != null && !"".equals(responStr) && responStr.length() > 30) {
            //줄바꿈이 없고 "<br>"로 줄바꿈됨
            //상품명에도 "<br>"이 있어 |<br>urlid 로 줄바꿈함
            String[] rows = ("|<br>" + responStr.trim() + urlid).split("\\|<br>" + urlid);

            if (rows != null && rows.length > 0) {
                int total = rows.length;

                //같은목록에 승인 및 취소가 있을때 취소row가 먼저 나와 최종적으로 '승인'으로 update되어서 루프를 거꾸로 돌림
                //for(int i=0; i<total; i++)
                for (int i = total - 1; i >= 0; i--) {

                    if (rows[i] != null && !"".equals(rows[i])) {

                        String[] cols = rows[i].split("\\|");

                        /*
										if(actCnt < 10)
										{
											for(int j=0; j<cols.length; j++)
												System.out.println("==================================== " + j + " : " + cols[j]);
										}
										*/

//                        지불수단 | MID | TID | 주문번호 | 승인일자 | 취소일자 | 거래금액 | 거래상태 | 제휴할인금액 | 제휴할인명
										/*
										 * Ezwel007GI|MOBILE|ezweltest2 |INIMX_HPP_ezweltest220230531173138695563|1104163273|20230531|173138|||승인|승인|700|테스트|***|20230531|<br>
										==================================== 0 : Ezwel007GI
										==================================== 1 : MOBILE // MOBILE거래
										==================================== 2 : ezweltest2 // 상정ID // 승인일자
										==================================== 3 : INIMX_HPP_ezweltest220230531XXXXXXX // TID(Full)
										==================================== 4 : 1111111111 // 주문번호
										==================================== 5 : 20230531 // 승인일자
										==================================== 6 : 173138 // 승인시간
										==================================== 7 :  // 취소날짜
										==================================== 8 :  // 취소시간
										==================================== 9 :  승인 // 거래상태
										==================================== 10 : 승인 // 취소구분
										==================================== 11 : 700 // 승인금액
										==================================== 12 : 테스트 // 구매자
										==================================== 13 : *** // 이통사
										==================================== 14 : 20230531 // 입금확인일
										*/

                        //공통
                        String tid = getField(cols, 3);
                        String orderNum = getField(cols, 4);
                        String ezwelOrderNum = "";
                        String storeId = getField(cols, 2);
                        String dealType = "M";
                        String deviceType = getField(cols, 1);
                        String payType = "휴대폰";
                        String dealStatus = getField(cols, 9); // 거래상태
                        String confirmDd = getField(cols, 5);
                        String confirmTm = getField(cols, 6);
                        String cancelDd = getField(cols, 7);
                        String cancelTm = getField(cols, 8);
                        String dealAmt = getField(cols, 11);
                        String cancelAmt = "";
                        String goodsNm = ""; //cols[19];

                        String cancelTid = getField(cols, 3);

                        //카드
                        String cardAmt = "";
                        String alliPoint = "";
                        String instMm = "";
                        String cardType = "";
                        String confirmNum = "";
                        String simplePay = "";

                        //계좌이체
                        String cancelRemainAmt = "";
                        String bankNm = "";

                        //추가필드
                        String payDt = "";            //지불일시
                        String statusCd = "";        //상태코드

                        //취소일 경우
                        if (dealStatus != null && dealStatus.indexOf("취소") > -1) {
                            cancelAmt = getField(cols, 11);
                        }

                        //주문번호가 없거나 뒤 10자리가 숫자가 아닌경우가 있어 처리
                        try {
                            String tmpOrderNum = "";

                            //이지웰 주문번호 : orderNum 뒤 10자리
                            if (orderNum != null && orderNum.length() > 10)
                                tmpOrderNum = orderNum.substring(orderNum.length() - 10, orderNum.length());
                            else
                                tmpOrderNum = orderNum;

                            //ezwelOrderNum 숫자변환
                            ezwelOrderNum = "" + Long.parseLong(tmpOrderNum);
                        } catch (Exception e) {
                        }

                        if (orderNum == null || "".equals(orderNum))
                            orderNum = "EMPTY";
                        if (ezwelOrderNum == null || "".equals(ezwelOrderNum))
                            ezwelOrderNum = "0";

                        //상품명에 '|'가 있어 cols.length > 20 이면 뒤로 붙인다
                        if (cols.length > 20) {
                            for (int k = 20; k < cols.length; k++)
                                goodsNm += "|" + cols[k];
                        }
                        if (goodsNm != null)
                            goodsNm = goodsNm.trim();

                        //상태코드 승인:0001, 취소:0002, 나머지:9999
                        if (dealStatus.indexOf("승인") > -1)
                            statusCd = "0001";
                        else if (dealStatus.indexOf("취소") > -1)
                            statusCd = "0002";
                        else
                            statusCd = "9999";

                        //지불일시
                        if ("0002".equals(statusCd))
                            payDt = cancelDd + "" + cancelTm;
                        else
                            payDt = confirmDd + "" + confirmTm;

                        //cancelTid가 없을경우 승인tid 넣는다
                        if (cancelTid == null || "".equals(cancelTid))
                            cancelTid = tid;

                        EzEcOrderPayLogEntity mobilLog =
                                EzEcOrderPayLogEntity.builder().tid(tid).orderNum(orderNum)
                                        .ezwelOrderNum(Long.valueOf(ezwelOrderNum))
                                        .storeId(storeId)
                                        .dealType(dealType)
                                        .deviceType(deviceType)
                                        .payType(payType)
                                        .dealStatus(dealStatus)
                                        .confirmDd(confirmDd)
                                        .confirmTm(confirmTm)
                                        .cancelDd(cancelDd)
                                        .cancelTm(cancelTm)
                                        .dealAmt(new BigDecimal(dealAmt))
                                        .cancelAmt(new BigDecimal(cancelAmt))
                                        .cancelTid(cancelTid)
                                        .cardAmt(new BigDecimal(cardAmt))
                                        .alliPoint(alliPoint)

                                        .instMm(instMm)
                                        .alliPoint(alliPoint)
                                        .cardType(cardType)
                                        .confirmNum(confirmNum)
                                        .simplePay(simplePay)
                                        .cancelRemainAmt(new BigDecimal(cancelRemainAmt))
                                        .bankNm(bankNm)
                                        .goodsNm(goodsNm)
                                        .statusCd(statusCd)
                                        .payDt(payDt).build();



                        //승인일이 현재 일시와 같으면 insert 하지 않는다 (다음 배치때 insret) TODO 해당 로직을 유지하는지 고민
//                                    String confirmDdTm = "";
//                                    if(confirmDd != null)							confirmDdTm += confirmDd;					//승인 일
//                                    if(confirmTm != null && confirmTm.length() > 1) confirmDdTm += confirmTm.substring(0, 2);	//승인 시분초 앞 두자리

//                                    if(batchActionDt.equals(confirmDdTm))
//                                    {
//                                        log.debug("======= [이니시스 카드 대사] " + actionDt + " PASS : " + dataMap);
//                                    }

                        createEzEcOrderPayLog(mobilLog);
                    }

                }
            }

        }
    }


    private String connectPgCall(String url, String query) {
        log.debug("url:{}",InicisBaseUrl+url+query);
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(Duration.ofSeconds(5));
        requestFactory.setReadTimeout(Duration.ofSeconds(60));

        ResponseEntity<String> entity = RestClient.builder()
                .baseUrl(InicisBaseUrl)
                .requestFactory(requestFactory)
                .build()
                .get()
                .uri(url + query)
                .retrieve().toEntity(String.class);

        if (!entity.getStatusCode().is2xxSuccessful()) {
            log.error("{}", entity.getBody());
            throw new ServiceException();
        }

        String ret = entity.getBody().toString();

        if(ret.startsWith("0001:")){
            log.error(ret);
            throw new ServiceException();
        }else{
            return ret;
        }
    }



}
