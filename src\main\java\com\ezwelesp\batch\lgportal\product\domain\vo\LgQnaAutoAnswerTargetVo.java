package com.ezwelesp.batch.lgportal.product.domain.vo;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgQnaAutoAnswerTargetVo")
@Getter
@SuperBuilder
public class LgQnaAutoAnswerTargetVo {
    private Integer gdsInqSeq;    // 문의 번호
    private String gdsCd;          // 이지웰상품번호
    private String lgpBbsNo;      // LG 문의 번호
    private String targetGoodsCd;     // lg상품번호


    public String getgdsInqSeqString() {
        return String.valueOf(gdsInqSeq);
    }

    public Integer getLgpBbsNoInteger() {
        return Integer.valueOf(lgpBbsNo);
    }

}

