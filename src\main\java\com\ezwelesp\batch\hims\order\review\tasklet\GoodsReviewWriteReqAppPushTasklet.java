package com.ezwelesp.batch.hims.order.review.tasklet;

import com.ezwelesp.batch.hims.order.review.domain.GoodsReviewWriteRequestDto;
import com.ezwelesp.batch.hims.order.review.service.GoodsReviewWriteRequestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Component
public class GoodsReviewWriteReqAppPushTasklet implements Tasklet {
    private final GoodsReviewWriteRequestService goodsReviewWriteRequestService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            List<GoodsReviewWriteRequestDto> target = goodsReviewWriteRequestService.getGoodsReviewWriteRqAppPushTarget();

            log.info("상품평 대상 size --> {}", target.size());

            // TODO 앱푸시
            // 전송할 내용 고정
            String msgTitle = "상품평 작성 알림";
            String sendMsg = "(광고) 주문하신 상품은 어떠셨나요?\n상품평을 작성해주시면 적립금으로 즉시 적립해드려요.\n※수신거부:설정>PUSH알림";

            //이지체크인 상품평 푸시
            List<GoodsReviewWriteRequestDto> ezCheckin = goodsReviewWriteRequestService.getEzCheckinAppPushTarget();
            log.info("이지체크인 대상 size --> {}", ezCheckin.size());
            String sendEzcMsg = "(광고) 이용하신 숙소는 어떠셨나요?\n상품평을 작성해주시면 적립금으로 즉시 적립해드려요. \n※수신거부:설정>PUSH알림";

            // 발송여부 Y 업데이트
            for (GoodsReviewWriteRequestDto dto : target) {
                // goodsReviewWriteRequestService.updateOrderGoodsPushYn(dto.getOrderGoodsList());
            }
        } catch (Exception e) {
            log.error("GoodsReviewWriteReqAppPushTasklet failed. {}, {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.debug("GoodsReviewWriteReqAppPushTasklet finished");
        return RepeatStatus.FINISHED;
    }

}
