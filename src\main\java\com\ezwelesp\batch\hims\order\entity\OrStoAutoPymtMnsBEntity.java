package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 정기구독자동결제수단기본(ez_or.or_sto_auto_pymt_mns_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrStoAutoPymtMnsBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 정기구독자동결제수단번호(sto_auto_pymt_mns_no) not null
     */
    private String stoAutoPymtMnsNo;

    /**
     * PG종류코드(pg_knd_cd)
     */
    private String pgKndCd;

    /**
     * 암호화PG자동결제번호(enc_pg_auto_pymt_no)
     */
    private String encPgAutoPymtNo;

    /**
     * 정기구독메인청구여부(sto_main_blng_yn) not null
     */
    private String stoMainBlngYn;

    /**
     * PG가맹점번호(pg_frcs_no) not null
     */
    private String pgFrcsNo;

    /**
     * 카드종류코드(crd_knd_cd) not null
     */
    private String crdKndCd;

    /**
     * 암호화카드번호(enc_crd_no) not null
     */
    private String encCrdNo;

    /**
     * 사용여부(use_yn) not null
     */
    private String useYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
