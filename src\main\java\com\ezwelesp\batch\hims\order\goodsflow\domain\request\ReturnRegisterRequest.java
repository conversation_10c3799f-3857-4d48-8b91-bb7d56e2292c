package com.ezwelesp.batch.hims.order.goodsflow.domain.request;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 굿스플로 반품등록 request
 */
@Getter
@Setter
@Builder
@ToString
public class ReturnRegisterRequest {
    private ReturnData data;

    @Getter
    @Setter
    @Builder
    @ToString
    public static class ReturnData {
        private List<ReturnOrder> items;
    }

    @Getter
    @Setter
    @Builder
    @ToString
    public static class ReturnOrder {
        private String transUniqueCd; // 배송고유번호
        private String centerCode; // 발송지코드
        private String deliverCode; // 택배사코드
        private String sndName; // 보내는분명
        private String sndZipCode; // 보내는분 우편번호
        private String sndAddr1; // 보내는분 기본주소
        private String sndAddr2; // 보내는분 상세주소
        private String sndTel1; // 보내는분 전화1
        private String sndTel2; // 보내는분 전화2
        private String rcvName; // 받는분명
        private String rcvZipCode; // 받는분 우편번호
        private String rcvAddr1; // 받는분 기본주소
        private String rcvAddr2; // 받는분 상세주소
        private String rcvTel1; // 받는분 전화1
        private String rcvTel2; // 받는분 전화2
        private String mallId; // 판매자 ID
        private String marketPlace; // 판매처
        private String ordName; // 주문자명
        private String ordTel1; // 주문자전화1
        private String ordTel2; // 주문자전화2
        private String status; // 처리상태 (N: 일반, O: 원송장 * N(일반)고정)
        private String orgDeliverCode; // 원택배사코드
        private String sheetNo; // 운송장번호
        private String paymentTypeCode; // 지불방법코드 (SH: 선불, BH: 착불)
        private String boxSize; // 기본 박스규격
        private String msgToTrans; // 배송메시지
        private List<ReturnItem> orderItems;
    }

    @Getter
    @Setter
    @Builder
    @ToString
    public static class ReturnItem {
        private String uniqueCd; // 고객사용번호
        private String ordNo; // 주문번호
        private Integer ordLineNo; // 주문행번호
        private String itemCode; // 상품코드
        private String itemName; // 상품명
        private String itemOption; // 상품옵션
        private Integer itemQty; // 상품수량
        private Integer itemPrice; // 상품단가
        private String ordDate; // 주문일시 (YYYYMMDDHH24MISS)
        private String defCode1; // 업체관리코드1
        private String defCode2; // 업체관리코드2
    }
}
