package com.ezwelesp.batch.lgportal.entity;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.List;

/**
  * 주문조회 시 사용 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see OrderTmpEntity
  */
@SuperBuilder(toBuilder = true)
@Getter
public class OrderTmpEntity {
    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 고객사주문번호(clnt_ord_no)
     */
    private String clntOrdNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 주문일시(ord_dtm) not null
     */

    private String ordDtm;

    /**
     * 주문상태코드(ord_st_cd) not null
     */
    private String ordStCd;

    /**
     * 주문유형코드(ord_typ_cd) not null
     */
    private String ordTypCd;

    /**
     * 주문배송종류코드(ord_dlv_knd_cd) not null
     */
    private String ordDlvKndCd;

    /**
     * 접속디바이스코드(acss_dvc_cd)
     */
    private String acssDvcCd;

    /**
     * 배송준비시작일시(dlv_rdy_strt_dtm)
     */
    private String dlvRdyStrtDtm;

    /**
     * 배송시작일시(dlv_strt_dtm)
     */
    private String dlvStrtDtm;

    /**
     * 배송완료일시(dlv_cmpt_dtm)
     */
    private String dlvCmptDtm;

    /**
     * 취소일시(cncl_dtm)
     */
    private String cnclDtm;

    /**
     * 주문금액(ord_amt) not null
     */
    private BigDecimal ordAmt;

    /**
     * 주문자명(ordr_nm) not null
     */
    private String ordrNm;

    /**
     * 주문자전화번호(ordr_telno)
     */
    private String ordrTelno;

    /**
     * 주문자모바일전화번호(ordr_mbl_telno)
     */
    private String ordrMblTelno;

    /**
     * 주문자이메일주소(ordr_eml_adr)
     */
    private String ordrEmlAdr;

    /**
     * 가족회원번호(fam_mem_no)
     */
    private String famMemNo;

    /**
     * 실제주문자명(real_ordr_nm)
     */
    private String realOrdrNm;

    /**
     * 실제주문자모바일전화번호(real_ordr_mbl_telno)
     */
    private String realOrdrMblTelno;

    /**
     * 제휴사주문협력사코드(asp_ord_csp_cd)
     */
    private String aspOrdCspCd;

    /**
     * 제휴사주문완료연결URL(asp_ord_cmpt_conn_url)
     */
    private String aspOrdCmptConnUrl;

    /**
     * 취소수수료주문여부(cncl_cms_ord_yn) not null
     */
    @Builder.Default
    private String cnclCmsOrdYn = "N";

    /**
     * 숙박주문취소수수료금액(ldg_ord_cncl_cms_amt) not null
     */
    @Builder.Default
    private BigDecimal ldgOrdCnclCmsAmt = new BigDecimal(0);

    /**
     * 개인통관고유부호재사용여부(pcc_ruse_yn) not null
     */
    @Builder.Default
    private String pccRuseYn = "N";

    /**
     * 노출여부(exps_yn) not null
     */
    @Builder.Default
    private String expsYn = "N";

    /**
     * 표준메뉴코드(std_menu_cd)
     */
    private String stdMenuCd;

    /**
     * 이관주문여부(trsf_ord_yn) not null
     */
    @Builder.Default
    private String trsfOrdYn = "N";


    /**
     * 주문상품 정보 Dto
     */
    private List<OrderGdsTmpEntity> gdsTmpEntityList;

    /**
     * 주문배송 정보 Dto
     */
    private OrderDlvTmpEntity dlvTmpEntity;

    /**
     * 배송비용 기본 Dto
     */
    private DlDlvExpBTmpEntity dlDlvExpTmpEntity;

    /**
     * 결제기본 Dto
     */
    private PyPymtTmpEntity pyPymtTmpEntity;
}
