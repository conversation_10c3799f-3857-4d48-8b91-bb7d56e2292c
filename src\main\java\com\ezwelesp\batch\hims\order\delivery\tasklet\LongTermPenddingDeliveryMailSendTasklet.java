package com.ezwelesp.batch.hims.order.delivery.tasklet;

import com.ezwelesp.batch.hims.order.delivery.domain.PenddingDeliveryDto;
import com.ezwelesp.batch.hims.order.delivery.service.PenddingDeliveryMailSendService;
import com.ezwelesp.batch.hims.order.reseller.service.MonitorJobService;
import com.ezwelesp.framework.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class LongTermPenddingDeliveryMailSendTasklet implements Tasklet {
    private final PenddingDeliveryMailSendService penddingDeliveryMailSendService;
    private final MonitorJobService monitorJobService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            List<PenddingDeliveryDto> result = penddingDeliveryMailSendService.getLongTermPenddingDeliveryList();

            log.info("MD별 장기미배송건 조회 결과: {}", result);

            monitorJobService.saveScMntMonitorRes(DateUtils.dateTimeNow(), "longTermPenddingDeliveryMailSendJob", result.size(), makeResEtc(result));
        } catch (Exception e) {
            log.error("LongTermPenddingDeliveryMailSendTasklet Failed: {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.debug("LongTermPenddingDeliveryMailSendTasklet finished");

        return RepeatStatus.FINISHED;
    }

    private String makeResEtc(List<PenddingDeliveryDto> result) {
        if (result.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("장기미배송건");
        for (PenddingDeliveryDto dto : result) {
            sb.append(String.format("담당 MD: %s(%s) 배송대기: %s건, 배송준비중: %s건, 출고진행: %s건, 배송중: %s건",
                    dto.getMdNm(), dto.getMgrId(), dto.getStbyCnt(), dto.getRdyCnt(), dto.getObndCnt(), dto.getDlvCnt()));
        }

        return sb.toString();
    }
}
