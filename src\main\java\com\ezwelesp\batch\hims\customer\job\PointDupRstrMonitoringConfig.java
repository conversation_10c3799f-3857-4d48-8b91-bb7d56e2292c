package com.ezwelesp.batch.hims.customer.job;

import com.ezwelesp.batch.hims.customer.tasklet.PointDupRstrMonitoringTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;



/**
 * 선복, 특복 이중복구모니터링 cuser_monitoringSms_real.sh
 */

@Configuration
public class PointDupRstrMonitoringConfig {

    private final PointDupRstrMonitoringTasklet pointDupRstrMonitoringTasklet;

    public PointDupRstrMonitoringConfig(
            @Qualifier("pointDupRstrMonitoringTasklet")
            PointDupRstrMonitoringTasklet pointDupRstrMonitoringTasklet
    ) {
        this.pointDupRstrMonitoringTasklet = pointDupRstrMonitoringTasklet;
    }

    @Bean("BA_HIPM00006")
    public Job pointDupRstrMonitoringJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPM00006_STEP") Step pointDupRstrMonitoringStep
    ) {
        return new JobBuilder("BA_HIPM00006", jobRepository)
                .start(pointDupRstrMonitoringStep)
                .build();
    }


    @Bean("BA_HIPM00006_STEP")
    public Step pointDupRstrMonitoringStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
    ) {
        return new StepBuilder("BA_HIPM00006_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(pointDupRstrMonitoringTasklet, transactionManager)
                .build();
    }
}
