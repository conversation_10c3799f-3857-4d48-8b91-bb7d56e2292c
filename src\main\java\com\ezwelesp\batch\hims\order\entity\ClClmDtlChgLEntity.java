package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 클레임상세변경내역(ez_or.cl_clm_dtl_chg_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClClmDtlChgLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 클레임상세변경내역순번(clm_dtl_chg_lst_seq) not null
     */
    private Long clmDtlChgLstSeq;

    /**
     * 클레임상세변경종류코드(clm_dtl_chg_knd_cd)
     */
    private String clmDtlChgKndCd;

    /**
     * 클레임상태코드(clm_st_cd)
     */
    private String clmStCd;

    /**
     * 클레임변경이후내용(clm_chg_aft_cntn)
     */
    private String clmChgAftCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
