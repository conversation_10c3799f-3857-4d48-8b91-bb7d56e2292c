package com.ezwelesp.batch.hims.order.reseller.job;

import com.ezwelesp.batch.hims.order.reseller.tasklet.SuspectedResellerOrderAWeekNotificationTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 복지샵,브랜드몰 고객 주별 리셀러 의심 주문 알림 [BA_HIOR00032]
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.reseller.job
 * @since 2025.05.20
 */
@Configuration
@RequiredArgsConstructor
public class SuspectedResellerOrderAWeekNotificationJobConfig {
    private final CommonJobListener commonJobListener;
    private final SuspectedResellerOrderAWeekNotificationTasklet suspectedResellerOrderAWeekNotificationTasklet;

    @Bean("BA_HIOR00032")
    public Job suspectedResellerOrderAWeekNotificationJob(
            JobRepository jobRepository,
            @Qualifier("BA_HIOR00032_STEP") Step suspectedResellerOrderAWeekNotificationStep) {
        return new JobBuilder("BA_HIOR00032", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(suspectedResellerOrderAWeekNotificationStep)
                .build();
    }

    @Bean("BA_HIOR00032_STEP")
    public Step suspectedResellerOrderAWeekNotificationStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00032_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(suspectedResellerOrderAWeekNotificationTasklet, transactionManager)
                .build();
    }
}
