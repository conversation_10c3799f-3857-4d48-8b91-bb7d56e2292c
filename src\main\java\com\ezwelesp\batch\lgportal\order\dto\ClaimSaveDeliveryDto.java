package com.ezwelesp.batch.lgportal.order.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 교환/반품 배송정보
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto
 * @since 2025.02.26
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class ClaimSaveDeliveryDto implements Serializable {

    /* 배송비용기본 정보 */
    // 협력사출고위치번호
    private String cspObndLocNo;

    // 배송정책순번
    private long dlvPlcySeq;

    // 조건부무료배송여부
    private String cndlNchgDlvYn;

    // 무료배송적용결제금액
    private BigDecimal nchgDlvAplyPymtAmt;

    // 설정배송비용
    private BigDecimal stupDlvExp;

    // 배송비용
    private BigDecimal dlvExp;

    // 할인쿠폰배송비용
    private BigDecimal dcCpnDlvExp;

    // 취소할인쿠폰배송비용
    private BigDecimal cnclDcCpnDlvExp;

    // 제주도추가배송비용
    private BigDecimal jejuAddDlvExp;

    // 도서산간추가배송비용
    private BigDecimal ismtAddDlvExp;

    // 취소이후발생배송비용
    private BigDecimal cnclAftOcrnDlvExp;

    // 착불송금배송비용
    private BigDecimal arpayRmttDlvExp;

    // 현대이지웰부담배송비용
    private BigDecimal ezwlBudnDlvExp;

    // 쿠폰번호
    private String cpnNo;

    // 사용자쿠폰번호
    private long usrCpnNo;

    // 배송주소정보
    private List<ClaimDeliveryAddressDto> deliveryAddressList;

    // 배송기본 정보 리스트
    private List<ClaimDeliveryBaseInfoDto> deliveryBaseInfoList;
}
