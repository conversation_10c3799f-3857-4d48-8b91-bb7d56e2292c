package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * EZ멤버스매출기본(ez_or.or_ezmbrs_sale_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrEzmbrsSaleBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * EZ멤버스매출번호(ezmbrs_sale_no) not null
     */
    private String ezmbrsSaleNo;

    /**
     * EZ멤버스전문종류코드(ezmbrs_tlgm_knd_cd) not null
     */
    private String ezmbrsTlgmKndCd;

    /**
     * EZ멤버스정산유형코드(ezmbrs_stl_typ_cd) not null
     */
    private String ezmbrsStlTypCd;

    /**
     * EZ멤버스승인번호(ezmbrs_apv_no) not null
     */
    private String ezmbrsApvNo;

    /**
     * EZ멤버스카드종류코드(ezmbrs_crd_knd_cd)
     */
    private String ezmbrsCrdKndCd;

    /**
     * EZ멤버스매출종류코드(ezmbrs_sale_knd_cd)
     */
    private String ezmbrsSaleKndCd;

    /**
     * 원본EZ멤버스매출번호(orgl_ezmbrs_sale_no)
     */
    private String orglEzmbrsSaleNo;

    /**
     * 사용일자(use_dt) not null
     */
    private String useDt;

    /**
     * 사용금액(use_amt) not null
     */
    private BigDecimal useAmt;

    /**
     * 할인금액(dc_amt) not null
     */
    private BigDecimal dcAmt;

    /**
     * 매출일자(sale_dt) not null
     */
    private String saleDt;

    /**
     * 정산년월(stl_ym) not null
     */
    private String stlYm;

    /**
     * 취소여부(cncl_yn) not null
     */
    private String cnclYn;

    /**
     * 브랜드코드(brnd_cd)
     */
    private String brndCd;

    /**
     * 브랜드명(brnd_nm)
     */
    private String brndNm;

    /**
     * EZ멤버스가맹점코드(ezmbrs_frcs_cd) not null
     */
    private String ezmbrsFrcsCd;

    /**
     * 가맹점명(frcs_nm)
     */
    private String frcsNm;

    /**
     * 가맹점업종코드(frcs_biztp_cd)
     */
    private String frcsBiztpCd;

    /**
     * 가맹점업종명(frcs_biztp_nm)
     */
    private String frcsBiztpNm;

    /**
     * EZ멤버스제휴카드코드(ezmbrs_alli_crd_cd)
     */
    private String ezmbrsAlliCrdCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
