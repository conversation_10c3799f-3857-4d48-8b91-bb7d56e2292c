package com.ezwelesp.batch.hims.order.review.mapper.query;

import com.ezwelesp.batch.hims.order.review.domain.GoodsReviewWriteRequestDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface GoodsReviewWriteRequestQueryMapper {
    /**
     * 상품평 작성 요청 대상건 조회
     */
    List<GoodsReviewWriteRequestDto> selectGoodsReviewWriteRequestTarget();

    List<GoodsReviewWriteRequestDto> selectEzCheckinAppPushTarget();

}
