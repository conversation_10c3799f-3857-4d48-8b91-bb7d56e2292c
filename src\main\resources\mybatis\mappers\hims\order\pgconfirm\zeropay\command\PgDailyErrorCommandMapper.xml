<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgconfirm.zeropay.mapper.command.PgDailyErrorCommandMapper">
	
	<insert id="insertPgApproveErrList">
		<selectKey resultType="String" keyProperty="errSeq" order="BEFORE"> 
	      	SELECT coalesce(max(pg_pymt_cmpr_err_mtrg_lst_seq),0)+1 FROM ez_or.py_pg_pymt_cmpr_err_mtrg_l
	    </selectKey>
   		
	   		insert into ez_or.py_pg_pymt_cmpr_err_mtrg_l (
	   			pg_pymt_cmpr_err_mtrg_lst_seq
	   			, pg_pymt_err_cd
	   			, ord_no
	   			, ord_st_cd
	   			, asp_ord_no
	   			, ord_ch_nm
	   			, pymt_amt
	   			, pg_pymt_apv_amt
	   			, pg_knd_cd
	   			, pymt_mns_nm
	   			, pg_frcs_no
	   			, pg_apv_dtm
	   			, pg_apv_no
	   			, pg_crdc_apv_no
	   			, pg_frcs_ord_no
	   			, acss_te_typ_nm
	   			, mgr_cnft_cmpt_yn
	   			, mgr_memo
	   			, frst_reg_dtm
	   			, frst_reg_usr_id
	   			, frst_reg_pgm_id
	   			, last_mod_dtm
	   			, last_mod_usr_id
	   			, last_mod_pgm_id
	   		) values (
	   			#{errSeq}
				, #{pgPymtErrCd}
				, #{ordNo}
				, #{ordStCd}
				, #{aspOrdNo}
				, #{ordChNm}
				, #{pymtAmt}
				, #{pgPymtApvAmt}
				, #{pgKndCd}
				, #{pymtMnsNm}
				, #{pgFrcsNo}
				, #{pgApvNo}
				, #{pgCrdcApvNo}
				, #{pgFrcsOrdNo}
				, #{acssTeTypNm}
				, #{mgrCnftCmptYn}
				, #{mgrMemo}
				, to_char(now(), 'YYYYMMDDHH24MISS')
				, 'BATCH'
				, 'BATCH'
				, to_char(now(), 'YYYYMMDDHH24MISS')
				, 'BATCH'
				, 'BATCH'
	   		) 		
	</insert>


</mapper>