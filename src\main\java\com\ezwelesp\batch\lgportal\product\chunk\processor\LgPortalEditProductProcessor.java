package com.ezwelesp.batch.lgportal.product.chunk.processor;

import com.ezwelesp.batch.lgportal.product.chunk.processor.handler.LgPortalEditProductHandlerRegistry;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@StepScope
@RequiredArgsConstructor
public class LgPortalEditProductProcessor
        implements ItemProcessor<LgEditProductTargetVo, LgProductApiResponseDto> {

    private final LgPortalEditProductHandlerRegistry handlerRegistry;

    @Override
    public LgProductApiResponseDto process(@NonNull LgEditProductTargetVo item) {
        try {
            // sendKey별로 LgPortalEditProductHandlerConfiguration에서 정의된 헨들러 호출해서 처리
            // LgPortalEditProductBasicInfoHandler     1001 : 상품 기본정보 수정
            // LgPortalEditProductContentsHandler      1003 : 상품 컨텐츠 수정
            // LgPortalEditProductAnnouncementHandler  1006 : 상품 고시정보 수정
            // LgPortalEditProductDeliveryHandler      1007 : 상품 배송 및 교환반품 정보 수정
            // LgPortalEditProductImageHandler         1002 : 상품 이미지 수정
            // LgPortalEditProductQnaAnswerHandler     1015 : 상품 QnA 답변 등록

            //  LgPortalEditProductPriceHandler        1005 : 상품 가격변동 등록 // 가격 변경 배치

            //  LgPortalEditProductSoldOutHandler      1012 : 상품 품절 처리 // 품절처리 배치
            //  LgPortalEditProductSaleHandler         1013 : 상품 판매 처리 // 품절처리 배치

            val handler = handlerRegistry.getHandler(item.getSendKeyEnum());

            if (handler == null) {
                log.warn("LgPortalEditProductProcessor Not found Handler: {}", item.getSendKeyEnum());
                return null; // 처리할 Handler가 없으면 null 리턴
            }

            return handler.handle(item);
        } catch (Exception e) {
            log.error("LgPortalEditProductProcessor Fail : {},{}", item.getGdsCd(), e.getMessage());

            return null;
        }
    }

}

