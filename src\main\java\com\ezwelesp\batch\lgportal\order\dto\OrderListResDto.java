package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Data;

import java.util.List;

/**
  * LG포털 주문조회 시 사용 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see OrderListResDto
  */
@Data
public class OrderListResDto {

    private Long orderNo;
    private Long salesOrderNo;
    private String orderStatusCode;
    private String orderName;
    private String orderEmail;
    private String orderMobilePhone;
    private Long entryComId;

    private String entryComName;
    private Long deliveryNo;
    private String zipcode;
    private String addr;
    private String addr2;
    private String recvName;
    private String recvMobilePhone;
    private Long orderTotalAmt;
    private Long deliveryTotalAmt;
    private Long pltfrDiscountTotalAmt;
    private Long entDiscountTotalAmt;
    private Long pgPaymentTotalAmt;
    private Long ohtPaymentTotalAmt;

    private List<DeliveryItem> deliveryItems;
    // 변수명에 오타 'deliverAm', 'y' 누락
    private List<DeliveryAmt> deliverAmt;

    @Data
    public static class DeliveryItem {
        private Long deliveryCmdNo;
        private Long orderItemNo;
        private String standardCategoryNo;
        private String productNo;
        private String productName;
        private String productCode;
        private String salesTypeCode;
        private String skuVal;
        private String skuCode;
        private String skuNo;
        private String overseasDeliveryYN;
        private Integer realOrderCnt;
        private Long vendorPrice;
        private Long salesPrice;
        private Long sellingComSalesPrice;
        private String deliveryComCode;
        private String deliveryComName;
        private String deliveryMemo;
        private String invoiceNo;
        private String deliveryStatusCode;
        private Long deliveryAmtNo;
        private String deliveryCmdTypeCode;
        private String deliveryTypeCode;
        private String created;
        private String lastStatusUpdated;
    }

    @Data
    public static class DeliveryAmt {
        private Long deliveryAmtNo;
        private Long pay;
        private String deliveryCmdTypeCode;
        private Long orderItemNo;
    }

    @Data
    public static class DlvExp {
        private Long dlvExp;
    }
}
