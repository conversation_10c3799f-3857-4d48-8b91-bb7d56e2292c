<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.calculate.mapper.query.EzmktB2cSaleInfMmBatQueryMapper">
    <select id="selectEzmktB2cSaleInfMmBat01">
        /* 이지웰마켓B2C 상세 데이터 원천 Source */
        select account_ym
             , ch_cd
             , order_num
             , order_goods_num
             , order_cancel_yn
             , order_dt
             , cancel_dt
             , order_status
             , order_status_nm
             , csp_cd
             , mk_user_key
             , goods_cd
             , goods_nm
             , tax_yn
             , sale_price
             , order_qty
             , order_amt - dc_price as order_amt
             , point
             , card
             , virture
             , reserve
             , special
             , trans
             , coupon
             , zeropay
             , naver_pay
             , coalesce(onnuri_pay,0) as onnuri_pay
             , coalesce(digital_onnuri,0) as digital_onnuri
             , request
             , approval
             , paylater
             , coalesce(korcham_amt,0) as korcham_amt
             , order_nm
             , (point+
                card+
                virture+
                reserve+
                special+
                trans+
                coupon+
                zeropay+
                naver_pay
                /*+request TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
                +approval
                /*+paylater TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
                +korcham_amt
                +onnuri_pay
                +digital_onnuri
               ) as total_amt
             , dlvr_amt
          from (select #{stlYm} as account_ym
                     , cp.ch_cd /*TODO 채널코드로 통합됨*/
                     , eo.order_num as order_num
                     , eog.order_goods_num as order_goods_num
                     , eog.calc_dt as order_dt
                     , eog.cancel_dt as cancel_dt
                     , eog.order_status as order_status
                     , (select comm_cd_nm
                          from ez_market.mk_sys_comm_cd
                         where comm_tp = '1003'
                           and eog.order_status = comm_cd) as order_status_nm
                     , cp.csp_cd
                     , eo.mk_user_key
                     , eog.goods_cd
                     , eog.goods_nm
                     , case when (gi.tax_div = '1001') then '00' /*과세*/
                            when (gi.tax_div = '1002') then '01' /*면세*/
                            when (gi.tax_div = '1003') then '04' /*영세*/
                            else '03'                            /*영수증*/
                        end as tax_yn
                     , eog.sale_price as sale_price
                     , (eog.qty) as order_qty
                     , eog.order_goods_amt as order_amt
                     , eog.dc_price as dc_price
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '1001'),0) as point /*부분취소가 발생가능 pay_grp_num = 1 추가 기존 팔도와 동일한 형태로 유지*/
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and (s.pay_type = '1002' or s.pay_type = '2004' or s.pay_type = '2006')),0) as card
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '1010'),0) as virture
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '2005'),0) as reserve
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '1008'),0) as special
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '1003'),0) as trans
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '1012'),0) as coupon
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type in ('1016','1019')),0) as zeropay
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '1022'),0) as naver_pay /*네이버페이 20230420*/
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '2001'),0) as request /*신청형*/
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '2002'),0) as approval /*승인형*/
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '2003'),0) as paylater /*후불형*/
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '1009'),0) as korcham_amt /*대한상공회의소 결제 20220511 추가*/
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '1027'),0) as onnuri_pay /*온누리페이 20240613 추가*/
                     , coalesce((select sum(s.pay_amt)
                                   from ez_market.mk_order_pay s
                                  where s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_grp_num = 1
                                    and s.pay_type = '1029'),0) as digital_onnuri /*디지털온누리상품권 20250221 추가*/
                     , coalesce((select s.dlvr_price
                                   from ez_market.mk_order_goods s
                                  where s.order_num = eo.order_num
                                    and s.order_goods_num = eog.order_goods_num),0) as dlvr_amt
                     , eo.order_nm
                     , 'N' as order_cancel_yn
                     , eog.calc_dt
                  from ez_market.mk_order eo
                     , ez_market.mk_order_goods eog
                     , ez_market.mk_goods gi
                     , ez_market.mk_user_b2c ut
                     , ez_co.co_csp_b cp
                 where eo.order_num = eog.order_num
                   and ut.mk_user_key = eo.mk_user_key
                   and gi.csp_cd::text = cp.csp_cd /*TODO 대응개발 엔티티 타입은 그대로이므로 캐스팅 필요*/
                   and eog.goods_cd = gi.goods_cd
                   and ut.test_yn = 'N'
                   and eo.svc_type = '1001'
                   and eo.user_div in ('1002','1003') /*1002 지자체 , 1003 개인회원*/
                   and eog.order_status is not null /*임시처리 안들어오는게 있음*/
                   and eog.calc_dt between #{stlYm} || '********' and #{stlYmd} || '235959'
                   and cp.csp_cd <![CDATA[ <> ]]> '********' /*테스트 cp제외처리 *********/
                union all
                select #{stlYm} as account_ym
                     , cp.ch_cd /*TODO 채널코드로 통합됨*/
                     , eo.order_num as order_num
                     , eog.order_goods_num as order_goods_num
                     , eog.calc_dt as order_dt
                     , eog.cancel_dt as cancel_dt
                     , eog.order_status as order_status
                     , (select comm_cd_nm
                          from ez_market.mk_sys_comm_cd
                         where comm_tp = '1003'
                           and eog.order_status = comm_cd) as order_status_nm
                     , cp.csp_cd
                     , eo.mk_user_key
                     , eog.goods_cd
                     , eog.goods_nm
                     , case when (gi.tax_div = '1001') then '00' /*과세*/
                            when (gi.tax_div = '1002') then '01' /*면세*/
                            when (gi.tax_div = '1003') then '04' /*영세*/
                            else '03'                            /*영수증*/
                        end as tax_yn
                     , eog.sale_price as sale_price
                     , (-1*eog.qty) as order_qty
                     , (-1*eog.order_goods_amt) as order_amt
                     , (-1* eog.dc_price) as dc_price
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '1001'),0) as point
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and (s.pay_type = '1002' or s.pay_type = '2004' or s.pay_type = '2006')),0) as card
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '1010'),0) as virture
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '2005'),0) as reserve
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '1008'),0) as special
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '1003'),0) as trans
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '1012'),0) as coupon
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type in ('1016','1019')),0) as zeropay
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '1022'),0) as naver_pay
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '2001'),0) as request
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '2002'),0) as approval
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '2003'),0) as paylater
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '1009'),0) as korcham_amt
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '1027'),0) as onnuri_pay
                     , coalesce((select -sum(s.cancel_amt)
                                   from ez_market.mk_order_pay s
                                  where s.cancel_dt like #{stlYm} || '%'
                                    and s.order_num = eo.order_num
                                    and eog.order_goods_num = 1
                                    and s.pay_status = '1003'
                                    and s.pay_type = '1029'),0) as digital_onnuri
                     , coalesce(-1* (select s.dlvr_price
                                       from ez_market.mk_order_goods s
                                      where s.order_num = eo.order_num
                                        and s.order_goods_num = eog.order_goods_num),0) as dlvr_amt
                     , eo.order_nm
                     , 'Y' as order_cancel_yn
                     , eog.calc_dt
                  from ez_market.mk_order eo
                     , ez_market.mk_order_goods eog
                     , ez_market.mk_goods gi
                     , ez_market.mk_user_b2c ut
                     , ez_co.co_csp_b cp
                 where eo.order_num = eog.order_num
                   and ut.mk_user_key = eo.mk_user_key
                   and gi.csp_cd::text = cp.csp_cd /*TODO 대응개발 엔티티 타입은 그대로이므로 캐스팅 필요*/
                   and eog.goods_cd = gi.goods_cd
                   and eog.order_status is not null /*임시처리 안들어오는게 있음*/
                   and ut.test_yn = 'N'
                   and eo.svc_type = '1001'
                   and eo.user_div in ('1002','1003') /*1002 지자체 , 1003 개인회원 , 1001 일반회원 (b2e,b2b)*/
                   and eog.calc_dt > '**************'
                   and eog.calc_cancel_dt between #{stlYm} || '********' and #{stlYmd} || '235959'
                   and cp.csp_cd <![CDATA[ <> ]]> '********' /*테스트 cp제외처리 *********/
               )
         limit #{_pagesize} offset #{_skiprows}
    </select>
    
    <select id="selectEzmktB2cSaleInfMmBat02">
        /* 이지웰마켓B2C 상세 데이터 원천 Source (갱신용) */
        select stl_ym as account_ym
             , ord_no as order_num
             , min(ord_gds_seq) as order_goods_num /* 부분취소가 가능하여 ord_gds_seq = 1번이 아닌 것이 부분취소가 된경우 취소된 주문번호의 1번을 제외한 다른 max(order_goods_num)을 가져와서 해당 주문상세에 금액을 넣는다. */
             , ord_cncl_yn as order_cancel_yn
             , crd_pymt_amt as r_card
             , acnt_trns_pymt_amt as r_trans
             , zrpay_pymt_amt as r_zeropay
             , nvpay_pymt_amt as r_naver_pay
             , onpay_pymt_amt as r_onnuri_pay
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '1001'),0) as point
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and (s.pay_type = '1002' or s.pay_type = '2004' or s.pay_type = '2006')),0) as card
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '1010'),0) as virture
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '2005'),0) as reserve
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '1008'),0) as special
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '1003'),0) as trans
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '1012'),0) as coupon
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type in ('1016','1019')),0) as zeropay
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '1022'),0) as naver_pay
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '2001'),0) as request
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '2002'),0) as approval
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '2003'),0) as paylater
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '1009'),0) as korcham_amt
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '1027'),0) as onnuri_pay
             , coalesce((select -sum(s.cancel_amt)
                           from ez_market.mk_order_pay s
                          where s.cancel_dt like #{stlYm} || '%'
                            and s.order_num::text = eo.ord_no
                            and s.pay_status = '1003'
                            and s.pay_type = '1029'),0) as digital_onnuri
          from ez_ca.ca_mkt_b2c_stl_d eo
         where eo.stl_ym = #{stlYm}
           and eo.ord_cncl_yn = 'Y'
           and eo.cncl_dtm between #{stlYm} || '********' and #{stlYmd} || '235959'
           and eo.ord_no in (select distinct s.ord_no  /*부분취소가 가능하여 ord_gds_seq = 1번이 아닌 것이 부분취소가 된경우 취소된 주문번호의 1번을 제외한 다른 max(ord_gds_seq) 을 가져와서 해당 주문상세에 금액을 넣는다.*/
                               from ez_ca.ca_mkt_b2c_stl_d s
                              where s.stl_ym = #{stlYm}
                                and s.cncl_dtm between #{stlYm} || '********' and #{stlYmd} || '235959'
                                and s.ord_cncl_yn = 'Y'
                                and s.ord_gds_seq <![CDATA[ <> ]]> 1
                             except
                             select distinct s.ord_no
                               from ez_ca.ca_mkt_b2c_stl_d s
                              where s.stl_ym = #{stlYm}
                                and s.cncl_dtm between #{stlYm} || '********' and #{stlYmd} || '235959'
                                and s.ord_cncl_yn = 'Y'
                                and s.ord_gds_seq = 1)
         group by stl_ym
                , ord_no
                , ord_cncl_yn
                , crd_pymt_amt
                , acnt_trns_pymt_amt
                , zrpay_pymt_amt
                , nvpay_pymt_amt
                , onpay_pymt_amt
         limit #{_pagesize} offset #{_skiprows}
    </select>
</mapper>