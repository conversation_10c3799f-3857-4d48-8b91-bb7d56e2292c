package com.ezwelesp.batch.hpas.alarm.mapper.command;

import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import com.ezwelesp.batch.hpas.alarm.dto.GoodsFlowCspDataSyncMainDto;

@Mapper
public interface GoodsFlowCspDataSyncMainCommandMapper {

    void setCspReturnDlvrUseYnN(Map<String, String> map);

    void setCspReturnDlvrUseYnN(GoodsFlowCspDataSyncMainDto goodsFlowCspDataSyncMainDto);

    void delCspDlvrContractBoxRate(GoodsFlowCspDataSyncMainDto goodsFlowCspDataSyncMainDto);

    void setCspDlvrContractBoxRate(GoodsFlowCspDataSyncMainDto goodsFlowCspDataSyncMainDto);

    void setCspReturnDlvr(GoodsFlowCspDataSyncMainDto goodsFlowCspDataSyncMainDto);
}
