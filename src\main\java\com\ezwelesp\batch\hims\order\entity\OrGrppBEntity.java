package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 공동구매기본(ez_or.or_grpp_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrGrppBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 공동구매코드(grpp_cd) not null
     */
    private String grppCd;

    /**
     * 공동구매명(grpp_nm)
     */
    private String grppNm;

    /**
     * 신청가능시작일시(apl_poss_strt_dtm)
     */
    private String aplPossStrtDtm;

    /**
     * 신청가능종료일시(apl_poss_end_dtm)
     */
    private String aplPossEndDtm;

    /**
     * 판매시작일시(sell_strt_dtm)
     */
    private String sellStrtDtm;

    /**
     * 판매종료일시(sell_end_dtm)
     */
    private String sellEndDtm;

    /**
     * 사용여부(use_yn) not null
     */
    private String useYn;

    /**
     * 상단이미지사용여부(top_img_use_yn) not null
     */
    private String topImgUseYn;

    /**
     * PC상단이미지경로(pc_top_img_path)
     */
    private String pcTopImgPath;

    /**
     * PC상단배경이미지경로(pc_top_bg_img_path)
     */
    private String pcTopBgImgPath;

    /**
     * PC상단배경RGB(pc_top_bg_rgb)
     */
    private String pcTopBgRgb;

    /**
     * PC시각영역배경RGB(pc_time_trty_bg_rgb)
     */
    private String pcTimeTrtyBgRgb;

    /**
     * 모바일상단이미지경로(mbl_top_img_path)
     */
    private String mblTopImgPath;

    /**
     * 모바일시각배경RGB(mbl_time_bg_rgb)
     */
    private String mblTimeBgRgb;

    /**
     * 상단배경종류코드(top_bg_knd_cd)
     */
    private String topBgKndCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
