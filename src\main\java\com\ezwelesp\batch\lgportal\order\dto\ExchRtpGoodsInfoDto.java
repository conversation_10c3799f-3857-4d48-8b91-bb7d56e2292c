package com.ezwelesp.batch.lgportal.order.dto;

import com.ezwelesp.batch.hims.order.entity.OrOrdGdsDEntity;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
  * 교환/반품 주문 정보 Dto
  *
  * <AUTHOR>
  * @since 2025.04.08
  * @see com.ezwelesp.batch.lgportal.entity
  */
@Jacksonized
@Getter
@SuperBuilder
public class ExchRtpGoodsInfoDto extends OrOrdGdsDEntity {
    /**
     * 반품배송 사용여부
     */
    private String returnDlvrUseYn;

    /**
     * 반품상품 회수 대상 여부
     */
    private String returnYn;

    /**
     * 상품옵션유형코드
     */
    private String gdsOptTypCd;

    /**
     * 상품옵션 개수
     */
    private int optCnt;

    /**
     * 배송주소번호(dlv_adr_no)
     */
    private Long dlvAdrNo;
}
