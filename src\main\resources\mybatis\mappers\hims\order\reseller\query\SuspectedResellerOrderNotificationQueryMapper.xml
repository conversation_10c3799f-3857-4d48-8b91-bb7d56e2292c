<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.reseller.mapper.query.SuspectedResellerOrderNotificationQueryMapper">

    <select id="selectSuspectedResellerByGdsOneDay" resultType="com.ezwelesp.batch.hims.order.reseller.domain.SuspectedResellerOrderDto">
        SELECT ord.clnt_cd
             , ord.user_key
             , (SELECT clnt.clnt_nm FROM ez_ct.ct_clnt_b clnt WHERE clnt.clnt_cd = ord.clnt_cd) AS clnt_nm
             , (SELECT cub.usr_nm FROM ez_ct.ct_usr_b cub WHERE cub.user_key = ord.user_key) AS usr_nm
             , COUNT(ord.gds_cd) AS ord_gds_sku_qty
             , SUM(ord.ord_gds_qty) AS ord_gds_qty
             , SUM(ord.ord_amt) AS ord_amt
          FROM (
                SELECT gds.clnt_cd
                     , gds.user_key
                     , gds.gds_cd
                     , SUM(gds.ord_gds_qty) as ord_gds_qty
                     , SUM(gds.ord_gds_sell_prc * gds.ord_gds_qty) as ord_amt
                  FROM (
                        SELECT oob.clnt_cd
                             , oob.user_key
                             , oogd.gds_cd
                             , oogd.ord_gds_qty - oogd.cncl_gds_qty - coalesce(cgds.clm_gds_qty, 0) AS ord_gds_qty
                             , oogd.ord_gds_sell_prc
                          FROM ez_or.or_ord_b oob
                          JOIN ez_or.or_ord_gds_d oogd ON oogd.ord_no = oob.ord_no
                          LEFT JOIN LATERAL (SELECT ccgd.ord_no
                                                  , ccgd.ord_gds_seq
                                                  , SUM(ccgd.clm_gds_qty - ccgd.cncl_qty) AS clm_gds_qty
                                               FROM ez_or.cl_clm_gds_d ccgd
                                               JOIN ez_or.cl_clm_b ccb ON ccb.clm_no = ccgd.clm_no
                                              WHERE ccgd.ord_no = oogd.ord_no
                                                AND ccgd.ord_gds_seq = oogd.ord_gds_seq
                                                AND ((ccb.clm_knd_cd = 'CNCL' AND ccb.clm_st_cd = 'APL')
                                                        OR ccb.clm_knd_cd = 'RTP')
                                              GROUP BY ccgd.ord_no, ccgd.ord_gds_seq) cgds ON cgds.ord_no = oogd.ord_no AND cgds.ord_gds_seq = oogd.ord_gds_seq
                         WHERE oob.ord_dtm BETWEEN CONCAT(TO_CHAR(CURRENT_DATE - interval '1 days', 'YYYYMMDD'), '000000')
                                            AND CONCAT(TO_CHAR(CURRENT_DATE - interval '1 days', 'YYYYMMDD'), '235959')
                           AND oogd.ch_cd IN ('100', '101') /* 복지샵, 브랜드몰 */
                       ) gds
                 GROUP BY gds.clnt_cd, gds.user_key, gds.gds_cd
                HAVING SUM(gds.ord_gds_qty) >= 10
               ) ord
         GROUP BY ord.clnt_cd, ord.user_key
    </select>

    <select id="selectSuspectedResellerByOrder" parameterType="java.lang.String" resultType="com.ezwelesp.batch.hims.order.reseller.domain.SuspectedResellerOrderDto">
        SELECT ord.clnt_cd
             , ord.user_key
             , (SELECT clnt.clnt_nm FROM ez_ct.ct_clnt_b clnt WHERE clnt.clnt_cd = ord.clnt_cd) AS clnt_nm
             , (SELECT cub.usr_nm FROM ez_ct.ct_usr_b cub WHERE cub.user_key = ord.user_key) AS usr_nm
             , COUNT(ord.ord_no) AS ord_cnt
             , SUM(ord.ord_gds_qty) AS ord_gds_qty
             , SUM(ord.ord_amt) AS ord_amt
          FROM (
                SELECT oob.clnt_cd
                     , oob.user_key
                     , oob.ord_no
                     , SUM(oogd.ord_gds_qty - oogd.cncl_gds_qty - coalesce(cgds.clm_gds_qty, 0)) AS ord_gds_qty
                     , SUM(oogd.ord_gds_sell_prc * (oogd.ord_gds_qty - oogd.cncl_gds_qty - coalesce(cgds.clm_gds_qty, 0))) AS ord_amt
                  FROM ez_or.or_ord_b oob
                  JOIN ez_or.or_ord_gds_d oogd ON oogd.ord_no = oob.ord_no
                  LEFT JOIN LATERAL (SELECT ccgd.ord_no
                                          , ccgd.ord_gds_seq
                                          , SUM(ccgd.clm_gds_qty - ccgd.cncl_qty) AS clm_gds_qty
                                       FROM ez_or.cl_clm_gds_d ccgd
                                       JOIN ez_or.cl_clm_b ccb ON ccb.clm_no = ccgd.clm_no
                                      WHERE ccgd.ord_no = oogd.ord_no
                                        AND ccgd.ord_gds_seq = oogd.ord_gds_seq
                                        AND ((ccb.clm_knd_cd = 'CNCL' AND ccb.clm_st_cd = 'APL')
                                                  OR ccb.clm_knd_cd = 'RTP')
                                      GROUP BY ccgd.ord_no, ccgd.ord_gds_seq) cgds ON cgds.ord_no = oogd.ord_no AND cgds.ord_gds_seq = oogd.ord_gds_seq
                <where>
                    <choose>
                    <when test="searchTermType == 'week'">
                        oob.ord_dtm BETWEEN CONCAT(TO_CHAR(CURRENT_DATE - interval '8 days', 'YYYYMMDD'), '000000')
                        AND CONCAT(TO_CHAR(CURRENT_DATE - interval '1 days', 'YYYYMMDD'), '235959')
                    </when>
                    <when test="searchTermType == 'month'">
                        oob.ord_dtm BETWEEN CONCAT(TO_CHAR(CURRENT_DATE - interval '1 month', 'YYYYMMDD'), '000000')
                        AND CONCAT(TO_CHAR(CURRENT_DATE - interval '1 days', 'YYYYMMDD'), '235959')
                    </when>
                    <otherwise>
                        oob.ord_dtm BETWEEN CONCAT(TO_CHAR(CURRENT_DATE - interval '1 days', 'YYYYMMDD'), '000000')
                        AND CONCAT(TO_CHAR(CURRENT_DATE - interval '1 days', 'YYYYMMDD'), '235959')
                    </otherwise>
                </choose>
                   AND oogd.ch_cd IN ('100', '101') /* 복지샵, 브랜드몰 */
                </where>
                 GROUP BY oob.clnt_cd, oob.user_key, oob.ord_no
               ) ord
         GROUP BY ord.clnt_cd, ord.user_key
        <choose>
            <when test="searchTermType == 'week'">
                HAVING COUNT(ord.ord_no) >= 10
            </when>
            <when test="searchTermType == 'month'">
                HAVING COUNT(ord.ord_no) >= 50
            </when>
            <otherwise>
                HAVING COUNT(ord.ord_no) >= 5
            </otherwise>
        </choose>
    </select>
</mapper>
