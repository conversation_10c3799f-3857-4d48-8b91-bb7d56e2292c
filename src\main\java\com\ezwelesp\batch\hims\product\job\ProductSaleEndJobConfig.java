package com.ezwelesp.batch.hims.product.job;


import com.ezwelesp.batch.hims.product.chuck.processor.LgPortalProductSaleEndProcessor;
import com.ezwelesp.batch.hims.product.chuck.reader.LgPortalProductSaleEndReader;
import com.ezwelesp.batch.hims.product.chuck.writer.LgPortalProductSaleEndWriter;
import com.ezwelesp.batch.hims.product.domain.dto.LgPortalProductSaleEndDto;
import com.ezwelesp.batch.hims.product.tasklet.DisableProductApprovalTasklet;
import com.ezwelesp.batch.listener.CommonStepListener;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;



/**
 * 판매기간 설정 상품 상태변경(LG API 품절처리 포함)
 */
//Scheduled(cron = "0 0 00 * * *")

@Configuration
public class ProductSaleEndJobConfig {

    private final DisableProductApprovalTasklet disableProductApprovalTasklet;
    private final int CHUNK_SIZE = 1000;

    public ProductSaleEndJobConfig(
            @Qualifier("disableProductApprovalTasklet")
            DisableProductApprovalTasklet disableProductApprovalTasklet
    ) {
        this.disableProductApprovalTasklet = disableProductApprovalTasklet;
    }

    @Bean("BA_HIPO00005")
    public Job productSaleEndJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPO00005_STEP1") Step lgPortalProductSaleEndStep
            , @Qualifier("BA_HIPO00005_STEP2") Step disableProductApprovalStep
    ) {
        return new JobBuilder("BA_HIPO00005", jobRepository)
                .start(lgPortalProductSaleEndStep)
                .next(disableProductApprovalStep)
                .build();
    }

    // 유형상품의 일반배송 상품 중
    // 판매 중인 판매기간 도래한 상품 lg 포털 쪽 상품 품절 처리 위한 step
    // 상품 상태값 변경전에 LG 쪽 먼저 처리.
    @Bean("BA_HIPO00005_STEP1")
    public Step lgPortalProductSaleEndStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
            , LgPortalProductSaleEndReader lgPortalProductSaleEndReader
            , LgPortalProductSaleEndProcessor lgPortalProductSaleEndProcessor
            , LgPortalProductSaleEndWriter lgPortalProductSaleEndWriter
            , CommonStepListener commonStepListener
    ) {
        return new StepBuilder("BA_HIPO00005_STEP1", jobRepository)
                .allowStartIfComplete(true)
                .<LgPortalProductSaleEndDto, LgPortalProductSaleEndDto>chunk(CHUNK_SIZE, transactionManager)
                .reader(lgPortalProductSaleEndReader)
                .processor(lgPortalProductSaleEndProcessor)
                .writer(lgPortalProductSaleEndWriter)
                .listener(commonStepListener)
                .build();

    }

    // 유형상품의 일반배송 상품 중
    // 판매 중인 판매기간 도래한 상품 판매중지 처리
    @Bean("BA_HIPO00005_STEP2")
    public Step disableProductApprovalStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
    ) {
        return new StepBuilder("BA_HIPO00005_STEP2", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(disableProductApprovalTasklet, transactionManager)
                .build();
    }
}
