package com.ezwelesp.batch.hims.order.receipt.service.command;

import com.ezwelesp.batch.hims.order.receipt.domain.CashPublicationGatherDto;
import com.ezwelesp.batch.hims.order.receipt.domain.CashReceiptParamDto;
import com.ezwelesp.batch.hims.order.receipt.domain.OrderInfoDto;


public interface CashReceiptCommandService {

    /**
     * 현금영수증 기초데이터 생성
     *
     * @param cashReceipt{ordNo:~ clmNo:~}
     */
    public OrderInfoDto createCashReceipt(CashReceiptParamDto cashReceipt);



    public int modifyPublicationReceipt(CashPublicationGatherDto cashPubGatherDto);

    public int modifyPublicationReceiptError(CashPublicationGatherDto cashPubGatherDto);

    public int modifyPublicationReceiptNumError(CashPublicationGatherDto cashPubGatherDto);

    public int deletePublicationReceiptError(CashPublicationGatherDto cashPubGatherDto);

    public int modifyPublicationReceiptCancel(CashPublicationGatherDto cashPubGatherDto);

    public int modifyPublicationReceiptCancelError(CashPublicationGatherDto cashPubGatherDto);

    public int createPublicationReceiptError(CashPublicationGatherDto cashPubGatherDto);

}
