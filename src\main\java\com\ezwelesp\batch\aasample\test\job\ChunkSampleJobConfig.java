package com.ezwelesp.batch.aasample.test.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import com.ezwelesp.batch.aasample.test.chunk.processor.ChunkSampleItemProcessor;
import com.ezwelesp.batch.aasample.test.chunk.reader.ChunkSampleItemReader;
import com.ezwelesp.batch.aasample.test.chunk.writer.ChunkSampleItemWriter;
import com.ezwelesp.batch.aasample.test.domain.Test;
import com.ezwelesp.batch.listener.CommonJobListener;
import com.ezwelesp.batch.listener.CommonStepListener;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class ChunkSampleJobConfig {
    private final CommonJobListener commonJobListener;
    private final int CHUNK_SIZE = 1000;

    @Bean("chunkSampleJob")
    public Job chunkSampleJob(JobRepository jobRepository, Step chunkSampleStep) {
        return new JobBuilder("chunkSampleJob", jobRepository)
                .listener(commonJobListener)
                .start(chunkSampleStep)
                .build();
    }

    @Primary
    @Bean("chunkSampleStep")
    public Step chunkSampleStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager, ChunkSampleItemReader chunkSampleItemReader,
                                ChunkSampleItemProcessor chunkSampleItemProcessor, ChunkSampleItemWriter chunkSampleItemWriter, CommonStepListener commonStepListener) {
        return new StepBuilder("chunkSampleStep", jobRepository)
                .allowStartIfComplete(true)
                .<Test, Test>chunk(CHUNK_SIZE, transactionManager)
                .reader(chunkSampleItemReader)
                .processor(chunkSampleItemProcessor)
                .writer(chunkSampleItemWriter)
                .listener(commonStepListener)
                .build();

    }
}
