package com.ezwelesp.batch.lgportal.order.chunk.writer;

import com.ezwelesp.batch.lgportal.entity.OrOrdBEntity;
import com.ezwelesp.batch.lgportal.order.dto.DeliveryAcceptReqDto;
import com.ezwelesp.batch.lgportal.order.mapper.command.ApiBatchCommandMapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 배송완료 처리
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order
 * @since 2025.03.05
 */
@Component
public class OrderListItemWriter implements ItemWriter<OrOrdBEntity> {

    @Value("${lgportal.server.host}")
    private String serverHost;

    @Value("${lgportal.server.port}")
    private int serverPort;

    @Value("${lgportal.server.timeout}")
    private int timeout;

    @Value("${lgportal.api-key}")
    private String apiKey;


    private final SqlSessionTemplate sqlSessionTemplate;

    /**
     * Instantiates a new Delivery complete item writer.
     *
     * @param sqlSessionFactory the sql session factory
     */
    public OrderListItemWriter(@Qualifier("primaryCommandSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory);
    }

    @Override
    public void write(Chunk<? extends OrOrdBEntity> chunks) {
        // OD_OR 데이터 생성

        // 주문 API 호출

        // 이력 생성
        // insertSKUSoldout, MERGE INTO WELFARE_DEV.API_BATCH_SEND
        /*orderParamMap.put("apiKey", PropertiesManager.getProperty("lgcns.api.apiKey"));
        orderParamMap.put("providerNo", "419");
        orderParamMap.put("certKey", PropertiesManager.getProperty("lgcns.api.certKey"));*/

        for (OrOrdBEntity entity : chunks) {
            OrOrdBEntity ordEntity = this.deliveryComplete(entity);
            this.sqlSessionTemplate.insert(ApiBatchCommandMapper.class.getName() + ".insertBatchDlvr", null);
        }
    }

    private OrOrdBEntity deliveryComplete(OrOrdBEntity entity) {
        DeliveryAcceptReqDto deliveryAcceptDto = new DeliveryAcceptReqDto(this.apiKey);

        /*deliveryAcceptDto.setDeliveryItems(Arrays.asList(DeliveryAcceptReqDto.DeliveryItem.builder().deliveryCmdNo(
                Long.parseLong(entity.getDeliveryNo())).build()));

        try {
            AjaxResult
                    result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_DELIVERY_COMPLETE_URL, this.timeout, JsonObjectConverter.serialize(deliveryAcceptDto));

            entity.setSendKey(1010);

            if (StringUtils.equals("200", result.get("result").toString())) {
                entity.setSendYn("Y");
                entity.setReturnCode(result.get("result").toString());
                entity.setReturnMessage(result.get("resultMessage").toString());
            } else {
                entity.setSendYn("F");
                entity.setReturnCode(result.get("result").toString());
                entity.setReturnMessage(result.get("resultMessage").toString());
            }
            return entity;

        } catch (JsonProcessingException e) {
            throw new RuntimeException("LG portal api request failed");
        }*/
        return null;
    }
}
