package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 클레임기본(ez_or.cl_clm_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClClmBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 원본클레임번호(orgl_clm_no)
     */
    private String orglClmNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 사용자명(usr_nm) not null
     */
    private String usrNm;

    /**
     * 사용자모바일전화번호(usr_mbl_telno) not null
     */
    private String usrMblTelno;

    /**
     * 사용자이메일주소(usr_eml_adr)
     */
    private String usrEmlAdr;

    /**
     * 비재직사용자클레임신청여부(nhof_usr_clm_apl_yn) not null
     */
    private String nhofUsrClmAplYn;

    /**
     * 클레임일시(clm_dtm) not null
     */
    private String clmDtm;

    /**
     * 클레임접수일시(clm_acpt_dtm)
     */
    private String clmAcptDtm;

    /**
     * 클레임환불요청일시(clm_rfnd_req_dtm)
     */
    private String clmRfndReqDtm;

    /**
     * 배송비용결제요청일시(dlv_exp_pymt_req_dtm)
     */
    private String dlvExpPymtReqDtm;

    /**
     * 배송비용결제완료일시(dlv_exp_pymt_cmpt_dtm)
     */
    private String dlvExpPymtCmptDtm;

    /**
     * 클레임환불완료일시(clm_rfnd_cmpt_dtm)
     */
    private String clmRfndCmptDtm;

    /**
     * 클레임철회일시(clm_wtdr_dtm)
     */
    private String clmWtdrDtm;

    /**
     * 클레임처리관리자ID(clm_prcs_mgr_id)
     */
    private String clmPrcsMgrId;

    /**
     * 클레임상태코드(clm_st_cd) not null
     */
    private String clmStCd;

    /**
     * 클레임환불상태코드(clm_rfnd_st_cd)
     */
    private String clmRfndStCd;

    /**
     * 클레임종류코드(clm_knd_cd) not null
     */
    private String clmKndCd;

    /**
     * 클레임요청주체코드(clm_req_magn_cd) not null
     */
    private String clmReqMagnCd;

    /**
     * 클레임추가비용처리방법코드(clm_add_exp_prcs_mthd_cd)
     */
    private String clmAddExpPrcsMthdCd;

    /**
     * 취소수수료클레임종류코드(cncl_cms_clm_knd_cd)
     */
    private String cnclCmsClmKndCd;

    /**
     * 접속디바이스코드(acss_dvc_cd)
     */
    private String acssDvcCd;

    /**
     * 클레임상담관리자ID(clm_cnsl_mgr_id)
     */
    private String clmCnslMgrId;

    /**
     * 클레임상담관리자명(clm_cnsl_mgr_nm)
     */
    private String clmCnslMgrNm;

    /**
     * 클레임취소요청처리일시(clm_cncl_req_prcs_dtm)
     */
    private String clmCnclReqPrcsDtm;

    /**
     * 클레임취소요청처리관리자ID(clm_cncl_req_prcs_mgr_id)
     */
    private String clmCnclReqPrcsMgrId;

    /**
     * 클레임취소요청승인상태코드(clm_cncl_req_apv_st_cd)
     */
    private String clmCnclReqApvStCd;

    /**
     * 클레임취소요청반려사유코드(clm_cncl_req_rjct_rsn_cd)
     */
    private String clmCnclReqRjctRsnCd;

    /**
     * 클레임취소요청반려사유내용(clm_cncl_req_rjct_rsn_cntn)
     */
    private String clmCnclReqRjctRsnCntn;

    /**
     * 클레임취소요청주체코드(clm_cncl_req_magn_cd)
     */
    private String clmCnclReqMagnCd;

    /**
     * 클레임취소사유코드(clm_cncl_rsn_cd)
     */
    private String clmCnclRsnCd;

    /**
     * 클레임취소사유내용(clm_cncl_rsn_cntn)
     */
    private String clmCnclRsnCntn;

    /**
     * 추가결제요청금액(add_pymt_req_amt) not null
     */
    private BigDecimal addPymtReqAmt;

    /**
     * 클레임요청배송비용(clm_req_dlv_exp)
     */
    private BigDecimal clmReqDlvExp;

    /**
     * 설정배송비용(stup_dlv_exp)
     */
    private BigDecimal stupDlvExp;

    /**
     * 전체반품환불배송비용(all_rtp_rfnd_dlv_exp)
     */
    private BigDecimal allRtpRfndDlvExp;

    /**
     * 제휴사클레임연동번호(asp_clm_intl_no)
     */
    private String aspClmIntlNo;

    /**
     * 클레임고유난수번호(clm_innt_rdno_no)
     */
    private String clmInntRdnoNo;

    /**
     * 적립금전환예정금액(mlg_swtc_due_amt)
     */
    private BigDecimal mlgSwtcDueAmt;

    /**
     * 적립금전환귀책사유주체코드(mlg_swtc_atbr_magn_cd)
     */
    private String mlgSwtcAtbrMagnCd;

    /**
     * 적립금전환일시(mlg_swtc_dtm)
     */
    private String mlgSwtcDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
