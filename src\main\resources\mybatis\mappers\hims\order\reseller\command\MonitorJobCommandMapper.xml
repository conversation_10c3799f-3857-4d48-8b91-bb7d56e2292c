<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.reseller.mapper.command.MonitorJobCommandMapper">

    <insert id="insertScMntMonitorRes">
        INSERT INTO ez_cm.sc_mnt_monitor_res (
              res_seq
            , job_id
            , job_start_dt
            , job_end_dt
            , alram_yn
            , alram_cnt_max_yn
            , alram_cnt_max
            , alram_user
            , critical_value_yn
            , critical_value
            , critical_value_unit
            , res_status
            , res_value
            , res_etc
            , res_etc2
            , res_ip
            , reg_dt
            , modi_dt
            , send_status
            , send_start_dt
            , send_end_dt
            , send_desc
        ) VALUES (
              (SELECT coalesce(MAX(res_seq), 0) + 1 FROM ez_cm.sc_mnt_monitor_res)
            , #{jobId}
            , #{jobStartDt}
            , #{jobEndDt}
            , #{alramYn}
            , #{alramCntMaxYn}
            , #{alramCntMax}
            , #{alramUser}
            , #{criticalValueYn}
            , #{criticalValue}
            , #{criticalValueUnit}
            , #{resStatus}
            , #{resValue}
            , #{resEtc}
            , #{resEtc2}
            , #{resIp}
            , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
            , TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
            , #{sendStatus}
            , #{sendStartDt}
            , #{sendEndDt}
            , #{sendDesc}
        )
    </insert>
</mapper>
