package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * [비표준]모니터링결과응답
 * sc_mnt_monitor_res
 */
@Getter
@SuperBuilder
public class ScMntMonitorResEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 응답번호
     */
    private BigDecimal resSeq;

    /**
     * 모니터링잡아이디
     */
    private String jobId;

    /**
     * 모니터링시작일시
     */
    private String jobStartDt;

    /**
     * 모니터링종료일시
     */
    private String jobEndDt;

    /**
     * 알람발송여부
     */
    private String alramYn;

    /**
     * 알람연속발송최대수량사용유무
     */
    private String alramCntMaxYn;

    /**
     * 알람연속발송최대수량
     */
    private Long alramCntMax;

    /**
     * 알람수신자
     */
    private String alramUser;

    /**
     * 임계치사용유무
     */
    private String criticalValueYn;

    /**
     * 임계치결과값
     */
    private Long criticalValue;

    /**
     * 임계치단위
     */
    private String criticalValueUnit;

    /**
     * 알람요청상태
     */
    private String resStatus;

    /**
     * 호출결과값
     */
    private BigDecimal resValue;

    /**
     * 호출비고
     */
    private String resEtc;

    /**
     * 호출비고2
     */
    private String resEtc2;

    /**
     * 요청자IP
     */
    private String resIp;

    /**
     * 등록일시
     */
    private String regDt;

    /**
     * 수정일시
     */
    private String modiDt;

    /**
     * 알람발송상태
     */
    private String sendStatus;

    /**
     * 알람발송시작일
     */
    private String sendStartDt;

    /**
     * 알람발송종료일
     */
    private String sendEndDt;

    /**
     * 알람발송상세
     */
    private String sendDesc;
}
