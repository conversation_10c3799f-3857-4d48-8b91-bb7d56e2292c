package com.ezwelesp.batch.hims.order.receipt.domain;

import lombok.Data;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Jacksonized
@Data
@SuperBuilder
public class OrderPaymentPackDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;

    List<OrderPaymentDto> orderPaymentList;

}
