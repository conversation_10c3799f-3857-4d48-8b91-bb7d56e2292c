package com.ezwelesp.batch.lgportal.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 주문 배송정보 Dto
 *
 * <AUTHOR>
 * @see OrderDlvTmpEntity
 * @since 2025.03.04
 */
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OrderDlvTmpEntity implements Serializable {

    private String dlvAdrNo;

    private String dlvNo;

    private String dlvStCd;

    private String rcvrNm;

    private String rcvrMblTelno;

    private String rcvrZipcd;

    private String rcvrBasAdr;

    private String rcvrDtlAdr;

    private String dlvReqCntn;

}
