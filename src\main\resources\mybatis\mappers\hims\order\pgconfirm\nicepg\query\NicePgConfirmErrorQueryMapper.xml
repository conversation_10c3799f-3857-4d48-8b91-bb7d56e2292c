<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgconfirm.nicepg.mapper.query.NicePgConfirmErrorQueryMapper">

    <select id="selectNicePgConfirmErrorList"
            resultType="com.ezwelesp.batch.hims.order.pgconfirm.nicepg.domain.NicePgErrorDto">
        WITH PG_TARGET_TMP AS (
            SELECT
                TT.*,
                ROW_NUMBER() OVER (PARTITION BY CANCEL_TID ORDER BY DT ASC) NUM
            FROM (
                     SELECT
                         TID AS TID,
                         SUBSTR(A.MOID, 1, 10) AS CANCEL_TID,
                         CASE WHEN TRANS_CD = '01' THEN 'I' ELSE 'C' END TMP_STATUS,
                         APPR_DT || APPR_TM AS DT,
                         APPR_AMT::numeric AS DEAL_AMT,
                         'N' AS PG_TYPE,
                         '' AS GOODS_NM,
                         '' AS PAY_TYPE,
                         '' AS DEVICE_TYPE,
                         A.MOID AS SHOP_ORDER_NUM,
                         '' AS CONFIRM_NUM,
                         A.STORE_ID
                     FROM EZ_IF.EZ_EC_NICEPG A
                     WHERE A.MOID IN (
                         SELECT A.MOID FROM EZ_IF.EZ_EC_NICEPG A, (
                             SELECT SUBSTR(MOID, 1, 10) AS MOID FROM EZ_IF.EZ_EC_NICEPG
                             WHERE APPR_DT || APPR_TM  BETWEEN to_char(now() + interval '-3' hour, 'YYYYMMDDHH24') || '0000'
                                 AND to_char(now() + interval '-3' hour, 'YYYYMMDDHH24') || '5959'
                         ) B
                         WHERE
                             A.MOID LIKE B.MOID || '%' AND A.MOID != '0000000000'
                 )
            ) TT
        ), TARGET_DT AS (
            SELECT
                TO_CHAR(TO_DATE(MIN(DT), 'YYYYMMDDHH24MISS')-1, 'YYYYMMDDHH24MISS') AS MIN_DT,
                TO_CHAR(TO_DATE(MAX(DT), 'YYYYMMDDHH24MISS')+1, 'YYYYMMDDHH24MISS') AS MAX_DT
            FROM PG_TARGET_TMP
        ), PG_DATA AS (
            SELECT
                T.*,
                CASE WHEN TMP_STATUS = 'C' AND REAL_AMT <![CDATA[ > ]]> 0 THEN 'P' ELSE TMP_STATUS END AS STATUS
            FROM (
                SELECT
                (
                    SELECT
                    SUM(CASE WHEN TMP_STATUS = 'I' THEN DEAL_AMT ELSE DEAL_AMT * -1 END)
                    FROM PG_TARGET_TMP
                    WHERE
                    (CANCEL_TID = A.CANCEL_TID)
                    AND DT <![CDATA[ <= ]]> A.DT
                ) AS REAL_AMT,
                A.TID,
                B.ORG_TID AS CANCEL_TID,
                A.TMP_STATUS,
                A.DT,
                A.DEAL_AMT,
                A.PG_TYPE,
                A.GOODS_NM,
                A.PAY_TYPE,
                A.DEVICE_TYPE,
                A.SHOP_ORDER_NUM,
                A.CONFIRM_NUM,
                A.STORE_ID,
                A.NUM
                FROM PG_TARGET_TMP A, (
                    SELECT
                    A.TID AS ORG_TID,
                    A.CANCEL_TID,
                    ROW_NUMBER() OVER (PARTITION BY A.CANCEL_TID ORDER BY DT ASC) NUM
                    FROM PG_TARGET_TMP A
                ) B
                WHERE A.CANCEL_TID = B.CANCEL_TID
                AND B.NUM = 1
            ) T ORDER BY DT
        ), ORDER_DATA AS (
            SELECT
                T.*,
                ROW_NUMBER() OVER (PARTITION BY CANCEL_TID ORDER BY ORDER_NUM DESC) NUM
            FROM (
                     SELECT
                         O.ord_no as ORDER_NUM ,
                         O.clnt_cd as CLIENT_CD,
                         O.asp_ord_no AS ASP_ORDER_NUM,
                         O.ord_st_cd AS ORDER_STATUS,
                         OP.pymt_amt as RECG_PRICE,
                         OP.pg_frcs_no as STORE_ID,
                         op.pymt_div_cd AS PAY_CD,
                         OP.pg_apv_no AS CANCEL_TID,
                         O.ord_gds_smry_nm as goods_nm,
                         CASE WHEN O.ord_typ_cd = '1001' THEN '복지관'
                              WHEN O.ord_typ_cd = '1002' THEN '복지샵'
                              WHEN O.ord_typ_cd = '1003' THEN '제휴사'
                              ELSE O.ord_typ_cd END AS TARGET
                     FROM ez_or.or_ord_b O, ez_or.py_pymt_b OP
                     WHERE
                         O.ord_dtm BETWEEN (SELECT MIN_DT FROM TARGET_DT) AND (SELECT MAX_DT FROM TARGET_DT)
                       AND O.ord_typ_cd NOT IN ( '1009' ) /*마켓*/
                       AND O.ord_no = OP.ord_no
                       AND OP.pg_apv_no IN (
                         SELECT CANCEL_TID FROM PG_DATA
                     )
                       AND OP.pg_knd_cd = 'N'
                 ) T
        )
        SELECT
            A.ERROR_CODE AS ERR_TYPE,
            C.ORDER_NUM,
            C.CLIENT_CD,
            C.ORDER_STATUS,
            C.ASP_ORDER_NUM,
            C.TARGET AS ORDER_TYPE,
            C.RECG_PRICE,
            CASE WHEN (SELECT COUNT(1) FROM PG_DATA WHERE CANCEL_TID = A.CANCEL_TID) = 0 THEN '-' ELSE (SELECT sum(CASE WHEN STATUS = 'I' THEN DEAL_AMT ELSE DEAL_AMT * -1 END)::char FROM PG_DATA WHERE CANCEL_TID = A.CANCEL_TID) END AS PG_AMT,
            coalesce (C.GOODS_NM, A.GOODS_NM) AS GOODS_NM,
            A.PG_TYPE AS PG_TYPE,
            A.PAY_TYPE AS PAY_TYPE,
            A.STORE_ID AS STORE_ID,
            B.DT AS PG_DATE,
            A.CANCEL_TID AS CANCEL_TID,
            A.CONFIRM_NUM AS CARD_CONFIRM_NUM,
            A.SHOP_ORDER_NUM,
            A.DEVICE_TYPE
        FROM (
                 SELECT
                     A.*,
                     '1001' AS ERROR_CODE,
                     'ERROR' AS ORDER_STATUS,
                     '' AS ORDER_NUM,
                     '' AS ASP_ORDER_NUM,
                     '' AS ORDER_TYPE
                 FROM
                     PG_DATA A
                 WHERE STATUS = 'I'
                   AND NOT EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                         CANCEL_TID = A.CANCEL_TID
                 )
                   AND (
                     SELECT SUM(CASE WHEN STATUS = 'I' THEN DEAL_AMT ELSE DEAL_AMT * -1 END) FROM PG_DATA
                     WHERE CANCEL_TID = A.CANCEL_TID
                 ) != 0
                 UNION ALL
                 SELECT
                     A.*,
                     '1002' AS ERROR_CODE,
                     'ERROR' AS ORDER_STATUS,
                     '' AS ORDER_NUM,
                     '' AS ASP_ORDER_NUM,
                     '' AS ORDER_TYPE
                 FROM
                     PG_DATA A
                 WHERE STATUS = 'I'
                   AND EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                     )
                   AND NOT EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                   AND RECG_PRICE = A.REAL_AMT
                     )
                 UNION ALL
                 SELECT
                     A.*,
                     '1003' AS ERROR_CODE,
                     'ERROR' AS ORDER_STATUS,
                     '' AS ORDER_NUM,
                     '' AS ASP_ORDER_NUM,
                     '' AS ORDER_TYPE
                 FROM
                     PG_DATA A
                 WHERE STATUS = 'P'
                   AND NOT EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                   AND RECG_PRICE = A.REAL_AMT
                     ) AND CANCEL_TID NOT IN (
                     SELECT CANCEL_TID FROM
                     PG_DATA A
                     WHERE STATUS = 'I'
                   AND NOT EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                     )
                     UNION ALL
                     SELECT CANCEL_TID FROM
                     PG_DATA A
                     WHERE STATUS = 'I'
                   AND EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                     )
                   AND NOT EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                   AND RECG_PRICE = A.REAL_AMT
                     )
                     )
                 UNION ALL
                 SELECT
                     A.*,
                     '1004' AS ERROR_CODE,
                     'ERROR' AS ORDER_STATUS,
                     '' AS ORDER_NUM,
                     '' AS ASP_ORDER_NUM,
                     '' AS ORDER_TYPE
                 FROM
                     PG_DATA A
                 WHERE STATUS = 'C'
                   AND NOT EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                   AND RECG_PRICE = DEAL_AMT + (SELECT (SUM(CASE WHEN STATUS = 'I' THEN DEAL_AMT ELSE DEAL_AMT * -1 END)) FROM PG_DATA WHERE CANCEL_TID = A.CANCEL_TID)
                   AND ORDER_STATUS = '1003'
                     ) AND CANCEL_TID NOT IN (
                     SELECT CANCEL_TID FROM
                     PG_DATA A
                     WHERE STATUS = 'I'
                   AND NOT EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                     )
                     UNION ALL
                     SELECT CANCEL_TID FROM
                     PG_DATA A
                     WHERE STATUS = 'I'
                   AND EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                     )
                   AND NOT EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                   AND RECG_PRICE = A.REAL_AMT
                     )
                     UNION
                     SELECT CANCEL_TID FROM
                     PG_DATA A
                     WHERE STATUS = 'P'
                   AND NOT EXISTS (
                     SELECT 1 FROM ORDER_DATA
                     WHERE
                     CANCEL_TID = A.CANCEL_TID
                   AND RECG_PRICE = A.REAL_AMT
                     )
                 )
             ) A
             join PG_DATA B on A.CANCEL_TID = B.CANCEL_TID AND B.NUM = 1
             left join ORDER_DATA C on B.CANCEL_TID = C.CANCEL_TID AND C.NUM = 1
    </select>

</mapper>
