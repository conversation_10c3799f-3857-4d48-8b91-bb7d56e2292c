package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.tasklet.BsicInfOrdLoanDdBatPamTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatPamJobConfig {
    private final CommonJobListener commonJobListener;
    private final BsicInfOrdLoanDdBatPamTasklet bsicInfOrdLoanDdBatPamTasklet;

    /**
     * 항공마일리지 데이터 처리 일배치
     * 주기 : TODO 매일 오전 3시 0분 0초 (AS-IS 기준)
     * 설명 : 항공마일리지 데이터 처리 일배치
     * 
     * @param jobRepository
     * @param bsicInfOrdLoanDdBatPamTaskletStep
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatPamJob")
    Job bsicInfOrdLoanDdBatPamJob(
            JobRepository jobRepository,
            Step bsicInfOrdLoanDdBatPamTaskletStep
    ) {
        return new JobBuilder("bsicInfOrdLoanDdBatPamJob", jobRepository)
                .listener(commonJobListener)
                .start(bsicInfOrdLoanDdBatPamTaskletStep) // 항공마일리지 데이터 처리 프로시저 호출 Step
                .build();
    }

    /**
     * 항공마일리지 데이터 처리 프로시저 호출
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatPamTaskletStep")
    Step bsicInfOrdLoanDdBatPamTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("bsicInfOrdLoanDdBatPamTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(bsicInfOrdLoanDdBatPamTasklet, transactionManager)
                .build();
    }
}