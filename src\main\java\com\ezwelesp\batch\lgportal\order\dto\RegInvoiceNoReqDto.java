package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
  * 송장번호등록 요청 사용 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see RegInvoiceNoReqDto
  */
@Data
public class RegInvoiceNoReqDto {
    private String apiKey;

    private List<DeliveryItem> deliveryItems;

    public RegInvoiceNoReqDto(String apiKey) {
        this.apiKey = apiKey;
        this.deliveryItems = new ArrayList<>();
    }

    @Builder
    @Data
    public static class DeliveryItem {
        private Long deliveryCmdNo;
        private String invoiceNo;
        private String deliveryComCode;
    }

}
