<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ezwelesp.batch.aasample.test.mapper.command.UserCommandMapper">
    <update id="updateNtcTitl">
        UPDATE tble_ntc 
           SET ntc_titl = #{ntcTitl} 
         WHERE ntc_id = #{ntcId}
    </update>

    <insert id="insertNtc">
        INSERT INTO tble_ntc(ntc_id, sys_id, ntc_titl)
        VALUES ((SELECT NEXTVAL('seq_tble_ntc')), 1, #{ntcTitl})
    </insert>
    
    <update id="updateNtcStat">
        UPDATE tble_ntc 
           SET ntc_stat = #{ntcStat}
         WHERE ntc_id = #{ntcId}
    </update>
</mapper>
