package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 여행주문접수기본(ez_or.or_trip_ord_acpt_b)
 */
@Jacksonized
@Getter
@SuperBuilder(toBuilder = true)
public class OrTripOrdAcptBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 여행주문접수순번(trip_ord_acpt_seq) not null
     */
    private Long tripOrdAcptSeq;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 여행주문협력사예약번호(trip_ord_csp_rsv_no)
     */
    private String tripOrdCspRsvNo;

    /**
     * 여행주문승인상태코드(trip_ord_apv_st_cd) not null
     */
    private String tripOrdApvStCd;

    /**
     * 여행주문유형코드(trip_ord_typ_cd)
     */
    private String tripOrdTypCd;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 복지몰메뉴코드(hezo_menu_cd)
     */
    private String hezoMenuCd;

    /**
     * 상품명(gds_nm)
     */
    private String gdsNm;

    /**
     * 상품코드(gds_cd)
     */
    private String gdsCd;

    /**
     * 단가금액(uprc_amt) not null
     */
    private BigDecimal uprcAmt;

    /**
     * 주문수량(ord_qty)
     */
    private Integer ordQty;

    /**
     * 공급가격(spl_prc) not null
     */
    private BigDecimal splPrc;

    /**
     * 주문금액(ord_amt) not null
     */
    private BigDecimal ordAmt;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * 주문자명(ordr_nm)
     */
    private String ordrNm;

    /**
     * 주문자이메일주소(ordr_eml_adr)
     */
    private String ordrEmlAdr;

    /**
     * 주문자전화번호(ordr_telno)
     */
    private String ordrTelno;

    /**
     * 주문자모바일전화번호(ordr_mbl_telno)
     */
    private String ordrMblTelno;

    /**
     * 주문일자(ord_dt)
     */
    private String ordDt;

    /**
     * 주문시각(ord_time)
     */
    private String ordTime;

    /**
     * 주문자IP(ordr_ip)
     */
    private String ordrIp;

    /**
     * 제휴사주문완료연결URL(asp_ord_cmpt_conn_url)
     */
    private String aspOrdCmptConnUrl;

    /**
     * 출발일자(dptr_dt)
     */
    private String dptrDt;

    /**
     * 도착일자(arvl_dt)
     */
    private String arvlDt;

    /**
     * 여행주문접수담당자명(trip_ord_acpt_chrgr_nm)
     */
    private String tripOrdAcptChrgrNm;

    /**
     * 관리자승인결제금액(mgr_apv_pymt_amt) not null
     */
    private BigDecimal mgrApvPymtAmt;

    /**
     * 목록노출여부(ctlg_exps_yn) not null
     */
    private String ctlgExpsYn;

    /**
     * 암호화주민등록번호(enc_rrn)
     */
    private String encRrn;

    /**
     * 가족회원번호(fam_mem_no)
     */
    private String famMemNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
