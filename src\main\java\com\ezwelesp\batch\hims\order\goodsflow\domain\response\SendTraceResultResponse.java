package com.ezwelesp.batch.hims.order.goodsflow.domain.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 배송결과수신 response (SendTraceResultResponse API)
 */
@Getter
@Setter
@ToString
public class SendTraceResultResponse {
    private boolean success;
    private Error error;

    @Getter
    @Setter
    @ToString
    public static class Error {
        private int status;
        private String message;
        private String detailMessage;
    }
}
