package com.ezwelesp.batch.lgportal.entity;

import java.io.Serializable;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * 배송비용기본
 * dl_dlv_exp_b
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlDlvExpBTmpEntity implements Serializable {
    /**
     * 배송비용번호
     */
    private Long dlvExpNo;

    /**
     * 주문번호
     */
    private String ordNo;

    /**
     * 배송주소번호
     */
    private Long dlvAdrNo;

    /**
     * 협력사출고위치번호
     */
    private String cspObndLocNo;

    /**
     * 배송정책순번
     */
    private Long dlvPlcySeq;

    /**
     * 조건부무료배송여부
     */
    private String cndlNchgDlvYn;

    /**
     * 무료배송적용결제금액
     */
    private Long nchgDlvAplyPymtAmt;

    /**
     * 설정배송비용
     */
    private Long stupDlvExp;

    /**
     * 배송비용
     */
    private Long dlvExp;

    /**
     * 할인쿠폰배송비용
     */
    private Long dcCpnDlvExp;

    /**
     * 취소할인쿠폰배송비용
     */
    private Long cnclDcCpnDlvExp;

    /**
     * 도서산간추가배송비용
     */
    private Long ismtAddDlvExp;

    /**
     * 취소이후발생배송비용
     */
    private Long cnclAftOcrnDlvExp;

    /**
     * 착불송금배송비용
     */
    private Long arpayRmttDlvExp;

    /**
     * 현대이지웰부담배송비용
     */
    private Long ezwlBudnDlvExp;

    /**
     * 쿠폰번호
     */
    private String cpnNo;

    /**
     * 쿠폰사용상세번호
     */
    private Long cpnUseDtlNo;

    /**
     * 쿠폰사용취소일시
     */
    private String cpnUseCnclDtm;

    /**
     * 최초등록일시
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID
     */
    private String lastModPgmId;

    private static final long serialVersionUID = 1L;
}
