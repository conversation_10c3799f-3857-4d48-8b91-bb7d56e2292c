<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.product.mapper.command.ProductManagementCommandMapper">
    <insert id="insertLgPortalProductSaleEnd">
        INSERT INTO EZ_IF.api_batch_send
        ( api_seq
        , provider_no
        , certkey
        , product_no
        , send_key
        , send_yn
        , reg_dt
        , reg_id
        , delivery_no)
        VALUES ( nextval('ez_if.sq_api_batch_send')
               , #{providerNo}
               , #{certkey}
               , #{productNo}
               , #{sendKey}
               , #{sendYn}
               , to_char(now(), 'yyyymmddhh24miss')
               , #{regId}
               , #{deliveryNo})
    </insert>


    <update id="updateDisableProductApproval">
        /* 판매기간 종료시 상태 변경 : 판매중 -> 판매중지*/
        UPDATE EZ_PD.pd_gds_c
        SET gds_sell_st_cd  = '1005'
          , sell_strt_dtm   = ''
          , sell_end_dtm    = ''
          , last_mod_dtm    = to_char(now(), 'yyyymmddhh24miss')
          , last_mod_usr_id = 'batch'
          , last_mod_pgm_id = 'productSaleEndJob'
        WHERE gds_sell_st_cd = '1002'
          AND sell_end_dtm <![CDATA[ < ]]> to_char(now(), 'yyyymmddhh24miss');
    </update>


    <delete id="deleteProductRecentViewGoods">
        /* 최근본 상품 테이블 30일  지난 내역 삭제 */
        DELETE
        FROM ez_pd.pd_rcnt_vw_gds_l
        WHERE last_mod_dtm <![CDATA[ < ]]> to_char(current_date - 30, 'yyyymmddhh24miss')
    </delete>


</mapper>
