package com.ezwelesp.batch.hims.order.pgcompare.pinpay.tasklet;

import java.util.Optional;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.order.pgcompare.pinpay.service.PinpayService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class PinpayTasklet implements Tasklet{
	private final PinpayService pinpayService;

	@Override
	public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
		log.info("############## 핀페이 결제대사 START ##############");
		String searchDtm = Optional.ofNullable(chunkContext.getStepContext().getJobParameters().get("searchDtm"))
			                .map(Object::toString)
			                .filter(s -> !s.isBlank())
			                .orElse(null);
		
		this.pinpayService.callPinpayApi(searchDtm);
		log.info("############## 핀페이 결제대사 END ##############");
	
		return RepeatStatus.FINISHED;
	}
}
