package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 하나투어예약기본(ez_or.or_hanat_rsv_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrHanatRsvBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 하나투어예약코드(hanat_rsv_cd) not null
     */
    private String hanatRsvCd;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 대표하나투어상품코드(rps_hanat_gds_cd)
     */
    private String rpsHanatGdsCd;

    /**
     * 판매하나투어상품코드(sell_hanat_gds_cd)
     */
    private String sellHanatGdsCd;

    /**
     * 하나투어예약상태코드(hanat_rsv_st_cd)
     */
    private String hanatRsvStCd;

    /**
     * 전체예약금액(all_rsv_amt) not null
     */
    private BigDecimal allRsvAmt;

    /**
     * 주문요청메모(ord_req_memo)
     */
    private String ordReqMemo;

    /**
     * CS관리자ID(cs_mgr_id)
     */
    private String csMgrId;

    /**
     * CS관리자메모(cs_mgr_memo)
     */
    private String csMgrMemo;

    /**
     * 출발일자(dptr_dt)
     */
    private String dptrDt;

    /**
     * 도착일자(arvl_dt)
     */
    private String arvlDt;

    /**
     * 선할인율(prdc_rt)
     */
    private Double prdcRt;

    /**
     * 하나투어예약취소사유코드(hanat_rsv_cncl_rsn_cd)
     */
    private String hanatRsvCnclRsnCd;

    /**
     * 취소사유내용(cncl_rsn_cntn)
     */
    private String cnclRsnCntn;

    /**
     * 결제노출여부(pymt_exps_yn) not null
     */
    private String pymtExpsYn;

    /**
     * 정산대상금액(stl_obj_amt) not null
     */
    private BigDecimal stlObjAmt;

    /**
     * 정산적용여부(stl_aply_yn) not null
     */
    private String stlAplyYn;

    /**
     * 정산적용일시(stl_aply_dtm)
     */
    private String stlAplyDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
