package com.ezwelesp.batch.aasample.test.chunk.processor;

import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.aasample.test.domain.Test;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@StepScope
public class ChunkSampleItemProcessor implements ItemProcessor<Test, Test> {
    private final String ntcTitl;

    public ChunkSampleItemProcessor(@Value("#{jobParameters['ntcTitl']}") String ntcTitl) {
        log.debug("ChunkSampleItemProcessor===============");
        this.ntcTitl = ntcTitl;
    }

    @Override
    public Test process(Test item) throws Exception {
        log.debug("process=====================");
        item.setNtcTitl(item.getNtcTitl());
//        if(item.getName().equals(userName)) {
//            item.setId(999);
//            item.setName("CHUNK");
//        }
        return item;
    }

}
