package com.ezwelesp.batch.hims.order.delivery.job;

import com.ezwelesp.batch.hims.order.delivery.tasklet.DeliveryDelayTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class DeliveryDelayTaskletJobConfig {
    private final CommonJobListener commonJobListener;
    private final DeliveryDelayTasklet deliveryDelayTasklet;

    @Bean("BA_HIOR00107")
    public Job deliveryDelayJob(JobRepository jobRepository, @Qualifier("BA_HIOR00107_STEP") Step deliveryDelayStep) {
        return new JobBuilder("BA_HIOR00107", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(deliveryDelayStep)
                .build();
    }

    @Bean("BA_HIOR00107_STEP")
    public Step deliveryDelayStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00107_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(deliveryDelayTasklet, transactionManager)
                .build();
    }
}
