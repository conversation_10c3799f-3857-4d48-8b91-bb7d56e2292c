package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.tasklet.BsicInfOrdLoanDdBatLoanTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatLoanJobConfig {
    private final CommonJobListener commonJobListener;
    private final BsicInfOrdLoanDdBatLoanTasklet bsicInfOrdLoanDdBatLoanTasklet;

    /**
     * 채권 데이터 처리 일배치
     * 주기 : TODO 매일 오전 3시 0분 0초 (AS-IS 기준)
     * 설명 : 채권 데이터 처리 일배치
     * 
     * @param jobRepository
     * @param bsicInfOrdLoanDdBatLoanTaskleStep
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatLoanJob")
    Job bsicInfOrdLoanDdBatLoanJob(
            JobRepository jobRepository,
            Step bsicInfOrdLoanDdBatLoanTaskleStep
    ) {
        return new JobBuilder("bsicInfOrdLoanDdBatLoanJob", jobRepository)
                .listener(commonJobListener)
                .start(bsicInfOrdLoanDdBatLoanTaskleStep) // 채권 데이터 처리 프로시저 호출 Step
                .build();
    }
    
    /**
     * 채권원장 생성 프로시저 호출
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatLoanTaskleStep")
    Step bsicInfOrdLoanDdBatLoanTaskleStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("bsicInfOrdLoanDdBatLoanTaskleStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(bsicInfOrdLoanDdBatLoanTasklet, transactionManager)
                .build();
    }
}