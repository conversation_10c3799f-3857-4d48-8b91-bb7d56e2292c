package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 주문쿠폰상세(ez_or.or_ord_cpn_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrOrdCpnDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문쿠폰번호(ord_cpn_no) not null
     */
    private Long ordCpnNo;

    /**
     * 쿠폰번호(cpn_no) not null
     */
    private String cpnNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq)
     */
    private Long ordGdsSeq;

    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * 사용자쿠폰번호(usr_cpn_no) not null
     */
    private Long usrCpnNo;

    /**
     * 쿠폰사용취소여부(cpn_use_cncl_yn) not null
     */
    private String cpnUseCnclYn;

    /**
     * 쿠폰사용금액(cpn_use_amt) not null
     */
    private BigDecimal cpnUseAmt;

    /**
     * 쿠폰사용상품건수(cpn_use_gds_cnt) not null
     */
    private Integer cpnUseGdsCnt;

    /**
     * 쿠폰사용자조건코드(cpn_usr_cond_cd)
     */
    private String cpnUsrCondCd;

    /**
     * 쿠폰할인종류코드(cpn_dc_knd_cd)
     */
    private String cpnDcKndCd;

    /**
     * 쿠폰종류코드(cpn_knd_cd)
     */
    private String cpnKndCd;

    /**
     * 상품코드(gds_cd)
     */
    private String gdsCd;

    /**
     * 고객사코드(clnt_cd)
     */
    private String clntCd;

    /**
     * 쿠폰명(cpn_nm)
     */
    private String cpnNm;

    /**
     * 사용일자(use_dt) not null
     */
    private String useDt;

    /**
     * 취소일자(cncl_dt)
     */
    private String cnclDt;

    /**
     * 쿠폰할인율(cpn_dc_rt) not null
     */
    private Double cpnDcRt;

    /**
     * 쿠폰할인금액(cpn_dc_amt) not null
     */
    private BigDecimal cpnDcAmt;

    /**
     * 쿠폰최대할인금액(cpn_max_dc_amt) not null
     */
    private BigDecimal cpnMaxDcAmt;

    /**
     * 쿠폰사용가능최소구매금액(cpn_use_poss_min_buy_amt) not null
     */
    private BigDecimal cpnUsePossMinBuyAmt;

    /**
     * 동일상품사용가능수(same_gds_use_poss_cnt) not null
     */
    private Integer sameGdsUsePossCnt;

    /**
     * 중복할인가능여부(dup_dc_poss_yn) not null
     */
    private String dupDcPossYn;

    /**
     * 옵션가격포함여부(opt_prc_incl_yn) not null
     */
    private String optPrcInclYn;

    /**
     * 협력사코드(csp_cd)
     */
    private String cspCd;

    /**
     * 협력사부담비율(csp_budn_rt) not null
     */
    private Double cspBudnRt;

    /**
     * 협력사부담금액(csp_budn_amt) not null
     */
    private BigDecimal cspBudnAmt;

    /**
     * 고객사부담비율(clnt_budn_rt) not null
     */
    private Double clntBudnRt;

    /**
     * 고객사부담금액(clnt_budn_amt) not null
     */
    private BigDecimal clntBudnAmt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
