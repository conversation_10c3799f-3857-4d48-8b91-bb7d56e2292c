package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 하나투어예약변경로그(ez_or.or_hanat_rsv_chg_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrHanatRsvChgGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 로그등록일시(log_reg_dtm) not null
     */
    private String logRegDtm;

    /**
     * 하나투어예약코드(hanat_rsv_cd) not null
     */
    private String hanatRsvCd;

    /**
     * 하나투어여행객코드(hanat_trvlr_cd) not null
     */
    private String hanatTrvlrCd;

    /**
     * 대표하나투어상품코드(rps_hanat_gds_cd)
     */
    private String rpsHanatGdsCd;

    /**
     * 판매하나투어상품코드(sell_hanat_gds_cd)
     */
    private String sellHanatGdsCd;

    /**
     * 하나투어예약상태코드(hanat_rsv_st_cd)
     */
    private String hanatRsvStCd;

    /**
     * 결제노출여부(pymt_exps_yn) not null
     */
    private String pymtExpsYn;

    /**
     * CS관리자ID(cs_mgr_id)
     */
    private String csMgrId;

    /**
     * CS관리자메모(cs_mgr_memo)
     */
    private String csMgrMemo;

    /**
     * 예약취소여부(rsv_cncl_yn) not null
     */
    private String rsvCnclYn;

    /**
     * 하나투어예약단계코드(hanat_rsv_stp_cd)
     */
    private String hanatRsvStpCd;

    /**
     * 주문요청메모(ord_req_memo)
     */
    private String ordReqMemo;

    /**
     * 주문자명(ordr_nm)
     */
    private String ordrNm;

    /**
     * 주문자영문성씨명(ordr_enlst_nm)
     */
    private String ordrEnlstNm;

    /**
     * 주문자영문이름명(ordr_enm_nm)
     */
    private String ordrEnmNm;

    /**
     * 주문자생년월일일자(ordr_bymd_dt)
     */
    private String ordrBymdDt;

    /**
     * 성별코드(gndr_cd)
     */
    private String gndrCd;

    /**
     * 여행상품나이구분코드(trip_gds_age_div_cd)
     */
    private String tripGdsAgeDivCd;

    /**
     * 수수료율(cms_rt)
     */
    private Double cmsRt;

    /**
     * 전체상품금액(all_gds_amt) not null
     */
    private BigDecimal allGdsAmt;

    /**
     * 제세공과금금액(txpc_amt) not null
     */
    private BigDecimal txpcAmt;

    /**
     * 유류할증금액(oil_excg_amt) not null
     */
    private BigDecimal oilExcgAmt;

    /**
     * 기타변경금액(etc_chg_amt) not null
     */
    private BigDecimal etcChgAmt;

    /**
     * 주문자전화번호(ordr_telno)
     */
    private String ordrTelno;

    /**
     * 주문자이메일주소(ordr_eml_adr)
     */
    private String ordrEmlAdr;

    /**
     * 취소일시(cncl_dtm)
     */
    private String cnclDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
