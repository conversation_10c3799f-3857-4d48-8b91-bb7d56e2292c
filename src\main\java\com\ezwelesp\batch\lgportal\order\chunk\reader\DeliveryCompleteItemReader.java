package com.ezwelesp.batch.lgportal.order.chunk.reader;

import com.ezwelesp.batch.lgportal.entity.ApiBatchDlvrEntity;
import com.ezwelesp.batch.lgportal.order.mapper.query.ApiBatchQueryMapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@StepScope
public class DeliveryCompleteItemReader extends MyBatisPagingItemReader<ApiBatchDlvrEntity> {

    public DeliveryCompleteItemReader(@Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory sqlSessionFactory) {

        this.setName(this.getName());
        this.setSqlSessionFactory(sqlSessionFactory);
        this.setQueryId(ApiBatchQueryMapper.class.getName() + ".selectDeliveryCompleteList");
    }
}
