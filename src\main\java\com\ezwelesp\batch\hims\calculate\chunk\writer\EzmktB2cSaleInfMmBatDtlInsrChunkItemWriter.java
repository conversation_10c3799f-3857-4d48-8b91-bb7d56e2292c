package com.ezwelesp.batch.hims.calculate.chunk.writer;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisBatchItemWriter;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.calculate.domain.dto.EzmktB2cSaleInfMmBatDto;
import com.ezwelesp.batch.hims.calculate.mapper.command.EzmktB2cSaleInfMmBatCommandMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@StepScope
public class EzmktB2cSaleInfMmBatDtlInsrChunkItemWriter extends MyBatisBatchItemWriter<EzmktB2cSaleInfMmBatDto> {
    public EzmktB2cSaleInfMmBatDtlInsrChunkItemWriter(@Qualifier("primaryCommandSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        log.debug("{}","EzmktB2cSaleInfMmBatChunkItemWriter01 start :");
        this.setSqlSessionFactory(sqlSessionFactory);
        this.setStatementId(EzmktB2cSaleInfMmBatCommandMapper.class.getName() + ".insertCaMktB2cStlD");
        log.debug("{}","EzmktB2cSaleInfMmBatChunkItemWriter01 end :");
    }
}