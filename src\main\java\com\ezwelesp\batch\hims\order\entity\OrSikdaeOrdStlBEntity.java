package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 식권대장주문정산기본(ez_or.or_sikdae_ord_stl_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrSikdaeOrdStlBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 정산년월(stl_ym) not null
     */
    private String stlYm;

    /**
     * 복지몰메뉴코드(hezo_menu_cd) not null
     */
    private String hezoMenuCd;

    /**
     * 주문금액(ord_amt) not null
     */
    private BigDecimal ordAmt;

    /**
     * 정산금액(stl_amt) not null
     */
    private BigDecimal stlAmt;

    /**
     * 상계금액(stof_amt) not null
     */
    private BigDecimal stofAmt;

    /**
     * 취소금액(cncl_amt) not null
     */
    private BigDecimal cnclAmt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
