package com.ezwelesp.batch.hims.calculate.chunk.processor;

import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.calculate.domain.dto.EzmktB2cSaleInfMmBatDto;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@StepScope
public class EzmktB2cSaleInfMmBatDtlUpdChunkItemProcessor implements ItemProcessor<EzmktB2cSaleInfMmBatDto, EzmktB2cSaleInfMmBatDto> {

    @Override
    public EzmktB2cSaleInfMmBatDto process(EzmktB2cSaleInfMmBatDto item) throws Exception {
        log.debug("{}","EzmktB2cSaleInfMmBatChunkItemProcessor02 start :");
        EzmktB2cSaleInfMmBatDto newItem = item.toBuilder()
                .build();
        log.debug("{}","EzmktB2cSaleInfMmBatChunkItemProcessor02 end :");
        return newItem;
    }
}