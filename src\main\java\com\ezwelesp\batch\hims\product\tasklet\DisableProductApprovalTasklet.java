package com.ezwelesp.batch.hims.product.tasklet;


import com.ezwelesp.batch.hims.product.mapper.command.ProductManagementCommandMapper;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class DisableProductApprovalTasklet implements Tasklet {
    private final ProductManagementCommandMapper productManagementCommandMapper;

    @Override
    public RepeatStatus execute(
            @NonNull StepContribution contribution
            , @NonNull ChunkContext chunkContext
    ) {
        productManagementCommandMapper.updateDisableProductApproval();

        return RepeatStatus.FINISHED;
    }
}

