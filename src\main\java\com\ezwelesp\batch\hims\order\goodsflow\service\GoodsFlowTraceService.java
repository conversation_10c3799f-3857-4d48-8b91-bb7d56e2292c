package com.ezwelesp.batch.hims.order.goodsflow.service;

import com.ezwelesp.batch.hims.order.entity.DlGoodsfDlvIntlBEntity;
import com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowDeliveryFinishDto;
import com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowSendTraceRequestDto;
import com.ezwelesp.batch.hims.order.goodsflow.domain.response.ReceiveTraceResultResponse;

import java.util.List;

public interface GoodsFlowTraceService {

    /**
     * 배송추적 인터페이스 대상건 조회 (ez_or.dl_goodsf_dlv_intl_b)
     */
    List<DlGoodsfDlvIntlBEntity> getDeliveryTraceTargetList();

    /**
     * 배송추적 대상건 등록 (ez_or.dl_goodsf_dlv_intl_b -> ez_if.gikimimast, ez_if.gikimidetail)
     */
    void insertDeliveryTraceTraget(DlGoodsfDlvIntlBEntity dlGoodsfDlvIntlBEntity);

    /**
     * 배송추적 인터페이스 결과 조회 (ez_if.gikimiresult)
     */
    List<GoodsFlowDeliveryFinishDto> getDeliveryFinishList();

    /**
     * 배송추적 인터페이스 결과 처리
     */
    void updateDeliveryFinish(GoodsFlowDeliveryFinishDto deliveryFinishDto);

    /**
     * 굿스플로 배송추적 API 연동 대상 조회 (ez_if.gikimimast)
     */
    List<GoodsFlowSendTraceRequestDto> getSendTraceRequestTargetForApi();

    /**
     * 굿스플로 배송추적 API 연동
     */
    void sendTraceRequestByApi(List<GoodsFlowSendTraceRequestDto> param);

    /**
     * 굿스플로 배송결과수신 API 연동
     */
    ReceiveTraceResultResponse getReceiveTraceResultByApi();

    /**
     * 굿스플로 배송결과수신 결과 등록
     */
    void insertReceiveTraceResultByApi(List<ReceiveTraceResultResponse.Item> param);

    void deleteGikimimastRecordBeforeOneYear();
    void deleteGikimiresultRecordBeforeOneYear();
}
