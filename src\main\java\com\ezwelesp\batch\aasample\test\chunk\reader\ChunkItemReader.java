package com.ezwelesp.batch.aasample.test.chunk.reader;

import java.util.Map;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.aasample.test.domain.Test;

@Component
@StepScope
public class ChunkItemReader extends MyBatisPagingItemReader<Test> {
    // 1회 read 시 가져올 row 개수
    private final int PAGE_SIZE = 1000;

    public ChunkItemReader(@Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory primaryQuerySqlSessionFactory, @Value("#{jobParameters['ntcId']}") Long ntcId) {
        this.setName("SampleItemReader"); // reader명
        this.setSqlSessionFactory(primaryQuerySqlSessionFactory);
        this.setQueryId("com.ezwelesp.batch.aasample.test.mapper.query.UserQueryMapper.selectNtcTitlByNtcId");
        this.setParameterValues(Map.of("ntcId", ntcId));
        this.setPageSize(PAGE_SIZE);
    }

}
