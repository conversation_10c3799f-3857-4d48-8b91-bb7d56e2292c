package com.ezwelesp.batch.hims.order.receipt.domain;

public class CashReceiptTransferDto {
    public final static int leng = 450;
    public final static int stx = 1;
    public final static int msgType = 4;
    public final static int tsDate = 14;
    public final static int saleDate = 8;
    public final static int termId = 10;
    public final static int issueTp = 1;
    public final static int inputTp = 1;
    public final static int inData = 37;
    public final static int dealAmt = 9;
    public final static int vat = 9;
    public final static int svcFee = 9;
    public final static int saleAmt = 9;
    public final static int coTp = 3;
    public final static int bizNo = 10;
    public final static int ktrcNo = 20;
    public final static int aprvNo = 9;
    public final static int rspCd = 4;
    public final static int rspMsg = 40;
    public final static int cancelCd = 1;
    public final static int oriAprvNo = 9;
    public final static int oriSaleDate = 8;
    public final static int itemNm = 100;
    public final static int custNm = 50;
    public final static int filler1 = 20;
    public final static int filler2 = 20;
    public final static int filler3 = 42;
    public final static int etx = 1;
    public final static int cr = 1;

    public static String stxStr = "STR";
    public static String msgTypeStr = "MST_TYPE";
    public static String tsDateStr = "TS_DATE";
    public static String saleDateStr = "SALE_DATE";
    public static String termIdStr = "TERM_ID";
    public static String issueTpStr = "ISSUE_TP";
    public static String inputTpStr = "INPUT_TP";
    public static String inDataStr = "IN_DATA";
    public static String dealAmtStr = "DEAL_AMT";
    public static String vatStr = "VAT";
    public static String svcFeeStr = "SVC_FEE";
    public static String saleAmtStr = "SALE_AMT";
    public static String coTpStr = "CO_TP";
    public static String bizNoStr = "BIZ_NO";
    public static String ktrcNoStr = "KTRC_NO";
    public static String aprvNoStr = "APRV_NO";
    public static String rspCdStr = "RSP_CD";
    public static String rspMsgStr = "RSP_MSG";
    public static String cancelCdStr = "CANCEL_CD";
    public static String oriAprvNoStr = "ORI_APRV_NO";
    public static String oriSaleDateStr = "ORI_SALE_DATE";
    public static String itemNmStr = "ITEM_NM";
    public static String custNmStr = "CUST_NM";
    public static String filler1Str = "FILLER1";
    public static String filler2Str = "FILLER2";
    public static String filler3Str = "FILLER3";
    public static String etxStr = "ETX";
    public static String crStr = "CR";

    public static byte[] stxByte = new byte[stx];
    public static byte[] msgTypeByte = new byte[msgType];
    public static byte[] tsDateByte = new byte[tsDate];
    public static byte[] saleDateByte = new byte[saleDate];
    public static byte[] termIdByte = new byte[termId];
    public static byte[] issueTpByte = new byte[issueTp];
    public static byte[] inputTpByte =new byte[inputTp];
    public static byte[] inDataByte = new byte[inData];
    public static byte[] dealAmtByte = new byte[dealAmt];
    public static byte[] vatByte = new byte[vat];
    public static byte[] svcFeeByte = new byte[svcFee];
    public static byte[] saleAmtByte = new byte[saleAmt];
    public static byte[] coTpByte = new byte[coTp];
    public static byte[] bizNoByte = new byte[bizNo];
    public static byte[] ktrcNoByte = new byte[ktrcNo];
    public static byte[] aprvNoByte = new byte[aprvNo];
    public static byte[] rspCdByte = new byte[rspCd];
    public static byte[] rspMsgByte = new byte[rspMsg];
    public static byte[] cancelCdByte = new byte[cancelCd];
    public static byte[] oriAprvNoByte = new byte[oriAprvNo];
    public static byte[] oriSaleDateByte = new byte[oriSaleDate];
    public static byte[] itemNmByte = new byte[itemNm];
    public static byte[] custNmByte = new byte[custNm];
    public static byte[] filler1Byte = new byte[filler1];
    public static byte[] filler2Byte = new byte[filler2];
    public static byte[] filler3Byte = new byte[filler3];
    public static byte[] etxByte = new byte[etx];
    public static byte[] crByte = new byte[cr];


    public static int[] arrayId = { stx, msgType, tsDate, saleDate, termId, issueTp, inputTp, inData, dealAmt, vat,
            svcFee, saleAmt, coTp, bizNo, ktrcNo, aprvNo, rspCd, rspMsg, cancelCd, oriAprvNo, oriSaleDate,  itemNm, custNm, filler1, filler2, filler3, etx, cr };

    public static String[] arrayStr = { stxStr, msgTypeStr, tsDateStr, saleDateStr, termIdStr, issueTpStr, inputTpStr, inDataStr, dealAmtStr, vatStr,
            svcFeeStr, saleAmtStr, coTpStr, bizNoStr, ktrcNoStr, aprvNoStr, rspCdStr, rspMsgStr, cancelCdStr, oriAprvNoStr, oriSaleDateStr,  itemNmStr, custNmStr, filler1Str, filler2Str, filler3Str, etxStr, crStr };

    public static byte[] arrayByte[] = { stxByte, msgTypeByte, tsDateByte, saleDateByte, termIdByte, issueTpByte, inputTpByte, inDataByte, dealAmtByte, vatByte,
            svcFeeByte, saleAmtByte, coTpByte, bizNoByte, ktrcNoByte, aprvNoByte, rspCdByte, rspMsgByte, cancelCdByte, oriAprvNoByte, oriSaleDateByte,
            itemNmByte, custNmByte, filler1Byte, filler2Byte, filler3Byte, etxByte, crByte   };

    public static String[] arrayKey = { "stxStr", "msgTypeStr", "tsDateStr", "saleDateStr", "termIdStr", "issueTpStr", "inputTpStr", "inDataStr", "dealAmtStr", "vatStr", "svcFeeStr", "saleAmtStr", "coTpStr",
            "bizNoStr", "ktrcNoStr", "aprvNoStr", "rspCdStr", "rspMsgStr", "cancelCdStr", "oriAprvNoStr", "oriSaleDateStr", "itemNmStr", "custNmStr",
            "filler1Str", "filler2Str", "filler3Str", "etxStr", "crStr"  };
}
