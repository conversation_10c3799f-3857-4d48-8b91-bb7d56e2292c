package com.ezwelesp.batch.lgportal.product.service;


import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgAddProductRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgAddProductTargetVo;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgProductAnnouncementVo;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgProductOptionCombVo;
import com.ezwelesp.batch.lgportal.product.mapper.command.ProductLGPortalCommandMapper;
import com.ezwelesp.batch.lgportal.product.mapper.query.ProductLGPortalQueryMapper;
import com.ezwelesp.framework.apiresponse.BaseEspHttpStatus;
import com.ezwelesp.framework.exception.ServiceException;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.ezwelesp.batch.lgportal.product.service.LgProductUtils.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class LgAddProductService {

    private final ProductLGPortalQueryMapper productLGPortalQueryMapper;
    private final ProductLGPortalCommandMapper productLGPortalCommandMapper;
    private final LgApiClient lgApiClient;

    public void addProduct() {

        try {
            // 전송 상품 조회
            val addProductTargetListRaw = productLGPortalQueryMapper.selectLgAddProductTarget();

            // 이미지 실제 존재 하는 건만 처리
            val addProductTargetList = lgAddProductImageCheck(addProductTargetListRaw);

            val addProductTargetGoodsCodeList = addProductTargetList.stream()
                    .map(LgAddProductTargetVo::getGdsCd)
                    .toList();

            // 상품번호로 옵션정보 조회
            val addProductsOptionList = productLGPortalQueryMapper.selectLgProductOptionByGdsCds(
                    addProductTargetGoodsCodeList
            );

            // List를 gdsCd를 키값으로 가지는 Map으로 변환
            val productsOptionMap = addProductsOptionList.stream()
                    .collect(Collectors.groupingBy(LgProductOptionCombVo::getGdsCd));

            // 상품번호로 고시정보 조회
            val addProductsAnnouncementList = productLGPortalQueryMapper.selectLgProductAnnouncementByGdsCd(
                    addProductTargetGoodsCodeList
            );

            // List를 gdsCd를 키값으로 가지는 Map으로 변환
            val productsAnnouncementMap = addProductsAnnouncementList.stream()
                    .collect(Collectors.groupingBy(LgProductAnnouncementVo::getGdsCd));


            // 상품정보 기준으로 옵션정보 및 고시정보 생성 후 api 통신 -> 통신결과 받아오기
            val addProductResultDtoList = addProductTargetList.stream()
                    .map(targetVo -> {
                        // 해당상품의 옵션정보
                        val productOptions = productsOptionMap.getOrDefault(
                                targetVo.getGdsCd(),
                                Collections.emptyList()
                        );

                        // 해당상품의 고시정보
                        val productAnnouncements = productsAnnouncementMap.getOrDefault(
                                targetVo.getGdsCd(),
                                Collections.emptyList()
                        );

                        return processAddProduct(targetVo, productOptions, productAnnouncements); // 반환 추가
                    }).toList();

            // 최종 결과 로그 출력
            log.info("LGPortal AddProduct Result List: {}", addProductResultDtoList);

        } catch (Exception e) {
            log.error("LGPortal AddProduct Error: {}", e.getMessage(), e);
            throw new ServiceException(BaseEspHttpStatus.INTERNAL_SERVER_ERROR, "addProduct Error: " + e.getMessage());
        }

    }

    private LgProductApiResponseDto processAddProduct(
            @NonNull LgAddProductTargetVo target,
            @NonNull List<LgProductOptionCombVo> productOptions,
            @NonNull List<LgProductAnnouncementVo> productAnnouncements
    ) {
        try {
            //고시정보 필수 값이여서 null 이면 바로 에러 반환
            if (productAnnouncements.isEmpty()) {
                // 한 건이 오류가 나더라도 실패건으로 반환
                // 그래서 debug 로 로깅
                log.debug("productAnnouncements is empty: {}", target);

                return LgProductApiResponseDto.builder()
                        .result("500")
                        .resultMessage("productAnnouncements is empty")
                        .productNo(null)
                        .gdsCd(target.getGdsCd())
                        .build();
            }

            // todo 해당 상품의 콘텐츠 정보 조회 및 셋팅필요
            val productContent = productLGPortalQueryMapper.selectLgProductContentByGdsCd(target.getGdsCd());

            // 상품내역 Lg request 형태로 변경
            val lgAddProductRequestDto = mapToAddProductRequest(target, productOptions, productContent.getGdocCntn());
            // 상품 전송내역 db insert
            productLGPortalCommandMapper.insertApiBatchGoodsBeforeSend(target);

            // 상품 전송 api
            val lgAddProductApiResponse = lgApiClient.sendLgAddProductApi(lgAddProductRequestDto);
            if (isApiFailure(lgAddProductApiResponse)) {
                return handleAddProductApiFailure(
                        lgAddProductApiResponse,
                        "lgAddProductApi Fail"
                );
            }

            // LG포털 상품고시등록 api용 request형태 변환
            val lgProductAnnouncementDto = mapToProductAnnouncement(productAnnouncements);

            // 상품 고시정보 전송 api
            val lgAddProductAnnouncementResult = lgApiClient.sendLgAddProductAnnouncementApi(
                    lgProductAnnouncementDto
            );

            if (isApiFailure(lgAddProductAnnouncementResult)) {
                // 고시정보 저장 실패시 상품정보 삭제
                val lgDeleteProductResult = lgApiClient.sendLgDeleteProductApi(lgAddProductApiResponse);
                if (isApiFailure(lgDeleteProductResult)) {
                    // 상품정보 삭제 실패시
                    log.debug("lgDeleteProductApi Fail: {}", lgDeleteProductResult);
                }
                return handleAddProductApiFailure(
                        lgAddProductAnnouncementResult,
                        "lgAddProductAnnouncementApi Fail"
                );
            }

            // 성공내역 db 업데이트
            productLGPortalCommandMapper.updateApiBatchGoodsAfterSend(lgAddProductApiResponse);

            return lgAddProductApiResponse;

        } catch (Exception e) {
            // 한 건이 오류가 나더라도 익셉션처리하지 않고 실패건으로 반환
            // 그래서 debug 로 로깅
            log.debug("processAddProduct Exception: {} {}", e.getMessage(), e);

            return LgProductApiResponseDto.builder()
                    .result("500")
                    .resultMessage("processAddProduct Exception: " + e.getMessage())
                    .productNo(null)
                    .gdsCd(target.getGdsCd())
                    .build();
        }
    }

    // 상품추가 api 실패시 처리로직
    private LgProductApiResponseDto handleAddProductApiFailure(
            LgProductApiResponseDto apiResult,
            String logMessage
    ) {
        log.debug("{}: {}", logMessage, apiResult);
        val failureResponse = createFailureResponse(apiResult, logMessage);
        // 실패 내역 db 업데이트
        productLGPortalCommandMapper.updateApiBatchGoodsAfterSend(failureResponse);
        return failureResponse;
    }


    private static LgAddProductRequestDto mapToAddProductRequest(
            @NonNull LgAddProductTargetVo target,
            @NonNull List<LgProductOptionCombVo> productOptions,
            @NonNull String productContent
    ) {
        return LgAddProductRequestDto.builder()
                .productMappingCode(target.getGdsCd())
                .standardCategoryNo(target.getLgCtgCd())
                .productCode(target.getGdsCd())
                .productName(target.getGdsNm())
                .brandNo(target.getBrandNo())
                .modelName(target.getGdsMdlNm())
                .madeCompany(target.getMnfcCoNm())
                .madeCountry(target.getOrgnNm())
                .productSummary(target.getGdsSimpDesc())
                .taxYN(target.getTaxnKndCd())
                .consumerPrice(target.getNrmlSellPrc())
                .salesPrice(target.getRealSellPrc())
                .orderMaxCnt("Y".equals(target.getT1BuyPossMaxQtyUseYn()) ? target.getT1BuyPossMaxQty() : 0)
                .sumLimitCnt("Y".equals(target.getPsn1BuyPossMaxQtyUseYn()) ? target.getPsn1BuyPossMaxQty() : 0)
                .contentsDetail(productContent)// todo 아직 확정이 안남.
                .mainUrl("http://org-img.ezwelfare.net/welfare_shop"
                        + target.getImgPath())// todo 이미지 URL 수정 필요 // mainUrl; // 이미지url
                .shippingLoc(target.getShippingLoc())
                .returnLoc(target.getReturnLoc())
                .exchangeLoc(target.getExchangeLoc())
                // 아래 내용들은 옵션정보 셋팅
                .sKUAttrs(getSKUAttrs(productOptions))          // 옵션 헤더명 호출
                .sKUTypeCode(getSKUTypeCode(productOptions))    // 옵션 개수 마다 코드 하드코딩
                .sku(mapToSKUList(
                        target.getGdsOptUseYn(),
                        target.getStckQty(),
                        productOptions))      // 옵션 내역 생성 옵션 존재유무에 따라 동작 없을경우 기본값 셋팅
                .build();
    }

    private static List<LgAddProductTargetVo> lgAddProductImageCheck(
            List<LgAddProductTargetVo> addProductTargetListRaw
    ) {
        // 이미지 존재하지 않는 건 에러 로깅
        val invalidImageProductList = addProductTargetListRaw.stream()
                .filter(target -> !isValidProductWithImage(target.getImgPath()))
                .toList();

        if (!invalidImageProductList.isEmpty()) {
            log.debug("LgAddProductService invalidImageProductList: {}",
                    invalidImageProductList.stream()
                            .map(LgAddProductTargetVo::getGdsCd) // 상품 ID만 출력
                            .toList()
            );
        }

        // 이미지 존재하는 건만 반환
        return addProductTargetListRaw.stream()
                .filter(target -> isValidProductWithImage(target.getImgPath()))
                .toList();
    }

}

