package com.ezwelesp.batch.lgportal.product.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

import java.util.List;

@Alias("LgAddProductRequestDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgAddProductRequestDto {

    private String productMappingCode; // 이지웰상품 코드
    private Integer standardCategoryNo; // 카테고리 번호 (상품의 대분류 카테고리 번호)

    @Builder.Default
    private String productTypeCode = "PRD1081"; // 상품 유형 코드 (PRD1081: 일반상품, PRD1084: 신청상품) 기본값: 일반상품
    @Builder.Default
    private String salesTypeCode = "PRD1071"; // 판매 유형 코드 (PRD1071: 기본) 기본값: 기본
    @Builder.Default
    private String deliveryTypeCode = "VEN4021"; // 배송 유형 코드 (VEN4021: 택배) 기본값: 택배

    private String productCode; // 이지웰상품 코드
    private String productName; // 상품명
    private Integer brandNo; // 브랜드 번호 (개발: 13, 운영: 39) 기본값: 개발 환경 기준 prdBrandListAPI 활용하여 조회 가능
    // todo 나중에 운영 개발 분기처리 필요 애초에 조회하는 VO 만들어줄때 분개 처리 만들고 그걸 set 해주는 방식으로 수정 필요

    private String modelName; // 모델명 (상품 모델 식별에 사용)
    private String madeCompany; // 제조사명 (상품 제조사 정보)
    private String madeCountry; // 원산지명 (상품 원산지 정보)
    private String productSummary; // 상품 요약 (상품 요약 설명)

    @Builder.Default
    private String madeDate = "-"; // 제조일 (고정값: "-")
    @Builder.Default
    private String salesStatusCode = "PRD1022"; // 판매 상태 코드 (PRD1022: 판매 중)  고정값: 판매 중
    @Builder.Default
    private String salesMethodCode = "PRD4011"; // 판매 방식 코드 (PRD4011: 일반)  고정값: 일반
    private String taxYN; // 과세 여부 (Y: 과세, N: 면세 여부 등록)
    @Builder.Default
    private String deliveryDate = ""; // 배송 예정 일자 (날짜 또는 공백 처리)
    @Builder.Default
    private String deliveryDay = ""; // 배송 처리 예상 소요일 (값이 없는 경우 빈 값 유지)
    @Builder.Default
    private String barcode = ""; // 상품에 대한 바코드 정보 (기본값: 공백)
    @Builder.Default
    private String limitedSalesCode = "PRD3021"; // 한정 판매 코드 (PRD3021: 사용안함)  고정값: 사용안함
    @Builder.Default
    private String limitStockCntYN = "Y"; // 재고 수량 제한 여부 (Y: 한정 수량, N: 제한 없음)  고정값: 제한 있음
    @Builder.Default
    private String limitedEndDate = ""; // 한정 판매 종료일 (값이 없으면 공백)
    @Builder.Default
    private Integer orderMinCnt = 1; // 주문 건당 최소 수량  고정값: 1

    private Integer orderMaxCnt; // 1회 주문 최대 수량
    private Integer sumLimitCnt; // 구매자별 누적 최대 주문 수량

    @Builder.Default
    private String displayYN = "Y"; // 전시 여부 (Y: 전시, N: 비전시)  고정값
    @Builder.Default
    private String conditionOpenExposeYN = "Y"; // 반오픈 사이트 가격 미노출 여부  고정값
    @Builder.Default
    private String crawlingCode = "-1"; // 가격 원부 코드 (없을 경우 -1)  고정값
    @Builder.Default
    private String adultSafetyYn = "N"; // 성인 인증 상품 여부 (Y: 성인 인증 필요, N: 일반)  고정값: 일반
    @Builder.Default
    private String mobileProductYN = "N"; // 모바일 상품 여부 (Y: 모바일 전용, N: 기본)  고정값
    @Builder.Default
    private String giftYN = "N"; // 선물 여부 (Y: 선물 가능, N: 선물 불가능)  고정값
    @Builder.Default
    private String subscriptionYN = "N"; // 정기 배송 여부 (Y: 정기 배송, N: 일반)  고정값
    @Builder.Default
    private String voucherYN = "N"; // 지류 상품권 여부 (Y: 지류 상품권, N: 일반)  고정값
    @Builder.Default
    private String bundleDeliveryYN = "Y"; // 묶음 배송 여부 (Y: 가능, N: 불가능)  고정값
    @Builder.Default
    private String massOrderYN = "N"; // 대량 구매 여부 (Y: 가능, N: 불가능)  고정값
    @Builder.Default
    private String estimateYN = "N"; // 맞춤 견적 여부 (Y: 맞춤 견적, N: 일반)  고정값
    @Builder.Default
    private String overseasYN = "N"; // 해외 직구 여부 (Y: 가능, N: 불가능)  고정값
    @Builder.Default
    private String noticeTypeCode = "INFO111"; // 공지사항 유형 코드 (INFO111: 공지사항 없음)  고정값
    @Builder.Default
    private String noticeImageUrl = ""; // 공지사항 이미지 경로 (기본값: 공백)
    @Builder.Default
    private String resaleYN = "Y"; // 재판매 가능 여부 (Y: 재판매 가능, N: 불가능)  고정값
    @Builder.Default
    private String parallelImportYN = "N"; // 병행 수입 여부 (Y: 병행 수입, N: 일반)  고정값
    @Builder.Default
    private String exchangeName = ""; // 교환처명 (기본값: 공백)

    private Long consumerPrice; // 소비자 가격
    private Long salesPrice; // 판매 가격

    @Builder.Default
    private Long vendorPrice = 0L; // 공급가 (기본값: 0)

    private Integer shippingLoc; // 상품 출고지 식별 번호
    private Integer exchangeLoc; // 교환지 식별 번호
    private Integer returnLoc; // 반품지 식별 번호

    @Builder.Default
    private String addDeliveryCost = ""; // 도서 산간 배송비 추가 금액 (기본값: 공백)
    @Builder.Default
    private String displayContents = ""; // 도서 산간 표시 내용 (기본값: 공백)
    @Builder.Default
    private String searchWords = ""; // 검색 키워드 (기본값: 공백)
    private String contentsDetail; // 상품 설명
    @Builder.Default
    private String contentsExp = "상세 페이지 참조"; // 기본 배송 및 교환/반품 설명  고정값
    @Builder.Default
    private String isbn = ""; // 국제표준도서번호 (기본값: 공백)
    @Builder.Default
    private String publishDate = ""; // 출판일 (기본값: 공백)
    @Builder.Default
    private String closureDate = ""; // 폐간일 (기본값: 공백)
    @Builder.Default
    private String mainImageType = "I"; // 메인 이미지 유형 (I: 이미지, L: 동영상 링크)

    private String mainUrl; // 대표 상품 이미지 URL


    private String sKUTypeCode; // SKU 유형 코드 (옵션 유형 구분)
    private String sKUAttrs; // SKU 속성 (옵션 정보가 포함됨)

    private List<LgProductSKURequestDto> sku; //SKU 내역
}
