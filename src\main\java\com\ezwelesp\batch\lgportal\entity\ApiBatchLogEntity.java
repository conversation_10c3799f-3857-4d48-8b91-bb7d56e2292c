package com.ezwelesp.batch.lgportal.entity;

import java.io.Serializable;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * [인터페이스] LG API 주문 로그
 * api_batch_log
 */
@Jacksonized
@Getter
@SuperBuilder
public class ApiBatchLogEntity implements Serializable {
    /**
     * 고객사 주문번호
     */
    private String clientOrderNum;

    /**
     * 전송 키
     */
    private String sendKey;

    /**
     * 배송전표항목번호
     */
    private Long deliveryItem;

    /**
     * 배송전표번호
     */
    private String deliveryNo;

    /**
     * 리턴 DATA정보
     */
    private String returnData;

    /**
     * 리턴 코드
     */
    private String returnCode;

    /**
     * 리턴 메시지
     */
    private String returnMessage;

    /**
     * 등록일자
     */
    private String regDt;

    /**
     * 등록자ID
     */
    private String regId;

    private static final long serialVersionUID = 1L;
}
