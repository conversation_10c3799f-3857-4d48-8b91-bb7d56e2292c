package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 정기구독신청기본(ez_or.or_sto_apl_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrStoAplBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 정기구독번호(sto_no) not null
     */
    private String stoNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 정기구독상태코드(sto_st_cd) not null
     */
    private String stoStCd;

    /**
     * 정기구독사용차수코드(sto_use_nos_cd) not null
     */
    private String stoUseNosCd;

    /**
     * 정기구독결제주기코드(sto_pymt_cycl_cd) not null
     */
    private String stoPymtCyclCd;

    /**
     * 결제수단코드(pymt_mns_cd) not null
     */
    private String pymtMnsCd;

    /**
     * 정기구독대표결제수단코드(sto_rps_pymt_mns_cd) not null
     */
    private String stoRpsPymtMnsCd;

    /**
     * 정기구독주문차수(sto_ord_nos) not null
     */
    private Long stoOrdNos;

    /**
     * 채널코드(ch_cd) not null
     */
    private String chCd;

    /**
     * 다음결제예약일자(nxt_pymt_rsv_dt)
     */
    private String nxtPymtRsvDt;

    /**
     * 정기구독결제재시도최대건수(sto_pymt_rtry_max_cnt) not null
     */
    private Integer stoPymtRtryMaxCnt;

    /**
     * 정기구독시작일자(sto_strt_dt) not null
     */
    private String stoStrtDt;

    /**
     * 정기구독종료일자(sto_end_dt) not null
     */
    private String stoEndDt;

    /**
     * 정기구독중도해지일시(sto_mdwy_tmnn_dtm)
     */
    private String stoMdwyTmnnDtm;

    /**
     * 정기구독중도해지사유코드(sto_mdwy_tmnn_rsn_cd)
     */
    private String stoMdwyTmnnRsnCd;

    /**
     * 상품코드(gds_cd) not null
     */
    private String gdsCd;

    /**
     * 쿠폰번호(cpn_no)
     */
    private String cpnNo;

    /**
     * 사용자쿠폰번호(usr_cpn_no)
     */
    private Long usrCpnNo;

    /**
     * 주문상품수량(ord_gds_qty) not null
     */
    private Integer ordGdsQty;

    /**
     * 배송비용(dlv_exp) not null
     */
    private BigDecimal dlvExp;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * 정기구독약관동의일자(sto_tnc_agr_dt) not null
     */
    private String stoTncAgrDt;

    /**
     * 정기구독결제승인동의일자(sto_pymt_apv_agr_dt)
     */
    private String stoPymtApvAgrDt;

    /**
     * 정기구독가격변경유지동의일자(sto_prc_chg_keep_agr_dt)
     */
    private String stoPrcChgKeepAgrDt;

    /**
     * 주문자명(ordr_nm) not null
     */
    private String ordrNm;

    /**
     * 주문자모바일전화번호(ordr_mbl_telno) not null
     */
    private String ordrMblTelno;

    /**
     * 수신자이메일주소(rcvr_eml_adr) not null
     */
    private String rcvrEmlAdr;

    /**
     * 수신자명(rcvr_nm) not null
     */
    private String rcvrNm;

    /**
     * 수신자모바일전화번호(rcvr_mbl_telno) not null
     */
    private String rcvrMblTelno;

    /**
     * 수신자우편번호(rcvr_zipcd)
     */
    private String rcvrZipcd;

    /**
     * 수신자기본주소(rcvr_bas_adr)
     */
    private String rcvrBasAdr;

    /**
     * 수신자상세주소(rcvr_dtl_adr)
     */
    private String rcvrDtlAdr;

    /**
     * 배송요청내용(dlv_req_cntn)
     */
    private String dlvReqCntn;

    /**
     * 난수발송주체코드(rdno_snd_magn_cd)
     */
    private String rdnoSndMagnCd;

    /**
     * 문자메시지발송종류코드(tmsg_snd_knd_cd)
     */
    private String tmsgSndKndCd;

    /**
     * 주문발송메시지코드(ord_snd_msg_cd)
     */
    private String ordSndMsgCd;

    /**
     * 발송메시지내용(snd_msg_cntn)
     */
    private String sndMsgCntn;

    /**
     * 발송요청내용(snd_req_cntn)
     */
    private String sndReqCntn;

    /**
     * 정기구독현금영수증발행시점코드(sto_csrc_pblc_pntm_cd)
     */
    private String stoCsrcPblcPntmCd;

    /**
     * 현금영수증발행종류코드(csrc_pblc_knd_cd)
     */
    private String csrcPblcKndCd;

    /**
     * 현금영수증발행번호(csrc_pblc_no)
     */
    private String csrcPblcNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
