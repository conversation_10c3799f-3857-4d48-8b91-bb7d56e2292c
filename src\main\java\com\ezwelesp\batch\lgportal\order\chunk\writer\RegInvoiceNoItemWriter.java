package com.ezwelesp.batch.lgportal.order.chunk.writer;

import com.ezwelesp.batch.lgportal.entity.ApiBatchDlvrEntity;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.RegInvoiceNoReqDto;
import com.ezwelesp.batch.lgportal.order.mapper.command.ApiBatchCommandMapper;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.ezwelesp.framework.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 배송완료 처리
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order
 * @since 2025.03.05
 */
@Component
public class RegInvoiceNoItemWriter implements ItemWriter<ApiBatchDlvrEntity> {

    @Value("${lgportal.server.host}")
    private String serverHost;

    @Value("${lgportal.server.timeout}")
    private int timeout;

    @Value("${lgportal.api-key}")
    private String apiKey;



    private final SqlSessionTemplate sqlSessionTemplate;

    /**
     * Instantiates a new Delivery complete item writer.
     *
     * @param sqlSessionFactory the sql session factory
     */
    public RegInvoiceNoItemWriter(@Qualifier("primaryCommandSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory);
    }

    @Override
    public void write(Chunk<? extends ApiBatchDlvrEntity> chunks) {
        for (ApiBatchDlvrEntity entity : chunks) {
            ApiBatchDlvrEntity dlvrEntity = this.regInvoiceNo(entity);
            this.sqlSessionTemplate.update(ApiBatchCommandMapper.class.getName() + ".updateApiBatchDlvr", dlvrEntity);
        }
    }

    private ApiBatchDlvrEntity regInvoiceNo(ApiBatchDlvrEntity entity) {
        RegInvoiceNoReqDto regInvoiceNoReqDto = new RegInvoiceNoReqDto(this.apiKey);

        regInvoiceNoReqDto.setDeliveryItems(Collections.singletonList(RegInvoiceNoReqDto.DeliveryItem.builder()
                .deliveryCmdNo(Long.parseLong(entity.getDeliveryNo()))
                .invoiceNo(entity.getInvoiceNo())
                .deliveryComCode(entity.getLogisticsNo())
                .build()));

        try {
            AjaxResult result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_SET_ADD_INVOICE_NO, this.timeout, JsonObjectConverter.serialize(regInvoiceNoReqDto));

            if (StringUtils.equals("200", result.get("result").toString())) {
                entity.setSendYn("Y");
            } else {
                entity.setSendYn("F");
            }

            entity.setReturnCode(result.get("result").toString());
            entity.setReturnMessage(result.get("resultMessage").toString());

            return entity;

        } catch (JsonProcessingException e) {
            throw new RuntimeException("LG portal api request failed");
        }
    }
}
