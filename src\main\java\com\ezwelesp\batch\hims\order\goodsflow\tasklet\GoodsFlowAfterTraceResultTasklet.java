package com.ezwelesp.batch.hims.order.goodsflow.tasklet;

import com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowDeliveryFinishDto;
import com.ezwelesp.batch.hims.order.goodsflow.service.GoodsFlowTraceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsFlowAfterTraceResultTasklet implements Tasklet {
    private final GoodsFlowTraceService goodsFlowTraceService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        List<GoodsFlowDeliveryFinishDto> targetList = goodsFlowTraceService.getDeliveryFinishList();

        for (GoodsFlowDeliveryFinishDto target : targetList) {
            try {
                goodsFlowTraceService.updateDeliveryFinish(target);
            } catch (Exception e) {
                log.error("GoodsFlowAfterTraceResultTasklet Failed: {}", e.getMessage(), e);
            }
        }

        log.debug("GoodsFlowAfterTraceResultTasklet finished");

        return RepeatStatus.FINISHED;
    }
}
