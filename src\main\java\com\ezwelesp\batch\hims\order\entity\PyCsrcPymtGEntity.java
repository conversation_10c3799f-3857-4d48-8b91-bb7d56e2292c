package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 현금영수증결제로그(ez_or.py_csrc_pymt_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyCsrcPymtGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 현금영수증결제로그순번(csrc_pymt_log_seq) not null
     */
    private Long csrcPymtLogSeq;

    /**
     * 결제번호(pymt_no) not null
     */
    private String pymtNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 현금영수증발행여부(csrc_pblc_yn) not null
     */
    private String csrcPblcYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
