package com.ezwelesp.batch.hims.calculate.chunk.reader;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.calculate.domain.dto.EzmktB2cSaleInfMmBatDto;
import com.ezwelesp.batch.hims.calculate.mapper.query.EzmktB2cSaleInfMmBatQueryMapper;
import com.ezwelesp.batch.hims.calculate.util.CalculateBatchUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@StepScope
public class EzmktB2cSaleInfMmBatDtlInsrChunkItemReader extends MyBatisPagingItemReader<EzmktB2cSaleInfMmBatDto> {
    
    private final int PAGE_SIZE = 1000; // 1회 read 시 가져올 row 개수

    public EzmktB2cSaleInfMmBatDtlInsrChunkItemReader(@Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory sqlSessionFactory, @Value("#{jobParameters}") Map<String, Object> jobParameters) {
        log.debug("{}","EzmktB2cSaleInfMmBatChunkItemReader01 start :");
        
        this.setName("EzmktB2cSaleInfMmBatChunkItemReader");
        this.setSqlSessionFactory(sqlSessionFactory);
        this.setQueryId(EzmktB2cSaleInfMmBatQueryMapper.class.getName() + ".selectEzmktB2cSaleInfMmBat01");
        
        // 정산년월 가져오기
        String pStlYm = Optional.ofNullable(jobParameters.get("pStlYm"))
                                .map(Object::toString)
                                .filter(s -> !s.isBlank())
                                .orElse(null);
        
        Map <String, Object> parameter = new HashMap<>();
        if (pStlYm == null) {
            parameter.put("stlYm", CalculateBatchUtil.getCurrentBasicBeforeMonth());
            parameter.put("stlYmd", CalculateBatchUtil.getCurrentBasicBeforeMonthLastDay());
        } else {
            parameter.put("stlYm", pStlYm);
            parameter.put("stlYmd", CalculateBatchUtil.getLastDayOfMonth(pStlYm));
        }
        
        this.setParameterValues(parameter);
        this.setPageSize(PAGE_SIZE);
        
        log.debug("{}","EzmktB2cSaleInfMmBatChunkItemReader01 end :");
    }
}