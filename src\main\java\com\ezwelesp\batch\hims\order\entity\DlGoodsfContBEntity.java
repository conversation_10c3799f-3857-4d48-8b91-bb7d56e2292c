package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 굿스플로계약기본(ez_or.dl_goodsf_cont_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlGoodsfContBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 굿스플로계약순번(goodsf_cont_seq) not null
     */
    private Long goodsfContSeq;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 협력사출고위치번호(csp_obnd_loc_no) not null
     */
    private String cspObndLocNo;

    /**
     * 배송회사코드(dlv_co_cd) not null
     */
    private String dlvCoCd;

    /**
     * 굿스플로승인방법코드(goodsf_apv_mthd_cd) not null
     */
    private String goodsfApvMthdCd;

    /**
     * 협력사사업자등록번호(csp_bzrn) not null
     */
    private String cspBzrn;

    /**
     * 굿스플로계약번호(goodsf_cont_no) not null
     */
    private String goodsfContNo;

    /**
     * 협력사굿스플로계약번호(csp_goodsf_cont_no)
     */
    private String cspGoodsfContNo;

    /**
     * 협력사명(csp_nm) not null
     */
    private String cspNm;

    /**
     * 대표자명(rpsr_nm)
     */
    private String rpsrNm;

    /**
     * 협력사전화번호1(csp_telno1)
     */
    private String cspTelno1;

    /**
     * 협력사전화번호2(csp_telno2)
     */
    private String cspTelno2;

    /**
     * 협력사이메일주소(csp_eml_adr)
     */
    private String cspEmlAdr;

    /**
     * 출고위치명(obnd_loc_nm) not null
     */
    private String obndLocNm;

    /**
     * 출고위치우편번호(obnd_loc_zipcd) not null
     */
    private String obndLocZipcd;

    /**
     * 출고위치기본주소(obnd_loc_bas_adr) not null
     */
    private String obndLocBasAdr;

    /**
     * 출고위치상세주소(obnd_loc_dtl_adr)
     */
    private String obndLocDtlAdr;

    /**
     * 출고위치전화번호1(obnd_loc_telno1) not null
     */
    private String obndLocTelno1;

    /**
     * 출고위치전화번호2(obnd_loc_telno2)
     */
    private String obndLocTelno2;

    /**
     * 선박배송비용(ship_dlv_exp)
     */
    private BigDecimal shipDlvExp;

    /**
     * 항공배송비용(air_dlv_exp)
     */
    private BigDecimal airDlvExp;

    /**
     * 처리완료여부(prcs_cmpt_yn) not null
     */
    private String prcsCmptYn;

    /**
     * 굿스플로승인결과코드(goodsf_apv_rslt_cd)
     */
    private String goodsfApvRsltCd;

    /**
     * 승인일시(apv_dtm)
     */
    private String apvDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
