package com.ezwelesp.batch.hims.order.samsung.job;

import com.ezwelesp.batch.hims.product.tasklet.SamsungOrderGoodsNameUpdateTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 삼성 주문상품명 수정 [BA_HIPO00016]
 */
@Configuration
@RequiredArgsConstructor
public class SamsungOrderGoodsNameUpdateJobConfig {

    private final CommonJobListener commonJobListener;
    private final SamsungOrderGoodsNameUpdateTasklet samsungOrderGoodsNameUpdateTasklet;

    @Bean("BA_HIPO00016")
    public Job samsungOrderGoodsNameUpdateJob(JobRepository jobRepository,
            @Qualifier("BA_HIPO00016_STEP") Step samsungOrderGoodsNameUpdateStep) {
        return new JobBuilder("BA_HIPO00016", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(samsungOrderGoodsNameUpdateStep)
                .build();
    }

    @Bean("BA_HIPO00016_STEP")
    public Step samsungOrderGoodsNameUpdateStep(JobRepository jobRepository,
            @Qualifier("primaryCommandTransactionManager") DataSourceTransactionManager commandTransactionManager) {
        return new StepBuilder("BA_HIPO00016_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(samsungOrderGoodsNameUpdateTasklet, commandTransactionManager)
                .build();
    }
}
