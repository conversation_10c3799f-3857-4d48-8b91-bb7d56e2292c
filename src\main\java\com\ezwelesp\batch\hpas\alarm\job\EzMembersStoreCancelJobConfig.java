package com.ezwelesp.batch.hpas.alarm.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import com.ezwelesp.batch.hpas.alarm.tasklet.EzMemberStoreCancelTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class EzMembersStoreCancelJobConfig {
    private final EzMemberStoreCancelTasklet ezMemberStoreCancelTasklet;
    private final CommonJobListener commonJobListener;

    @Bean("ezMembersStoreCancelJob")
    public Job ezMembersStoreCancelJob(JobRepository jobRepository,
            @Qualifier("ezMembersStoreCancelStep") Step ezMembersStoreCancelStep) {
        return new JobBuilder("EzMembersStoreCancelJob", jobRepository)
                .listener(commonJobListener)
                .start(ezMembersStoreCancelStep)
                .build();
    }

    @Bean("ezMembersStoreCancelStep")
    public Step EzMembersStoreCancelStep(JobRepository jobRepository,
            DataSourceTransactionManager transactionManager) {
        return new StepBuilder("ezMembersStoreCancelStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(ezMemberStoreCancelTasklet, transactionManager)
                .build();

    }
}
