package com.ezwelesp.batch.lgportal.product.tasklet;


import com.ezwelesp.batch.lgportal.product.service.LgEditProductSKUService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgEditProductSKUTasklet implements Tasklet {
    private final LgEditProductSKUService lgEditProductSKUService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) {
        try {
            lgEditProductSKUService.lgEditProductSKU();
        } catch (Exception e) {
            log.error("LgEditProductSKUTasklet Batch Failed: {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        return RepeatStatus.FINISHED;
    }
}

