package com.ezwelesp.batch.hims.calculate.mapper.query;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.ezwelesp.batch.hims.calculate.domain.dto.EzmktB2cSaleInfMmBatDto;

@Mapper
public interface EzmktB2cSaleInfMmBatQueryMapper {
    
    /**
     * 이지웰마켓B2C 상세 데이터 원천 Source
     * @return
     */
    List<EzmktB2cSaleInfMmBatDto> selectEzmktB2cSaleInfMmBat01();
    
    /**
     * 이지웰마켓B2C 상세 데이터 원천 Source (갱신용)
     * @return
     */
    List<EzmktB2cSaleInfMmBatDto> selectEzmktB2cSaleInfMmBat02();
}