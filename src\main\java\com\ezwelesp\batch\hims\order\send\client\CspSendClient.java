package com.ezwelesp.batch.hims.order.send.client;

import com.ezwelesp.framework.exception.ServiceException;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Unmarshaller;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class CspSendClient {
    private final CloseableHttpClient cspSendHttpClient;
    // private final CloseableHttpClient cspSendUnsafeHttpClient; 사용해야하면 생성자주입 방식으로 변경 필요

    public <T> T post(String cspCd, String url, Map<String, String> param, Class<T> responseType) {
        return sendWithHttpClient(cspCd, url, param, responseType, cspSendHttpClient);

        // asis - 30000151 협력사 https 인증우회 (통신에러가 발생하는데 원인을 알 수 없어 인증우회 적용했다고 함)
        // tobe - 우회안하고 테스트 진행 후 문제 생기면 확인 후 처리하기로 함
        // 필요시 cspSendUnsafeHttpClient 소스 적용필요하나 보안문제 발생하므로 확인 필수
//        if ("30000151".equals(cspCd) && url.startsWith("https")) {
//            return sendWithHttpClient(cspCd, url, param, responseType, cspSendUnsafeHttpClient);
//        } else {
//            return sendWithHttpClient(cspCd, url, param, responseType, cspSendHttpClient);
//        }
    }

    private <T> T sendWithHttpClient(String cspCd, String url, Map<String, String> param, Class<T> responseType, CloseableHttpClient client) {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(10000)
                .setSocketTimeout(10000)
                .build();

        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestConfig);
        // 헤더 세팅
        this.setHeaders(cspCd, httpPost);

        // 협력사별 추가 파라미터 세팅
        this.setAddParameter(cspCd, param);

        List<NameValuePair> params = new ArrayList<>();
        for (Map.Entry<String, String> entry : param.entrySet()) {
            params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }

        httpPost.setEntity(new UrlEncodedFormEntity(params, StandardCharsets.UTF_8));

        byte[] xmlBytes;
        try (CloseableHttpResponse response = client.execute(httpPost);
             InputStream content = response.getEntity().getContent()) {

            xmlBytes = content.readAllBytes();
            log.info("협력사 url: {}, xmlString: {}", url, new String(xmlBytes, StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("sendWithHttpClient error: {}", e.getMessage());
            throw new ServiceException();
        }

        try (InputStream xmlInputStream = new ByteArrayInputStream(xmlBytes)) {
            JAXBContext jaxbContext = JAXBContext.newInstance(responseType);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

            return (T) unmarshaller.unmarshal(xmlInputStream);
        } catch (Exception e) {
            log.error("sendWithHttpClient 변환 에러: {}", e.getMessage());
            throw new ServiceException();
        }
    }

    private void setAddParameter(String cspCd, Map<String, String> param) {
        if ("30000103".equals(cspCd)) { // 쿠프마케팅 전용 파라미터
            param.put("LINK_SITE_CD", "30000103");
            param.put("OTSD_LINK_CD", param.get("cspGdsCd"));
        }
    }
    
    private void setHeaders(String cspCd, HttpPost httpPost) {
        httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded; charset=UTF-8");
        httpPost.setHeader(HttpHeaders.ACCEPT, "application/xml");
        httpPost.setHeader("user", "ezwel");
        // httpPost.setHeader("X-System-type", "hpas"); // 로컬 테스트용

        // TODO yml 처리
        if (cspCd.equals("30000142")) {
            //12cm 요청에 의한 파라미터 추가
            httpPost.setHeader("Authorization", "Basic NjAxNzA5NGNiMzg0NGNlNmI0ZmYwODVjYWJjN2UzZWQ6OFA4SEVTSlg1ODlLeGFNN1YxTDk1Nk5tdG03MHVsWFNPb2k2ejYySWRZZA==");
        } else if (cspCd.equals("30000153")) {
            //야놀자 엑세스 토큰
            // httpPost.setHeader("Access-Token", "494D52637394884E8E0D0E6ACB429F62"); //real
            httpPost.setHeader("Access-Token", "8DFEC0886A13161DC59C24F55F07F6BE"); //dev
        }
    }
}
