package com.ezwelesp.batch.hims.order.pgcompare.hpointpay.domain;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder(toBuilder = true)
@Getter
@RequiredArgsConstructor
public class TossTransactionDto implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String mId;
	private String transactionKey;
	private String paymentKey;
	private String orderId;
	private String currency;
	private String method;
	private String customerKey;
	private String useEscrow;
	private BigDecimal amount;
	private String status;
	private String transactionAt;
}
