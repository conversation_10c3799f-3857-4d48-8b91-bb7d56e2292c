package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 클레임상태변경내역(ez_or.cl_clm_st_chg_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClClmStChgLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 클레임상태변경내역순번(clm_st_chg_lst_seq) not null
     */
    private Long clmStChgLstSeq;

    /**
     * 변경이전클레임상태코드(chg_bef_clm_st_cd) not null
     */
    private String chgBefClmStCd;

    /**
     * 변경이후클레임상태코드(chg_aft_clm_st_cd) not null
     */
    private String chgAftClmStCd;

    /**
     * 클레임환불상태코드(clm_rfnd_st_cd)
     */
    private String clmRfndStCd;

    /**
     * 클레임처리관리자ID(clm_prcs_mgr_id)
     */
    private String clmPrcsMgrId;

    /**
     * 클레임취소요청승인상태코드(clm_cncl_req_apv_st_cd)
     */
    private String clmCnclReqApvStCd;

    /**
     * 클레임취소요청처리관리자ID(clm_cncl_req_prcs_mgr_id)
     */
    private String clmCnclReqPrcsMgrId;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
