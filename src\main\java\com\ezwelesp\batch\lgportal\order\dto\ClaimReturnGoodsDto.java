package com.ezwelesp.batch.lgportal.order.dto;

import com.ezwelesp.framework.web.dto.BaseEntity;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.File;
import java.io.Serial;
import java.util.List;

/**
 * 반품접수 상품
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto.ClaimReturnGoodsDto
 * @since 2025.02.27
 */
@Getter
@SuperBuilder
public class ClaimReturnGoodsDto {

    // 주문상품순번
    private long ordGdsSeq;

    // 클레임요청수량
    private int clmGdsQty;

    // 클레임귀책사유주체코드
    private String clmAtbrMagnCd;

    // 클레임사유코드
    private String clmRsnCd;

    // 클레임사유내용
    private String clmRsnCntn;

    // 클레임 첨부파일
    private List<File> claimAttachFileList;

    // LG포털API배송번호
    private String lgpApiDlvNo;

    // LG포털API배송상세번호
    private String lgpApiDlvDtlNo;
}
