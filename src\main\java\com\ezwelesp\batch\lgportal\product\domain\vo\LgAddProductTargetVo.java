package com.ezwelesp.batch.lgportal.product.domain.vo;

import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("AddProductTargetVo")
@Getter
@SuperBuilder
public class LgAddProductTargetVo {

    /*메인 테이블 조회해서 사용하는 영역*/
    private String gdsCd; //productMappingCode; // 이지웰상품코드
    private String gdsNm;//productName;  // 상품명
    private Integer lgCtgCd;// standardCategoryNo; // LG 카테고리 번호 dev test(15017) / 운영 test(24429) 그외 상품 카테고리번호
    private String gdsMdlNm; // modelName; // 모델명
    private String mnfcCoNm;//madeCompany; // 제조사명
    private String orgnNm;// madeCountry; // 원산지명
    private String gdsSimpDesc;//productSummary; // 상품 요약
    private String taxnKndCd;//taxYN; // 과세 여부
    private Long nrmlSellPrc;//consumerPrice; // 소비자 가격
    private Long realSellPrc;//salesPrice; // 판매 가격


    private String t1BuyPossMaxQtyUseYn;// 1회 주문 최대 수량 사용여부
    private Integer t1BuyPossMaxQty;//orderMaxCnt; // 1회 주문 최대 수량
    private String psn1BuyPossMaxQtyUseYn;// 1회 주문 최대 수량 사용여부
    private Integer psn1BuyPossMaxQty;//sumLimitCnt; // 구매자별 누적 최대 주문 수량

    private String imgPath; // 이미지url

    private String gdsOptUseYn; // 옵션사용Yn

    private Integer stckQty; // 재고수량

    private Integer sellQty; // 판매수량

    private String cspCd; // 협력사코드

    private String cspObndLocNo; // 협력사출고위치번호

    /* 출고지 테이블 조회 내역 */
    private Integer shippingLoc; //출고지
    private Integer returnLoc; //반품지
    private Integer exchangeLoc; //교환지

    @Builder.Default
    private String targetDiv = "LGCNS";



    public Integer getBrandNo() {
        return 13;
        // todo 나중에  브랜드코드(브랜드없음) (개발:13, 운영:39) 로 운영하고 test 분개
    }


}


