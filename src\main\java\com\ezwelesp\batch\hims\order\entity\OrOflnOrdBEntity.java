package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 오프라인주문기본(ez_or.or_ofln_ord_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrOflnOrdBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 오프라인주문순번(ofln_ord_seq) not null
     */
    private Long oflnOrdSeq;

    /**
     * 오프라인주문상품순번(ofln_ord_gds_seq) not null
     */
    private Long oflnOrdGdsSeq;

    /**
     * 원본오프라인주문순번(orgl_ofln_ord_seq)
     */
    private Long orglOflnOrdSeq;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 상품명(gds_nm) not null
     */
    private String gdsNm;

    /**
     * 오프라인주문등록관리자ID(ofln_ord_reg_mgr_id) not null
     */
    private String oflnOrdRegMgrId;

    /**
     * 현금영수증발행여부(csrc_pblc_yn) not null
     */
    private String csrcPblcYn;

    /**
     * 과세종류코드(taxn_knd_cd) not null
     */
    private String taxnKndCd;

    /**
     * 주문상품판매가격(ord_gds_sell_prc) not null
     */
    private BigDecimal ordGdsSellPrc;

    /**
     * 주문상품정상가격(ord_gds_nrml_prc) not null
     */
    private BigDecimal ordGdsNrmlPrc;

    /**
     * 주문상품매입가격(ord_gds_pchs_prc) not null
     */
    private BigDecimal ordGdsPchsPrc;

    /**
     * 수수료율(cms_rt) not null
     */
    private Double cmsRt;

    /**
     * 주문일시(ord_dtm) not null
     */
    private String ordDtm;

    /**
     * 주문자명(ordr_nm)
     */
    private String ordrNm;

    /**
     * 주문상품수량(ord_gds_qty) not null
     */
    private Integer ordGdsQty;

    /**
     * 주문금액(ord_amt) not null
     */
    private BigDecimal ordAmt;

    /**
     * 오프라인주문상태코드(ofln_ord_st_cd) not null
     */
    private String oflnOrdStCd;

    /**
     * 취소일시(cncl_dtm)
     */
    private String cnclDtm;

    /**
     * 삭제여부(del_yn) not null
     */
    private String delYn;

    /**
     * 장바구니사용여부(bskt_use_yn)
     */
    private String bsktUseYn;

    /**
     * 수수료매출여부(cms_sale_yn)
     */
    private String cmsSaleYn;

    /**
     * 매입매출구분코드(pchs_sale_div_cd)
     */
    private String pchsSaleDivCd;

    /**
     * 고객사공통부서코드(cc_dept_cd)
     */
    private String ccDeptCd;

    /**
     * 협력사현금영수증발행여부(csp_csrc_pblc_yn) not null
     */
    private String cspCsrcPblcYn;

    /**
     * 오프라인결제선택코드(ofln_pymt_choc_cd)
     */
    private String oflnPymtChocCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
