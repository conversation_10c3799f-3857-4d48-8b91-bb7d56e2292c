package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.tasklet.BsicInfOrdLoanDdBatOflnTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatOflnJobConfig {
    private final CommonJobListener commonJobListener;
    private final BsicInfOrdLoanDdBatOflnTasklet bsicInfOrdLoanDdBatOflnTasklet;

    /**
     * 오프라인 카드 정산 데이터 이관 일배치
     * 주기 : TODO 매일 오전 3시 0분 0초 (AS-IS 기준)
     * 설명 : 오프라인 카드 정산 데이터 이관 일배치
     * 
     * @param jobRepository
     * @param bsicInfOrdLoanDdBatOflnTaskletStep
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatOflnJob")
    Job bsicInfOrdLoanDdBatOflnJob(
            JobRepository jobRepository,
            Step bsicInfOrdLoanDdBatOflnTaskletStep
    ) {
        return new JobBuilder("bsicInfOrdLoanDdBatOflnJob", jobRepository)
                .listener(commonJobListener)
                .start(bsicInfOrdLoanDdBatOflnTaskletStep) // 오프라인 카드 정산 데이터 이관 프로시저 호출 Step TODO 개발필요
                .build();
    }

    /**
     * 오프라인 카드 정산 데이터 이관 프로시저 호출
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatOflnTaskletStep")
    Step bsicInfOrdLoanDdBatOflnTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("bsicInfOrdLoanDdBatOflnTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(bsicInfOrdLoanDdBatOflnTasklet, transactionManager)
                .build();
    }
}