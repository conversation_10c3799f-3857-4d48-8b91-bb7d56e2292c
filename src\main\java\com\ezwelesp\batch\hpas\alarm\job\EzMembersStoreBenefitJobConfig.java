package com.ezwelesp.batch.hpas.alarm.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import com.ezwelesp.batch.hpas.alarm.tasklet.EzMemberStoreBenefitTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class EzMembersStoreBenefitJobConfig {
    private final EzMemberStoreBenefitTasklet ezMemberStoreBenefitTasklet;
    private final CommonJobListener commonJobListener;

    @Bean("ezMembersStoreBenefitJob")
    public Job ezMembersStoreBenefitJob(JobRepository jobRepository,
            @Qualifier("ezMembersStoreBenefitStep") Step ezMembersStoreBenefitStep) {
        return new JobBuilder("EzMembersStoreBenefitJob", jobRepository)
                .listener(commonJobListener)
                .start(ezMembersStoreBenefitStep)
                .build();
    }

    @Bean("ezMembersStoreBenefitStep")
    public Step EzMembersStoreBenefitStep(JobRepository jobRepository,
            DataSourceTransactionManager transactionManager) {
        return new StepBuilder("ezMembersStoreBenefitStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(ezMemberStoreBenefitTasklet, transactionManager)
                .build();

    }
}
