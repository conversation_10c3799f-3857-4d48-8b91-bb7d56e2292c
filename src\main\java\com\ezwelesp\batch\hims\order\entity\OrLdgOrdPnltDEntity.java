package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * [2차]숙박주문페널티상세(ez_or.or_ldg_ord_pnlt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrLdgOrdPnltDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 숙박주문페널티순번(ldg_ord_pnlt_seq) not null
     */
    private Long ldgOrdPnltSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 숙박주문지원차수(ldg_ord_afe_nos) not null
     */
    private Long ldgOrdAfeNos;

    /**
     * 숙박제한적용년도(ldg_limt_aply_yyyy)
     */
    private String ldgLimtAplyYyyy;

    /**
     * 숙박제한종료일자(ldg_limt_end_dt)
     */
    private String ldgLimtEndDt;

    /**
     * 페널티차감점수(pnlt_sbtr_scor)
     */
    private Integer pnltSbtrScor;

    /**
     * 페널티사유내용(pnlt_rsn_cntn)
     */
    private String pnltRsnCntn;

    /**
     * 숙박페널티유형코드(ldg_pnlt_typ_cd)
     */
    private String ldgPnltTypCd;

    /**
     * 숙박주문페널티개월수코드(ldg_ord_pnlt_mcnt_cd)
     */
    private String ldgOrdPnltMcntCd;

    /**
     * 숙박페널티순번(ldg_pnlt_seq)
     */
    private Long ldgPnltSeq;

    /**
     * 사용여부(use_yn) not null
     */
    private String useYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
