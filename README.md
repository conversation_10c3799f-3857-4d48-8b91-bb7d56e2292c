### 실행 옵션
1. job.name : 실행하고자 하는 job Bean명
2. job parameter가 있을 시, 콤마로 구분하여 parameter 명시
```angular2html
java -jar --job.name=chunkParallelJob --param1=A,param2=B
```

### Job
```
스텝1               스텝2                스텝3
- ItemReader        - ItemReader         - ItemReader
- ItemProcessor  -> - ItemProcessor      - ItemProcessor
- ItemWriter        - ItemWriter         - ItemWriter
```

자바나 XML을 사용해 구성된 배치 잡은, 상태를 수집하고 이전 상태 -> 다음 상태로 전환된다.
개념적으로 스프링 배치 잡은 `상태 기계(State Machine)`에 지나지 않는다.

스프링배치에서 가장 일반적으로 상태를 보여주는 단위는 `스텝`이다.

> ex) 업무 시간 이후에 고객의 은행계좌 처리
>
> - 스텝1 : 다른 시스템에서 수신한 거래 정보 파일 읽어와 DB에 저장
> - 스텝2 : 모든 입금 정보를 계좌에 반영
> - 스텝3 : 모든 출금 정보를 계좌에 반영

### Step
잡을 구성하는 독립된 작업의 단위
Tasklet 기반 스텝과 Chunk 기반 스텝이 있으며, 태스크릿 기반 스텝의 구조가 더 간단하다.


### Tasklet 기반 스텝
Tasklet을 구현하여 사용할 수 있으며, 스텝이 중지될 때까지 excute 메소드가 계속 반복해서 수행된다. 이 때 excute 메소드를 호출할 때마다 독립적인 트랜잭션이 얻어진다.
초기화, 저장 프로시저 실행, 알림 전송 등과 같은 잡에서 일반적으로 사용된다.

### Chunk 기반 스텝
태스크릿 기반 스텝에 비해 구조가 약간 더 복잡하며, 아이템 기반의 처리에 사용한다.
ItemReader / ItemProcesspr / ItemWriter 라는 3개의 주요 부분으로 구성되며, 이 때 ItemProcessor는 필수는 아니다.
ItemReader와 ItemWriter 만으로도 스텝 실행이 가능한데, 이와 같은 스텝은 일반적으로 데이터 마이그레이션 잡에 많이 사용된다.

- Tasklet : 트랜잭션 내에서 로직이 실행될 수 있는 기능을 제공하는 전략(Strategy) 인터페이스
- ItemReader : 스텝 내에서 입력을 제공하는 전략 인터페이스
- ItemProcessor : 스텝 내에서 제공받은 개별 아이템에 업무 로직, 검증 등을 적용하는 역할을 수행하는 인터페이스
- ItemWriter : 스텝 내에서 아이템을 저장하는 전략 인터페이스

스프링의 잡 구조화 방법이 갖는 장점 : 각 스텝이 서로 독립적으로 처리될 수 있도록 분리되어 있다.
유연성 / 유지보수성 / 확장성 / 신뢰성 확보 가능

### JobRepository
스프링 배치 아키텍처 내에서 공유되는 주요 컴포넌트 가운데 하나.
배치 수행과 관련된 다양한 수치 데이터(시작 시간, 종료 시간, 상태, 읽기/쓰기 횟수 등) 및 잡의 상태를 유지 관리한다.
JobRepository는 일반적으로 관계형 데이터베이스를 사용하며, 스프링 배치 내의 대부분의 주요 컴포넌트가 이를 공유한다.

```
JobLauncher - Job - Step ---- ItemReader
    |_________/______|     -- ItemProcessor
              |            -- ItemWriter
        Job Repository
```

### JobLauncher
`잡을 실행`하는 역할 담당. (Job.excute 메서드 호출)
또한 잡의 재실행 가능여부 확인 / 잡의 실행 방법(현재 스레드에서 수행할지, 스레드 풀을 통해 실행할지 등) / 파라미터 유효성 검증 등의 처리를 수행한다.

잡 실행 -> 해당 잡은 각 스텝 실행 -> 각 스텝이 실행되면 JobRepository는 현재 상태로 갱신된다.
즉 실행된 스텝, 현재 상태, 읽은 아이템 및 처리된 아이템 수 등이 모두 JobRepository에 저장된다.

잡과 스텝의 처리 방식은 매우 유사한데, 잡은 구성된 스텝 목록에 따라 각 스텝을 실행한다.
여러 아이템으로 이뤄진 청크의 처리가 스텝 내에서 완료될 때 -> 스프링 배치는 JobRepository 내에 있는 JobExecution 또는 StepException을 현재 상태로 갱신한다.
스텝은 ItemReader가 읽은 아이템의 목록을 따라간다. 스텝이 각 청크를 처리할 때 마다 JobRepository 내 StepExecution의 스탭 상태가 업데이트 된다.
현재까지의 커밋 수, 시작 및 종료시간, 기다 다른 정보 등이 JobRepository에 저장되며, 잡 또는 스텝이 완료되면 JobExecution 또는 StepExecution이 최종 상태로 업데이트 된다.

### JobInstance
스프링 배치 잡의 논리적인 실행(Logical Execution)을 의미.

ex) 거래명세서를 생성하는 statementGenerator 라는 잡이 다른 파라미터로 실행될 때마다 새로운 JobInstance가 생성된다.

### Jobexecution
스프링 배치 잡의 실제 실행(execution)을 의미.
잡을 구동할 때마다 새로운 JobExecution을 얻게 된다. 그러나 잡 실행에 실패한 이후 다시 실행하면, 해당 실행은 앞선 것과 동일한 논리적 실행(파라미터가 동일함)이므로 새로운 JobInstance를 얻을 수 없으나, 두 번째 실제 실행을 추적하기 위한 새로운 JobExecution을 얻을 것이다.
즉 JobInstance는 여러 개의 JobExecution을 가질 수 있다.

### StepExecution
스텝의 실제 실행(execution)을 의미.
그러나 StepInstance 라는 개념은 존재하지 않는다.
JobExecution은 여러 개의 StepExecution과 연관된다.

### CQRS 패턴 적용
사용방법
application.yml를 작성한다.
```yml
spring:
  datasource:
    batch:
      hikari:
        jdbc-url: ***************************************
        username: postgres
        password: pass
        driver-class-name: org.postgresql.Driver
    esp:
      hikari:
        command:
          driver-class-name: org.postgresql.Driver
          jdbc-url: **********************************************
          username: // Database username
          password: // Database password
          max-lifetime: 175
          connection-timeout: 30000
          maximum-pool-size: 10
        query:
          driver-class-name: org.postgresql.Driver
          jdbc-url: **********************************************
          username: // Database username
          password: // Database password
          max-lifetime: 175
          connection-timeout: 30000
          maximum-pool-size: 10
 
mybatis:
  mapper-locations: classpath:mappers/**/**/**/**.xml
  config-location: classpath:mybatis-config.xml
```

DatsSourceConfig.java 추가한다.
DataSourceConfig.java
```java
@Configuration
public class DataSourceConfig {
    @Bean
    @ConditionalOnMissingBean(BatchDataSourceScriptDatabaseInitializer.class)
    BatchDataSourceScriptDatabaseInitializer batchDataSourceScriptDatabaseInitializer(DataSource dataSource,
                                                                                      @BatchDataSource ObjectProvider<DataSource> batchDataSource,
                                                                                      BatchProperties properties) {
        return new BatchDataSourceScriptDatabaseInitializer(batchDataSource.getIfAvailable(() -> dataSource), properties.getJdbc());
    }
 
    @BatchDataSource
    @Bean
    @Primary
    @Qualifier("batchDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.batch.hikari")
    public DataSource batchDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }
 
    @Bean("commandDataSource")
    @Qualifier("commandDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.esp.hikari.command")
    public DataSource commandDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }
 
    @Bean("queryDataSource")
    @Qualifier("queryDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.esp.hikari.query")
    public DataSource queryDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }
     
    @Bean
    @Primary
    @Qualifier("batchTransactionManager")
    public DataSourceTransactionManager batchTransactionManager(@Qualifier("batchDataSource") DataSource batchDataSource) {
        return new DataSourceTransactionManager(batchDataSource);
    }
 
    @Bean
    @Qualifier("commandTransactionManager")
    public DataSourceTransactionManager commandTransactionManager(@Qualifier("commandDataSource") DataSource commandDataSource) {
        return new DataSourceTransactionManager(commandDataSource);
    }
 
    @Bean
    @Qualifier("queryTransactionManager")
    public DataSourceTransactionManager queryTransactionManager(@Qualifier("queryDataSource") DataSource queryDataSource) {
        return new DataSourceTransactionManager(queryDataSource);
    }
}
```
조회(Query)와 명령(Command)의 구분으로 mapper를 나눈다.
```
esp-common-batch
└── sample.test
           └── mapper
                      ├── command
                       |         └── UserCommandMapper.java
                      └── query
                                └── UserQueryMapper.java

```
Query(조회) - mapper→query 패키지를 생성한다.
CUD(등록, 수정, 삭제) - mapper→ command 패키지를 생성한다.
