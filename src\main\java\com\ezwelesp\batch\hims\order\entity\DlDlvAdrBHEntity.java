package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 배송주소기본이력(ez_or.dl_dlv_adr_b_h)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlDlvAdrBHEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송주소이력순번(dlv_adr_his_seq) not null
     */
    private Long dlvAdrHisSeq;

    /**
     * 배송주소번호(dlv_adr_no) not null
     */
    private Long dlvAdrNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * 주문배송내용J(ord_dlv_cntnj)
     */
    private String ordDlvCntnj;

    /**
     * 이전데이터등록일시(bef_data_reg_dtm) not null
     */
    private String befDataRegDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
