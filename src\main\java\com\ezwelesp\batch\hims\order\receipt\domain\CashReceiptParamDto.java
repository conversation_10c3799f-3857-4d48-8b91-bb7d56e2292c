package com.ezwelesp.batch.hims.order.receipt.domain;


import lombok.Data;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serial;
import java.io.Serializable;

@Jacksonized
@Data
@SuperBuilder
public class CashReceiptParamDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;


    private String ordNo;
    private String clmNo;
    private String prevClmNo;
}
