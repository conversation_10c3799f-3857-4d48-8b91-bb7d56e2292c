package com.ezwelesp.batch.lgportal.order.chunk.writer;

import com.ezwelesp.batch.lgportal.entity.ApiBatchDlvrEntity;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.DeliveryAcceptReqDto;
import com.ezwelesp.batch.lgportal.order.mapper.command.ApiBatchCommandMapper;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 배송완료 처리
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order
 * @since 2025.03.05
 */
@Component
public class DeliveryCompleteItemWriter implements ItemWriter<ApiBatchDlvrEntity> {

    @Value("${lgportal.server.host}")
    private String serverHost;

    @Value("${lgportal.server.timeout}")
    private int timeout;

    @Value("${lgportal.api-key}")
    private String apiKey;

    private final SqlSessionTemplate sqlSessionTemplate;

    /**
     * Instantiates a new Delivery complete item writer.
     *
     * @param sqlSessionFactory the sql session factory
     */
    public DeliveryCompleteItemWriter(@Qualifier("primaryCommandSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory);
    }

    @Override
    public void write(Chunk<? extends ApiBatchDlvrEntity> chunks) {
        chunks.forEach(apiBatchDlvrEntity ->
                this.sqlSessionTemplate.insert(ApiBatchCommandMapper.class.getName() + ".insertApiBatchDlvr",
                        this.deliveryComplete(apiBatchDlvrEntity)));
    }

    private ApiBatchDlvrEntity deliveryComplete(ApiBatchDlvrEntity entity) {
        DeliveryAcceptReqDto deliveryAcceptReqDto = new DeliveryAcceptReqDto(this.apiKey);

        deliveryAcceptReqDto.setDeliveryItems(Arrays.asList(DeliveryAcceptReqDto.DeliveryItem.builder().deliveryCmdNo(
                Long.parseLong(entity.getDeliveryNo())).build()));

        try {
            AjaxResult result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_DELIVERY_COMPLETE_URL, this.timeout, JsonObjectConverter.serialize(deliveryAcceptReqDto));

            entity.setSendKey(1010);

            if (StringUtils.equals("200", result.get("result").toString())) {
                entity.setSendYn("Y");
                entity.setReturnCode(result.get("result").toString());
                entity.setReturnMessage(result.get("resultMessage").toString());
            } else {
                entity.setSendYn("F");
                entity.setReturnCode(result.get("result").toString());
                entity.setReturnMessage(result.get("resultMessage").toString());
            }
            return entity;

        } catch (JsonProcessingException e) {
            throw new RuntimeException("LG portal api request failed");
        }
    }
}
