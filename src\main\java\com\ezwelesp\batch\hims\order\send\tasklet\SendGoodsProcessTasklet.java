package com.ezwelesp.batch.hims.order.send.tasklet;

import com.ezwelesp.batch.hims.order.send.domain.SendGoodsBaseDto;
import com.ezwelesp.batch.hims.order.send.service.SendGoodsProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class SendGoodsProcessTasklet implements Tasklet {
    private final SendGoodsProcessService sendGoodsProcessService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            int repeat = 0;
            for(;;) {
                repeat++;
                List<SendGoodsBaseDto> targetList = sendGoodsProcessService.selectSendGoodsTarget();
                if (!targetList.isEmpty() && repeat < 999) {
                    for (SendGoodsBaseDto target : targetList) {
                        if ("01".equals(target.getRdnoSndMagnCd())) {
                            sendGoodsProcessService.sendEzwel(target); // 이지웰 발송
                        } else if ("02".equals(target.getRdnoSndMagnCd())) {
                            sendGoodsProcessService.sendCsp(target); // 협력사 발송
                        }
                    }
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("SendGoodsProcessTasklet failed", e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.info("SendGoodsProcessTasklet finished");
        return RepeatStatus.FINISHED;
    }
}
