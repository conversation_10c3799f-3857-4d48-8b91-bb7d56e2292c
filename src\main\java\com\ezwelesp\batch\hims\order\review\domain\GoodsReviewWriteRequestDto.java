package com.ezwelesp.batch.hims.order.review.domain;

import com.ezwelesp.batch.hims.order.entity.OrOrdGdsDEntity;
import lombok.Data;

import java.util.List;

@Data
public class GoodsReviewWriteRequestDto {
    private String userKey; // 유저키
    private String dvcInntCval; // 디바이스고유문자값
    private String dvcTknCval; // 디바이스토큰문자값
    private String mblOsDivCd; // 모바일운영체제구분코드
    private String pushInfmRcvAgrYn; // PUSH알림수신동의여부
    private String mblLastLginDtm; // 모바일최종로그인일시
    private String rgmGdPushRcvAgrYn; // 제도안내PUSH수신동의여부
    private String bnftInfPushRcvAgrYn; // 혜택정보PUSH수신동의여부
    private String pushInfmWayCd; // PUSH알림방식코드
    private String mblTelno; // 모바일전화번호
    private String appVerNm; // 앱버전명
    private String appPckgNm; // 앱패키지명
    private String affNm; // ?
    private List<OrOrdGdsDEntity> orderGoodsList; // 주문상품목록

    /* 이지체크인 용 */
    private Long ezwelOrderNum;
    private String clientCd;

}
