package com.ezwelesp.batch.lgportal.util;

import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.request.SyncRequest;
import com.ezwelesp.framework.utils.request.dto.Header;
import org.springframework.http.MediaType;
import org.springframework.http.client.JdkClientHttpRequestFactory;
import org.springframework.web.client.RestClient;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import java.net.http.HttpClient;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;


/**
 * The type Rest client util.
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.util
 * @since 2025.03.04
 */
public class RestClientUtil {

    /**
     * Request LG api ajax result.
     *
     * @param url the url
     * @param timeout the timeout
     * @param requestBody the requerst body
     * @return the ajax result
     */
    public static AjaxResult requestKyoboApi(String url, int timeout, Object requestBody) {

        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, null, null);
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            try (SSLSocket socket = (SSLSocket)sslSocketFactory.createSocket()) {
                socket.setEnabledProtocols(new String[]{"TLSv1", "TLSv1.1", "TLSv1.2"});
            }

            HttpClient httpClient = HttpClient.newBuilder()
                    .sslContext(sslContext)
                    .connectTimeout(Duration.ofSeconds(timeout))
                    .build();

            RestClient restClient = RestClient.builder()
                    .requestFactory(new JdkClientHttpRequestFactory(httpClient))
                    .build();

            List<Header> headerList = new ArrayList<>();
            headerList.add(new Header("Content-Type", "application/json; charset=UTF-8"));

            SyncRequest syncRequest = new SyncRequest(restClient, LgConstants.EMPTY_STR);

            return syncRequest.post(url, headerList, null, requestBody);

        } catch (Exception e) {
            throw new RuntimeException("LG portal api request failed");
        }

    }

    public static AjaxResult requestApi(String url, int timeout, Object requestBody) {

        try {
            HttpClient httpClient = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofSeconds(timeout))
                    .build();

            RestClient restClient = RestClient.builder()
                    .requestFactory(new JdkClientHttpRequestFactory(httpClient))
                    .build();

            return new SyncRequest(restClient, LgConstants.EMPTY_STR).post(
                    url,
                    List.of(new Header("Content-Type", MediaType.APPLICATION_JSON_VALUE)),
                    null,
                    requestBody);

        } catch (Exception e) {
            throw new RuntimeException("LG portal api request failed");
        }

    }

}
