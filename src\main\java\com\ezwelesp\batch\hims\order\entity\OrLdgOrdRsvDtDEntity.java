package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * [2차]숙박주문예약일자상세(ez_or.or_ldg_ord_rsv_dt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrLdgOrdRsvDtDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 숙박주문지원차수(ldg_ord_afe_nos) not null
     */
    private Long ldgOrdAfeNos;

    /**
     * 예약요청일자(rsv_req_dt) not null
     */
    private String rsvReqDt;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 객실코드(room_cd) not null
     */
    private String roomCd;

    /**
     * 숙박시설지점코드(accm_br_cd) not null
     */
    private String accmBrCd;

    /**
     * 숙박그룹코드(ldg_grp_cd) not null
     */
    private String ldgGrpCd;

    /**
     * 시즌구분코드(seas_div_cd) not null
     */
    private String seasDivCd;

    /**
     * 숙박그룹유형코드(ldg_grp_typ_cd)
     */
    private String ldgGrpTypCd;

    /**
     * 객실수(room_cnt) not null
     */
    private Integer roomCnt;

    /**
     * 숙박주문당첨여부(ldg_ord_win_yn) not null
     */
    private String ldgOrdWinYn;

    /**
     * 결제이전관리자선등록여부(pymt_bef_mgr_prrg_yn) not null
     */
    private String pymtBefMgrPrrgYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
