package com.ezwelesp.batch.hims.order.send.service.impl;

import com.ezwelesp.batch.hims.order.config.EcpnConstants;
import com.ezwelesp.batch.hims.order.entity.external.CoEcpnCspApiDEntity;
import com.ezwelesp.batch.hims.order.send.client.CspSendClient;
import com.ezwelesp.batch.hims.order.send.domain.CspSendDto;
import com.ezwelesp.batch.hims.order.send.domain.CspSendResponse;
import com.ezwelesp.batch.hims.order.send.domain.SendResponse;
import com.ezwelesp.batch.hims.order.send.mapper.query.SendManageQueryMapper;
import com.ezwelesp.batch.hims.order.send.service.CspSendManageService;
import com.ezwelesp.framework.apiresponse.BaseEspHttpStatus;
import com.ezwelesp.framework.exception.ServiceException;
import com.ezwelesp.framework.utils.CryptoUtils;
import com.ezwelesp.framework.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class CspSendManageServiceImpl implements CspSendManageService {
    private final SendManageQueryMapper sendManageQueryMapper;
    private final CspSendClient cspSendClient;

    @Override
    public SendResponse send(CspSendDto param) {
        // 유효성 체크
        this.validate(param);

        CoEcpnCspApiDEntity cspApi = getCspApiInfo(param.getCspCd());

        if ("ezwel".equals(cspApi.getUseApiNm())) {
            return this.cspCpnIssue(cspApi, param);
        } else {
            // 현재는 모두 ezwel 만 사용함
            throw new ServiceException();
        }
    }

    /**
     * 이지웰 -> 협력사 쿠폰발행 요청
     */
    private SendResponse cspCpnIssue(CoEcpnCspApiDEntity cspApi, CspSendDto param) {
        String callBack = EcpnConstants.getCallBackNum(param.getClntCd());
        String refundMsg = EcpnConstants.getRefundMsg(param.getClntCd());

        // tobe 명세에 rq 항목 암호화는 없음
        Map<String, String> map = new HashMap<>();
        map.put("ecpnTrid", param.getOrdNo() + ":" + param.getOrdSndGdsDtlSeq());
        map.put("cspCd", param.getCspCd());
        map.put("gdsCd", param.getGdsCd()); // goodsCd
        map.put("cspGdsCd", param.getCspGdsCd()); // cspGoodsCd
        map.put("ordNo", param.getOrdNo());
        map.put("sendPNum", callBack);
        map.put("rcvrTelno", param.getRcvrMblTelno());
        map.put("ordrNm", param.getOrdrNm());
        map.put("rcvrNm", param.getRcvrNm());
        map.put("ttl", "e쿠폰 발송안내");
        map.put("refundMsg", refundMsg);
        map.put("addMsg", param.getIntgGdsInfmSndTypCntn());
        map.put("optGdsCombSeq", String.valueOf(param.getOptGdsCombSeq()));
        map.put("apiCallCount", param.getApiCallCount());

        // String url = String.format("%s:%s%s", cspApi.getEcpnApiUrl(), cspApi.getEcpnApiPort(), cspApi.getEcpnSndUrl());
        String url = "http://localhost:8093/hi-order/api/xml";
        CspSendResponse response = cspSendClient.post(param.getCspCd(), url, map, CspSendResponse.class);

        log.info("send: {}", response);
        log.info("ezwel open API 쿠폰발송요청 업체 결과값 끝----------------------- ");

        try { // TODO psKey, iv 처리
            return SendResponse.builder()
                    .success("0000".equals(response.getResult()))
                    .result(CryptoUtils.decryptAES256(response.getResult(), "psKey", "iv"))
                    .message(CryptoUtils.decryptAES256(response.getMessage(), "psKey", "iv"))
                    .ecpnRn(CryptoUtils.decryptAES256(response.getEcpnRn(), "psKey", "iv"))
                    .ecpnCspReqInntNo(CryptoUtils.decryptAES256(response.getEcpnTrid(), "psKey", "iv"))
                    .build();
        } catch (Exception e) {
            log.error("협력사 쿠폰발송 복호화 실패: {}", e.getMessage());
            throw new ServiceException(BaseEspHttpStatus.valueOf("협력사 쿠폰발송 복호화 실패"));
        }
    }

    private CoEcpnCspApiDEntity getCspApiInfo(String cspCd) {
        CoEcpnCspApiDEntity cspApi = sendManageQueryMapper.selectCoEcpnCspApi(cspCd);

        if (cspApi == null) {
            log.error("CSP API 서버 정보가 DB에 없습니다.");
            throw new ServiceException();
        }

        return cspApi;
    }

    private void validate(CspSendDto param) {
        if (StringUtils.isEmpty(param.getCspCd())) {
            throw new ServiceException(BaseEspHttpStatus.valueOf("협력사코드는 필수입니다."));
        }
        if (StringUtils.isEmpty(param.getRcvrMblTelno())) {
            throw new ServiceException(BaseEspHttpStatus.valueOf("수신자전화번호는 필수입니다."));
        }
        if (StringUtils.isEmpty(param.getClntCd())) {
            throw new ServiceException(BaseEspHttpStatus.valueOf("고객사코드는 필수입니다."));
        }
    }
}
