package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 고속버스주문승차권상세(ez_or.or_exbs_ord_got_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrExbsOrdGotDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 제휴사주문번호(asp_ord_no) not null
     */
    private String aspOrdNo;

    /**
     * 고속버스예약구분코드(exbs_rsv_div_cd) not null
     */
    private String exbsRsvDivCd;

    /**
     * 고속버스좌석번호(exbs_seat_no) not null
     */
    private String exbsSeatNo;

    /**
     * 고속버스좌석예약승인번호(exbs_seat_rsv_apv_no) not null
     */
    private String exbsSeatRsvApvNo;

    /**
     * 고속버스승차권상태코드(exbs_got_st_cd) not null
     */
    private String exbsGotStCd;

    /**
     * 고속버스승차권금액(exbs_got_amt) not null
     */
    private BigDecimal exbsGotAmt;

    /**
     * 고속버스승차권발권일자(exbs_got_ifb_dt)
     */
    private String exbsGotIfbDt;

    /**
     * 발권고속버스터미널코드(ifb_exbstm_cd)
     */
    private String ifbExbstmCd;

    /**
     * 고속버스발권창구번호(exbs_ifb_countr_no)
     */
    private String exbsIfbCountrNo;

    /**
     * 고속버스발권번호(exbs_ifb_no)
     */
    private String exbsIfbNo;

    /**
     * 고속버스권종코드(exbs_bntp_cd)
     */
    private String exbsBntpCd;

    /**
     * 고속버스승차권QR코드번호(exbs_got_qrcode_no)
     */
    private String exbsGotQrcodeNo;

    /**
     * 고속버스승차권QR코드처리일자(exbs_got_qrcode_prcs_dt)
     */
    private String exbsGotQrcodePrcsDt;

    /**
     * 클레임요청주체코드(clm_req_magn_cd)
     */
    private String clmReqMagnCd;

    /**
     * 취소일시(cncl_dtm)
     */
    private String cnclDtm;

    /**
     * 주문취소수수료금액(ord_cncl_cms_amt) not null
     */
    private BigDecimal ordCnclCmsAmt;

    /**
     * 클레임귀책사유주체코드(clm_atbr_magn_cd)
     */
    private String clmAtbrMagnCd;

    /**
     * 고속버스주문취소API연동일시(exbs_ord_cncl_api_intl_dtm)
     */
    private String exbsOrdCnclApiIntlDtm;

    /**
     * 고속버스승차권취소상태코드(exbs_got_cncl_st_cd)
     */
    private String exbsGotCnclStCd;

    /**
     * 변경요청관리자ID(chg_req_mgr_id)
     */
    private String chgReqMgrId;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
