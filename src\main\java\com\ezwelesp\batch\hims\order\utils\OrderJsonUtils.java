package com.ezwelesp.batch.hims.order.utils;

import com.ezwelesp.framework.apiresponse.BaseEspHttpStatus;
import com.ezwelesp.framework.exception.ServiceException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @see
 * @since 2025.03.24
 */
@Slf4j
@Component
public class OrderJsonUtils {

    private static ObjectMapper objectMapper;
    private static ObjectMapper cloneMapper;
    private static ObjectMapper snakeObjectMapper;

    OrderJsonUtils() {
        objectMapper = configNotIndentObjectMapper();
        cloneMapper = configCloneObjectMapper();
        snakeObjectMapper = configSnakeMapper();
    }

    private ObjectMapper configNotIndentObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        return mapper;
    }

    private ObjectMapper configCloneObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return mapper;
    }

    private ObjectMapper configSnakeMapper() {
        ObjectMapper mapper = new ObjectMapper();
        // This option increases the response data size.
        mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

        return mapper;
    }

    /**
     * Convert object to json string.
     * <pre>
     *     JsonObjectConverter class 가 있으나 INDENT_OUTPUT true 로 인해
     *     PG 사 호출 시 Indent 처리 되지 않은 String JSON 필요로 인해 생성
     * </pre>
     *
     * @param obj the obj
     * @return the string
     * <AUTHOR>
     * @since 2025.02.25
     */
    public static String convertToJson(Object obj) {
        try {
            if (ObjectUtils.isEmpty(obj)) {
                return "";
            }

            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.error("getConvertToJson Exception stacktrace ", e);
            throw new ServiceException();
        }
    }


    /**
     * Convert Object with snake case
     *
     * @param <T> the type parameter
     * @param serializedString the serialized string
     * @param type the type
     * @return the t
     * @since 2025.03.24
     */
    public static <T> T deserializeSnake(String serializedString, Class<T> type) {
        try {
            JsonNode jsonNode = snakeObjectMapper.readTree(serializedString);

            return snakeObjectMapper.convertValue(jsonNode, type);
        } catch (JsonProcessingException e) {
            throw new ServiceException(BaseEspHttpStatus.FAILED_JSON_PROCESSING);
        }
    }

    /**
     * object가 다른 맴버 변수 복사
     *
     * @param serializedString
     * @param type
     * @param <T>
     * @return
     */
    public static <T> T deserializeClone(String serializedString, Class<T> type) {
        try {
            JsonNode jsonNode = cloneMapper.readTree(serializedString);
            return cloneMapper.convertValue(jsonNode, type);
        } catch (JsonProcessingException e) {
            throw new ServiceException(BaseEspHttpStatus.FAILED_JSON_PROCESSING);
        }
    }

    /**
     * Convert Array with snake case
     *
     * @param <T> the type parameter
     * @param serializedString the serialized string
     * @param type the type
     * @return the list
     * @since 2025.03.24
     */
    public static <T> List<T> deserializeSnakeArray(String serializedString, Class<T> type) {
        try {
            JsonNode jsonNode = snakeObjectMapper.readTree(serializedString);
            List<T> list = new ArrayList<>();

            Iterator<JsonNode> iter = jsonNode.elements();
            while (iter.hasNext()) {
                JsonNode node = iter.next();
                list.add(snakeObjectMapper.convertValue(node, type));
            }
            return list;
        } catch (JsonProcessingException e) {
            throw new ServiceException(BaseEspHttpStatus.FAILED_JSON_PROCESSING);
        }
    }

    /**
     * Convert Array with camel case
     *
     * @param serializedString
     * @param type
     * @param <T>
     * @return
     */
    public static <T> List<T> deserializeCamelArray(String serializedString, Class<T> type) {
        try {
            JsonNode jsonNode = cloneMapper.readTree(serializedString);
            List<T> list = new ArrayList<>();

            Iterator<JsonNode> iter = jsonNode.elements();
            while (iter.hasNext()) {
                JsonNode node = iter.next();
                list.add(cloneMapper.convertValue(node, type));
            }
            return list;
        } catch (JsonProcessingException e) {
            throw new ServiceException(BaseEspHttpStatus.FAILED_JSON_PROCESSING);
        }
    }

    public static void checkJsonWrap(String title, Object logstr) {
        try {
            log.debug("{}=>{}", title, objectMapper.writeValueAsString(logstr));
        } catch (JsonProcessingException e) {
            throw new ServiceException(BaseEspHttpStatus.FAILED_JSON_PROCESSING);
        }
    }

    public static void checkJsonWrap(Object logstr) {
        checkJsonWrap("json", logstr);
    }

}
