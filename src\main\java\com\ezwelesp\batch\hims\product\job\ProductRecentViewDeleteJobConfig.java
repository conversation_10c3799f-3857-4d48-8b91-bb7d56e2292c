package com.ezwelesp.batch.hims.product.job;


import com.ezwelesp.batch.hims.product.tasklet.ProductRecentViewDeleteTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;



/**
 * 최근 본 상품 데이터 삭제(30일)
 */

@Configuration
public class ProductRecentViewDeleteJobConfig {
    private final ProductRecentViewDeleteTasklet productRecentViewDeleteTasklet;

    public ProductRecentViewDeleteJobConfig(
            @Qualifier("productRecentViewDeleteTasklet")
            ProductRecentViewDeleteTasklet productRecentViewDeleteTasklet
    ) {
        this.productRecentViewDeleteTasklet = productRecentViewDeleteTasklet;
    }

    @Bean("BA_HIPO00003")
    public Job productRecentViewDeleteJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPO00003_STEP") Step productRecentViewDeleteStep
    ) {
        return new JobBuilder("BA_HIPO00003", jobRepository)
                .start(productRecentViewDeleteStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }

    // 유형상품의 일반배송 상품 중
    // 판매 중인 판매기간 도래한 상품 판매중지 처리
    @Bean("BA_HIPO00003_STEP")
    public Step productRecentViewDeleteStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager
    ) {
        return new StepBuilder("BA_HIPO00003_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(productRecentViewDeleteTasklet, transactionManager)
                .build();
    }
}
