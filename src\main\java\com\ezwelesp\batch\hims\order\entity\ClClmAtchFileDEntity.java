package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 클레임첨부파일상세(ez_or.cl_clm_atch_file_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClClmAtchFileDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 클레임첨부파일순번(clm_atch_file_seq) not null
     */
    private Long clmAtchFileSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq)
     */
    private Long ordGdsSeq;

    /**
     * 배송상품순번(dlv_gds_seq)
     */
    private Long dlvGdsSeq;

    /**
     * 회수상품순번(wtdw_gds_seq)
     */
    private Long wtdwGdsSeq;

    /**
     * 배송번호(dlv_no)
     */
    private String dlvNo;

    /**
     * 클레임거부신청차수(clm_rfs_apl_nos)
     */
    private Long clmRfsAplNos;

    /**
     * 첨부파일경로(atch_file_path) not null
     */
    private String atchFilePath;

    /**
     * 첨부파일명(atch_file_nm) not null
     */
    private String atchFileNm;

    /**
     * 클레임첨부파일등록유형코드(clm_atch_file_reg_typ_cd)
     */
    private String clmAtchFileRegTypCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
