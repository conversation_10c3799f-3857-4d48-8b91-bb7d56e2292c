package com.ezwelesp.batch.lgportal.order.job;

import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.tasklet.CancelTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class CancelListJobConfig {
    private final String REG_CANCEL_STEP = LgConstants.JobName.JOB_REG_CANCEL + "_STEP";

    private final CancelTasklet cancelTasklet;
    private final CommonJobListener commonJobListener;

    @Bean(LgConstants.JobName.JOB_REG_CANCEL)
    public Job cancelListJob(JobRepository jobRepository, @Qualifier(REG_CANCEL_STEP) Step cancelStep) {
        return new JobBuilder(LgConstants.JobName.JOB_REG_CANCEL, jobRepository)
                .listener(commonJobListener)
                .start(cancelStep)
                .build();
    }

    @Bean(name = REG_CANCEL_STEP)
    public Step cancelStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder(REG_CANCEL_STEP, jobRepository)
                .allowStartIfComplete(true)
                .tasklet(cancelTasklet, transactionManager)
                .build();
    }
}
