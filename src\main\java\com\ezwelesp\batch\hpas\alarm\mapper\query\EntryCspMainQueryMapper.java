package com.ezwelesp.batch.hpas.alarm.mapper.query;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ezwelesp.batch.hpas.alarm.dto.EntryCspMainDto;

@Mapper
public interface EntryCspMainQueryMapper {

    int selectEntryCspMainLoginCount();

    List<EntryCspMainDto> selectEntryCspMainLoginList(int pageInt);

    int selectEntryCspMainGoodsCount();

    List<EntryCspMainDto> selectEntryCspMainGoodsList(int pageInt);
}
