package com.ezwelesp.batch.hims.order.delivery.job;

import com.ezwelesp.batch.hims.order.delivery.tasklet.DeliveryDueDelayTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class DeliveryDueDelayTaskletJobConfig {
    private final CommonJobListener commonJobListener;
    private final DeliveryDueDelayTasklet deliveryDueDelayTasklet;

    @Bean("BA_HIOR00108")
    public Job deliveryDueDelayJob(JobRepository jobRepository, @Qualifier("BA_HIOR00108_STEP") Step deliveryDueDelayStep) {
        return new JobBuilder("BA_HIOR00108", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(deliveryDueDelayStep)
                .build();
    }

    @Bean("BA_HIOR00108_STEP")
    public Step deliveryDueDelayStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00108_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(deliveryDueDelayTasklet, transactionManager)
                .build();
    }
}
