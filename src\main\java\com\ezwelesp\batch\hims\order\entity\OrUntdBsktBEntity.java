package com.ezwelesp.batch.hims.order.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 통합장바구니기본(ez_or.or_untd_bskt_b)
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OrUntdBsktBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 장바구니번호(bskt_no) not null
     */
    private Long bsktNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 주문유형코드(ord_typ_cd)
     */
    private String ordTypCd;

    /**
     * 채널코드(ch_cd)
     */
    private String chCd;

    /**
     * 협력사코드(csp_cd)
     */
    private String cspCd;

    /**
     * 즉시구매대상여부(imdt_buy_obj_yn) not null
     */
    private String imdtBuyObjYn;

    /**
     * 상품코드(gds_cd)
     */
    private String gdsCd;

    /**
     * 협력사상품코드(csp_gds_cd)
     */
    private String cspGdsCd;

    /**
     * 주문상품수량(ord_gds_qty) not null
     */
    private Integer ordGdsQty;

    /**
     * 복지몰표준카테고리코드(hezo_std_ctgr_cd)
     */
    private String hezoStdCtgrCd;

    /**
     * 복지몰메뉴코드(hezo_menu_cd)
     */
    private String hezoMenuCd;

    /**
     * 복지몰번호(hezo_no)
     */
    private Long hezoNo;

    /**
     * 이벤트코드(evnt_cd)
     */
    private String evntCd;

    /**
     * 이벤트코너상세순번(evnt_conr_dtl_seq)
     */
    private Long evntConrDtlSeq;

    /**
     * 배송비용착불대상여부(dlv_exp_arpay_obj_yn) not null
     */
    private String dlvExpArpayObjYn;

    /**
     * 배송예정일자(dlv_due_dt)
     */
    private String dlvDueDt;

    /**
     * 주문상품옵션내용(ord_gds_opt_cntn)
     */
    private String ordGdsOptCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
