package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 협력사배송템플릿기본(ez_co.co_csp_dlv_tmpl_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class CoCspDlvTmplBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 배송템플릿순번(dlv_tmpl_seq) not null
     */
    private Long dlvTmplSeq;

    /**
     * 협력사출고위치번호(csp_obnd_loc_no) not null
     */
    private String cspObndLocNo;

    /**
     * 배송지역그룹코드(dlv_area_grp_cd)
     */
    private String dlvAreaGrpCd;
    /**
     * 배송지역그룹설정코드(dlv_area_grp_stup_cd)
     */
    private String dlvAreaGrpStupCd;

    /**
     * 배송지역그룹배송여부(dlv_area_grp_dlv_yn) not null
     */
    private String dlvAreaGrpDlvYn;

    /**
     * 배송안내순번(dlv_gd_seq)
     */
    private Long dlvGdSeq;

    /**
     * 배송템플릿명(dlv_tmpl_nm) not null
     */
    private String dlvTmplNm;

    /**
     * 상품유형상세코드(gds_typ_dtl_cd)
     */
    private String gdsTypDtlCd;

    /**
     * 배송비용결제방법코드(dlv_exp_pymt_mthd_cd) not null
     */
    private String dlvExpPymtMthdCd;

    /**
     * 반품상품회수대상여부(rtp_gds_wtdw_obj_yn) not null
     */
    private String rtpGdsWtdwObjYn;

    /**
     * 협력사배송비용부담여부(csp_dlv_exp_budn_yn) not null
     */
    private String cspDlvExpBudnYn;

    /**
     * 배송비용(dlv_exp) not null
     */
    @Builder.Default
    private BigDecimal dlvExp = BigDecimal.ZERO;

    /**
     * 교환배송비용(exch_dlv_exp)
     */
    private BigDecimal exchDlvExp;

    /**
     * 반품배송비용(rtp_dlv_exp)
     */
    private BigDecimal rtpDlvExp;

    /**
     * 제주도추가배송비용(jeju_add_dlv_exp)
     */
    private BigDecimal jejuAddDlvExp;

    /**
     * 도서산간추가배송비용(ismt_add_dlv_exp)
     */
    private BigDecimal ismtAddDlvExp;

    /**
     * 식품배송방법설명(food_dlv_mthd_desc)
     */
    private String foodDlvMthdDesc;

    /**
     * 사용여부(use_yn) not null
     */
    private String useYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
