package com.ezwelesp.batch.hcas.tasklet;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hcas.mapper.command.KoditSendSmsCommandMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class KoditSendSmsTasklet implements Tasklet {
    private final KoditSendSmsCommandMapper koditSendSmsCommandMapper;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {        
    	//koditSendSmsCommandMapper.getSendSmsDataList();
        
        return RepeatStatus.FINISHED;
    }
}