package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 클레임거부신청상세(ez_or.cl_clm_rfs_apl_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClClmRfsAplDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 클레임거부신청차수(clm_rfs_apl_nos) not null
     */
    private Long clmRfsAplNos;

    /**
     * 배송번호(dlv_no) not null
     */
    private String dlvNo;

    /**
     * 회수상품순번(wtdw_gds_seq) not null
     */
    private Long wtdwGdsSeq;

    /**
     * 클레임거부종류코드(clm_rfs_knd_cd) not null
     */
    private String clmRfsKndCd;

    /**
     * 클레임거부신청상태코드(clm_rfs_apl_st_cd) not null
     */
    private String clmRfsAplStCd;

    /**
     * 클레임거부신청협력사관리자ID(clm_rfs_apl_csp_mgr_id)
     */
    private String clmRfsAplCspMgrId;

    /**
     * 클레임거부신청일시(clm_rfs_apl_dtm) not null
     */
    private String clmRfsAplDtm;

    /**
     * 클레임거부승인일시(clm_rfs_apv_dtm)
     */
    private String clmRfsApvDtm;

    /**
     * 클레임거부불가처리일시(clm_rfs_ndmt_prcs_dtm)
     */
    private String clmRfsNdmtPrcsDtm;

    /**
     * 클레임거부처리관리자ID(clm_rfs_prcs_mgr_id)
     */
    private String clmRfsPrcsMgrId;

    /**
     * 클레임거부사유내용(clm_rfs_rsn_cntn)
     */
    private String clmRfsRsnCntn;

    /**
     * 클레임보류사유코드(clm_hold_rsn_cd)
     */
    private String clmHoldRsnCd;

    /**
     * 클레임거부사유코드(clm_rfs_rsn_cd)
     */
    private String clmRfsRsnCd;

    /**
     * 클레임거부신청주체코드(clm_rfs_apl_magn_cd)
     */
    private String clmRfsAplMagnCd;

    /**
     * 클레임처리내용(clm_prcs_cntn)
     */
    private String clmPrcsCntn;

    /**
     * 관리자메모(mgr_memo)
     */
    private String mgrMemo;

    /**
     * 해피콜안내완료여부(hcall_gd_cmpt_yn) not null
     */
    private String hcallGdCmptYn;

    /**
     * 클레임거부신청상세번호(clm_rfs_apl_dtl_no) not null
     */
    private Long clmRfsAplDtlNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
