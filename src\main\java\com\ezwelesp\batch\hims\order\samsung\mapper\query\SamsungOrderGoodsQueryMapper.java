package com.ezwelesp.batch.hims.order.samsung.mapper.query;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SamsungOrderGoodsQueryMapper {

    /**
     * 주문기본 - 특수문자가 포함된 주문상품요약명 건수 조회
     */
    int countOrdGdsSmryNmWithSpecialChars40();
    
    int countOrdGdsSmryNmWithSpecialChars41();
    
    int countOrdGdsSmryNmWithSpecialCharsQuot();
    
    int countOrdGdsSmryNmWithSpecialChars39();
    
    int countOrdGdsSmryNmWithSpecialChars35();
    
    int countOrdGdsSmryNmWithSpecialCharsAmp();

    /**
     * 주문상품상세 - 특수문자가 포함된 상품명 건수 조회
     */
    int countGdsNmWithSpecialChars40();
    
    int countGdsNmWithSpecialChars41();
    
    int countGdsNmWithSpecialCharsQuot();
    
    int countGdsNmWithSpecialChars39();
    
    int countGdsNmWithSpecialChars35();
    
    int countGdsNmWithSpecialCharsAmp();

    /**
     * 처리 대상 주문 건수 조회 (최근 2일)
     */
    int countTargetOrders();

    /**
     * 특정 주문번호의 주문상품요약명 조회
     */
    String selectOrdGdsSmryNmByOrdNo(@Param("ordNo") String ordNo);

    /**
     * 특정 주문번호의 상품명 조회
     */
    String selectGdsNmByOrdNo(@Param("ordNo") String ordNo);
}
