package com.ezwelesp.batch.hims.order.goodsflow.tasklet;

import com.ezwelesp.batch.hims.order.goodsflow.domain.GoodsFlowSendTraceRequestDto;
import com.ezwelesp.batch.hims.order.goodsflow.service.GoodsFlowTraceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsFlowSendTraceRequestTasklet implements Tasklet {
    private final GoodsFlowTraceService goodsFlowTraceService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {

        try {
            int repeat = 0;
            for(;;) {
                repeat++;
                List<GoodsFlowSendTraceRequestDto> targetList = goodsFlowTraceService.getSendTraceRequestTargetForApi();
                if (!targetList.isEmpty() && repeat < 999) {
                    goodsFlowTraceService.sendTraceRequestByApi(targetList);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("GoodsFlowSendTraceRequestTasklet failed", e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.debug("GoodsFlowSendTraceRequestTasklet finished");

        return RepeatStatus.FINISHED;
    }
}
