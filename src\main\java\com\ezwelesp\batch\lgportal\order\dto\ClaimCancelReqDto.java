package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 주문취소 신청 DTO
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto
 * @since 2025.04.15
 */
@Jacksonized
@Getter
@SuperBuilder(toBuilder = true)
@ToString
public class ClaimCancelReqDto implements Serializable {
    @Serial
    private static final long serialVersionUID = -3385439743902952502L;

    // 주문번호
    private String ordNo;

    // 배송번호
    private String dlvNo;

    // 배송상품순번
    private Long dlvGdsSeq;

    // 클레임종류
    private String claimKind;

    // 클레임귀책사유주체코드
    private String clmAtbrMagnCd;

    // 클레임추가비용처리방법코드
    private String clmAddExpPrcsMthdCd;

    // 클레임사유코드
    private String clmRsnCd;

    // 클레임사유내용
    private String clmRsnCntn;

    // 클레임신청상품목록
    private List<ClaimApplyGoodsInfoDto> gdsList;

    // 예금주명
    private String dpsrNm;

    // 금융기관코드
    private String fnnsCd;

    // 계좌번호
    private String actno;

    // 인증여부
    private String certificateYn;

    // 접속디바이스코드
    private String acssDvcCd;
}
