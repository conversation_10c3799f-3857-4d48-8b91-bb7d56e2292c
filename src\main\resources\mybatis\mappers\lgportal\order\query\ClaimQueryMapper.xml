<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">

<mapper namespace="com.ezwelesp.batch.lgportal.order.mapper.query.ClaimQueryMapper">

    <!-- 교환반품 회수상품 조회 -->
    <select id="selectWtdwGdsCount">
        SELECT
            COUNT(dlv_no)
        FROM ez_or.dl_wtdw_gds_d
        WHERE lgp_api_dlv_dtl_no = #{orderExceptNo}
    </select>

    <!-- 교환/반품 처리에 필요한 업체 반품처 조회 -->
    <select id="selectCspObndRtpLocInfo" parameterType="map">
        SELECT csp_cd
             , csp_obnd_loc_no
             , rtp_loc_bas_adr
             , rtp_loc_dtl_adr
             , rtp_loc_telno
             , rtp_loc_zipcd
        FROM ez_co.co_csp_obnd_rtp_loc_b
        WHERE csp_cd = #{cspCd}
            AND csp_obnd_loc_no = #{cspObndLocNo}
    </select>

    <!-- 교환/반품 취소(철회)/거부 목록 조회 -->
    <select id="selectExchRtpWtdrRfsList">
        SELECT
            api_no
             , except_status
             , sale_order_except_no
             , COALESCE(memo,
                        CASE WHEN (SELECT clm_rsn_cd FROM ez_or.cl_clm_rsn_l WHERE clm_no = wtdrRfs.clm_no) = '1001'
                            THEN '교환상품 품절'
                            ELSE '고객요청' END
               ) AS memo
        FROM (
            SELECT api.api_no
                 , 'C' AS except_status
                 , wgd.lgp_api_dlv_dtl_no AS sale_order_except_no
                 , (SELECT comm_dtl_cd_nm
                    FROM ez_cm.cm_comm_dtl_c ccdc
                    WHERE comm_cd = 'clm_cncl_rsn_cd'
                      AND comm_dtl_cd = clm.clm_cncl_rsn_cd) AS memo
                 , clm.clm_no
            FROM ez_or.cl_clm_b clm
                INNER JOIN ez_or.dl_dlv_b dlv ON (clm.clm_no = dlv.clm_no AND clm.ord_no = dlv.ord_no)
                INNER JOIN ez_or.dl_wtdw_gds_d wgd ON (dlv.dlv_no = wgd.dlv_no AND dlv.ord_no = wgd.ord_no)
                INNER JOIN ez_if.api_batch_chg_return api ON (wgd.lgp_api_dlv_dtl_no = api.api_chg_return_no
                                                                  AND api.send_yn = 'N'
                                                                  AND api.reg_dt > (TO_CHAR(NOW() - INTERVAL '30 days', 'YYYYMMDD') || '000000')
                                                                  AND api.send_key = '1019')
            UNION
            SELECT api.api_no
                 , 'R' AS except_status
                 , wgd.lgp_api_dlv_dtl_no AS sale_order_except_no
                 , (SELECT comm_dtl_cd_nm
                    FROM ez_cm.cm_comm_dtl_c ccdc
                    WHERE comm_cd = 'clm_rfs_rsn_cd'
                      AND comm_dtl_cd = rfs.clm_rfs_rsn_cd) AS memo
                 , rfs.clm_no
            FROM ez_or.cl_clm_rfs_apl_d rfs
                INNER JOIN ez_or.dl_wtdw_gds_d wgd ON (rfs.dlv_no = wgd.dlv_no
                                                           AND rfs.wtdw_gds_seq = wgd.wtdw_gds_seq)
                INNER JOIN ez_if.api_batch_chg_return api ON (wgd.lgp_api_dlv_dtl_no = api.api_chg_return_no
                                                                  AND api.send_yn = 'N'
                                                                  AND api.reg_dt > (TO_CHAR(NOW() - INTERVAL '30 days', 'YYYYMMDD') || '000000')
                                                                  AND api.send_key = '1020')
              ) wtdrRfs
    </select>

    <!--
        ez_if.api_batch_chg_return 참조 없이 클레임 데이터 직접 조회하는 방식
    -->
    <select id="selectWtdrRfsList2">
        SELECT
            except_status
             , sale_order_except_no
             , memo
        FROM (SELECT clm.clm_no
                   , 'C' AS except_status
                   , wgd.lgp_api_dlv_dtl_no AS sale_order_except_no
                   , (SELECT comm_dtl_cd_nm
                      FROM ez_cm.cm_comm_dtl_c ccdc
                      WHERE comm_cd = 'clm_cncl_rsn_cd'
                        AND comm_dtl_cd = clm.clm_cncl_rsn_cd) AS memo
              FROM ez_or.cl_clm_b clm
                       INNER JOIN ez_or.dl_dlv_b dlv ON (clm.clm_no = dlv.clm_no AND clm.ord_no = dlv.ord_no)
                       INNER JOIN ez_or.dl_wtdw_gds_d wgd ON (dlv.dlv_no = wgd.dlv_no AND dlv.ord_no = wgd.ord_no
                  AND wgd.lgp_api_dlv_dtl_no IS NOT NULL)
              WHERE 1 = 1
                AND clm.clm_knd_cd in ('RTP', 'EXCH')
                AND clm.clm_st_cd = 'WTDR'
                AND clm.clm_cncl_req_apv_st_cd = 'APV'
                AND clm.clm_cncl_req_prcs_dtm > (TO_CHAR(NOW() - INTERVAL '30 days', 'YYYYMMDD') || '000000')
              UNION
              SELECT rfs.clm_no
                   , 'R' AS except_status
                   , wgd.lgp_api_dlv_dtl_no AS sa   le_order_except_no
                   , (SELECT comm_dtl_cd_nm
                      FROM ez_cm.cm_comm_dtl_c ccdc
                      WHERE comm_cd = 'clm_rfs_rsn_cd'
                        AND comm_dtl_cd = rfs.clm_rfs_rsn_cd) AS memo
              FROM ez_or.cl_clm_rfs_apl_d rfs
                       INNER JOIN ez_or.dl_wtdw_gds_d wgd ON (rfs.dlv_no = wgd.dlv_no
                  AND rfs.wtdw_gds_seq = wgd.wtdw_gds_seq
                  AND wgd.lgp_api_dlv_dtl_no IS NOT NULL)
              WHERE 1 = 1
                AND rfs.clm_rfs_knd_cd = 'RFS'
                AND rfs.clm_rfs_apl_st_cd = 'APV'
                AND rfs.clm_rfs_apv_dtm > (TO_CHAR(NOW() - INTERVAL '30 days', 'YYYYMMDD') || '000000')) wtdrRfs
        WHERE NOT EXISTS (
            SELECT 'X'
            FROM ez_if.api_batch_chg_return api
            WHERE api.send_yn = 'Y'
              AND api.reg_dt > (TO_CHAR(NOW() - INTERVAL '30 days', 'YYYYMMDD') || '000000')
              AND api.chg_return_type IN ('E', 'R')
              AND api.api_chg_return_no = wtdrRfs.sale_order_except_no
        )
    </select>

    <select id="selectExchRtpOrderInfo">
        SELECT ord.ord_no
             , ord.asp_ord_no
             , dlvAdr.rcvr_nm
             , dlvAdr.rcvr_telno
             , dlvAdr.rcvr_mbl_telno
             , dlvAdr.rcvr_zipcd
             , dlvAdr.rcvr_bas_adr
             , dlvAdr.rcvr_dtl_adr
             , dlv.dlv_co_cd
             , dlv.invc_no
        FROM  ez_or.or_ord_b ord
                  INNER JOIN ez_or.dl_dlv_adr_b dlvAdr ON (ord.ord_no = dlvAdr.ord_no)
                  INNER JOIN ez_or.dl_dlv_b dlv ON (dlvAdr.ord_no = dlv.ord_no
            AND dlvAdr.dlv_adr_no = dlv.dlv_adr_no)
        WHERE ord.clnt_cd = 'lgapi'
          AND ord.clnt_ord_no = #{clientOrderNum}
          AND ord.ord_st_cd = 'ORD_CMPT'
        LIMIT 1
    </select>

    <select id="selectExchRtpOrderGoodsInfo" parameterType="map">
        SELECT ord.ord_no
             , ordGds.ord_gds_seq
             , ordGds.gds_cd
             , ordGds.stup_exch_dlv_exp
             , ordGds.stup_rtp_dlv_exp
             , ordGds.csp_cd
             , ordGds.csp_obnd_loc_no
             , ordGds.opt_gds_comb_seq
             , (CASE WHEN (SELECT COUNT(1)
                           FROM ez_co.co_csp_rtp_dlv_co_r rtpDlv
                           WHERE rtpDlv.csp_cd = ordGds.csp_cd
                             AND rtpDlv.csp_obnd_loc_no = ordGds.csp_obnd_loc_no
                             AND rtpDlv.dlv_co_cd = #{dlvCoCd}
                             AND rtpDlv.use_yn = 'Y') = 0 THEN 'N'
                     ELSE 'Y'
            END) AS return_dlvr_use_yn
             , coalesce(gds.rtp_gds_wtdw_obj_yn, 'Y') AS return_yn
             , gds.gds_opt_typ_cd
             , (SELECT COUNT(opt_gds_seq) FROM ez_pd.pd_gds_opt_b opt
                WHERE opt.gds_cd = gds.gds_cd
                  AND opt.mndr_choc_yn = 'Y'
                  AND opt.use_yn = 'Y') AS opt_cnt
             , (SELECT dlv_adr_no FROM ez_or.dl_dlv_adr_b dlvAdr
                                  WHERE dlvAdr.ord_no = ord.ord_no) AS dlv_adr_no
        FROM ez_or.or_ord_b ord
                 INNER JOIN ez_or.or_ord_gds_d ordGds ON (ord.ord_no = ordGds.ord_no)
                 INNER JOIN ez_or.dl_dlv_gds_d dlvGds ON (ordGds.ord_no = dlvGds.ord_no
            AND ordGds.ord_gds_seq = dlvGds.ord_gds_seq
            AND dlvGds.lgp_api_dlv_dtl_no = #{orderItemNo})
                 INNER JOIN ez_pd.pd_gds_c gds ON (ordGds.gds_cd = gds.gds_cd)
        WHERE ord.clnt_ord_no = #{clientOrderNum}
          AND ord.ord_st_cd = 'ORD_CMPT'
          AND ord.clnt_cd = 'lgapi'
    </select>

    <select id="selectCancelOrderGoodsCount">
        SELECT COUNT(dl.dlv_no)
        FROM ez_or.dl_dlv_gds_d dl
        WHERE dl.ord_no = #{ordNo}
          AND dl.lgp_api_dlv_no = #{lgpApiDlvNo}
          AND dl.lgp_api_dlv_dtl_no = #{lgpApiDlvDtlNo}
          AND dl.dlv_gds_st_cd = 'CNCL'
    </select>

    <select id="selectCancelOrderGoodsInfo">
        SELECT dlGds.ord_gds_seq
             , ord.ord_no
             , dlGds.dlv_gds_qty AS clm_gds_qty
             , dlv.dlv_no
             , dlGds.dlv_gds_seq
        FROM  ez_or.or_ord_b ord
            INNER JOIN ez_or.dl_dlv_b dlv ON (ord.ord_no = dlv.ord_no)
            INNER JOIN ez_or.dl_dlv_gds_d dlGds ON (dlv.ord_no = dlGds.ord_no
                                                        AND dlv.dlv_no = dlGds.dlv_no)
        WHERE ord.clnt_cd = 'lgapi'
          AND ord.ord_no = #{ordNo}
          AND ord.ord_st_cd = 'ORD_CMPT'
          AND dlGds.lgp_api_dlv_no = #{lgpApiDlvNo}
          AND dlGds.lgp_api_dlv_dtl_no = #{lgpApiDlvDtlNo}
    </select>


</mapper>
