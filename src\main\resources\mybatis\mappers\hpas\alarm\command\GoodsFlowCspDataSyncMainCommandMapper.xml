<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hpas.alarm.mapper.command.GoodsFlowCspDataSyncMainCommandMapper">

	<!-- csp사 굿스플로 계약 정보 sync 맞추기 (반품서비스 기존 택배사 사용안함 처리) -->
	<update id="setCspReturnDlvrUseYnN" parameterType="map">
	    update ez_co.co_csp_rtp_dlv_co_r
	       set use_yn = 'N'
	         , last_mod_dtm = to_char(now(), 'yyyymmddhh24miss')
	         , last_mod_usr_id = 'BATCH'
	         , last_mod_pgm_id = 'BATCH'
	     where csp_cd = #{cspCd}
	</update>
	
	<!-- csp사 굿스플로 계약 정보 sync 맞추기 (해당 csp 굿스플로 서비스 신청 내역 수정) -->
	<insert id="setUpdCspDlvrContract" parameterType="com.ezwelesp.batch.hpas.alarm.dto.GoodsFlowCspDataSyncMainDto">
        merge into welfare_dev.dl_goodsf_cont_b
        using dual
           on goodsf_cont_seq = #{requestkey}
         when matched then
             update
                set 
                    goodsf_apv_mthd_cd = #{verificationtype}
                  , csp_bzrn           = #{bizno}
                  , goodsf_cont_no     = #{contractno}
                  , csp_goodsf_cont_no = #{contractcustno}
                  , csp_nm             = #{mallname}
                  , rpsr_nm            = #{mallusername}
                  , csp_telno1         = #{mallusertel1}
                  , csp_telno2         = #{mallusertel2}
                  , csp_eml_adr        = #{malluseremail}
                  , obnd_loc_nm        = #{centername}
                  , obnd_loc_zipcd     = #{centerzipcode}
                  , obnd_loc_bas_adr   = #{centeraddr1}
                  , obnd_loc_dtl_adr   = #{centeraddr2}
                  , obnd_loc_telno1    = #{centertel1}
                  , obnd_loc_telno2    = #{centertel2}
                  , ship_dlv_exp       = #{shippingfee}
                  , air_dlv_exp        = #{flightfee}
                  , prcs_cmpt_yn       = #{status}
                  , goodsf_apv_rslt_cd = #{verifiedresult}
                  , apv_dtm            = #{verifieddatetime}
                  , last_mod_dtm       = to_char(now(), 'yyyymmddhh24miss')
                  , last_mod_usr_id    = 'BATCH'
                  , last_mod_pgm_id    = 'BATCH'
         when not matched then
             insert (
                    goodsf_cont_seq
                  , csp_cd
                  , csp_obnd_loc_no
                  , dlv_co_cd
                  , goodsf_apv_mthd_cd
                  , csp_bzrn
                  , goodsf_cont_no
                  , csp_goodsf_cont_no
                  , csp_nm
                  , rpsr_nm
                  , csp_telno1
                  , csp_telno2
                  , csp_eml_adr
                  , obnd_loc_nm
                  , obnd_loc_zipcd
                  , obnd_loc_bas_adr
                  , obnd_loc_dtl_adr
                  , obnd_loc_telno1
                  , obnd_loc_telno2
                  , ship_dlv_exp
                  , air_dlv_exp
                  , prcs_cmpt_yn
                  , goodsf_apv_rslt_cd
                  , apv_dtm
                  , frst_reg_dtm
                  , frst_reg_usr_id
                  , frst_reg_pgm_id
                  , last_mod_dtm
                  , last_mod_usr_id
                  , last_mod_pgm_id)
             values (
                    #{requestkey}
                  , #{partnercode}
                  , #{centercode}
                  , #{delivercode}
                  , #{verificationtype}
                  , #{bizno}
                  , #{contractno}
                  , #{contractcustno}
                  , #{mallname}
                  , #{mallusername}
                  , #{mallusertel1}
                  , #{mallusertel2}
                  , #{malluseremail}
                  , #{centername}
                  , #{centerzipcode}
                  , #{centeraddr1}
                  , #{centeraddr2}
                  , #{centertel1}
                  , #{centertel2}
                  , #{shippingfee}
                  , #{flightfee}
                  , #{status}
                  , #{verifiedresult}
                  , #{verifieddatetime}
                  , to_char(now(), 'yyyymmddhh24miss')
                  , 'BATCH'
                  , 'BATCH'
                  , to_char(now(), 'yyyymmddhh24miss')
                  , 'BATCH'
                  , 'BATCH')
	</insert>
	
	<!-- csp사 굿스플로 계약 정보 sync 맞추기 (해당 csp 굿스플로 서비스 신청 내역 수정) -->
	<delete id="delCspDlvrContractBoxRate" parameterType="com.ezwelesp.batch.hpas.alarm.dto.GoodsFlowCspDataSyncMainDto">
		delete ez_or.dl_goodsf_cont_d
		 where goodsf_cont_seq = #{requestKey}
	</delete>
	
	<!-- csp사 굿스플로 계약 정보 sync 맞추기 (해당 csp 굿스플로 서비스 신청 내역 수정) -->
	<insert id="setCspDlvrContractBoxRate"  parameterType="com.ezwelesp.batch.hpas.alarm.dto.GoodsFlowCspDataSyncMainDto">
		insert into ez_or.dl_goodsf_cont_d (
			goodsf_cont_seq
		  , csp_cd
		  , csp_obnd_loc_no
		  , dlv_co_cd
		  , box_spc_cd
		  , prpy_dlv_exp
		  , crdt_dlv_exp
		  , arpay_dlv_exp
		  , rtp_dlv_exp
		  , goodsf_cont_dtl_seq
		  , frst_reg_dtm
		  , frst_reg_usr_id
		  , frst_reg_pgm_id
		  , last_mod_dtm
		  , last_mod_usr_id
		  , last_mod_pgm_id)
		values (
			#{requestKey}
		  , #{partnerCode}
		  , #{centerCode}
		  , #{deliverCode}
		  , #{boxSize}
		  , #{shFare}
		  , #{scFare}
		  , #{bhFare}
		  , #{rtFare}
		  , (
			 select coalesce(MAX(goodsf_cont_dtl_seq), 0) + 1
			   from ez_or.dl_goodsf_cont_d
			  where goodsf_cont_seq = #{requestKey})
		  , to_char(now(), 'yyyymmddhh24miss')
		  , 'BATCH'
		  , 'BATCH'
		  , to_char(now(), 'yyyymmddhh24miss')
		  , 'BATCH'
		  , 'BATCH'
		)
	</insert>
	
	<!-- csp사 굿스플로 계약 정보 sync 맞추기 (반품서비스 택배사 정보 입력) -->
	<insert id="setCspReturnDlvr" parameterType="com.ezwelesp.batch.hpas.alarm.dto.GoodsFlowCspDataSyncMainDto">
        merge into ez_co.co_csp_rtp_dlv_co_r
        using dual
           on csp_cd = #{partnercode}
          and csp_obnd_loc_no = #{centercode}
          and dlv_co_cd = (select coalesce(dlv_co_cd , 0000)
                             from welfare_dev.ez_dlvr
                            where upper(baesong_mapping_cd) = #{delivercode}
         when matched then
             update
                set use_yn = (select nvl(confirm_result_cd , 'N')
                                from ez_or.dl_goodsf_cont_b
                               where goodsf_cont_seq = #{requestkey})
                  , last_mod_dtm       = to_char(now(), 'yyyymmddhh24miss')
                  , last_mod_usr_id    = 'BATCH'
                  , last_mod_pgm_id    = 'BATCH'
         when not matched then
             insert (
                    csp_cd
                  , dlv_co_cd
                  , csp_obnd_loc_no
                  , use_yn
                  , frst_reg_dtm
                  , frst_reg_usr_id
                  , frst_reg_pgm_id
                  , last_mod_dtm
                  , last_mod_usr_id
                  , last_mod_pgm_id)
             values (
                    #{partnercode}
                  , (select coalesce(dlv_co_cd , 0000)
                       from welfare_dev.ez_dlvr
                      where upper(baesong_mapping_cd) = #{delivercode})
                  , #{centercode}
                  , (select nvl(confirm_result_cd , 'N')
                       from ez_or.dl_goodsf_cont_b
                      where goodsf_cont_seq = #{requestkey})
                  , to_char(now(), 'yyyymmddhh24miss')
                  , 'BATCH'
                  , 'BATCH'
                  , to_char(now(), 'yyyymmddhh24miss')
                  , 'BATCH'
                  , 'BATCH')
	</insert>

</mapper>
