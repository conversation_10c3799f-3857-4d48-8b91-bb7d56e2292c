package com.ezwelesp.batch.hims.order.goodsflow.job;

import com.ezwelesp.batch.hims.order.goodsflow.tasklet.GoodsFlowSendTraceRequestTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 굿스플로 배송추적 요청 [BA_HIOR00141]
 *  - ez_if.gikimimast, detail 대상건 조회
 *  - 굿스플로 배송추적 API 호출
 *  - ez_if.gikimimast 결과 업데이트
 */
@Configuration
@RequiredArgsConstructor
public class GoodsFlowSendTraceRequestJobConfig {
    private final CommonJobListener commonJobListener;
    private final GoodsFlowSendTraceRequestTasklet goodsFlowSendTraceRequestTasklet;

    @Bean("BA_HIOR00141")
    public Job goodsFlowSendTraceRequestJob(JobRepository jobRepository, @Qualifier("BA_HIOR00141_STEP") Step goodsFlowSendTraceRequestStep) {
        return new JobBuilder("BA_HIOR00141", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(goodsFlowSendTraceRequestStep)
                .build();
    }

    @Bean("BA_HIOR00141_STEP")
    public Step goodsFlowSendTraceRequestStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00141_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(goodsFlowSendTraceRequestTasklet, transactionManager)
                .build();
    }
}
