package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import com.ezwelesp.batch.lgportal.product.mapper.query.ProductLGPortalQueryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

import static com.ezwelesp.batch.lgportal.product.service.LgProductUtils.mapToProductDeliveryToRequest;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalEditProductDeliveryHandler implements LgPortalEditProductProcessorHandler {

    private final LgApiClient lgApiClient;
    private final ProductLGPortalQueryMapper productLGPortalQueryMapper;

    @Override
    public LgProductApiResponseDto handle(LgEditProductTargetVo item) {

        val productDelivberyList = productLGPortalQueryMapper.selectLgEditProductDeliveryTargetByGdsCd(
                item.getGdsCd()
        );

        val requestDto = mapToProductDeliveryToRequest(productDelivberyList, item);

        val responseDto = lgApiClient.sendLgUpdateProductDeliveryApi(
                requestDto,
                item.getGdsCd(),
                item.getApiSeq()
        );


        if (responseDto != null && !"200".equals(responseDto.getResult())) {
            log.debug("sendLgUpdateProductDeliveryApi Fail : {},{}", item.getGdsCd(), responseDto.getResult());
        }

        return responseDto;
    }

}
