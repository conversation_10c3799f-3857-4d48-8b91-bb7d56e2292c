package com.ezwelesp.batch.hpas.alarm.service.impl;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import org.springframework.stereotype.Service;
import com.ezwelesp.batch.hpas.alarm.dto.MonitorJobsDto;
import com.ezwelesp.batch.hpas.alarm.dto.MonitorResDto;
import com.ezwelesp.batch.hpas.alarm.mapper.command.EzMemberStoreCommandMapper;
import com.ezwelesp.batch.hpas.alarm.mapper.query.EzMemberStoreQueryMapper;
import com.ezwelesp.batch.hpas.alarm.service.CommonAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommonAlarmServiceImpl implements CommonAlarmService {
    private final EzMemberStoreQueryMapper ezMemberStoreQueryMapper;
    private final EzMemberStoreCommandMapper ezMemberStoreCommandMapper;

    @Override
    public String getCommonMonitorJobs(String jobDate, String jobId, List<?> list,
            String resEtc) {
        MonitorJobsDto monitorJobsDto = ezMemberStoreQueryMapper.selectMonitorJobList(jobId);

        if (monitorJobsDto == null) {
            log.info("########################################");
            log.info("#### jobId is null !!!!!!");
            log.info("########################################");
            return "fail";
        }
        else {
            MonitorResDto monitorResDto = new MonitorResDto();
            monitorResDto.setJobId(monitorJobsDto.getJobId());
            monitorResDto.setJobStartDt(jobDate);
            //monitorResDto.setJobEndDt("");
            monitorResDto.setAlramYn(monitorJobsDto.getAlramYn());
            monitorResDto.setAlramCntMax(monitorJobsDto.getAlramCntMax());
            monitorResDto.setAlramUser("");
            monitorResDto.setCriticalValue(monitorJobsDto.getCriticalValue());
            monitorResDto.setCriticalValueUnit(monitorJobsDto.getCriticalValueUnit());
            monitorResDto.setResValue(list.size());
            //monitorResDto.setResStatus("");
            monitorResDto.setResEtc(resEtc);
            if (list.size() > 0) {
                monitorResDto.setResEtc2("CS팀에게 문의주세요!!");
            }
            else {
                monitorResDto.setResEtc2("");
            }
            try {
                monitorResDto.setResIp(InetAddress.getLocalHost().getHostAddress());
            } catch (UnknownHostException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            int resCnt = ezMemberStoreCommandMapper.insertMonitorRes(monitorResDto);

        }

        return "success";
    }

}
