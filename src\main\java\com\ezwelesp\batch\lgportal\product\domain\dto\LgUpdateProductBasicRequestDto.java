package com.ezwelesp.batch.lgportal.product.domain.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgUpdateProductBasicRequestDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgUpdateProductBasicRequestDto {
    private String productMappingCode;  // 이지웰 상품번호
    private Integer productNo;          // LG상품번호
    private String productName;         // 상품명
    private String productCode;         // 상품코드
    private Integer brandNo;            // 브랜드코드
    private String modelName;           // 모델명
    private String madeCountry;         // 원산지
    private String madeCompany;         // 제조사
    private String madeDate;            // 제조일자
    private String productSummary;      // 상품요약
    private String salesStatusCode;     // 판매 상태 코드
    private String salesMethodCode;     // 판매 방식 코드
    private String taxYN;               // 과세여부 (Y/N)
    private String deliveryDate;        // 배송 날짜
    private Integer deliveryDay;        // 배송 소요 일수
    private String barcode;             // 바코드
    private Integer orderMinCnt;        // 최소 주문 수량
    private Integer orderMaxCnt;        // 최대 주문 수량
    private Integer sumLimitCnt;        // 구매자별 누적 최대 주문 수량
    private Integer shippingLoc;        // 배송 위치
    private Integer exchangeLoc;        // 교환 위치
    private Integer returnLoc;          // 반품 위치
    private Integer addDeliveryCost;    // 추가 배송비
    private String displayContents;     // 표시 내용
}
