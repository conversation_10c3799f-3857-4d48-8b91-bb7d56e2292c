package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.tasklet.CspCostStlTrsfDdBatTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class CspCostStlTrsfDdBatJobConfig {
    private final CommonJobListener commonJobListener;
    private final CspCostStlTrsfDdBatTasklet cspCostStlTrsfDdBatTasklet;
    
    /**
     * 협력사 대금 정산이관 일배치
     * 주기 : TODO 매일 오전 2시 0분 0초 (AS-IS 기준)
     * 설명 : 협력사 대금 정산이관 일배치
     * 
     * @param jobRepository
     * @param jobRepository
     * @param cspCostStlTrsfDdBatTaskletStep
     * @return
     */
    @Bean("cspCostStlTrsfDdBatJob")
    Job cspCostStlTrsfDdBatJob(
            JobRepository jobRepository,
            Step cspCostStlTrsfDdBatTaskletStep)
    {
        return new JobBuilder("cspCostStlTrsfDdBatJob", jobRepository)
                .listener(commonJobListener)
                .start(cspCostStlTrsfDdBatTaskletStep) // 협력사 대금 정산이관 프로시저 호출 Step
                .build();
    }
    
    /**
     * 협력사 대금 정산이관 프로시저 호출
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("cspCostStlTrsfDdBatTaskletStep")
    Step cspCostStlTrsfDdBatTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("cspCostStlTrsfDdBatTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(cspCostStlTrsfDdBatTasklet, transactionManager)
                .build();
    }
}