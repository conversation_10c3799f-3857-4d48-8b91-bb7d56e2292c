package com.ezwelesp.batch.hims.order.send.domain;

import lombok.Builder;
import lombok.Getter;

/**
 * 이지웰, 협력사 발송 응답 Dto
 */
@Getter
@Builder
public class SendResponse {
    // 이지웰, 협력사 공통 필드
    private boolean success;
    private String message; // 오류메세지

    // 협력사발송 전용 필드
    private String result; // 0000: 정상 (그외 오류)
    private String ecpnRn; // 쿠폰번호 응답 (쿠폰응답 불가시 ecpnTrid 값이 넘어옴)
    private String ecpnCspReqInntNo; // 발송 이후 연동을위한 key 값으로 (명세 ecpnTrid 동일), 주문발송상품상세에 저장필요
}
