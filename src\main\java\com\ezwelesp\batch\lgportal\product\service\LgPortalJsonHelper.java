package com.ezwelesp.batch.lgportal.product.service;

import com.ezwelesp.batch.lgportal.product.domain.dto.LgApiBaseResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaResponseDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LgPortalJsonHelper {

    private final ObjectMapper objectMapper;

    // 필요한 공통 값을 생성자 초기화 (apiKey 등)
    public LgPortalJsonHelper() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 공통 Lg API용 키값 추가.
     *
     * @param request 원본 Object
     * @return String 변환된 JSON String
     */
    public String addLgApiKeysToString(String apiKey, Object request) {
        try {

            ObjectNode jsonNode = objectMapper.valueToTree(request);

            jsonNode.put("apiKey", apiKey);

            return objectMapper.writeValueAsString(jsonNode);
        } catch (Exception e) {
            log.error("addLgApiKeysToStringFail: {},{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Lg API 상품 코드 포함된 응답 파싱 메서드
     *
     * @param jsonResponse Api Response
     * @param gdsCd 상품 코드
     * @param apiSeq apiSeq 상품 및 sku 수정에서 사용 중
     * @return LgProductApiResponseDto
     */
    public LgProductApiResponseDto parseLgApiProductResponse(
            String jsonResponse,
            String gdsCd,
            Integer apiSeq) {
        try {
            // JSON 응답 파싱
            val root = objectMapper.readTree(jsonResponse);

            // 필드 추출
            val result = root.path("result").asText();
            val resultMessage = root.path("resultMessage").asText();
            val responseType = root.path("responseType").asText();
            val productNo = root.path("productNo").asInt();

            // 상품 코드나, apiSeq를 같이 넣어주려고 직접 파씽 안하고 직접 빌드
            return LgProductApiResponseDto.builder()
                    .result(result)
                    .resultMessage(resultMessage)
                    .responseType(responseType)
                    .productNo(productNo)
                    .gdsCd(gdsCd)
                    .apiSeq(apiSeq)
                    .build();

        } catch (Exception e) {
            log.error("parseLgApiProductResponse failed: {}, {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Lg API QnA List 맵핑작업
     *
     * @param jsonResponse Api Response
     * @return LgProductQnaListResponseDto
     */
    public LgProductQnaResponseDto parseLgProductQnaResponse(
            String jsonResponse
    ) {
        try {
            // JSONString  -> DTO 매핑
            return objectMapper.readValue(jsonResponse, LgProductQnaResponseDto.class);

        } catch (Exception e) {
            log.error("parseLgProductQnaResponse failed: {}, {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Lg API 응답 파싱 메서드
     *
     * @param jsonResponse Api Response
     * @return LgApiBaseResponseDto
     */
    public LgApiBaseResponseDto parseLgApiResponse(String jsonResponse) {
        try {
            // JSON 응답 파싱
            val root = objectMapper.readTree(jsonResponse);

            // 필드 추출
            val result = root.path("result").asText();
            val resultMessage = root.path("resultMessage").asText();

            return LgApiBaseResponseDto.builder()
                    .result(result)
                    .resultMessage(resultMessage)
                    .build();

        } catch (Exception e) {
            log.error("parseLgApiResponse failed: {}, {}", e.getMessage(), e);
            return null;
        }
    }



}
