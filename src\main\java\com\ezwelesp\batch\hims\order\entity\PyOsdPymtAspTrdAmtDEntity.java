package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 외부결제제휴사거래금액상세(ez_or.py_osd_pymt_asp_trd_amt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyOsdPymtAspTrdAmtDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 외부결제제휴사거래금액수기번호(osd_pymt_asp_trd_amt_hndw_no) not null
     */
    private Long osdPymtAspTrdAmtHndwNo;

    /**
     * 외부결제년월(osd_pymt_ym) not null
     */
    private String osdPymtYm;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
