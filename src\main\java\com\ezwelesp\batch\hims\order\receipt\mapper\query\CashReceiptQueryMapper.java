package com.ezwelesp.batch.hims.order.receipt.mapper.query;

import com.ezwelesp.batch.hims.order.receipt.domain.*;

import java.util.List;

public interface CashReceiptQueryMapper {

    List<OrderClaimDto> selectOrderCashReceiptTarget();

    List<OrderClaimDto> selectClaimCashReceiptTarget();

    List<OrderInfoDto> selectOrderInfo(String ordNo);

    int selectCountCashReceipt(CashReceiptParamDto param);

    List<CashPublicationGatherDto> selectCashReceiptPublicationGather(CashPublicationGatherDto dto);

    int selectCashReceiptPublicationGatherCount(CashPublicationGatherDto dto);

    ContentServiceProviderDto selectUnitedContentServiceProviderInfo(String cspCd);

}


