<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.code.mapper.command.CommCodeCommandMapper">

    <delete id="deleteCommCode">
        delete from ez_cm.cm_comm_c
    </delete>

    <insert id="insertCommCode">
        insert into ez_cm.cm_comm_c
        (comm_cd,
        comm_cd_nm,
        comm_cd_desc,
        high_comm_cd,
<!--        asis_comm_cd_cval,-->
        frst_reg_dtm,
        frst_reg_usr_id,
        frst_reg_pgm_id,
        last_mod_dtm,
        last_mod_usr_id,
        last_mod_pgm_id)
        select
        a.cd_id,
        a.cd_nm,
        a.cd_desc,
        a.up_cd_id ,
<!--        a.asis_cd,-->
        to_char(now(),'YYYYMMDDHH24MISS'),
        a.frst_reg_usr_id,
        a.frst_reg_usr_id,
        to_char(now(),'YYYYMMDDHH24MISS'),
        coalesce(a.last_mod_usr_id, '임시'),
        coalesce(a.last_mod_usr_id, '임시')
        from ez_cm.if_meta_comm_code a
    </insert>

    <delete id="deleteCommCodeDtl">
        delete from ez_cm.cm_comm_dtl_c
    </delete>

    <insert id="insertCommCodeDtl">
        insert into ez_cm.cm_comm_dtl_c
        (comm_cd,
        comm_dtl_cd,
        comm_dtl_cd_nm,
        comm_dtl_cd_desc,
        cd_add_inf_cval1,
        cd_add_inf_cval2,
        cd_add_inf_cval3,
        cd_add_inf_cval4,
        cd_add_inf_cval5,
        sort_ordg,
        frst_reg_dtm,
        frst_reg_usr_id,
        frst_reg_pgm_id,
        last_mod_dtm,
        last_mod_usr_id,
        last_mod_pgm_id,
        high_comm_dtl_cd
<!--        asis_comm_dtl_cd_cval-->
        )
        select
        a.cd_id,
        a.cd_val,
        a.cd_val_nm,
        a.cd_val_desc,
        a.cd_val_info01,
        a.cd_val_info02,
        a.cd_val_info03,
        a.cd_val_info04,
        a.cd_val_info05,
        a.order_no,
        to_char(now(),'YYYYMMDDHH24MISS'),
        a.frst_reg_usr_id,
        a.frst_reg_usr_id,
        to_char(now(),'YYYYMMDDHH24MISS'),
        a.last_mod_usr_id,
        a.last_mod_usr_id,
        a.up_cd_val
<!--        a.asis_cd_val-->
        from ez_cm.if_meta_comm_code_dtl a
    </insert>
</mapper>
