package com.ezwelesp.batch.hims.order.csp.tasklet;

import com.ezwelesp.batch.hims.order.csp.domain.CspOrderCurrentStateDto;
import com.ezwelesp.batch.hims.order.csp.service.CspOrderCurrentStateNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Component
public class CspOrderCurrentStateNotificationTasklet implements Tasklet {
    private final CspOrderCurrentStateNotificationService cspOrderCurrentStateNotificationService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            List<CspOrderCurrentStateDto> list = cspOrderCurrentStateNotificationService.getCspOrderCurrentSateList();

            // TODO 알림톡
            if (!list.isEmpty()) {
                list.forEach(this::toMsg);
            }
        } catch (Exception e) {
            log.error("CspOrderCurrentStateNotificationTasklet failed. {}, {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.debug("CspOrderCurrentStateNotificationTasklet finished");
        return RepeatStatus.FINISHED;
    }

    private void toMsg(CspOrderCurrentStateDto dto) {
        log.info("[주문 및 교환/반품 신청 안내] 협력사: {}, 담당자전화번호: {}, 주문완료: {}, 교환신청: {}, 반품신청: {}" +
                "[배송마감 주문안내] 배송대기: {}, 오늘출고마감: {}, 배송지연: {}, 교환지연: {}, 반품환불요청지연: {}, 교환수거지연: {}, 반품수거지연: {}" +
                "[상품문의 답변현황 안내] 답변대기: {}, 24시간초과대기: {}," +
                "[CS 문의 현황 안내] 긴급접수: {}, 일반접수: {}", dto.getCspCd(), dto.getMgrMblTelno(), dto.getOrdCnt(), dto.getExchAplCnt(), dto.getRtpAplCnt()
        , dto.getDlvWaitCnt(), dto.getTodayObndClsg(), dto.getDlvDlayCnt(), dto.getExchDlayCnt(), dto.getRtpRfndDlayCnt(), dto.getExchPkupDlayCnt(), dto.getRtpPkupDlayCnt()
        , dto.getGdsInqWaitCnt(), dto.getGdsInqOverWaitCnt(), dto.getCspEmgyInqCnt(), dto.getCspInqCnt());
        log.info("--------------------------------------------");
//                "[주문 및 교환/반품 신청 안내]"
//                +"\n"
//                +"-주문완료 : "+bean.getOrderCnt()+"건"
//                +"\n"
//                +"-교환/반품 신청 : "+bean.getExChangeCnt()+"건 / "+bean.getRefundCnt()+"건"
//                +"\n"
//                +"\n"
//                +"[배송마감 주문안내]"
//                +"\n"
//                +"* 발송정책이 설정된 상품 주문 기준"
//                +"\n"
//                +"-배송대기 : "+bean.getWaitingDvlrCnt()+"건"
//                +"\n"
//                +"-오늘출고마감 : "+bean.getDlvrDeadlineCnt()+"건"
//                +"\n"
//                +"-배송지연 : "+bean.getDlvrDelayCnt()+"건"
//                +"\n"
//                +"-교환지연 : "+bean.getExChangeDelayCnt()+"건"
//                +"\n"
//                +"-반품 환불요청 지연 : "+bean.getRefundDelayCnt()+"건"
//                +"\n"
//                +"-교환/반품 수거지연 : "+bean.getCollectionDelayCnt()+"건 / "+bean.getRefundCollectDelayCnt()+"건"
//                +"\n"
//                +"\n"
//                +"[상품문의 답변현황 안내]"
//                +"\n"
//                +"-총 답변대기 : "+bean.getWaitingRepCnt()+"건"
//                +"\n"
//                +"-24시간 초과 대기 : "+bean.getOverWaitingRepcnt()+"건"
//                +"\n"
//                +"\n"
//                +"[CS 문의 현황 안내]"
//                +"\n"
//                +"-긴급 접수 : "+bean.getCsAskUrgencyYCnt()+"건"
//                +"\n"
//                +"-일반 접수 : "+bean.getCsAskUrgencyNCnt()+"건"
    }
}
