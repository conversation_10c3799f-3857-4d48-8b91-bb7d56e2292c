package com.ezwelesp.batch.config;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.sql.DataSource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.h2.jdbcx.JdbcDataSource;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.batch.BatchDataSource;
import org.springframework.boot.autoconfigure.batch.BatchDataSourceScriptDatabaseInitializer;
import org.springframework.boot.autoconfigure.batch.BatchProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.util.ClassUtils;

import com.ezwelesp.framework.utils.ArrayUtils;
import com.zaxxer.hikari.HikariDataSource;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
@MapperScan(value = "com.ezwelesp.batch.**.mapper.query", sqlSessionFactoryRef = "primaryQuerySqlSessionFactory")
@MapperScan(value = "com.ezwelesp.batch.**.mapper.command", sqlSessionFactoryRef = "primaryCommandSqlSessionFactory")
public class DataSourceConfig {
    private final Environment env;

    static final String DEFAULT_RESOURCE_PATTERN = "**/*.class";

    @Bean
    @ConditionalOnMissingBean(BatchDataSourceScriptDatabaseInitializer.class)
    BatchDataSourceScriptDatabaseInitializer batchDataSourceScriptDatabaseInitializer(DataSource dataSource,
                                                                                      @BatchDataSource ObjectProvider<DataSource> batchDataSource,
                                                                                      BatchProperties properties) {
        return new BatchDataSourceScriptDatabaseInitializer(batchDataSource.getIfAvailable(() -> dataSource), properties.getJdbc());
    }

    /**
     * 로컬 개발환경용 batch datasource는 휘발성 h2 메모리 db 사용
     * @return
     */
    @BatchDataSource
    @Bean
    @Profile("local")
    DataSource localBatchDataSource() {
		var h2ds	= new JdbcDataSource();
		h2ds.setURL("jdbc:h2:mem:local-batch;DB_CLOSE_DELAY=-1");
		h2ds.setUser("batch-user");
		return h2ds;
    }
    
    /**
     * 서버 환경용 batch datasource는 primaryCommandHikariConfig 사용
     * @return
     */
    @BatchDataSource
    @Bean
    @ConditionalOnMissingBean(name="localBatchDataSource")
    DataSource batchDataSource() {
    	return this.primaryCommandHikariConfig();
    }
    

    @Bean
    @Primary
    @Qualifier("primaryCommandHikariConfig")
    @ConfigurationProperties(prefix = "spring.datasource.hikari.primary.command")
    public DataSource primaryCommandHikariConfig() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean
    @Qualifier("primaryQueryHikariConfig")
    @ConfigurationProperties(prefix = "spring.datasource.hikari.primary.query")
    public DataSource primaryQueryHikariConfig() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    
    @Bean
    @Primary
    @Qualifier("primaryCommandTransactionManager")
    public DataSourceTransactionManager primaryCommandTransactionManager(@Qualifier("primaryCommandHikariConfig") DataSource primaryCommandHikariConfig) {
        return new DataSourceTransactionManager(primaryCommandHikariConfig);
    }

    @Bean
    @Qualifier("primaryQueryTransactionManager")
    public DataSourceTransactionManager primaryQueryTransactionManager(@Qualifier("primaryQueryHikariConfig") DataSource primaryQueryHikariConfig) {
        return new DataSourceTransactionManager(primaryQueryHikariConfig);
    }

    @Bean
    @Primary
    @Qualifier("primaryCommandSqlSessionFactory")
    public SqlSessionFactory primaryCommandSqlSessionFactory() throws Exception {
        String typeAliasesPackage = env.getProperty("mybatis.primary-type-aliases-package");
        String mapperLocations = env.getProperty("mybatis.primary-command-mapper-locations");
        String configLocation = env.getProperty("mybatis.config-location");

        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(primaryCommandHikariConfig());
        sqlSessionFactoryBean.setTypeAliasesPackage(typeAliasesPackage);
        sqlSessionFactoryBean.setMapperLocations(resolveMapperLocations(StringUtils.split(mapperLocations, ",")));
        sqlSessionFactoryBean.setConfigLocation(new DefaultResourceLoader().getResource(configLocation));
        return sqlSessionFactoryBean.getObject();
    }

    @Bean
    @Qualifier("primaryQuerySqlSessionFactory")
    public SqlSessionFactory primaryQuerySqlSessionFactory() throws Exception {
        String typeAliasesPackage = env.getProperty("mybatis.primary-type-aliases-package");
        String mapperLocations = env.getProperty("mybatis.primary-query-mapper-locations");
        String configLocation = env.getProperty("mybatis.config-location");

        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(primaryQueryHikariConfig());
        sqlSessionFactoryBean.setTypeAliasesPackage(typeAliasesPackage);
        sqlSessionFactoryBean.setMapperLocations(resolveMapperLocations(StringUtils.split(mapperLocations, ",")));
        sqlSessionFactoryBean.setConfigLocation(new DefaultResourceLoader().getResource(configLocation));
        return sqlSessionFactoryBean.getObject();
    }

    public static String setTypeAliasesPackage(String typeAliasesPackage) {
        ResourcePatternResolver resolver = (ResourcePatternResolver) new PathMatchingResourcePatternResolver();
        MetadataReaderFactory metadataReaderFactory = new CachingMetadataReaderFactory(resolver);
        List<String> allResult = new ArrayList<String>();
        try {
            for (String aliasesPackage : typeAliasesPackage.split(",")) {
                List<String> result = new ArrayList<String>();
                aliasesPackage = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX
                        + ClassUtils.convertClassNameToResourcePath(aliasesPackage.trim()) + "/"
                        + DEFAULT_RESOURCE_PATTERN;
                Resource[] resources = resolver.getResources(aliasesPackage);
                if (ArrayUtils.isNotEmpty(resources)) {
                    MetadataReader metadataReader = null;
                    for (Resource resource : resources) {
                        if (resource.isReadable()) {
                            metadataReader = metadataReaderFactory.getMetadataReader(resource);
                            try {
                                result.add(Class.forName(metadataReader.getClassMetadata().getClassName()).getPackage()
                                        .getName());
                            } catch (ClassNotFoundException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(result)) {
                    HashSet<String> hashResult = new HashSet<String>(result);
                    allResult.addAll(hashResult);
                }
            }
            if (CollectionUtils.isNotEmpty(allResult)) {
                typeAliasesPackage = String.join(",", (String[]) allResult.toArray(new String[0]));
            }
            else {
                throw new RuntimeException("mybatis typeAliasesPackage 경로 검색 오류. 매개 변수 typeAliasesPackage:"
                        + typeAliasesPackage + "찾을 수 없습니다.");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return typeAliasesPackage;
    }

    public Resource[] resolveMapperLocations(String[] mapperLocations) {
        ResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();
        List<Resource> resources = new ArrayList<Resource>();

        if (ArrayUtils.isNotEmpty(mapperLocations)) {
            for (String mapperLocation : mapperLocations) {
                try {
                    Resource[] mappers = resourceResolver.getResources(mapperLocation);
                    resources.addAll(Arrays.asList(mappers));
                } catch (IOException e) {
                    // ignore
                }
            }
        }
        return resources.toArray(new Resource[resources.size()]);
    }
}
