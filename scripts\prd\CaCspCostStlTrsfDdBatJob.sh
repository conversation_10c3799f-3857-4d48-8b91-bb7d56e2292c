#!/bin/bash

#Job
JOB_NAME="cspCostStlTrsfDdBatJob"

#server env
PROFILE=prd

# 작업 시작 시간 로그
echo "Spring Batch Job 시작: $(date)" >> ../logs/$JOB_NAME.log

# Spring Batch Job 실행
echo java -jar /data/apps/esp-batch.jar --job.name=$JOB_NAME $@ --spring.profiles.active=$PROFILE >> ../logs/$JOB_NAME.log 2>&1
java -jar /data/apps/esp-batch.jar --job.name=$JOB_NAME $@ --spring.profiles.active=$PROFILE >> ../logs/$JOB_NAME.log 2>&1

# 작업 종료 시간 로그
echo "Spring Batch Job 종료: $(date)" >> ../logs/$JOB_NAME.log