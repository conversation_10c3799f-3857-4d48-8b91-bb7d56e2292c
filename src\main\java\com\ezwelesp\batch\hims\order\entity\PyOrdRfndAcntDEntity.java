package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 주문환불계좌상세(ez_or.py_ord_rfnd_acnt_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyOrdRfndAcntDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문환불계좌상세순번(ord_rfnd_acnt_dtl_seq) not null
     */
    private Long ordRfndAcntDtlSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 결제번호(pymt_no)
     */
    private String pymtNo;

    /**
     * 클레임번호(clm_no)
     */
    private String clmNo;

    /**
     * 예금주명(dpsr_nm)
     */
    private String dpsrNm;

    /**
     * 암호화계좌번호(enc_actno)
     */
    private String encActno;

    /**
     * 금융기관코드(fnns_cd)
     */
    private String fnnsCd;

    /**
     * 모바일결제익월취소처리코드(mbl_pymt_nmm_cncl_prcs_cd) not null
     */
    private String mblPymtNmmCnclPrcsCd;

    /**
     * 환불계좌알림톡발송상태코드(rfnd_acnt_nttk_snd_st_cd)
     */
    private String rfndAcntNttkSndStCd;

    /**
     * 알림톡솔루션발송순번(nttk_slutn_snd_seq)
     */
    private Long nttkSlutnSndSeq;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
