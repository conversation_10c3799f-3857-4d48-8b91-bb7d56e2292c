package com.ezwelesp.batch.hpas.alarm.tasklet;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.hpas.alarm.service.GoodsFlowCspDataSyncMainService;
import com.ezwelesp.batch.hpas.util.HpasUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsFlowCspDataSyncMainTasklet implements Tasklet {
    private final GoodsFlowCspDataSyncMainService goodsFlowCspDataSyncMainService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) {
        int dataCnt = 0; // 데이타 건

        try {
            log.info("#############################################");
            log.info("### [협력사 굿스플로 계약정보 동기화] 시작 일시 : " + HpasUtils.getCurrentDtm() + " ###");
            log.info("#############################################");

            dataCnt = goodsFlowCspDataSyncMainService.procGoodsFlowCspDataSyncMain();

            log.info("#############################################");
            log.info("### 데이터 총 건수 : " + dataCnt + " 건");
            log.info("### [협력사 굿스플로 계약정보 동기화] 종료 일시 : " + HpasUtils.getCurrentDtm() + " ###");
            log.info("#############################################");

        } catch (Exception e) {
            log.error("처리중 에러가 발생했습니다.", e);
        }

        return RepeatStatus.FINISHED;
    }

}
