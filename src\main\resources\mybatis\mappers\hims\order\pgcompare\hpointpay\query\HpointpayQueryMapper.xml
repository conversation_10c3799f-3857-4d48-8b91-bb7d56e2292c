<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgcompare.hpointpay.mapper.query.HpointpayQueryMapper">
	
	<select id="selectPgApproveAmtCuserDiffList" resultType="java.util.HashMap">
		with ORDER_TMP as (
			select ord.ord_no 
			     , ord.clnt_cd 
			     , ord.ord_dtm
			     , ord.asp_ord_no 
			     , ord.ord_st_cd
		    	 , case when ordGds.ch_cd = '1001' then '복지관'
				   when ordGds.ch_cd = '1002' then '복지샵'
				   when ordGds.ch_cd = '1003' then '제휴사'
				   else ordGds.ch_cd 
			       end as ch_cd
			     , ordGds.gds_nm 
			     , pymt.pg_apv_no 
			     , pymt.pymt_amt 
			     , pymt.pymt_st_cd
			     , pymt.pg_frcs_no 
			     , pymt.crdc_apv_no 
			     , pymt.pg_knd_cd
			     , pymt.pymt_dtm 
			     , pymt.pymt_mns_cd
			     , ord.acss_dvc_cd
			     , ord.cncl_dtm 
			  from ez_or.or_ord_b ord
		     inner join ez_or.or_ord_gds_d ordGds	
		        on ord.ord_no = ordGds.ord_no
		     inner join ez_or.py_pymt_b pymt
		        on ord.ord_no = pymt.ord_no
		     where pymt.pg_apv_no in (
				   select pymt.pg_apv_no 
				     from ez_or.or_ord_b ord
		            inner join ez_or.or_ord_gds_d ordGds	
		               on ord.ord_no = ordGds.ord_no
		            inner join ez_or.py_pymt_b pymt
		               on ord.ord_no = pymt.ord_no
			        where ( 
			        		ord.ord_dtm between to_char(now() + interval '-2' hour, 'YYYYMMDDHH24') || '0000' 
			                and to_char(now() + interval '-2' hour, 'YYYYMMDDHH24') || '5959'
			                and pymt.pg_knd_cd = 'H'
			                and ord.ord_st_cd in ('WAIT','ORD_CMPT')
			            ) or (
			               	ord.cncl_dtm between to_char(now() + interval '-2' hour, 'YYYYMMDDHH24') || '0000' 
			                and to_char(now() + interval '-2' hour, 'YYYYMMDDHH24') || '5959'
			                and pymt.pg_knd_cd = 'H'
			                and ord.ord_st_cd = 'CNCL'
			            )
			          and pymt.pg_knd_cd = 'H'
		       	 	  and ord.ord_no not like '52%'
		              and ord.ord_no not like '50%'
		              and pymt.pymt_mns_cd in ('1002', '1003')
		         )
		       and ord.ord_dtm <![CDATA[ <= ]]> to_char(now() + interval '-2' hour, 'YYYYMMDDHH24') || '5959'
		     
		), ORDER_DATA as (
		   select tmp.ord_no
		   	    , tmp.clnt_cd
		   	    , tmp.ord_dtm
		   	    , tmp.asp_ord_no
		   	    , tmp.pymt_amt
		   	    , (case when tmp.ord_st_cd in ('CNCL') then
		   	    		(case when tmp.cncl_dtm > to_char(now() + interval '-2' hour, 'YYYYMMDDHH24') || '5959' then 'ORD_CMPT' 
		   	    		 else tmp.ord_st_cd end )
		   		  else tmp.ord_st_cd
		   		  end) as ord_st_cd
		   		, tmp.ch_cd
		   		, tmp.gds_nm
		   		, tmp.pg_apv_no as cancel_tid
		   		, tmp.pymt_mns_cd
		   		, tmp.pg_frcs_no
		   		, tmp.pg_knd_cd
		   		, tmp.crdc_apv_no
		   		, tmp.acss_dvc_cd
		   		, row_number() over (partition by tmp.pg_apv_no order by tmp.pymt_dtm asc) num
		     from ORDER_TMP tmp
		), PG_TARGET_TMP as (
		   select toss_key as tid
		        , authcode as cancel_tid
		        , case when pay_type = 'A' then 'I' else 'C' end as tmp_status
		        , confirm_dt as dt
		        , pay_amt as deal_amt
		        , 'H' as pg_type
		        , '' as goods_nm
		        , pay_method as pay_type
		        , '' as device_type
		        , toss_order_num as shop_order_num
		        , '' as confirm_num
		        , store_id
		     from ez_if.ez_ec_hpointpay hpay
		    where AUTHCODE in (
		    	  select distinct cancel_tid from ORDER_DATA
		    )
		), PG_DATA as (
		   select T.*
	            , case when tmp_status = 'C' and real_amt > 0 then 'P' else tmp_status end as status
	         from (
				  select (
				  	     select sum(case when pay_type = 'A' then pay_amt else pay_amt * -1 end)
				           from ez_if.ez_ec_hpointpay h
				          where (authcode = A.cancel_tid)
				            and confirm_dt <![CDATA[ <= ]]> A.dt
				       ) as real_amt
				       , A.*
				    from PG_TARGET_TMP A
	            ) T order by dt
		)
		
		select '1000' as errType
		     , A.ord_no as orderNum
		     , A.clnt_cd as clientCd
		     , A.ord_st_cd as orderStatus 
		     , A.asp_ord_no as aspOrderNum
		     , A.ch_cd as orderType
		     , A.pymt_amt as recgPrice
		     , coalesce(B.real_amt, '-1') as pgAmt
		     , coalesce(B.goods_nm, A.gds_nm) as goodsNm
		     , coalesce(B.pg_type, A.pg_knd_cd) as pgType
		     , coalesce(B.pay_type, A.pymt_mns_cd) as payType
		     , coalesce(B.store_id, A.pg_frcs_no) as storeId
		     , coalesce(B.dt, (select ord_dtm from ORDER_DATA where cancel_tid = A.cancel_tid and num = 1)) as pgDate
		     , coalesce(B.cancel_tid, A.cancel_tid) as cancelTid
		     , coalesce(B.confirm_num, a.crdc_apv_no) as cardConfirmNum
		     , B.shop_order_num as shopOrderNum
		     , coalesce(B.device_type, a.acss_dvc_cd) as deviceType
		  from ORDER_DATA A
		  left outer join PG_DATA B
		    on A.cancel_tid = B.cancel_tid
		   and A.pymt_amt = B.real_amt
		 where B.cancel_tid is null
		 
		 union 
		
		select '1005' as errType
		     , A.ord_no as orderNum
		     , A.clnt_cd as clientCd
		     , A.ord_st_cd as orderStatus
		     , A.asp_ord_no as aspOrderNum
		     , A.ch_cd as orderType
		     , A.pymt_amt as recgPrice
		     , coalesce(B.real_amt, '-1') as pgAmt
		     , coalesce(B.goods_nm, A.gds_nm) as goodsNm
		     , coalesce(B.pg_type, A.pg_knd_cd) as pgType
		     , coalesce(B.pay_type, A.pymt_mns_cd) as payType
		     , coalesce(B.store_id, A.pg_frcs_no) as storeId
		     , coalesce(B.dt, (select ord_dtm from ORDER_DATA where cancel_tid = A.cancel_tid and num = 1)) as pgDate
		     , coalesce(B.cancel_tid, A.cancel_tid) as cancelTid
		     , coalesce(B.confirm_num, a.crdc_apv_no) as cardConfirmNum
		     , B.shop_order_num as showOrderNum
		     , coalesce(B.device_type, a.acss_dvc_cd) as deviceType
		  from ORDER_DATA A
		  left outer join PG_DATA B
		    on A.cancel_tid = B.cancel_tid
		   and B.real_amt = 0
		 where B.cancel_tid is null
		   and A.ord_st_cd = '1004'
	</select>
	<select id="selectPgApproveAmtInicisDiffList" resultType="java.util.HashMap">
		WITH PG_TARGET_TMP AS (
			select 
			    tt.*,
			    row_number() over (partition by cancel_tid order by dt asc) num
			from (
			    select
			        toss_key as tid,
			        authcode as cancel_tid,
			        case when pay_type = 'A' then 'I' else 'C' end as tmp_status,
			        confirm_dt as dt,
			        pay_amt as deal_amt,
			        'H' as pg_type,
			        '' as goods_nm,
			        pay_method as pay_type,
			        '' as device_type,
			        toss_order_num as shop_order_num,
			        '' as confirm_num,
			        store_id
			    from ez_if.ez_ec_hpointpay
			    where
			    	authcode in (
			    		select authcode from ez_if.ez_ec_hpointpay 
						where confirm_dt between to_char(now() + interval '-2' hour, 'YYYYMMDDHH24') || '0000'
			           		and to_char(now() + interval '-2' hour, 'YYYYMMDDHH24') || '5959'
			    	)
			) tt
		), TARGET_DT AS (
				    
		    SELECT 
		        to_char(to_date(min(dt), 'YYYYMMDDHH24MISS') + interval '-1' day, 'YYYYMMDDHH24MISS') as min_dt, 
		        to_char(to_date(max(dt), 'YYYYMMDDHH24MISS') + interval '1' day, 'YYYYMMDDHH24MISS') as max_dt
		    from PG_TARGET_TMP
		    
		), PG_DATA AS (
		    select 
		        t.*,
		        case when tmp_status = 'C' and real_amt > 0 then 'P' else tmp_status end as status
		    from (
		        select (
		            select
		                sum(case when pay_type = 'A' then pay_amt else pay_amt * -1 end) 
		            from ez_if.ez_ec_hpointpay
		            where 
		                (authcode = a.cancel_tid)
		                and confirm_dt  <![CDATA[ <= ]]>  a.dt
		        ) as real_amt, a.* 
		        from PG_TARGET_TMP a
		   ) t
		), ORDER_DATA AS (
			select T.*
			     , row_number() over (partition by pg_apv_no order by ord_no desc) as num
			  from (
				   select distinct ord.ord_no
					    , ord.clnt_cd 
						, ord.asp_ord_no
						, ord.ord_st_cd
						, pymt.pymt_amt
						, pymt.pg_frcs_no 
						, pymt.pg_apv_no
						, pymt.pymt_st_cd 
						, ordGds.gds_nm
						, case when ordGds.ch_cd = '1001' then '복지관'
						  when ordGds.ch_cd = '1002' then '복지샵'
						  when ordGds.ch_cd = '1003' then '제휴사'
						else ordGds.ch_cd end as target
					 from ez_or.or_ord_b ord
				    inner join ez_or.or_ord_gds_d ordGds	
				       on ord.ord_no = ordGds.ord_no
				    inner join ez_or.py_pymt_b pymt
				       on ord.ord_no = pymt.ord_no
				    where ord.ord_dtm between (select min_dt from TARGET_DT) AND (select max_dt from TARGET_DT)
				      and ordGds.ch_cd not in ('1008', '1009') /*마켓제외*/
				      and pymt.pg_apv_no in (
				          select cancel_tid from PG_TARGET_TMP
				        )
				 ) T
		)
		
		
		
		select A.error_code as errType
		     , coalesce(C.ord_no, '9999') as orderNum
		     , C.clnt_cd as clientCd
		     , C.ord_st_cd as orderStatus
		     , C.asp_ord_no as aspOrderNum
		     , C.target as orderType
		     , C.pymt_amt as recgPrice
		     , case when (select count(*) from pg_data where cancel_tid = a.cancel_tid) = 0 then '-1'
			 		else (select sum(case when status = 'I' then deal_amt else deal_amt * -1 end) from pg_data where cancel_tid = a.cancel_tid)
			 end as pgAmt
			 , coalesce(C.gds_nm, A.goods_nm) as goodsNm
			 , A.pg_type as pgType
			 , A.pay_type as payType
			 , A.store_id as storeId
			 , B.dt as pgDate
			 , A.cancel_tid as cancelTid
			 , A.confirm_num as cardConfirmNum
			 , A.shop_order_num as shopOrderNum
			 , A.device_type as deviceType
		  from (
				select A.*
				     , '1001' as error_code
				     , 'ERROR' as order_status
				     , '' as order_num
				     , '' as asp_order_num
				     , '' as order_type
				  from PG_DATA A
				 where status = 'I'
				   and not exists (
				       select 1 
				         from ORDER_DATA
				        where cancel_tid = a.cancel_tid 
				     )
				   and (
				       select sum(case when status = 'i' then deal_amt else deal_amt * -1 end) 
				         from PG_DATA
				        where cancel_tid = a.cancel_tid
				     ) != 0
				union 
				select A.*
				     , '1002' as error_code
				     , 'ERROR' as order_status     
					 , '' as order_num
					 , '' as asp_order_num
					 , '' as order_type
				  from PG_DATA A
				 where status = 'I'
				   and exists (
				   	   select 1 
				   	     from ORDER_DATA
				   	    where pg_apv_no = A.cancel_tid
				     )
				   and not exists(
				       select 1
				         from ORDER_DATA
				        where pg_apv_no = A.cancel_tid
				          and pymt_amt = A.real_amt
				     )
				union 
				select A.*
				     , '1003' as error_code
				     , 'ERROR' as order_status
				     , '' as order_num
				     , '' as asp_order_num
				     , '' as order_type
				  from PG_DATA A
				 where status = 'P'
				   and not exists (
				   	   select 1 
				   	     from ORDER_DATA
				   	    where pg_apv_no = A.cancel_tid
				   	      and pymt_amt = A.real_amt
				   	 )
				   and cancel_tid not in (
				       select cancel_tid
				         from PG_DATA A
				        where status = 'I'
				          and not exists(
				          	  select 1
				          	    from ORDER_DATA
				          	   where pg_apv_no = A.cancel_tid
				            )
				       union 
				       select cancel_tid
				         from PG_DATA A
				        where status = 'I'
				          and exists (
				          	  select 1
				          	    from ORDER_DATA
				          	   where pg_apv_no = A.cancel_tid
				            )
				          and not exists(
				          	  select 1
				          	    from ORDER_DATA
				          	   where pg_apv_no = A.cancel_tid
				          	     and pymt_amt = A.real_amt
				            )
				      )
				union 
				select A.*
				     , '1004' as error_code
				     , 'ERROR' as order_status
				     , '' as order_num
				     , '' as asp_order_num
				     , '' as order_type
				  from PG_DATA A
				 where status = 'C'
				   and not exists (
				       select 1 
				         from ORDER_DATA
				        where pg_apv_no = A.cancel_tid
				          and pymt_amt = deal_amt + (select (sum(case when status = 'I' then deal_amt else deal_amt * -1 end)) from PG_DATA where cancel_tid = A.cancel_tid)
				          and ord_st_cd = '1003'
				     ) 
				   and cancel_tid not in (
				   	   select cancel_tid
				   	     from PG_DATA
				   	    where status = 'I'
				   	      and not exists (
				   	      	  select 1
				   	      	    from ORDER_DATA
				   	      	   where pg_apv_no = A.cancel_tid
				   	        )
				   	   union 
				   	   select cancel_tid
				   	     from PG_DATA A
				   	    where status = 'I'
				   	      and exists(
				   	          select 1
				   	            from ORDER_DATA
				   	           where pg_apv_no = A.cancel_tid
				   	        )
				   	      and not exists(
				   	          select 1
				   	            from ORDER_DATA
				   	           where pg_apv_no = A.cancel_tid
				   	             and pymt_amt = A.real_amt
				   	        )
				   	   union 
				   	   select cancel_tid 
				   	     from PG_DATA A
				   	    where status = 'P'
				   	      and not exists(
				   	      	  select 1
				   	      	    from ORDER_DATA
				   	      	   where cancel_tid = A.cancel_tid
				   	      	     and pymt_amt = A.real_amt
				   	      )
				     ) 
		      ) A
		  inner join PG_DATA B
		     on A.cancel_tid = B.cancel_tid
		   left outer join ORDER_DATA C
		     on B.cancel_tid = C.pg_apv_no 
		    and C.num = 1
	
	</select>

</mapper>