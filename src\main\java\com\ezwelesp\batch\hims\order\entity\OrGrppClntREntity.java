package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 공동구매고객사관계(ez_or.or_grpp_clnt_r)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrGrppClntREntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 공동구매코드(grpp_cd) not null
     */
    private String grppCd;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
