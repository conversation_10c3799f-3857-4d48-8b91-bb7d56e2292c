package com.ezwelesp.batch.hpas.alarm.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class EzMemberStoreBenefitDto {
    private String crdSaleNo;
    private String crdApvNo;
    private String useDt;
    private String crdcFrcsBiztpCd;
    private String crdcFrcsBiztpNm;
    private String crdcFrcsNo;
    private String crdcFrcsNm;
    private String ezmbrsBrndNm;
    private String orglCrdSaleNo;
    private String cnclYn;
    private String expsYn;
    private String frcsIntlStCd;
    private String count;
}
