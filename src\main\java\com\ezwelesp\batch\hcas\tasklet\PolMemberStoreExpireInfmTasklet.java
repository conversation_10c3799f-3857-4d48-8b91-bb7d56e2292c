package com.ezwelesp.batch.hcas.tasklet;

import java.io.InputStream;
import java.util.List;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.hcas.domain.dto.PolMemberStoreExpireInfmDto;
import com.ezwelesp.batch.hcas.mapper.query.PolMemberStoreExpireInfmQueryMapper;
import com.ezwelesp.batch.hcas.util.HcasUtils;
import com.ezwelesp.batch.hcas.util.TemplateParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class PolMemberStoreExpireInfmTasklet implements Tasklet {
    private final PolMemberStoreExpireInfmQueryMapper polMemberStoreExpireInfmQueryMapper;

    private static final String MAIL_TEMPLATE_FILE = "polbokji_member_store_expire_mail_template.html";
    private static final String FROM_EMAIL_ADDR = "<EMAIL>";
    private static final String FROM_EMAIL_SENDER_NM = "현대이지웰";

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        List<PolMemberStoreExpireInfmDto> dataList = polMemberStoreExpireInfmQueryMapper.getMemberStoreList();

        String startDt = HcasUtils.getCurrentDt();
        int resultCnt = 0;

        if (dataList.size() > 0) {
            // 매핑
            for (PolMemberStoreExpireInfmDto mailBean : dataList) {
                // 메일 내용 세팅
                mailBean.setTemplateFileNm(MAIL_TEMPLATE_FILE);
                mailBean.setSubject("[현대이지웰] 경찰청 폴복지 " + mailBean.getFrcsNm() + " 경찰제휴할인 혜택기간이 종료됩니다.");

                // 날짜 포맷 변환
                mailBean.setBnftAplyStrtDt(HcasUtils.dateFormat(mailBean.getBnftAplyStrtDt()));
                mailBean.setBnftAplyEndDt(HcasUtils.dateFormat(mailBean.getBnftAplyEndDt()));
                System.out.println("benefitStartDt : " + mailBean.getBnftAplyStrtDt());
                System.out.println("benefitEndDt : " + mailBean.getBnftAplyEndDt());

                // 1차 발송 (to. 관리자 계정)
                if (!"".equals(mailBean.getMailReceiver())) {
                    System.out.println("[sendMail] (send1) to. " + mailBean.getMailReceiver());
                    resultCnt += sendMail(mailBean);
                }

                // 2차 발송 (to. <EMAIL>)
                mailBean.setMailReceiver("<EMAIL>");
                System.out.println("[sendMail] (send2) to. " + mailBean.getMailReceiver());
                resultCnt += sendMail(mailBean);

                // 3차 발송 -> 개발자 수신 확인용으로 수동 추가 ///////
                mailBean.setMailReceiver("<EMAIL>");
                System.out.println("[sendMail] (send3) to. " + mailBean.getMailReceiver());
                resultCnt += sendMail(mailBean);
                ////////////////////////////////////////////
            }
        }

        return RepeatStatus.FINISHED;
    }


    private int sendMail(PolMemberStoreExpireInfmDto mailBean) throws Exception {
        try {
            if (mailBean == null) {
                System.out.println("sendMail() FAIL!!");
            }

            //System.out.println("SERVER TYPE ====> " + serverType);

            System.out.println("[sendMail] title : " + mailBean.getSubject());
            System.out.println("[sendMail] to. " + mailBean.getMailReceiver());

            System.out.println("[sendMail] from. " + FROM_EMAIL_ADDR);

            InputStream is =
                    this.getClass().getResourceAsStream("../util/mailTemplate/" + mailBean.getTemplateFileNm());

            int rCnt = -1;
            byte[] buffer = new byte[1024];

            StringBuffer templateStr = new StringBuffer();
            while ((rCnt = is.read(buffer)) != -1) {
                templateStr.append(new String(buffer, 0, rCnt));
            }
            is.close();

            if (templateStr.length() > 0) {
                String mailStr = TemplateParser.mailParse(templateStr.toString(), mailBean);

                System.out.println("sendMail_template__" + mailStr);

                /*
                 * 20250404 이승도 : 메일발송 기능 임시 주석 처리
                Email email = new Email();
                email.setFrom(FROM_EMAIL_SENDER_NM + "<" + FROM_EMAIL_ADDR + ">");
                email.setTo(mailBean.getMailReceiver());
                email.setSubject(mailBean.getSubject());
                
                email.setHtmlMessage(mailStr);
                mailSendAction(email);
                */
                return 1;
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            System.out.println("sendMail FAIL!!");
        }

        return 0;
    }
}
