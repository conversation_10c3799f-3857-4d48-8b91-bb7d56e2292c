package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgUpdateProductImageRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

import static com.ezwelesp.batch.lgportal.product.service.LgProductUtils.isValidProductWithImage;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalEditProductImageHandler implements LgPortalEditProductProcessorHandler {

    private final LgApiClient lgApiClient;

    @Override
    public LgProductApiResponseDto handle(LgEditProductTargetVo item) {
        // 이미지가 S3 상에 정상적이지 않을 경우
        if (!isValidProductWithImage(item.getImgPath())) {
            return null;
        }
        val imageUrl = "http://org-img.ezwelfare.net/welfare_shop" + item.getImgPath();
        val requestDto = LgUpdateProductImageRequestDto.builder()
                .productNo(item.getProductNo())
                .mainImageType("I")
                .mainImageUrl(imageUrl)
                .build();

        val responseDto = lgApiClient.sendLgUpdateProductImageApi(
                requestDto,
                item.getGdsCd(),
                item.getApiSeq()
        );

        if (responseDto != null && !"200".equals(responseDto.getResult())) {
            log.debug("sendLgUpdateProductImageApi Fail : {},{}", item.getGdsCd(), responseDto.getResult());
        }
        return responseDto;
    }

}
