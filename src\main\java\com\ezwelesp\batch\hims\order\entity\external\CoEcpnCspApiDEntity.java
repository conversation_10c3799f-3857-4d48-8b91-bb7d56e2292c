package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * E쿠폰협력사API상세
 * co_ecpn_csp_api_d
 */
@Jacksonized
@Getter
@SuperBuilder
public class CoEcpnCspApiDEntity implements Serializable {
    /**
     * 협력사코드
     */
    private String cspCd;

    /**
     * 사용API명
     */
    private String useApiNm;

    /**
     * E쿠폰APIURL
     */
    private String ecpnApiUrl;

    /**
     * E쿠폰API포트
     */
    private String ecpnApiPort;

    /**
     * 시드암호화고유번호
     */
    private String seedEncInntNo;

    /**
     * 상품이미지APIURL
     */
    private String gdsImgApiUrl;

    /**
     * 상품이미지API포트
     */
    private String gdsImgApiPort;

    /**
     * E쿠폰발송URL
     */
    private String ecpnSndUrl;

    /**
     * E쿠폰재발송URL
     */
    private String ecpnRsndUrl;

    /**
     * E쿠폰발급취소URL
     */
    private String ecpnIssuCnclUrl;

    /**
     * E쿠폰기간변경URL
     */
    private String ecpnTermChgUrl;

    /**
     * E쿠폰발급안내URL
     */
    private String ecpnIssuGdUrl;

    /**
     * E쿠폰기간연장가능여부
     */
    private String ecpnTermExtPossYn;

    /**
     * E쿠폰발송확인대상여부
     */
    private String ecpnSndCnftObjYn;

    /**
     * 발송결과재확인URL
     */
    private String sndRsltRcnfUrl;

    /**
     * 최초등록일시
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID
     */
    private String lastModPgmId;

    private static final long serialVersionUID = 1L;
}
