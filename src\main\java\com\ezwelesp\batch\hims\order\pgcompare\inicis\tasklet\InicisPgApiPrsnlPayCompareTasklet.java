package com.ezwelesp.batch.hims.order.pgcompare.inicis.tasklet;

import com.ezwelesp.batch.hims.order.pgcompare.inicis.service.ApiPgPrsnlPayCompareService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class InicisPgApiPrsnlPayCompareTasklet implements Tasklet {

    private final ApiPgPrsnlPayCompareService apiPgPrsnlPayCompareService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {


            log.debug("!######################## InicisPgApiPrsnlPayCompareTasklet method start");
            apiPgPrsnlPayCompareService.connectInicisApiPgPrsnlPayCompareData();
            log.debug("!######################## InicisPgApiPrsnlPayCompareTasklet method start2");

        } catch (Exception e) {
            log.error("InicisPgApiPrsnlPayCompareTasklet Failed: {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }


        log.debug("!######################## InicisPgApiPrsnlPayCompareTasklet method finish");

        return RepeatStatus.FINISHED;
    }
}
