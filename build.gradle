buildscript {
	ext {
		nexusBaseUrl = 'https://nexus.ezwel.com'

		javaVersion = '17'
		springBootVersion = '3.2.0'
		springBootPluginManagementVersion = '1.1.6'
		springBatchVersion = '3.2.10'
		springBatchTestVersion = '3.0.10'
		lombokVersion = '1.18.34'
		ezwelFwModuleVersion = '1.1.14-heg015-SNAPSHOT'
        ezwelBizComModuleVersion = '0.0.1-SNAPSHOT'
	}
	
	// plugin repo 변경.
    repositories {
        maven {
            url "https://nexus.ezwel.com/repository/gradle-plugin-repo/"
            allowInsecureProtocol = true 
        } 
    }
}

plugins {
	id 'java'
	id 'org.springframework.boot' version "${springBootVersion}"
	id 'io.spring.dependency-management' version "${springBootPluginManagementVersion}"
}

ext {
    profile = project.hasProperty("profile") ? project.getProperty("profile") : "dev"
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

jar {
	enabled = false
}

bootJar {
	archivesBaseName = 'esp_batch'
	archiveFileName=project.name.replace('_', '-') + ".jar"
}

group = 'com.ezwelesp'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = javaVersion
targetCompatibility = javaVersion

dependencies {
	implementation("com.ezwelesp.framework:esp-common-module:${ezwelFwModuleVersion}") {
	    changing = true
	}
    implementation ("com.ezwelesp:esp-biz-common-module:${ezwelBizComModuleVersion}") {
	    changing = true
	}
    
	implementation "org.springframework.boot:spring-boot-starter-batch:${springBatchVersion}"
	implementation "org.springframework.boot:spring-boot-starter-jdbc:${springBootVersion}"
	implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'
	implementation 'org.postgresql:postgresql:latest.integration'
	implementation 'org.projectlombok:lombok:1.18.26'
	
	implementation 'p6spy:p6spy:3.9.1'
    implementation 'org.apache.logging.log4j:log4j-core:2.20.0'
    implementation 'org.apache.logging.log4j:log4j-slf4j-impl:2.20.0'    
    annotationProcessor 'org.apache.logging.log4j:log4j-core:2.20.0'

	annotationProcessor 'org.projectlombok:lombok:1.18.26'

	testImplementation "org.springframework.boot:spring-boot-starter-test:${springBatchVersion}"
	testImplementation "org.springframework.batch:spring-batch-test:5.0.2"
	testAnnotationProcessor 'org.projectlombok:lombok:1.18.26'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher:latest.integration'

	// Apache Commons Lang (필요시)
	implementation 'org.apache.commons:commons-lang3:3.12.0' // 최신 버전 확인

	implementation "org.apache.commons:commons-collections4:4.4"

	implementation "com.google.zxing:core:3.2.1"
	implementation "com.google.zxing:javase:3.2.1"
	implementation "jakarta.xml.bind:jakarta.xml.bind-api:3.0.1"
	implementation "org.glassfish.jaxb:jaxb-runtime:3.0.2"
    implementation "com.h2database:h2:2.3.232"
}

configurations {
	all {
		exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
	}
}

repositories {
    mavenLocal() // 로컬 캐시 저장소
	maven {
        url "${nexusBaseUrl}/repository/maven-public/"
        allowInsecureProtocol = true    // http 허용, https로 변경하면 필요없음.
	}
}

processResources {
    from("scripts/${profile}") {
    	into "scripts/${profile}"
    }
}
