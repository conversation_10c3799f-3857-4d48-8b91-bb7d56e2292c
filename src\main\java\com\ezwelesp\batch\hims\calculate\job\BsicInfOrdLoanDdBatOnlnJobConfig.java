package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.tasklet.BsicInfOrdLoanDdBatOnlnTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatOnlnJobConfig {
    private final CommonJobListener commonJobListener;
    private final BsicInfOrdLoanDdBatOnlnTasklet bsicInfOrdLoanDdBatOnlnTasklet;

    /**
     * 온라인 정산 데이터 이관 일배치
     * 주기 : TODO 매일 오전 3시 0분 0초 (AS-IS 기준)
     * 설명 : 온라인 정산 데이터 이관 일배치
     * 
     * @param jobRepository
     * @param bsicInfOrdLoanDdBatOnlnTaskletStep
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatOnlnJob")
    Job bsicInfOrdLoanDdBatOnlnJob(
            JobRepository jobRepository,
            Step bsicInfOrdLoanDdBatOnlnTaskletStep
    ) {
        return new JobBuilder("bsicInfOrdLoanDdBatOnlnJob", jobRepository)
                .listener(commonJobListener)
                .start(bsicInfOrdLoanDdBatOnlnTaskletStep) // 온라인 정산 데이터 이관 프로시저 호출 Step TODO 개발필요
                .build();
    }

    /**
     * 온라인 정산 데이터 이관 프로시저 호출
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatOnlnTaskletStep")
    Step bsicInfOrdLoanDdBatOnlnTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("bsicInfOrdLoanDdBatOnlnTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(bsicInfOrdLoanDdBatOnlnTasklet, transactionManager)
                .build();
    }
}