package com.ezwelesp.batch.hims.order.delivery.mapper.query;

import com.ezwelesp.batch.hims.order.delivery.domain.DeliveryDelayDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeliveryDelayQueryMapper {
    // 배송지연대상 목록 조회
    List<DeliveryDelayDto> selectDeliveryDelayTargetList();

    // 배송예정지연대상 목록 조회
    List<DeliveryDelayDto> selectDeliveryDueDelayTargetList();

    // 영업일수 조회
    int selectBusinessDayCnt(@Param("cspCd") String cspCd, @Param("startDate") String startDate,
                             @Param("satDlvPossYn") String satDlvPossYn, @Param("isFriHoliday") boolean isFriHoliday);
}


