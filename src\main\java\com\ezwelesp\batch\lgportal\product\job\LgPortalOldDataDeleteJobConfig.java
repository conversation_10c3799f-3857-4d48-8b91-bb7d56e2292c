package com.ezwelesp.batch.lgportal.product.job;


import com.ezwelesp.batch.lgportal.product.tasklet.LgPortalJobOldDataDeleteTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;


/**
 * LG 연동 데이터 과거 내역 삭제 배치
 */
@Configuration
public class LgPortalOldDataDeleteJobConfig {
    private final LgPortalJobOldDataDeleteTasklet lgPortalJobOldDataDeleteTasklet;

    public LgPortalOldDataDeleteJobConfig(
            @Qualifier("lgPortalJobOldDataDeleteTasklet")
            LgPortalJobOldDataDeleteTasklet lgPortalJobOldDataDeleteTasklet
    ) {
        this.lgPortalJobOldDataDeleteTasklet = lgPortalJobOldDataDeleteTasklet;
    }

    @Bean("BA_HIPO00029")
    public Job lgPortalOldDataDeleteJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPO00029_STEP") Step lgPortalOldDataDeleteStep
    ) {
        return new JobBuilder("BA_HIPO00029", jobRepository)
                .start(lgPortalOldDataDeleteStep)
                .build();
    }

    @Bean("BA_HIPO00029_STEP")
    public Step lgPortalOldDataDeleteStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
    ) {
        return new StepBuilder("BA_HIPO00029_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(lgPortalJobOldDataDeleteTasklet, transactionManager)
                .build();
    }
}
