package com.ezwelesp.batch.lgportal.product.job;


import com.ezwelesp.batch.lgportal.product.chunk.processor.LgPortalEditProductProcessor;
import com.ezwelesp.batch.lgportal.product.chunk.reader.LgPortalEditProductPriceReader;
import com.ezwelesp.batch.lgportal.product.chunk.writer.LgPortalEditProductWriter;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;



/**
 * LG_API 상품 가격변동 수정
 */
//Scheduled(cron = "00 */5 * * * *") 매 5분마다 실행
// 스케쥴 시간 및 조회 내역만 다를뿐, lg api 전송하는 방식은 동일하여,
// reader 까지만 수정하고, 그 뒤의 로직은 공통 Processor 를 타도록 개발 하였으나, 헨들러는 추가 개발함.

@Configuration
public class LgPortalEditProductPriceJobConfig {
    private final int CHUNK_SIZE = 1000;

    @Bean("BA_HIPO00032")
    public Job lgPortalEditProductPriceJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPO00032_STEP") Step lgPortalEditProductPriceStep
    ) {
        return new JobBuilder("BA_HIPO00032", jobRepository)
                .start(lgPortalEditProductPriceStep)
                .build();
    }

    @Bean("BA_HIPO00032_STEP")
    public Step lgPortalEditProductPriceStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
            , LgPortalEditProductPriceReader lgPortalEditProductPriceReader
            , LgPortalEditProductProcessor lgPortalEditProductProcessor
            , LgPortalEditProductWriter lgPortalEditProductWriter
    ) {
        return new StepBuilder("BA_HIPO00032_STEP", jobRepository)
                .allowStartIfComplete(true)
                .<LgEditProductTargetVo, LgProductApiResponseDto>chunk(CHUNK_SIZE, transactionManager)
                .reader(lgPortalEditProductPriceReader)
                .processor(lgPortalEditProductProcessor)
                .writer(lgPortalEditProductWriter)
                .build();

    }
}
