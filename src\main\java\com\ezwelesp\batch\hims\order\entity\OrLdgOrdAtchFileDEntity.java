package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * [2차]숙박주문첨부파일상세(ez_or.or_ldg_ord_atch_file_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrLdgOrdAtchFileDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 숙박주문지원차수(ldg_ord_afe_nos) not null
     */
    private Long ldgOrdAfeNos;

    /**
     * 첨부파일순번(atch_file_seq) not null
     */
    private Long atchFileSeq;

    /**
     * 삭제여부(del_yn) not null
     */
    private String delYn;

    /**
     * 첨부파일명(atch_file_nm)
     */
    private String atchFileNm;

    /**
     * 첨부파일경로(atch_file_path)
     */
    private String atchFilePath;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
