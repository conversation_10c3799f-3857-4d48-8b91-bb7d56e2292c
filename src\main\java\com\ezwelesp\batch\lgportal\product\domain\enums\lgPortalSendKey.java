package com.ezwelesp.batch.lgportal.product.domain.enums;

import lombok.Getter;

@Getter
public enum lgPortalSendKey {
    BASIC_INFO(1001),      // 1001 : 상품 기본정보 수정
    CONTENTS(1003),       // 1003 : 상품 컨텐츠 수정
    ANNOUNCEMENT(1006),   // 1006 : 상품 고시정보 수정
    DELIVERY(1007),       // 1007 : 상품 배송 및 교환반품 정보 수정
    IMAGE(1002),          // 1002 : 상품 이미지 수정
    QNA_ANSWER(1015),      // 1015 : 상품 QnA 답변 등록

    PRICE(1005),          // 1005 : 상품 가격 정보 수정

    SOLD_OUT(1012),        // 1012 : 상품 품절 처리 // 품절처리 배치
    SALE(1013),            // 1013 : 상품 판매 처리 // 품절처리 배치

    SKU_SOLD_OUT(1014),      // 1014 : SKU 품절, 상품에서는 사용 X이나 asis db에 있어서.
    ;

    private final int description;

    lgPortalSendKey(int description) {
        this.description = description;
    }

    public static lgPortalSendKey fromCode(int code) {
        for (lgPortalSendKey key : lgPortalSendKey.values()) {
            if (key.getDescription() == code) {
                return key;
            }
        }
        throw new IllegalArgumentException("Invalid code for lgPortalSendKey: " + code);
    }

}
