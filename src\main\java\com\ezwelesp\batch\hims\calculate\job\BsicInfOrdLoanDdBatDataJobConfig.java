package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.tasklet.BsicInfOrdLoanDdBatDataTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatDataJobConfig {
    private final CommonJobListener commonJobListener;
    private final BsicInfOrdLoanDdBatDataTasklet bsicInfOrdLoanDdBatDataTasklet;

    /**
     * 데이터 이관 처리 일배치
     * 주기 : TODO 매일 오전 3시 0분 0초 (AS-IS 기준)
     * 설명 : 데이터 이관 처리 일배치
     * 
     * @param jobRepository
     * @param bsicInfOrdLoanDdBatDataTaskletStep
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatDataJob")
    Job bsicInfOrdLoanDdBatDataJob(
            JobRepository jobRepository,
            Step bsicInfOrdLoanDdBatDataTaskletStep
    ) {
        return new JobBuilder("bsicInfOrdLoanDdBatDataJob", jobRepository)
                .listener(commonJobListener)
                .start(bsicInfOrdLoanDdBatDataTaskletStep) // 데이터 이관 처리 프로시저 호출 Step TODO 개발필요
                .build();
    }

    /**
     * 데이터 이관 처리 프로시저 호출
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("bsicInfOrdLoanDdBatDataTaskletStep")
    Step bsicInfOrdLoanDdBatDataTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("bsicInfOrdLoanDdBatDataTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(bsicInfOrdLoanDdBatDataTasklet, transactionManager)
                .build();
    }
}