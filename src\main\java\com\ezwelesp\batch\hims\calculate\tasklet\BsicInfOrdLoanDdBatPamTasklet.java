package com.ezwelesp.batch.hims.calculate.tasklet;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.calculate.mapper.command.BsicInfOrdLoanDdBatCommandMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class BsicInfOrdLoanDdBatPamTasklet implements Tasklet {
    private final BsicInfOrdLoanDdBatCommandMapper bsicInfOrdLoanDdBatCommandMapper;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        
        bsicInfOrdLoanDdBatCommandMapper.callBsicInfOrdLoanDdBatPamProc();
        
        return RepeatStatus.FINISHED;
    }
}