package com.ezwelesp.batch.hims.calculate.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.calculate.chunk.processor.EzmktB2cSaleInfMmBatDtlInsrChunkItemProcessor;
import com.ezwelesp.batch.hims.calculate.chunk.processor.EzmktB2cSaleInfMmBatDtlUpdChunkItemProcessor;
import com.ezwelesp.batch.hims.calculate.chunk.reader.EzmktB2cSaleInfMmBatDtlInsrChunkItemReader;
import com.ezwelesp.batch.hims.calculate.chunk.reader.EzmktB2cSaleInfMmBatDtlUpdChunkItemReader;
import com.ezwelesp.batch.hims.calculate.chunk.writer.EzmktB2cSaleInfMmBatDtlInsrChunkItemWriter;
import com.ezwelesp.batch.hims.calculate.chunk.writer.EzmktB2cSaleInfMmBatDtlUpdChunkItemWriter;
import com.ezwelesp.batch.hims.calculate.domain.dto.EzmktB2cSaleInfMmBatDto;
import com.ezwelesp.batch.hims.calculate.tasklet.EzmktB2cSaleInfMmBatDtlDelTasklet;
import com.ezwelesp.batch.hims.calculate.tasklet.EzmktB2cSaleInfMmBatMstDelTasklet;
import com.ezwelesp.batch.hims.calculate.tasklet.EzmktB2cSaleInfMmBatMstInsrTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import com.ezwelesp.batch.listener.CommonStepListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class EzmktB2cSaleInfMmBatJobConfig {
    private final CommonJobListener commonJobListener;
    private final EzmktB2cSaleInfMmBatDtlDelTasklet ezmktB2cSaleInfMmBatDtlDelTasklet;
    private final EzmktB2cSaleInfMmBatMstDelTasklet ezmktB2cSaleInfMmBatMstDelTasklet;
    private final EzmktB2cSaleInfMmBatMstInsrTasklet ezmktB2cSaleInfMmBatMstInsrTasklet;
    
    private final int CHUNK_SIZE = 1000;
    
    /**
     * 마켓B2C 매출 정보 월배치
     * 주기 : TODO 매월 1일 오전 1시 0분 0초 (AS-IS 기준)
     * 설명 : 마켓B2C 기준정보 테이블 집계하여 정산 데이터 생성
     * 
     * @param jobRepository
     * @param ezmktB2cSaleInfMmBatDtlDelTaskletStep
     * @param ezmktB2cSaleInfMmBatMstDelTaskletStep
     * @param ezmktB2cSaleInfMmBatDtlInsrChunkStep
     * @param ezmktB2cSaleInfMmBatDtlUpdChunkStep
     * @param ezmktB2cSaleInfMmBatMstInsrTaskletStep
     * @return
     */
    @Bean("ezmktB2cSaleInfMmBatJob")
    Job ezmktB2cSaleInfMmBatJob(
            JobRepository jobRepository,
            Step ezmktB2cSaleInfMmBatDtlDelTaskletStep,
            Step ezmktB2cSaleInfMmBatMstDelTaskletStep,
            Step ezmktB2cSaleInfMmBatDtlInsrChunkStep,
            Step ezmktB2cSaleInfMmBatDtlUpdChunkStep,
            Step ezmktB2cSaleInfMmBatMstInsrTaskletStep)
    {
        return new JobBuilder("ezmktB2cSaleInfMmBatJob", jobRepository)
                .listener(commonJobListener)
                .start(ezmktB2cSaleInfMmBatDtlDelTaskletStep) // 마켓B2C정산상세 삭제 Step
                .next(ezmktB2cSaleInfMmBatMstDelTaskletStep) // 마켓B2C정산기본 삭제 Step
                .next(ezmktB2cSaleInfMmBatDtlInsrChunkStep) // 마켓B2C정산상세 입력 Step (source → target)
                .next(ezmktB2cSaleInfMmBatDtlUpdChunkStep) // 마켓B2C정산상세 갱신 Step (source → target)
                .next(ezmktB2cSaleInfMmBatMstInsrTaskletStep) // 마켓B2C정산기본 입력 Step (summary select insert)
                .build();
    }
    
    /**
     * 마켓B2C정산상세 삭제
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("ezmktB2cSaleInfMmBatDtlDelTaskletStep")
    Step ezmktB2cSaleInfMmBatDtlDelTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("ezmktB2cSaleInfMmBatDtlDelTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(ezmktB2cSaleInfMmBatDtlDelTasklet, transactionManager)
                .build();
    }

    /**
     * 마켓B2C정산기본 삭제
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("ezmktB2cSaleInfMmBatMstDelTaskletStep")
    Step ezmktB2cSaleInfMmBatMstDelTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("ezmktB2cSaleInfMmBatMstDelTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(ezmktB2cSaleInfMmBatMstDelTasklet, transactionManager)
                .build();
    }

    /**
     * 마켓B2C정산상세 입력
     * 
     * @param jobRepository
     * @param transactionManager
     * @param chunkItemReader
     * @param chunkItemProcessor
     * @param chunkItemWriter
     * @param commonStepListener
     * @return
     */
    @Bean("ezmktB2cSaleInfMmBatDtlInsrChunkStep")
    Step ezmktB2cSaleInfMmBatDtlInsrChunkStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager,
            EzmktB2cSaleInfMmBatDtlInsrChunkItemReader chunkItemReader,
            EzmktB2cSaleInfMmBatDtlInsrChunkItemProcessor chunkItemProcessor,
            EzmktB2cSaleInfMmBatDtlInsrChunkItemWriter chunkItemWriter,
            CommonStepListener commonStepListener)
    {
        return new StepBuilder("ezmktB2cSaleInfMmBatDtlInsrChunkStep", jobRepository)
                .allowStartIfComplete(true)
                .<EzmktB2cSaleInfMmBatDto, EzmktB2cSaleInfMmBatDto>chunk(CHUNK_SIZE, transactionManager)
                .reader(chunkItemReader) // read
                .processor(chunkItemProcessor) // process
                .writer(chunkItemWriter) // write
                .listener(commonStepListener)
                .build();
    }
    
    /**
     * 마켓B2C정산상세 갱신
     * 
     * @param jobRepository
     * @param transactionManager
     * @param chunkItemReader
     * @param chunkItemProcessor
     * @param chunkItemWriter
     * @param commonStepListener
     * @return
     */
    @Bean("ezmktB2cSaleInfMmBatDtlUpdChunkStep")
    Step ezmktB2cSaleInfMmBatDtlUpdChunkStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager,
            EzmktB2cSaleInfMmBatDtlUpdChunkItemReader chunkItemReader,
            EzmktB2cSaleInfMmBatDtlUpdChunkItemProcessor chunkItemProcessor,
            EzmktB2cSaleInfMmBatDtlUpdChunkItemWriter chunkItemWriter,
            CommonStepListener commonStepListener)
    {
        return new StepBuilder("ezmktB2cSaleInfMmBatDtlUpdChunkStep", jobRepository)
                .allowStartIfComplete(true)
                .<EzmktB2cSaleInfMmBatDto, EzmktB2cSaleInfMmBatDto>chunk(CHUNK_SIZE, transactionManager)
                .reader(chunkItemReader) // read
                .processor(chunkItemProcessor) // process
                .writer(chunkItemWriter) // write
                .listener(commonStepListener)
                .build();
    }
    
    /**
     * 마켓B2C정산기본 입력
     * 
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("ezmktB2cSaleInfMmBatMstInsrTaskletStep")
    Step ezmktB2cSaleInfMmBatMstInsrTaskletStep(
            JobRepository jobRepository,
            DataSourceTransactionManager transactionManager)
    {
        return new StepBuilder("ezmktB2cSaleInfMmBatMstInsrTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(ezmktB2cSaleInfMmBatMstInsrTasklet, transactionManager)
                .build();
    }
}