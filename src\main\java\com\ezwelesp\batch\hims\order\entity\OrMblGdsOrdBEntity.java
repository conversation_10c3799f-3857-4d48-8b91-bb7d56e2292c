package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 모바일상품주문기본(ez_or.or_mbl_gds_ord_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrMblGdsOrdBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 제휴사주문번호(asp_ord_no) not null
     */
    private String aspOrdNo;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 선결제금액(pstm_amt) not null
     */
    private BigDecimal pstmAmt;

    /**
     * 할부개월수(inst_mcnt) not null
     */
    private Integer instMcnt;

    /**
     * 할부원금금액(inst_prnc_amt) not null
     */
    private BigDecimal instPrncAmt;

    /**
     * 모바일상품약정할부할인금액(mbl_gds_stip_inst_dc_amt) not null
     */
    private BigDecimal mblGdsStipInstDcAmt;

    /**
     * 단말기판매가격(te_sell_prc) not null
     */
    private BigDecimal teSellPrc;

    /**
     * 모바일상품기본가격(mbl_gds_bas_prc) not null
     */
    private BigDecimal mblGdsBasPrc;

    /**
     * 모바일상품약정요금제할인금액(mbl_gds_stip_trsy_dc_amt) not null
     */
    private BigDecimal mblGdsStipTrsyDcAmt;

    /**
     * 월청구금액(mm_blng_amt) not null
     */
    private BigDecimal mmBlngAmt;

    /**
     * 단말기모델명(te_mdl_nm) not null
     */
    private String teMdlNm;

    /**
     * 통신회사구분코드(tlcm_co_div_cd) not null
     */
    private String tlcmCoDivCd;

    /**
     * 모바일상품판매유형코드(mbl_gds_sell_typ_cd) not null
     */
    private String mblGdsSellTypCd;

    /**
     * 모바일요금제관리유형코드(mbl_trsy_mng_typ_cd) not null
     */
    private String mblTrsyMngTypCd;

    /**
     * 모바일가입비용유형코드(mbl_join_exp_typ_cd) not null
     */
    private String mblJoinExpTypCd;

    /**
     * 유심제외여부(usim_excld_yn) not null
     */
    private String usimExcldYn;

    /**
     * 모바일상품판매상세내용(mbl_gds_sell_dtl_cntn)
     */
    private String mblGdsSellDtlCntn;

    /**
     * 모바일상품구매유의사항내용(mbl_gds_buy_mdsa_cntn)
     */
    private String mblGdsBuyMdsaCntn;

    /**
     * 개통문서양식파일명(opng_doc_form_file_nm)
     */
    private String opngDocFormFileNm;

    /**
     * 개통문서양식파일경로(opng_doc_form_file_path)
     */
    private String opngDocFormFilePath;

    /**
     * 이동통신온라인개통URL(motl_onln_opng_url)
     */
    private String motlOnlnOpngUrl;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
