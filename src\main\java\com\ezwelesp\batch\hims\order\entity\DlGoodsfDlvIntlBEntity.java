package com.ezwelesp.batch.hims.order.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 굿스플로배송연동기본(ez_or.dl_goodsf_dlv_intl_b)
 */
@Jacksonized
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DlGoodsfDlvIntlBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송연동고유번호(dlv_intl_innt_no) not null
     */
    private String dlvIntlInntNo;

    /**
     * 배송연동입력구분코드(dlv_intl_insr_div_cd) not null
     */
    private String dlvIntlInsrDivCd;

    /**
     * API연동일시(api_intl_dtm) not null
     */
    private String apiIntlDtm;

    /**
     * 배송번호(dlv_no)
     */
    private String dlvNo;

    /**
     * 협력사명(csp_nm) not null
     */
    private String cspNm;

    /**
     * 배송회사코드(goodsf_dlv_co_cd) not null
     */
    private String goodsfDlvCoCd;

    /**
     * 운송장번호(invc_no) not null
     */
    private String invcNo;

    /**
     * 운송장번호등록일시(invc_no_reg_dtm) not null
     */
    private String invcNoRegDtm;

    /**
     * 발송자명(sndr_nm) not null
     */
    private String sndrNm;

    /**
     * 주문자명(ordr_nm) not null
     */
    private String ordrNm;

    /**
     * 발송자우편번호(sndr_zipcd) not null
     */
    private String sndrZipcd;

    /**
     * 발송자기본주소(sndr_bas_adr) not null
     */
    private String sndrBasAdr;

    /**
     * 발송자상세주소(sndr_dtl_adr)
     */
    private String sndrDtlAdr;

    /**
     * 발송자전화번호1(sndr_telno1)
     */
    private String sndrTelno1;

    /**
     * 발송자전화번호2(sndr_telno2)
     */
    private String sndrTelno2;

    /**
     * 수신자명(rcvr_nm) not null
     */
    private String rcvrNm;

    /**
     * 수신자우편번호(rcvr_zipcd) not null
     */
    private String rcvrZipcd;

    /**
     * 수신자기본주소(rcvr_bas_adr) not null
     */
    private String rcvrBasAdr;

    /**
     * 수신자상세주소(rcvr_dtl_adr)
     */
    private String rcvrDtlAdr;

    /**
     * 수신자전화번호1(rcvr_telno1) not null
     */
    private String rcvrTelno1;

    /**
     * 수신자전화번호2(rcvr_telno2) not null
     */
    private String rcvrTelno2;

    /**
     * 기본운임부담주체코드(bas_fare_budn_magn_cd) not null
     */
    private String basFareBudnMagnCd;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 배송연동주문고유번호(dlv_intl_ord_innt_no) not null
     */
    private String dlvIntlOrdInntNo;

    /**
     * 굿스플로고유배송번호(goodsf_innt_dlv_no) not null
     */
    private String goodsfInntDlvNo;

    /**
     * 상품코드(gds_cd) not null
     */
    private String gdsCd;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 상품명(gds_nm) not null
     */
    private String gdsNm;

    /**
     * 주문상품옵션내용(ord_gds_opt_cntn)
     */
    private String ordGdsOptCntn;

    /**
     * 주문수량(ord_qty) not null
     */
    private Integer ordQty;

    /**
     * 상품가격(gds_prc)
     */
    private BigDecimal gdsPrc;

    /**
     * 주문일시(ord_dtm) not null
     */
    private String ordDtm;

    /**
     * 결제일시(pymt_dtm) not null
     */
    private String pymtDtm;

    /**
     * 굿스플로배송구분코드(goodsf_dlv_div_cd)
     */
    private String goodsfDlvDivCd;

    /**
     * 굿스플로데이터연동검토코드(goodsf_data_intl_chck_cd)
     */
    private String goodsfDataIntlChckCd;

    /**
     * 굿스플로사용자정의코드1(goodsf_usr_dfn_cd1)
     */
    private String goodsfUsrDfnCd1;

    /**
     * 굿스플로사용자정의코드2(goodsf_usr_dfn_cd2)
     */
    private String goodsfUsrDfnCd2;

    /**
     * 굿스플로합배송차수(goodsf_cdlv_nos)
     */
    private Long goodsfCdlvNos;

    /**
     * 입금확인일시(dpst_cnft_dtm)
     */
    private String dpstCnftDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
