package com.ezwelesp.batch.lgportal.product.domain.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

import java.util.List;

@Alias("LgProductQnaResponseDto")
@Getter
@SuperBuilder
public class LgProductQnaResponseDto extends LgApiBaseResponseDto {


    private Integer totalCount;            // 총 개수
    private Integer resultCount;           // 결과 개수
    private Integer pageSize;              // 페이지당 노출 개수
    private Integer pageCount;             // 총 페이지 개수
    private Integer currentPage;           // 현재 페이지
    private List<LgProductQnaListDto> items; // 항목 (개별 상품 문의 리스트)

}
