package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgUpdateProductContentsRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import com.ezwelesp.batch.lgportal.product.mapper.query.ProductLGPortalQueryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalEditProductContentsHandler implements LgPortalEditProductProcessorHandler {

    private final LgApiClient lgApiClient;
    private final ProductLGPortalQueryMapper productLGPortalQueryMapper;

    @Override
    public LgProductApiResponseDto handle(LgEditProductTargetVo item) {

        val productContent = productLGPortalQueryMapper.selectLgProductContentByGdsCd(item.getGdsCd());

        val requestDto = LgUpdateProductContentsRequestDto.builder()
                .productNo(item.getProductNo())
                .typeCode("INFO102")
                .contents(productContent.getGdocCntn()) // todo 컨텐츠 추가 필요 콘텐츠 관련된 내역은 실제 저장 어떻게 되는지 확인후 수정 예정
                .build();

        val responseDto = lgApiClient.sendLgUpdateProductContentsApi(
                requestDto,
                item.getGdsCd(),
                item.getApiSeq()
        );


        if (responseDto != null && !"200".equals(responseDto.getResult())) {
            log.debug("sendLgUpdateProductContentsApi Fail : {},{}", item.getGdsCd(), responseDto.getResult());
        }

        return responseDto;
    }

}
