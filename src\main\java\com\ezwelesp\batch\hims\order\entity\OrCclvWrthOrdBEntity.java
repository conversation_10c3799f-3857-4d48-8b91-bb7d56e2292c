package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 경조화환주문기본(ez_or.or_cclv_wrth_ord_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrCclvWrthOrdBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 경조화환주문순번(cclv_wrth_ord_seq) not null
     */
    private Long cclvWrthOrdSeq;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 주문자명(ordr_nm) not null
     */
    private String ordrNm;

    /**
     * 주문자전화번호(ordr_telno) not null
     */
    private String ordrTelno;

    /**
     * 주문자모바일전화번호(ordr_mbl_telno) not null
     */
    private String ordrMblTelno;

    /**
     * 경조화환대상고인명(cclv_wrth_obj_dead_nm)
     */
    private String cclvWrthObjDeadNm;

    /**
     * 경조화환대상관계명(cclv_wrth_obj_rel_nm)
     */
    private String cclvWrthObjRelNm;

    /**
     * 수신자명(rcvr_nm) not null
     */
    private String rcvrNm;

    /**
     * 수신자사원번호(rcvr_empno) not null
     */
    private String rcvrEmpno;

    /**
     * 수신자전화번호(rcvr_telno) not null
     */
    private String rcvrTelno;

    /**
     * 수신자모바일전화번호(rcvr_mbl_telno) not null
     */
    private String rcvrMblTelno;

    /**
     * 수신자우편번호(rcvr_zipcd) not null
     */
    private String rcvrZipcd;

    /**
     * 수신자기본주소(rcvr_bas_adr) not null
     */
    private String rcvrBasAdr;

    /**
     * 수신자상세주소(rcvr_dtl_adr) not null
     */
    private String rcvrDtlAdr;

    /**
     * 비고(rmrk)
     */
    private String rmrk;

    /**
     * 주문일시(ord_dtm) not null
     */
    private String ordDtm;

    /**
     * 주문자IP(ordr_ip)
     */
    private String ordrIp;

    /**
     * 경조화환주문자소속명(cclv_wrth_ordr_blgt_nm)
     */
    private String cclvWrthOrdrBlgtNm;

    /**
     * 배송희망일자(dlv_hope_dt)
     */
    private String dlvHopeDt;

    /**
     * 경조화환구분코드(cclv_wrth_div_cd)
     */
    private String cclvWrthDivCd;

    /**
     * 경조화환배송상태코드(cclv_wrth_dlv_st_cd)
     */
    private String cclvWrthDlvStCd;

    /**
     * 상품명(gds_nm)
     */
    private String gdsNm;

    /**
     * 배송요청내용(dlv_req_cntn)
     */
    private String dlvReqCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
