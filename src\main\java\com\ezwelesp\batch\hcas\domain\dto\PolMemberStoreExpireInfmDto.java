package com.ezwelesp.batch.hcas.domain.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class PolMemberStoreExpireInfmDto {
    private String templateFileNm;
    private String subject; // 메일 제목	
    private String mailReceiver; // 수신자

    // email 발송을 위한 user정보 setting
    private String frcsNm;
    private String commDtlCdNm;
    private String mgrNm;
    private String ccBrCd;
    private String branchNm;
    private String ccDeptCd;
    private String branch2Nm;
    private String bnftAplyStrtDt;
    private String bnftAplyEndDt;
}
