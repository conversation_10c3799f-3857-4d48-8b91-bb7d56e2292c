package com.ezwelesp.batch.aasample.test.chunk.reader;

import java.util.Map;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.aasample.test.domain.Test;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@StepScope
public class ChunkSampleItemReader extends MyBatisPagingItemReader<Test> {
    // 1회 read 시 가져올 row 개수
    private final int PAGE_SIZE = 1000;

    public ChunkSampleItemReader(@Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory primaryQuerySqlSessionFactory,
            @Value("#{jobParameters['ntcTitl']}") String ntcTitl) {
        log.debug("ChunkSampleItemReader ChunkSampleItemReader {}", ntcTitl);
        this.setName("SampleItemReader"); // reader명
        this.setSqlSessionFactory(primaryQuerySqlSessionFactory);
        this.setQueryId("com.ezwelesp.batch.aasample.test.mapper.query.UserQueryMapper.selectNtcByNtcTitl");
        this.setParameterValues(Map.of("ntcTitl", ntcTitl));
        this.setPageSize(PAGE_SIZE);
    }

}
