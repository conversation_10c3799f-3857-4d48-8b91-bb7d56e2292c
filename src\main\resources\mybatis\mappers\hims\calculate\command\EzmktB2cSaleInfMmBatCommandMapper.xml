<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.calculate.mapper.command.EzmktB2cSaleInfMmBatCommandMapper">
    <delete id="deleteCaMktB2cStlD">
        /* 이지웰마켓B2C 상세 기존 데이터 삭제 */
        delete from ez_ca.ca_mkt_b2c_stl_d
         where stl_ym = #{stlYm}
    </delete>
    
    <delete id="deleteCaMktB2cStlB">
        /* 이지웰마켓B2C 기본 기존 데이터 삭제 */
        delete from ez_ca.ca_mkt_b2c_stl_b
         where stl_ym = #{stlYm}
    </delete>
    
    <insert id="insertCaMktB2cStlD">
        /* 마켓B2C정산상세 입력 */
        insert into ez_ca.ca_mkt_b2c_stl_d (
            stl_ym
            , ch_cd
            , ord_no
            , ord_gds_seq
            , ord_cncl_yn
            , ord_dtm
            , cncl_dtm
            , ezmkt_b2c_ord_st_cd
            , csp_cd
            , user_key
            , gds_cd
            , gds_nm
            , taxn_knd_cd
            , sell_prc
            , ord_qty
            , ord_amt
            , gnrl_pnt_pymt_amt
            , crd_pymt_amt
            , vtac_dpst_amt
            , mlg_pymt_amt
            , spp_pymt_amt
            , acnt_trns_pymt_amt
            , cpn_pymt_amt
            , zrpay_pymt_amt
            , nvpay_pymt_amt
            , onpay_pymt_amt
            /*, digital_onnuri TODO 25년2월에 신규 생성된 항목임-차세대에 반영되지않음*/
            /*, request TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
            , apv_amt
            /*, paylater TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
            , kcci_pymt_amt
            , ord_nm
            , ord_tot_amt
            , dlv_exp
            , frst_reg_dtm
            , frst_reg_usr_id
            , frst_reg_pgm_id
            , last_mod_dtm
            , last_mod_usr_id
            , last_mod_pgm_id
        ) values (
            #{accountYm}
            , #{chCd}
            , #{orderNum}
            , #{orderGoodsNum}
            , #{orderCancelYn}
            , #{orderDt}
            , #{cancelDt}
            , #{orderStatus}
            , #{orderStatusNm}
            , #{cspCd}
            , #{mkUserKey}
            , #{goodsCd}
            , #{goodsNm}
            , #{taxYn}
            , #{salePrice}
            , #{orderQty}
            , #{orderAmt}
            , #{point}
            , #{card}
            , #{virture}
            , #{reserve}
            , #{special}
            , #{trans}
            , #{coupon}
            , #{zeropay}
            , #{naverPay}
            , #{onnuriPay}
            , #{approval}
            , #{korchamAmt}
            , #{orderNm}
            , #{totalAmt}
            , #{dlvrAmt}
            , to_char(statement_timestamp(),'yyyyMMddhh24miss')
            , 'BATCH'
            , 'BA_HICA00001'
            , to_char(statement_timestamp(),'yyyyMMddhh24miss')
            , 'BATCH'
            , 'BA_HICA00001'
        )
    </insert>
    
    <update id="updateCaMktB2cStlD">
        /* 마켓B2C정산상세 갱신 */
        update ez_ca.ca_mkt_b2c_stl_d
         <set>
             , t.crd_pymt_amt = ot.crd_pymt_amt
             , t.acnt_trns_pymt_amt = ot.acnt_trns_pymt_amt
             , t.zrpay_pymt_amt = ot.zrpay_pymt_amt
             , t.nvpay_pymt_amt = ot.nvpay_pymt_amt
             , t.cpn_pymt_amt = ot.cpn_pymt_amt
             , t.vtac_dpst_amt = ot.vtac_dpst_amt
             , t.mlg_pymt_amt = ot.mlg_pymt_amt
             /*, t.request = ot.request TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
             , t.apv_amt = ot.apv_amt
             /*, t.paylater = ot.paylater TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
             , t.spp_pymt_amt = ot.spp_pymt_amt
             , t.gnrl_pnt_pymt_amt = ot.gnrl_pnt_pymt_amt
             , t.kcci_pymt_amt = ot.kcci_pymt_amt
             , t.onpay_pymt_amt = ot.onpay_pymt_amt
             /*, t.digital_onnuri = ot.digital_onnuri TODO 25년2월에 신규 생성된 항목임-차세대에 반영되지않음*/
             , last_mod_dtm = statement_timestamp()
             , last_mod_usr_id = 'BATCH'
             , last_mod_pgm_id = 'BA_HICA00001'
         </set>
         where stl_ym = #{accountYm}
           and ord_no = #{orderNum}
           and ord_gds_seq = #{orderGoodsNum}
           and ord_cncl_yn = #{orderCancelYn}
    </update>
    
    <insert id="insertCaMktB2cStlB">
        /* 마켓B2C정산기본 입력 */
        insert into ez_ca.ca_mkt_b2c_stl_b (
            stl_ym
            , ch_cd
            , stl_strt_dt
            , stl_end_dt
            , ord_amt
            , gnrl_pnt_pymt_amt
            , crd_pymt_amt
            , vtac_dpst_amt
            , mlg_pymt_amt
            , spp_pymt_amt
            , acnt_trns_pymt_amt
            , cpn_pymt_amt
            , zrpay_pymt_amt
            , nvpay_pymt_amt
            /*, request TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
            , apv_amt
            /*, paylater TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
            , kcci_pymt_amt
            , onpay_pymt_amt
            /*, digital_onnuri TODO 25년2월에 신규 생성된 항목임-차세대에 반영되지않음*/
            , frst_reg_dtm
            , frst_reg_usr_id
            , frst_reg_pgm_id
            , last_mod_dtm
            , last_mod_usr_id
            , last_mod_pgm_id
        )
        select stl_ym
             , ch_cd
             , stl_ym || '01'
             , #{stlYmd}
             , sum(ord_amt)
             , sum(gnrl_pnt_pymt_amt)
             , sum(crd_pymt_amt)
             , sum(vtac_dpst_amt)
             , sum(mlg_pymt_amt)
             , sum(spp_pymt_amt)
             , sum(acnt_trns_pymt_amt)
             , sum(cpn_pymt_amt)
             , sum(zrpay_pymt_amt)
             , sum(nvpay_pymt_amt)
             /*, , sum(request) TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
             , sum(apv_amt)
             /*, sum(paylater) TODO 0원 데이터만 있어서 TO-BE 에는 삭제됨*/
             , sum(kcci_pymt_amt)
             , sum(onpay_pymt_amt)
             /*, sum(digital_onnuri) TODO 25년2월에 신규 생성된 항목임-차세대에 반영되지않음*/
             , statement_timestamp()
             , 'BATCH'
             , 'BA_HICA00001'
             , statement_timestamp()
             , 'BATCH'
             , 'BA_HICA00001'
          from ez_ca.ca_mkt_b2c_stl_d
         where stl_ym = #{stlYm}
         group by stl_ym, ch_cd
    </insert>
</mapper>