package com.ezwelesp.batch.hims.order.pgcompare.inicis.service;

import com.ezwelesp.batch.hims.order.entity.external.ApiPgPrsnlPayEntity;
import com.ezwelesp.batch.hims.order.entity.external.EzEcOrderPayLogEntity;

public interface ApiPgPrsnlPayCompareService {



    /**
     * 거래대사 정보 입력
     * @param apiPayLog
     * @return
     */
    int createApiPgPrsnlPayLog(ApiPgPrsnlPayEntity apiPayLog);

    /**
     * 이니시스 대사정보를 수집한다
     * @return
     */
    void connectInicisApiPgPrsnlPayCompareData();

}
