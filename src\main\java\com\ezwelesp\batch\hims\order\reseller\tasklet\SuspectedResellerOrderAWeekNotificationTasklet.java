package com.ezwelesp.batch.hims.order.reseller.tasklet;

import com.ezwelesp.batch.hims.order.config.SuspectedResellerConstants;
import com.ezwelesp.batch.hims.order.reseller.domain.SuspectedResellerOrderDto;
import com.ezwelesp.batch.hims.order.reseller.service.MonitorJobService;
import com.ezwelesp.batch.hims.order.reseller.service.SuspectedResellerOrderNotificationService;
import com.ezwelesp.framework.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.reseller.tasklet
 * @since 2025.05.20
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SuspectedResellerOrderAWeekNotificationTasklet implements Tasklet {
    private final SuspectedResellerOrderNotificationService suspectedResellerOrderNotificationService;
    private final MonitorJobService monitorJobService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            List<SuspectedResellerOrderDto> list =
                    suspectedResellerOrderNotificationService.getSuspectedResellerByOrder(
                            SuspectedResellerConstants.SearchTermTypeCodeEnum.WEEK.getCode());

            String resEtc = list.isEmpty() ? SuspectedResellerConstants.EMPTY_STRING : makeResEtc(list);
            log.info("####### resEtc: {}", resEtc);

            monitorJobService.saveScMntMonitorRes(DateUtils.dateTimeNow(),
                    SuspectedResellerConstants.JOB_ID_BA_HIOR00032, list.size(), resEtc);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        return RepeatStatus.FINISHED;
    }

    private String makeResEtc(List<SuspectedResellerOrderDto> list) {
        StringBuilder sb = new StringBuilder();
        sb.append(
                "<table><thead><tr><th>고객사코드</th><th>고객사명</th><th>고객명</th><th>유저키</th><th>주문건수</th><th>&nbsp; 주문수량 &nbsp;</th><th>결제금액</th></tr></thead><tbody>");

        for (SuspectedResellerOrderDto reseller : list) {
            sb.append(String.format("<tr><td>%s</td><td>%s</td>", reseller.getClntCd(), reseller.getClntNm()));
            sb.append(String.format("<td>%s</td><td style='padding: 0 5px;'>%s</td>", reseller.getUsrNm(),
                    reseller.getUserKey()));
            sb.append(String.format("<td>%s건</td><td>%s개</td>", reseller.getOrdCnt(), reseller.getOrdGdsQty()));
            sb.append(String.format("<td>%s원</td></tr>", reseller.getOrdAmt()));

            log.debug("#####고객사코드: {}, 고객사명: {}, 고객명: {}, 유저키: {}, 주문건수: {}, 주문수량: {}, 결제금액: {}#####",
                    reseller.getClntCd(), reseller.getClntNm(), reseller.getUserKey(), reseller.getUsrNm(),
                    reseller.getOrdCnt(), reseller.getOrdGdsQty(), reseller.getOrdAmt());
        }

        sb.append("</tbody></table>");

        return sb.toString();
    }
}
