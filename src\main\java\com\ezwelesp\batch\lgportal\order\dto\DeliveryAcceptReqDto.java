package com.ezwelesp.batch.lgportal.order.dto;

import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
  * 배송접수 처리 사용 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see DeliveryAcceptReqDto
  */
@Data
public class DeliveryAcceptReqDto {
    private String apiKey;

    private List<DeliveryItem> deliveryItems;

    public DeliveryAcceptReqDto(String apiKey) {
        this.apiKey = apiKey;
        this.deliveryItems = new ArrayList<>();
    }

    @Builder
    @Data
    public static class DeliveryItem {
        private Long deliveryCmdNo;
    }

}
