package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Data;

/**
 * 교환/반품 취소(철회)/거부 조회 및 전송 시 사용되는 DTO
 *
 * <AUTHOR>
 * @see ClaimWtdrRfsReqDto
 * @since 2025.02.17
 */
@Data
public class ClaimWtdrRfsReqDto {

    // API 일련번호
    private Long apiNo;

    // 거부/취소(철회)상태,  R 거부, C 취소(철회)
    private String exceptStatus;

    // 판매주문예외번호
    private String saleOrderExceptNo;

    // 관리자메모, 사유코드
    private String memo;
}
