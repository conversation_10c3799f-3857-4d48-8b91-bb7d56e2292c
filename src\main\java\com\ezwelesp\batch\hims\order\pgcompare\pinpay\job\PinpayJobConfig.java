package com.ezwelesp.batch.hims.order.pgcompare.pinpay.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.order.pgcompare.pinpay.tasklet.PinpayTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 핀페이 결제대사
 */

@Slf4j
@Configuration
@RequiredArgsConstructor
public class PinpayJobConfig {
	private final CommonJobListener commonJobListener;
	private final PinpayTasklet pinpayTasklet;
	
	@Bean("BA_HIOR00009")
	public Job BA_HIOR00009(JobRepository jobRepository, @Qualifier("BA_HIOR00009_STEP") Step step) {
		return new JobBuilder("BA_HIOR00009", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(step)
                .build();
	}
	
	
	@Bean("BA_HIOR00009_STEP")
	public Step BA_HIOR00009_STEP(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
		  return new StepBuilder("BA_HIOR00009_STEP", jobRepository)
	                .allowStartIfComplete(true)
	                .tasklet(pinpayTasklet, transactionManager)
	                .build();
		
	}
}
