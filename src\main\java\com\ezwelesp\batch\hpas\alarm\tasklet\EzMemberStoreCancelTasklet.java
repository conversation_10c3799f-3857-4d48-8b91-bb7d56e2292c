package com.ezwelesp.batch.hpas.alarm.tasklet;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreClosedDto;
import com.ezwelesp.batch.hpas.alarm.service.CommonAlarmService;
import com.ezwelesp.batch.hpas.alarm.service.EzMemberStoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class EzMemberStoreCancelTasklet implements Tasklet {
    private final EzMemberStoreService ezMemberStoreService;
    private final CommonAlarmService commonAlarmService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        EzMemberStoreClosedDto ezMemberStoreClosedDto = new EzMemberStoreClosedDto();
        ezMemberStoreClosedDto.setFrcsIntlStCd("1002");
        List<EzMemberStoreClosedDto> ezMembersStoreClosedList =
                ezMemberStoreService.getEzMemberStoreClosedList(ezMemberStoreClosedDto);
        String resEtc = "";

        log.info("###########################################################");
        log.info("###  가맹점 해지 알람  시작일시: " + getCurrentDt());
        log.info("###########################################################");

        resEtc = "<table><tbody><tr><th>[해지 가맹정] 날짜 </th><td colspan='5'>" + getCurrentDt() + "총"
                + ezMembersStoreClosedList.size() + "건</td></tr>";

        if (ezMembersStoreClosedList.size() > 0) {
            for (EzMemberStoreClosedDto store : ezMembersStoreClosedList) {
                resEtc += "<th>가맹점코드 </td><td>" + store.getCrdcFrcsNo() + "</td>"
                        + "<th>가맹점명 </th><td>" + store.getCrdcFrcsNm() + "</td></tr>";
            }
        }
        resEtc += "</tbody></table>";

        String result = commonAlarmService.getCommonMonitorJobs(getCurrentDt(), "EZMEMBERS_STORE_CANCEL_001",
                ezMembersStoreClosedList, resEtc);

        log.info("################################################");
        log.info("### 가맹점 해지 알람 종료 일시 : " + getCurrentDt());
        log.info("### 가맹점 해지 알람 배치결과 : " + result);
        log.info("################################################");

        return RepeatStatus.FINISHED;
    }

    public String getCurrentDt() {
        SimpleDateFormat SDF = new SimpleDateFormat("yyyyMMddHHmmss");
        return SDF.format(new Date());
    }
}

