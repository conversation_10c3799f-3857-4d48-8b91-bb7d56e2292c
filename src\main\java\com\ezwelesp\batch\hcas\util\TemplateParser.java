package com.ezwelesp.batch.hcas.util;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import lombok.extern.log4j.Log4j;

public class TemplateParser {
	
	
	/**
	 * Object �� bean�� Param���� �Ͽ� ���
	 * @param templateStr
	 * @param obj
	 * @return
	 * @throws NoSuchMethodException 
	 * @throws InvocationTargetException 
	 * @throws IllegalAccessException 
	 */
	public static String mustMailParse(String templateStr, Object obj) throws IllegalAccessException, InvocationTargetException, NoSuchMethodException{
		
		Map actionTagMap = parseActionTag(templateStr);

		if (actionTagMap != null && actionTagMap.size() > 0) {

			Set keySet = actionTagMap.keySet();
			Iterator keyIter = keySet.iterator();

			while (keyIter.hasNext()) {

				String keyStr = (String) keyIter.next();


				String actionTemplateStr = (String) actionTagMap.get(keyStr);

				StringBuffer replaceStr = new StringBuffer();
				
				Map memorealMap = BeanUtils.describe(obj);

				replaceStr.append(parseReplaceTag(actionTemplateStr, memorealMap));
				
				templateStr = replaceActonTag(keyStr, templateStr, replaceStr.toString());
			}
				

		}
		
		templateStr = parseReplaceTag(templateStr, BeanUtils.describe(obj));
		return templateStr;
	
	}
	
	private static String replaceActonTag(String tagName, String templateStr, String replaceStr) {	
		if (templateStr != null && templateStr.length() > 0 ) {

			String startTagName = "<#"+tagName+">";
			String endTagName = "<#"+tagName+"/>";

			int position = templateStr.indexOf(startTagName);

			if ( position == -1 ) {
				return templateStr;
			}

			StringBuffer reStr = new StringBuffer();
			reStr.append(templateStr.substring(0, position));
			reStr.append(replaceStr);

			int endPos = templateStr.indexOf(endTagName);

			if ( endPos == -1 ) {
				return reStr.toString();
			}

			reStr.append(templateStr.substring(endPos + endTagName.length()));

			return reStr.toString();

		}

		// TODO Auto-generated method stub
		return templateStr;
	}  

	@SuppressWarnings("unchecked")
	public static Map parseActionTag(String parseStr) {


		Map tagMap = new HashMap();

		while( parseStr.length() > 0 ) {

			int position = parseStr.indexOf("<#");
			if ( position == -1 ) {
				break;
			}

			if ( parseStr.length() == position + 2 ) break;

			String remainder = parseStr.substring(position+2);

			int markEndPos = remainder.indexOf(">");
			if ( markEndPos == -1 ) break;

			String tagName = remainder.substring(0, markEndPos).trim();

			if ( remainder.length() == markEndPos + 1 ) break;
			parseStr = remainder.substring(markEndPos + 1);

			String endTagName = "<#" + tagName + "/>";

			int endTagPos = parseStr.indexOf(endTagName);
			if ( endTagPos == -1 ) {
				break;
			}

			String content = parseStr.substring(0, endTagPos);

			tagMap.put(tagName, content);

			parseStr = parseStr.substring(endTagPos + endTagName.length());
		}
		return tagMap;

	}

	@SuppressWarnings("unchecked")
	public static String parseReplaceTag(String parseStr, Map args) {

		StringBuffer content = new StringBuffer();
		while( parseStr.length() > 0 ) {
			int position = parseStr.indexOf("<@");
			if ( position == -1 ) {
				content.append(parseStr);
				break;
			}
			if ( position != 0 ) content.append(parseStr.substring(0,position));

			if ( parseStr.length() == position + 2 ) break;
			String remainder = parseStr.substring(position+2);

			int markEndPos = remainder.indexOf(">");
			if ( markEndPos == -1 ) break;

			String argname = remainder.substring(0, markEndPos).trim();

			String value = (String)args.get(argname);
			if ( value != null ) {
				content.append(value.replaceAll("\n", "<br/>"));
			} else content.append(" ");

			if ( remainder.length() == markEndPos + 1 ) break;
			parseStr = remainder.substring(markEndPos + 1);


		}
		return content.toString();

	}


	/**
	 *  tag �̿��� ���Ͽ�
	 * @param templateStr
	 * @param ezEcMemorialDayBean
	 * @return
	 */
	public static String mailParse(String templateStr, Object obj) throws Exception {

		Map<?,?> actionTagMap = parseActionTag(templateStr);

		try {
			
			Map<?,?> map = getMasterMap(obj);
			
			if (actionTagMap != null && actionTagMap.size() > 0) {
	
				Set<?> keySet = actionTagMap.keySet();
				Iterator<?> keyIter = keySet.iterator();
				
				while (keyIter.hasNext()) {
	
					String keyStr = (String) keyIter.next();
	
					if(StringUtils.isBlank((String)map.get(keyStr))) {
						
						templateStr = replaceActonTag(keyStr, templateStr, "");
						continue;
						
					}
					
					String actionTemplateStr = (String) actionTagMap.get(keyStr);
	
					StringBuffer replaceStr = new StringBuffer();
	
					replaceStr.append(parseReplaceTag(actionTemplateStr, map));
					
					templateStr = replaceActonTag(keyStr, templateStr, replaceStr.toString());
				}
			}
			
			templateStr = parseReplaceTag(templateStr, map);
		}
		catch (Exception ex) {
			System.out.println(ex.getMessage());
		}
		return templateStr;

	}	
	

	/**
	 * Contents를 만들기 위한 Bean을 Map로 변경
	 * @param ezEcMemorialDayBean
	 * @return
	 */
	private static Map<String, String> getMasterMap(Object obj) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		
		try {
			if(obj != null){
			
				Class<?> c = obj.getClass();
				Field[] f = c.getDeclaredFields();
				Log logger = LogFactory.getLog(TemplateParser.class);
				logger.debug("------------------------------");
				for (int i = 0; i < f.length; i++) {
					
					Object returnValue = null;
					try {
						String mthName = "get" + StringUtils.substring(f[i].getName(), 0, 1).toUpperCase() + StringUtils.substring(f[i].getName(), 1);
						Method m = c.getDeclaredMethod(mthName, new Class[]{});
						returnValue = m.invoke(obj, new Object[]{});
					}
					catch (NoSuchMethodException ex) {}
					
					if (returnValue != null) {					
						//logger.debug(f[i].getName()+"/"+ returnValue.toString());
						
						if (returnValue instanceof Number) {
							// TODO : 이승도 CurrencyUtil 임시 삭제
							//map.put(f[i].getName(), CurrencyUtil.getWon(returnValue.toString()));
						} else {
							map.put(f[i].getName(), returnValue.toString());
						}
						
					}
				}
				
				logger.debug("------------------------------");
			}
		}
		catch (Exception ex) {
			System.out.println(ex.getMessage());
		}
		return map;	
	}
	
	public static String orderMailParse(String templateStr, Object obj) throws Exception {
		Map<?,?> actionTagMap = parseActionTag(templateStr);

		try {
			
			Map<?,?> map = getMasterMap(obj);
			
			if (actionTagMap != null && actionTagMap.size() > 0) {
	
				Set<?> keySet = actionTagMap.keySet();
				Iterator<?> keyIter = keySet.iterator();
				
				int i=0;
				
				while (keyIter.hasNext()) {
					StringBuffer replaceStr = new StringBuffer();
					String keyStr = (String) keyIter.next();
					
					if ((keyStr.equals("pnt_price") && map.get("PNT_PRICE") == null)
					|| (keyStr.equals("sp_price") && map.get("SP_PRICE") == null)
					|| (keyStr.equals("card_price") && map.get("CARD_PRICE") == null)
					|| (keyStr.equals("acc_price") && map.get("ACC_PRICE") == null)
					|| (keyStr.equals("dedt_price") && map.get("DEDT_PRICE") == null)
					|| (keyStr.equals("mileage_price") && map.get("MILEAGE_PRICE") == null)
					|| (keyStr.equals("mail_banner_html") && map.get("MAIL_BANNER_HTML") == null) ) {
						
						templateStr = replaceActonTag(keyStr, templateStr, replaceStr.toString());
						continue;
					}
					
					String actionTemplateStr = (String) actionTagMap.get(keyStr);
	
					replaceStr.append(parseReplaceTag(actionTemplateStr, map));
					
					templateStr = replaceActonTag(keyStr, templateStr, replaceStr.toString());
					
					i++;
				}
			}
			
			templateStr = parseReplaceTag(templateStr, map);
		}
		catch (Exception ex) {
			System.out.println(ex.getMessage());
		}
		return templateStr;
   
	}	
	
}
