package com.ezwelesp.batch.aasample.test.mapper.query;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.ezwelesp.batch.aasample.test.domain.Test;

@Mapper
public interface UserQueryMapper {

    String selectNtcStatByNtcId(@Param("ntcId") Long id);
    
    List<Test> selectNtcByNtcTitl(@Param("ntcTitl") String ntcTitl);
    
    Long selectMaxId();
    
    List<Test> selectNtcByStatus();
}