package com.ezwelesp.batch.hpas.alarm.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import com.ezwelesp.batch.hpas.alarm.tasklet.LowPriceSmsMainTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class LowPriceSmsMainJobConfig {
    private final CommonJobListener commonJobListener;
    private final LowPriceSmsMainTasklet lowPriceSmsMainTasklet;

    @Bean("lowPriceSmsMainJob")
    public Job lowPriceSmsMainJob(JobRepository jobRepository,
            @Qualifier("lowPriceSmsMainStep") Step lowPriceSmsMainStep) {
        return new JobBuilder("lowPriceSmsMainJob", jobRepository)
                .listener(commonJobListener)
                .start(lowPriceSmsMainStep)
                .build();
    }

    @Bean("lowPriceSmsMainStep")
    public Step lowPriceSmsMainStep(JobRepository jobRepository,
            DataSourceTransactionManager transactionManager) {
        return new StepBuilder("lowPriceSmsMainStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(lowPriceSmsMainTasklet, transactionManager)
                .build();

    }
}
