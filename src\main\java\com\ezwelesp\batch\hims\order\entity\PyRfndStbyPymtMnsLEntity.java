package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 환불대기결제수단내역(ez_or.py_rfnd_stby_pymt_mns_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyRfndStbyPymtMnsLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 환불대기결제수단내역순번(rfnd_stby_pymt_mns_lst_seq) not null
     */
    private Long rfndStbyPymtMnsLstSeq;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 결제번호(pymt_no)
     */
    private String pymtNo;

    /**
     * PG가맹점번호(pg_frcs_no)
     */
    private String pgFrcsNo;

    /**
     * PG승인번호(pg_apv_no)
     */
    private String pgApvNo;

    /**
     * 결제파라미터내용J(pymt_para_cntnj)
     */
    private String pymtParaCntnj;

    /**
     * 결제수단코드(pymt_mns_cd) not null
     */
    private String pymtMnsCd;

    /**
     * 환불금액(rfnd_amt) not null
     */
    private BigDecimal rfndAmt;

    /**
     * 복지제도포인트구분코드(wsp_div_cd)
     */
    private String wspDivCd;

    /**
     * 복지제도포인트코드(wsp_cd)
     */
    private String wspCd;

    /**
     * 환불대기결제수단상태코드(rfnd_stby_pymt_mns_st_cd) not null
     */
    private String rfndStbyPymtMnsStCd;

    /**
     * 환불요청건수(rfnd_req_cnt) not null
     */
    private Integer rfndReqCnt;

    /**
     * 오류메시지내용(err_msg_cntn)
     */
    private String errMsgCntn;

    /**
     * 배치처리완료일시(bat_prcs_cmpt_dtm)
     */
    private String batPrcsCmptDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
