package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 클레임상품상세(ez_or.cl_clm_gds_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClClmGdsDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 클레임상품순번(clm_gds_seq) not null
     */
    private Long clmGdsSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 배송번호(dlv_no)
     */
    private String dlvNo;

    /**
     * 배송상품순번(dlv_gds_seq)
     */
    private Long dlvGdsSeq;

    /**
     * 교환주문상품순번(exch_ord_gds_seq)
     */
    private Long exchOrdGdsSeq;

    /**
     * 주문발송순번(ord_snd_seq)
     */
    private Long ordSndSeq;

    /**
     * 주문발송상품상세순번(ord_snd_gds_dtl_seq)
     */
    private Long ordSndGdsDtlSeq;

    /**
     * 클레임상품수량(clm_gds_qty) not null
     */
    private Integer clmGdsQty;

    /**
     * 취소수량(cncl_qty) not null
     */
    private Integer cnclQty;

    /**
     * 클레임수기처리내용(clm_hndw_prcs_cntn)
     */
    private String clmHndwPrcsCntn;

    /**
     * 관리자메모(mgr_memo)
     */
    private String mgrMemo;

    /**
     * 주문상품옵션내용(ord_gds_opt_cntn)
     */
    private String ordGdsOptCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
