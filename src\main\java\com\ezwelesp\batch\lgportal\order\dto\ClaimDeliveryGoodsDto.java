package com.ezwelesp.batch.lgportal.order.dto;

import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * 교환/반품의 회수/배송(출고) 상품 정보
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto.ClaimDeliveryGoodsDto
 * @since 2025.02.26
 */
@Data
@SuperBuilder
public class ClaimDeliveryGoodsDto {

    @Serial
    private static final long serialVersionUID = -2325575459559189821L;

    // 주문번호
    private String ordNo;

    // 주문상품순번
    private long ordGdsSeq;

    // 회수/배송 상품수량
    private int goodsQty;

    // 배송상품상태코드
    private LgConstants.DeliveryGoodsStatusCodeEnum dlvGdsStCd;

    // 회수상품상태코드
    private LgConstants.ClaimWithdrawGoodsStatusCodeEnum wtdwGdsStCd;

    // 클레임상품순번
    private long clmGdsSeq;
}
