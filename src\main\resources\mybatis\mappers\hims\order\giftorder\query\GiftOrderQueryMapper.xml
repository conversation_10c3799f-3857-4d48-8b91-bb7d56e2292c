<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.giftorder.mapper.query.GiftOrderQueryMapper">

    <select id="selectGiftOrderAfterFourDaysList" resultType="com.ezwelesp.batch.hims.order.giftorder.domain.GiftOrderDto">
        SELECT ogob.gvgft_ord_no
             , oob.ord_no
             , oob.asp_ord_no
             , oogd.gds_nm
             , ogob.ordr_nm
             , ogob.rcvr_nm
             , ogob.rcvr_mbl_telno
             , oob.ord_dtm
             , oob.clnt_cd
             , ccb.clnt_typ_cd
          FROM ez_or.or_gvgft_ord_b ogob
          JOIN ez_or.or_ord_b oob ON oob.ord_no = ogob.ord_no
          JOIN ez_or.or_ord_gds_d oogd ON oogd.ord_no = oob.ord_no
          LEFT JOIN ez_ct.ct_clnt_b ccb ON ccb.clnt_cd = oob.clnt_cd
         WHERE ogob.gvgft_ord_st_cd IN ('1001', '1002', '1003') /* 선물하기, 선물발송(카카오열람), 선물열람 */
           AND ogob.gvgft_ord_knd_cd = 'GNRL'   /* 일반선물하기 */
           AND oogd.ord_gds_qty - oogd.cncl_gds_qty > 0
           AND oob.ord_dtm BETWEEN CONCAT(TO_CHAR(NOW() + interval '-3 days', 'yyyymmdd'), '000000')
                    AND CONCAT(TO_CHAR(NOW() + interval '-3 days', 'yyyymmdd'), '235959')
    </select>

    <select id="selectGiftOrderAutoCancelTargetOrdNoList" resultType="java.lang.Long">
        SELECT gvgft_ord_no
          FROM (
                SELECT ogob.gvgft_ord_no
                  FROM ez_or.or_gvgft_ord_b ogob
                  JOIN ez_or.or_ord_b oob ON oob.ord_no = ogob.ord_no
                  JOIN ez_or.or_ord_gds_d oogd ON oogd.ord_no = oob.ord_no
                 WHERE ogob.gvgft_ord_st_cd = '1010' /* 선물거절(사용자) */
                   AND ogob.gvgft_ord_knd_cd = 'GNRL' /* 일반선물하기 */
                   AND oogd.ord_gds_qty - oogd.cncl_gds_qty > 0
                   AND oob.ord_dtm >= CONCAT(TO_CHAR(NOW() + interval '-7 days', 'yyyymmdd'), '000000')
                 UNION ALL
                SELECT ogob.gvgft_ord_no
                  FROM ez_or.or_gvgft_ord_b ogob
                  JOIN ez_or.or_ord_b oob ON oob.ord_no = ogob.ord_no
                  JOIN ez_or.or_ord_gds_d oogd ON oogd.ord_no = oob.ord_no
                  JOIN ez_or.dl_dlv_adr_b ddab ON ddab.ord_no = oob.ord_no
                 WHERE ogob.gvgft_ord_st_cd IN ('1001', '1002', '1003') /* 선물하기, 선물발송(카카오열람), 선물열람 */
                   AND ogob.gvgft_ord_knd_cd = 'GNRL' /* 일반선물하기 */
                   AND ddab.gvgft_dlv_adr_insr_dtm IS NULL
                   AND oogd.ord_gds_qty - oogd.cncl_gds_qty > 0
                   AND oob.ord_dtm BETWEEN CONCAT(TO_CHAR(NOW() + interval '-7 days', 'yyyymmdd'), '000000')
                            AND CONCAT(TO_CHAR(NOW() + interval '-3 days', 'yyyymmdd'), '000000')
               )
         GROUP BY gvgft_ord_no
    </select>
</mapper>
