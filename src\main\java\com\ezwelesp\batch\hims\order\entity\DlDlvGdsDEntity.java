package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 배송상품상세(ez_or.dl_dlv_gds_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlDlvGdsDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송번호(dlv_no) not null
     */
    private String dlvNo;

    /**
     * 배송상품순번(dlv_gds_seq) not null
     */
    private Long dlvGdsSeq;

    /**
     * 회수배송번호(wtdw_dlv_no)
     */
    private String wtdwDlvNo;

    /**
     * 회수상품순번(wtdw_gds_seq)
     */
    private Long wtdwGdsSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 배송상품수량(dlv_gds_qty) not null
     */
    private Integer dlvGdsQty;

    /**
     * 배송상품상태코드(dlv_gds_st_cd) not null
     */
    private String dlvGdsStCd;

    /**
     * 배송취소상품수량(dlv_cncl_gds_qty) not null
     */
    private Integer dlvCnclGdsQty;

    /**
     * 배송취소일시(dlv_cncl_dtm)
     */
    private String dlvCnclDtm;

    /**
     * LG포털API배송번호(lgp_api_dlv_no)
     */
    private String lgpApiDlvNo;

    /**
     * LG포털API배송상세번호(lgp_api_dlv_dtl_no)
     */
    private String lgpApiDlvDtlNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
