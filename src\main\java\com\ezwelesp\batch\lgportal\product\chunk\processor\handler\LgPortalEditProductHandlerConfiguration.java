package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;

import static com.ezwelesp.batch.lgportal.product.domain.enums.lgPortalSendKey.*;

@Configuration
@RequiredArgsConstructor
public class LgPortalEditProductHandlerConfiguration {

    private final LgPortalEditProductHandlerRegistry handlerRegistry;

    private final LgPortalEditProductBasicInfoHandler basicInfoHandler;
    private final LgPortalEditProductContentsHandler contentsHandler;
    private final LgPortalEditProductAnnouncementHandler announcementHandler;
    private final LgPortalEditProductDeliveryHandler deliveryHandler;
    private final LgPortalEditProductImageHandler imageHandler;
    private final LgPortalEditProductQnaAnswerHandler qnaAnswerHandler;

    private final LgPortalEditProductPriceHandler priceHandler;

    private final LgPortalEditProductSoldOutHandler soldOutHandler;
    private final LgPortalEditProductSaleHandler saleHandler;


    @PostConstruct
    public void initHandlers() {
        handlerRegistry.registerHandler(BASIC_INFO, basicInfoHandler);    // 1001 : 상품 기본정보 수정
        handlerRegistry.registerHandler(CONTENTS, contentsHandler);     // 1003 : 상품 컨텐츠 수정
        handlerRegistry.registerHandler(ANNOUNCEMENT, announcementHandler); // 1006 : 상품 고시정보 수정
        handlerRegistry.registerHandler(DELIVERY, deliveryHandler);     // 1007 : 상품 배송 및 교환반품 정보 수정
        handlerRegistry.registerHandler(IMAGE, imageHandler);        // 1002 : 상품 이미지 수정
        handlerRegistry.registerHandler(QNA_ANSWER, qnaAnswerHandler);    // 1015 : 상품 QnA 답변 등록

        handlerRegistry.registerHandler(PRICE, priceHandler);        // 1005 : 상품 가격 정보 수정

        handlerRegistry.registerHandler(SOLD_OUT, soldOutHandler);      // 1012 : 상품 품절 처리 // 품절처리 배치
        handlerRegistry.registerHandler(SALE, saleHandler);         // 1013 : 상품 판매 처리 // 품절처리 배치
    }
}
