package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 주문노출로그(ez_or.or_ord_exps_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrOrdExpsGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문노출로그순번(ord_exps_log_seq) not null
     */
    private Long ordExpsLogSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 노출여부(exps_yn)
     */
    private String expsYn;

    /**
     * 주문노출변경결과코드(ord_exps_chg_rslt_cd)
     */
    private String ordExpsChgRsltCd;

    /**
     * 결과메시지내용(rslt_msg_cntn)
     */
    private String rsltMsgCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
