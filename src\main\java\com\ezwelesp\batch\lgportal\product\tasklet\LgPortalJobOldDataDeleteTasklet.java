package com.ezwelesp.batch.lgportal.product.tasklet;


import com.ezwelesp.batch.lgportal.product.mapper.command.ProductLGPortalCommandMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalJobOldDataDeleteTasklet implements Tasklet {


    private final ProductLGPortalCommandMapper productLGPortalCommandMapper;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) {
        try {
            productLGPortalCommandMapper.deleteApiBatchSendBefore30Days();

            productLGPortalCommandMapper.deleteApiBatchSkuBefore30Days();

        } catch (Exception e) {
            log.error("LgPortalJobOldDataDeleteTasklet Failed: {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        return RepeatStatus.FINISHED;
    }
}
