package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 운송장번호변경내역(ez_or.dl_invc_no_chg_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlInvcNoChgLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 운송장번호변경내역순번(invc_no_chg_lst_seq) not null
     */
    private Long invcNoChgLstSeq;

    /**
     * 배송번호(dlv_no) not null
     */
    private String dlvNo;

    /**
     * 변경이전운송장번호(chg_bef_invc_no)
     */
    private String chgBefInvcNo;

    /**
     * 변경이전운송장번호등록일시(chg_bef_invc_no_reg_dtm) not null
     */
    private String chgBefInvcNoRegDtm;

    /**
     * 변경이후운송장번호(chg_aft_invc_no)
     */
    private String chgAftInvcNo;

    /**
     * 운송장번호등록일시(invc_no_reg_dtm) not null
     */
    private String invcNoRegDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
