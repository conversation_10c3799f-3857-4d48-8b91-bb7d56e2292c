package com.ezwelesp.batch.lgportal.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * [인터페이스] API 배치 전송
 * api_batch_send
 */
@Jacksonized
@Getter
@SuperBuilder
@NoArgsConstructor
public class ApiBatchSendEntity implements Serializable {
    /**
     * API 시퀀스
     */
    private Long apiSeq;

    /**
     * 공급사번호(API 신청시 할당받은 공급사번호)
     */
    private Long providerNo;

    /**
     * 인증키(API 신청시 할당받은 인증키)
     */
    private String certkey;

    /**
     * 상품번호
     */
    private Long productNo;

    /**
     * 상품명
     */
    private String productName;

    /**
     * 상품요약
     */
    private String productSummary;

    /**
     * 이미지1
     */
    private String image1;

    /**
     * SKU관리코드 KEY값
     */
    private String skuMappingCode;

    /**
     * 재고수량
     */
    private Integer sellingStockAmount;

    /**
     * 변동가격
     */
    private Long skuPriceControl;

    /**
     * 변동공급가격
     */
    private Long skuVendorPriceControl;

    /**
     * 1001:상품기본정보 수정,1002:상품이미지 수정,1003:SKU 재고수량 수정,1004
     */
    private Integer sendKey;

    /**
     * 전송여부 Y:전송   N:미전송
     */
    private String sendYn;

    /**
     * ERROR 메시지
     */
    private String errorMessage;

    /**
     * 등록일시
     */
    private String regDt;

    /**
     * 등록자ID
     */
    private String regId;

    /**
     * 소비자가
     */
    private Long listPrice;

    /**
     * 판매가
     */
    private Long netPrice;

    /**
     * 공급가
     */
    private Long vendorPrice;

    /**
     * 옵션명
     */
    private String skuValue;

    /**
     * 배송전표번호
     */
    private String deliveryNo;

    /**
     * 배송전표항목번호
     */
    private Long deliveryItemNo;

    /**
     * 송장번호
     */
    private String invoiceNo;

    /**
     * 택배사명
     */
    private String logisticsName;

    /**
     * 품질보증기준 제목
     */
    private String titleArr;

    /**
     * 품질보증기준 내용
     */
    private String valueArr;

    /**
     * API QA 시퀀스
     */
    private Long boardNo;

    /**
     * 취소수량
     */
    private Integer cancelQty;

    private static final long serialVersionUID = 1L;
}
