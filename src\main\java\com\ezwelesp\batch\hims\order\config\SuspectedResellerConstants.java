package com.ezwelesp.batch.hims.order.config;

import lombok.Getter;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.config
 * @since 2025.05.20
 */
public class SuspectedResellerConstants {
    public static final String RES_ETC2_DEFAULT = "CS팀에게 문의주세요!!";
    public static final String EMPTY_STRING = "";
    public static final String RESULT_FAIL = "FAIL";
    public static final String RESULT_SUCCESS = "SUCCESS";
    public static final String JOB_ID_BA_HIOR00029 = "RESELLER_PREVENTION_GOODS_001";
    public static final String JOB_ID_BA_HIOR00030 = "RESELLER_PREVENTION_USERKEY_003";
    public static final String JOB_ID_BA_HIOR00031 = "RESELLER_PREVENTION_USERKEY_001";
    public static final String JOB_ID_BA_HIOR00032 = "RESELLER_PREVENTION_USERKEY_002";


    @Getter
    public enum SearchTermTypeCodeEnum {
        DAY("day", "일별"),
        WEEK("week", "주별"),
        MONTH("month", "월별"),
        ;

        private final String code;
        private final String name;

        SearchTermTypeCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    /**
     * [비표준]모니터링결과응답 - 알람요청상태
     */
    @Getter
    public enum AlarmReqStatusEnum {
        REQ_STBY("T", "요청대기"),
        REQ_CMPT("F", "요청완료"),
        REQ_FAIL("N", "요청실패");

        private final String code;
        private final String name;

        AlarmReqStatusEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    /**
     * [비표준]모니터링결과응답 - 알람발송상태
     */
    @Getter
    public enum AlarmSendStatusEnum {
        SEND_STBY("T", "발송대기"),
        SEND_CMPT("F", "발송완료"),
        SEND_FAIL("N", "발송실패");

        private final String code;
        private final String name;

        AlarmSendStatusEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
