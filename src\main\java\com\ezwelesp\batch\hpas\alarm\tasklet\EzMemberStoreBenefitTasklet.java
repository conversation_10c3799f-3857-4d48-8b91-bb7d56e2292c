package com.ezwelesp.batch.hpas.alarm.tasklet;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreBenefitDto;
import com.ezwelesp.batch.hpas.alarm.service.CommonAlarmService;
import com.ezwelesp.batch.hpas.alarm.service.EzMemberStoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class EzMemberStoreBenefitTasklet implements Tasklet {
    private final EzMemberStoreService ezMemberStoreService;
    private final CommonAlarmService commonAlarmService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        EzMemberStoreBenefitDto ezMemberStoreBenefitDto = new EzMemberStoreBenefitDto();
        List<EzMemberStoreBenefitDto> ezMembersStoreBenefitList =
                ezMemberStoreService.getEzMemberStoreBenefitList(ezMemberStoreBenefitDto);
        String resEtc = "";

        log.info("###########################################################");
        log.info("###  가맹점 할인율 오차 알람  시작일시: " + getCurrentDt());
        log.info("###########################################################");

        resEtc = "<table><tbody><tr><th>[가맹정 할인율 오차] 날짜 </th><td colspan='5'>" + getCurrentDt() + "총"
                + ezMembersStoreBenefitList.size() + "건</td></tr>";

        if (ezMembersStoreBenefitList.size() > 0) {
            for (EzMemberStoreBenefitDto store : ezMembersStoreBenefitList) {
                resEtc += "<th>가맹점코드 </td><td>" + store.getCrdcFrcsNo() + "</td>"
                        + "<th>가맹점명 </th><td>" + store.getCrdcFrcsNm() + "</td></tr>"
                        + "<th>건수 </th><td>" + store.getCount() + "</td></tr>";
            }
        }
        resEtc += "</tbody></table>";

        String result = commonAlarmService.getCommonMonitorJobs(getCurrentDt(), "EZMEMBERS_STORE_BENEFIT_001",
                ezMembersStoreBenefitList, resEtc);

        log.info("################################################");
        log.info("### 가맹점 할인율 오차 알람 종료 일시 : " + getCurrentDt());
        log.info("### 가맹점 할인율 오차 알람 배치결과 : " + result);
        log.info("################################################");

        return RepeatStatus.FINISHED;
    }

    public String getCurrentDt() {
        SimpleDateFormat SDF = new SimpleDateFormat("yyyyMMddHHmmss");
        return SDF.format(new Date());
    }
}

