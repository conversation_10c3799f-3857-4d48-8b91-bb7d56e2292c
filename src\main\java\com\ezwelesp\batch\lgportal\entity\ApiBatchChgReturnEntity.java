package com.ezwelesp.batch.lgportal.entity;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * [인터페이스] API  배치 교환반품
 * api_batch_chg_return
 */
@SuperBuilder(toBuilder = true)
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ApiBatchChgReturnEntity implements Serializable {
    /**
     * API 일련번호
     */
    private Long apiNo;

    /**
     * 전송키(COMM_CD:1206)
     */
    private int sendKey;

    /**
     * 전송여부(Y:전송, N:미전송)
     */
    private String sendYn;

    /**
     * 교환/반품 구분(E:교환, R:반품)
     */
    private String chgReturnType;

    /**
     * API 교환/반품 번호(주문 예외번호)
     */
    private String apiChgReturnNo;

    /**
     * 리턴 코드
     */
    private String returnCd;

    /**
     * 리턴 메시지
     */
    private String returnMsg;

    /**
     * 등록일자
     */
    private String regDt;

    /**
     * 등록ID
     */
    private String regId;

    private static final long serialVersionUID = 1L;
}
