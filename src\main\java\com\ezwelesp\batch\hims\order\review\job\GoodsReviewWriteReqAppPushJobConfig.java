package com.ezwelesp.batch.hims.order.review.job;

import com.ezwelesp.batch.hims.order.review.tasklet.GoodsReviewWriteReqAppPushTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 상품평작성요청 안내 앱푸쉬 발송 [BA_HIOR00051]
 */
@Configuration
@RequiredArgsConstructor
public class GoodsReviewWriteReqAppPushJobConfig {
    private final CommonJobListener commonJobListener;
    private final GoodsReviewWriteReqAppPushTasklet goodsReviewWriteReqAppPushTasklet;

    @Bean("BA_HIOR00051")
    public Job goodsReviewWriteReqAppPushJob(JobRepository jobRepository, @Qualifier("BA_HIOR00051_STEP") Step goodsReviewWriteReqAppPushStep) {
        return new JobBuilder("BA_HIOR00051", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(goodsReviewWriteReqAppPushStep)
                .build();
    }

    @Bean("BA_HIOR00051_STEP")
    public Step goodsReviewWriteReqAppPushStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00051_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(goodsReviewWriteReqAppPushTasklet, transactionManager)
                .build();
    }
}
