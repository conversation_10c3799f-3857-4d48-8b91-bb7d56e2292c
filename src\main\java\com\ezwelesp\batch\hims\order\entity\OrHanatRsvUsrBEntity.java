package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 하나투어예약사용자기본(ez_or.or_hanat_rsv_usr_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrHanatRsvUsrBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 하나투어예약코드(hanat_rsv_cd) not null
     */
    private String hanatRsvCd;

    /**
     * 제휴사주문번호(asp_ord_no) not null
     */
    private String aspOrdNo;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 가족회원번호(fam_mem_no)
     */
    private String famMemNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
