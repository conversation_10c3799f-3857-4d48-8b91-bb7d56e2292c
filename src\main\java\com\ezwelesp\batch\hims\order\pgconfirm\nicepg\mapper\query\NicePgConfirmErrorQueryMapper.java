package com.ezwelesp.batch.hims.order.pgconfirm.nicepg.mapper.query;

import com.ezwelesp.batch.hims.order.entity.external.ApiPgPrsnlPayEntity;
import com.ezwelesp.batch.hims.order.entity.external.EzEcOrderPayLogEntity;
import com.ezwelesp.batch.hims.order.pgconfirm.nicepg.domain.NicePgErrorDto;

import java.util.List;

public interface NicePgConfirmErrorQueryMapper {


    public List<NicePgErrorDto> selectNicePgConfirmErrorList();
}


