package com.ezwelesp.batch.hims.order.receipt.domain;



import com.ezwelesp.batch.hims.order.entity.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


@Data
public class OrderInfoDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;

    private String ordNo;

    private String clmNo;

    private String prevClmNo;

    private Long prcsNos;

    private OrOrdBEntity ord;

    private List<OrOrdCpnDEntity> cpnList;

    private List<OrOrdCpnGdsDEntity> cpnGdsList;

    private List<DeliveryExpenseDto> dlvExpList;

    private List<DlDlvGdsDEntity> dlvGdsList;

    private List<OrderPaymentDto> ordPayList;

    private List<OrdGdsDto> ordGdsList;

    private List<OrdGdsDto> exOrdGdsList;

    private ClClmBEntity clm;

    private List<OrOrdCpnDEntity> clmCpnList;

    private List<OrOrdCpnGdsDEntity> clmCpnGdsList;

    private List<ClClmGdsDEntity> clmGdsList;

    private List<DeliveryExpenseDto> clmDlvExpList;

    private List<OrderPaymentDto> clmPayList;

    private List<DlDlvGdsDEntity> clmDlvGdsList;



}
