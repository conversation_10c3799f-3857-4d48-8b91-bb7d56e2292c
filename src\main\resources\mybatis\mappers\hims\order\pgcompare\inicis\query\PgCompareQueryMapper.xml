<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgcompare.inicis.mapper.query.PgCompareQueryMapper">


    <select id="selectEzEcOrderPayLogCount" resultType="java.lang.Integer">
        select
            count(1)
        from
            ez_if.ez_ec_order_pay_log
        where
            tid =  #{tid, jdbcType = VARCHAR}
        and status_cd = #{statusCd, jdbcType = VARCHAR}
    </select>

    <select id="selectApiPgPayCount" resultType="java.lang.Integer">
        select
            count(1)
        from
            ez_if.API_PG_PRSNL_PAY
        where
            tid =  #{tid, jdbcType = VARCHAR}
          and status_cd = #{statusCd, jdbcType = VARCHAR}
    </select>

</mapper>
