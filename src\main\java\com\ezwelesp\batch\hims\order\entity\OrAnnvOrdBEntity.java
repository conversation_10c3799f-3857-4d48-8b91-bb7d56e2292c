package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 기념일주문기본(ez_or.or_annv_ord_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrAnnvOrdBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 기념일주문순번(annv_ord_seq) not null
     */
    private Long annvOrdSeq;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 신청자명(aplr_nm) not null
     */
    private String aplrNm;

    /**
     * 신청자소속명(aplr_blgt_nm) not null
     */
    private String aplrBlgtNm;

    /**
     * 신청자전화번호(aplr_telno)
     */
    private String aplrTelno;

    /**
     * 신청자모바일전화번호(aplr_mbl_telno)
     */
    private String aplrMblTelno;

    /**
     * 지급대상사원명(pay_obj_emp_nm) not null
     */
    private String payObjEmpNm;

    /**
     * 지급대상사원소속명(pay_obj_emp_blgt_nm) not null
     */
    private String payObjEmpBlgtNm;

    /**
     * 지급대상사원번호(pay_obj_empno) not null
     */
    private String payObjEmpno;

    /**
     * 지급대상사원전화번호(pay_obj_emp_telno)
     */
    private String payObjEmpTelno;

    /**
     * 지급대상사원모바일전화번호(pay_obj_emp_mbl_telno) not null
     */
    private String payObjEmpMblTelno;

    /**
     * 기념일대리주문자사원번호(annv_sbst_ordr_empno)
     */
    private String annvSbstOrdrEmpno;

    /**
     * 수신자명(rcvr_nm) not null
     */
    private String rcvrNm;

    /**
     * 수신자모바일전화번호(rcvr_mbl_telno) not null
     */
    private String rcvrMblTelno;

    /**
     * 수신자우편번호(rcvr_zipcd) not null
     */
    private String rcvrZipcd;

    /**
     * 수신자기본주소(rcvr_bas_adr) not null
     */
    private String rcvrBasAdr;

    /**
     * 수신자상세주소(rcvr_dtl_adr) not null
     */
    private String rcvrDtlAdr;

    /**
     * 상품코드(gds_cd) not null
     */
    private String gdsCd;

    /**
     * 상품명(gds_nm) not null
     */
    private String gdsNm;

    /**
     * 주문수량(ord_qty) not null
     */
    private Integer ordQty;

    /**
     * 공급가격(spl_prc) not null
     */
    private BigDecimal splPrc;

    /**
     * 판매가격(sell_prc) not null
     */
    private BigDecimal sellPrc;

    /**
     * 기념일주문상태코드(annv_ord_st_cd) not null
     */
    private String annvOrdStCd;

    /**
     * 고객사공통기념일종류코드(cc_annv_knd_cd) not null
     */
    private String ccAnnvKndCd;

    /**
     * 기념일주문배송상태코드(annv_ord_dlv_st_cd) not null
     */
    private String annvOrdDlvStCd;

    /**
     * 출산상품승인상태코드(chldb_gds_apv_st_cd)
     */
    private String chldbGdsApvStCd;

    /**
     * 기념일주문우대혜택종류코드(annv_ord_prim_bnft_knd_cd)
     */
    private String annvOrdPrimBnftKndCd;

    /**
     * 주문일시(ord_dtm) not null
     */
    private String ordDtm;

    /**
     * 배송희망일자(dlv_hope_dt) not null
     */
    private String dlvHopeDt;

    /**
     * 기념일주문자변경일시(annv_ordr_chg_dtm)
     */
    private String annvOrdrChgDtm;

    /**
     * 주문상태변경일시(ord_st_chg_dtm)
     */
    private String ordStChgDtm;

    /**
     * 배송상태변경일시(dlv_st_chg_dtm)
     */
    private String dlvStChgDtm;

    /**
     * 기념일일자(annv_dt)
     */
    private String annvDt;

    /**
     * 승인일시(apv_dtm)
     */
    private String apvDtm;

    /**
     * 기념일주문수신자관계명(annv_ord_rcvr_rel_nm) not null
     */
    private String annvOrdRcvrRelNm;

    /**
     * 배송요청내용(dlv_req_cntn)
     */
    private String dlvReqCntn;

    /**
     * 관리자메모(mgr_memo)
     */
    private String mgrMemo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최초등록IP(frst_reg_ip) not null
     */
    private String frstRegIp;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

    /**
     * 최종수정IP(last_mod_ip) not null
     */
    private String lastModIp;

}
