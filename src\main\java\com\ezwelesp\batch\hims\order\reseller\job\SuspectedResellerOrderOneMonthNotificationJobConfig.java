package com.ezwelesp.batch.hims.order.reseller.job;

import com.ezwelesp.batch.hims.order.reseller.tasklet.SuspectedResellerOrderOneMonthNotificationTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 복지샵,브랜드몰 고객 월별 리셀러 의심 주문 알림 [BA_HIOR00030]
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.reseller.job
 * @since 2025.05.20
 */
@Configuration
@RequiredArgsConstructor
public class SuspectedResellerOrderOneMonthNotificationJobConfig {
    private final CommonJobListener commonJobListener;
    private final SuspectedResellerOrderOneMonthNotificationTasklet suspectedResellerOrderOneMonthNotificationTasklet;

    @Bean("BA_HIOR00030")
    public Job suspectedResellerOrderOneMonthNotificationJob(
            JobRepository jobRepository,
            @Qualifier("BA_HIOR00030_STEP") Step suspectedResellerOrderOneMonthNotificationStep) {
        return new JobBuilder("BA_HIOR00030", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(suspectedResellerOrderOneMonthNotificationStep)
                .build();
    }

    @Bean("BA_HIOR00030_STEP")
    public Step suspectedResellerOrderOneMonthNotificationStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00030_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(suspectedResellerOrderOneMonthNotificationTasklet, transactionManager)
                .build();
    }
}
