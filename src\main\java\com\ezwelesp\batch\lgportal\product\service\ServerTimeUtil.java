package com.ezwelesp.batch.lgportal.product.service;

import lombok.val;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class ServerTimeUtil {

    private ServerTimeUtil() {
        // 생성자를 명시적으로 선언하여 기본 생성자 추가 방지
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    // 서버의 현재 시간 반환
    public static LocalDateTime getServerTime() {
        return LocalDateTime.now();
    }

    // 서버의 현재 시간 반환
    public static LocalDateTime getServerTimeHour() {
        return LocalDateTime.now()
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
    }

    //yyyy-MM-dd HH:mm:ss 형태 String
    public static String getTimeToString(LocalDateTime dateTime) {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }

    // formatter 받아서 처리하는 로직
    public static String getCurrentServerTimeToStringWithFormatter(LocalDateTime dateTime, String formatter) {
        return dateTime.format(DateTimeFormatter.ofPattern(formatter));
    }



}

