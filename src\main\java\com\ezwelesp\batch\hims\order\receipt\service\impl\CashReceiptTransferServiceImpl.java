package com.ezwelesp.batch.hims.order.receipt.service.impl;

import com.ezwelesp.batch.hims.order.receipt.domain.CashPublicationGatherDto;
import com.ezwelesp.batch.hims.order.receipt.domain.CashReceiptTransferDto;
import com.ezwelesp.batch.hims.order.receipt.domain.ReceivedLinkedMapData;
import com.ezwelesp.batch.hims.order.receipt.service.CashReceiptTransferService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.Socket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.LinkedHashMap;

@Slf4j
@Configuration
@Service
@RequiredArgsConstructor
class CashReceiptTransferServiceImpl extends CashReceiptTransferDto implements CashReceiptTransferService {

    static Socket socket;
    static DataInputStream dis;
    static DataOutputStream dos;

    @Value("${hyundai-futurenet.host}")
    private String host;
    @Value("${hyundai-futurenet.port}")
    private int port;



    public static void start(String host,int port){
        try {
//            // 개발 : *************    개발(U2L) : *************      운영 : *************    운영(U2L) : *************
            log.debug("host:{}",host);
            log.debug("port:{}",port);

            socket = new Socket(host, port);
            socket.setSoTimeout(30*1000);

            dis = new DataInputStream(new BufferedInputStream(socket.getInputStream()));
            dos = new DataOutputStream(new BufferedOutputStream(socket.getOutputStream()));

        } catch (UnknownHostException e) {
            log.error(e.getMessage(),e);
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        }
    }




    public void cashReciptIssue(CashPublicationGatherDto cashPubGatherDto){
        log.info("===========================================================");
        log.info("=================      IT&E POS 발행 호출      ======================");
        log.info("===========================================================");

        try {
            String sendPos = null;
            String receivePos = null;
            StringBuilder sendSb = new StringBuilder();

            start(host,port);

            String staTx = new String(new byte[] {(byte)0x02});
            String endTx = new String(new byte[] {(byte)0x03});
            String crTx = new String(new byte[] {(byte)0x0D});

            sendSb.append(fillLengthEUCKRLeft(stx,staTx,false));                                                                    //STX(전문시작)
            sendSb.append(fillLengthEUCKRLeft(msgType,"0200",false)); 															//MSG_TYPE(거래구분) - 0200 승인요청 고정
            sendSb.append(fillLengthEUCKRLeft(tsDate,"",false)); 																	//TS_DATE(전문생성일자) - 공란
            sendSb.append(fillLengthEUCKRLeft(saleDate,cashPubGatherDto.getCsrcPblcObjDt(),false)); 					//SALE_DATE(거래일자) - TARGET_ISSUE_DT
            if("Y".equals(cashPubGatherDto.getClxpItdObjYn())){
                sendSb.append(fillLengthEUCKRLeft(termId,"DTC4000102",false)); 								//TERM_ID(단말기번호) - 쿼리문 및 필드 추가 필요
            }else{
                sendSb.append(fillLengthEUCKRLeft(termId,"DTC0000102",false)); 								//TERM_ID(단말기번호) - 쿼리문 및 필드 추가 필요
            }

            sendSb.append(fillLengthEUCKRLeft(issueTp,cashPubGatherDto.getCsrcPblcUsgCd(),false)); 								//ISSUE_TP(거래자구분) - USE_OPT
            String inputTpStr = (cashPubGatherDto.getEncCsrcPblcNo().getBytes().length > 12)?"0":"1";

            sendSb.append(fillLengthEUCKRLeft(inputTp,inputTpStr,false)); 								//INPUT_TP(입력구분) - ISSUE_NUM 복호화후 12자리 이상 여부로 판단
            sendSb.append(fillLengthEUCKRLeft(inData,cashPubGatherDto.getEncCsrcPblcNo(),false)); 								//IN_DATA(신분확인) - ISSNU_NUM
            sendSb.append(fillLengthEUCKRRight(dealAmt,String.valueOf(cashPubGatherDto.getCsrcSplPrc()),true)); 	//DEAL_AMT(공급가액) - SUPPLY_AMT
            sendSb.append(fillLengthEUCKRRight(vat,String.valueOf(cashPubGatherDto.getCsrcVatAmt()),true)); 				//VAT(부가세) - VAT_AMT
            sendSb.append(fillLengthEUCKRRight(svcFee,"0",true)); 																	//SVC_FEE(봉사료) - 0 고정
            sendSb.append(fillLengthEUCKRRight(saleAmt,String.valueOf(cashPubGatherDto.getCsrcGdsPblcAmt()),true)); 	//SALE_AMT(거래금액) - REAL_PAY_AMT
            sendSb.append(fillLengthEUCKRLeft(coTp,"102",false)); 																	//CO_TP(회사구분) - 102 고정
            sendSb.append(fillLengthEUCKRLeft(bizNo,cashPubGatherDto.getCspBzrn(),false)); 								//BIZ_NO(가맹점사업자번호) - BSNS_NUM
            sendSb.append(fillLengthEUCKRLeft(ktrcNo,"",false)); 																	//KTRC_NO(거래고유번호) - 공란
            sendSb.append(fillLengthEUCKRLeft(aprvNo,"",false)); 																	//APRV_NO(승인번호) - 공란
            sendSb.append(fillLengthEUCKRLeft(rspCd,"",false)); 																	//RSP_CD(응답코드) - 공란
            sendSb.append(fillLengthEUCKRLeft(rspMsg,"",false)); 																	//RSP_MSG(응답MSG) - 공란
            sendSb.append(fillLengthEUCKRLeft(cancelCd,"",false)); 																//CANCEL_CD(취소사유) - 공란
            sendSb.append(fillLengthEUCKRLeft(oriAprvNo,"",false)); 																//ORI_APRV_NO(원거래승인번호) - 공란
            sendSb.append(fillLengthEUCKRLeft(oriSaleDate,"",false)); 															//ORI_SALE_DATE(원거래일자) - 공란
            sendSb.append(fillLengthEUCKRLeft(itemNm,cashPubGatherDto.getGdsNm(),false)); 							//ITEM_NM(상품명) - GOODS_NM 30LEN SUBSTR
            sendSb.append(fillLengthEUCKRLeft(custNm,cashPubGatherDto.getCspNm(),false)); 								//CUST_NM(구매자명) - SND_NM
            sendSb.append(fillLengthEUCKRLeft(filler1,cashPubGatherDto.getOrdNo(),false)); 								//FILLER1(예비필드1) - ORDER_NUM
            sendSb.append(fillLengthEUCKRLeft(filler2,(cashPubGatherDto.getAspOrdNo()==null)?"":cashPubGatherDto.getAspOrdNo(),false)); 							//FILLER2(예비필드2) - ASP_ORDER_NUM
            String filter3Str = "ERM-" + cashPubGatherDto.getOrdNo() +"-"+ cashPubGatherDto.getOrdGdsSeq() +"-"+ cashPubGatherDto.getClmNo() +"-"+ "1002";
            sendSb.append(fillLengthEUCKRLeft(filler3,filter3Str,false)); 									//FILLER3(예비필드3) - ERM-ORDER_NUM-ORDER_GOODS_SEQ-DB_TYPE
            sendSb.append(fillLengthEUCKRLeft(etx,endTx,false)); 						            								//ETX(전문종료)
            sendSb.append(fillLengthEUCKRLeft(cr,crTx,false)); 						                                                //CR(종료처리)

            sendPos = sendSb.toString();
            log.debug("\n[ Data Send ]\n" + sendPos);

            byte[] sendByte = new byte[leng];
            sendByte = sendPos.getBytes("EUC-KR");

            log.debug("\n[ Byte length ]\n" + sendByte.length);

            // 요청 패킷을 송신한다.
            send(sendByte);

            byte[] receiveByte = new byte[leng];
            // 응답 패킷을 수신한다
            receiveByte = receive(leng);
            receivePos = new String(receiveByte, 0, leng, "EUC-KR");
            log.debug("\n[ Data Received ]\n" + receivePos);
            log.debug("\n[ Byte length ]\n" + sendByte.length);

            ReceivedLinkedMapData receivedLinkedMapData = new ReceivedLinkedMapData();
            readPosReceive(receiveByte, receivedLinkedMapData);

            /************************************************************************************
             * IT&E POS 응답코드
             * 0000 정상완료(전문 메시지타입 오류)
             * 8003 FORMAT 오류 재시도 요망(전문 메시지타입 오류)
             * 8006 거래금액 너무 적음(1원이상 거래요망)
             * 8010 금액입력오류(금액입력오류)
             * 8016 해당서비스 미연계 가맹점(도서공연비 승인불가 가맹점)
             * 8414 승인 거래 없음(원거래 조회 실패)
             * 8433 이미 취소된 거래입니다(기취소 거래)
             * 8735 서비스미등록 가맹점(미등록 가맹점 TID)
             * 9951 휴/폐업 가맹점 사업자번호(BL 가맹점 사업자번호)
             * 9952 원거래 식별번호 불일치(원거래 식별번호 불일치)
             * 9953 미등록 사업자번호(미등록 사업자번호)
             * 9954 카드번호 복호화 오류(HSM복호화오류)
             * 9958 신분확인 오류(8003에 대해서 신분확인만 별도 추가 요청한 응답코드)
             * S101 승인처리 중 DB에러(DB SELECT 실패)
             * S411 전문 데이터 길이 오류(신분확인 데이터 길이 오류)
             * 3010 현금영수증 대외계 시스템 장애(현금영수증 시스템 담당자 확인 요망)
             ************************************************************************************/

            log.debug("==================================================================");
            log.debug("RSP_CD : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("rspCdStr") + "\'");
            log.debug("RSP_MSG : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("rspMsgStr") + "\'");
            log.debug("KTRC_NO : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("ktrcNoStr") + "\'");
            log.debug("APRV_NO : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("aprvNoStr") + "\'");
            log.debug("TS_DATE : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("tsDateStr") + "\'");
            log.debug("DEAL_AMT : " + "\'" + Integer.parseInt(receivedLinkedMapData.getReceivedLinkedHashMap().get("dealAmtStr")) + "\'");
            log.debug("VAT : " + "\'" + Integer.parseInt(receivedLinkedMapData.getReceivedLinkedHashMap().get("vatStr")) + "\'");
            log.debug("FILLER3 : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("filler3Str") + "\'");
            log.debug("==================================================================");

            if(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspCdStr").equals("0000")) {
                cashPubGatherDto.setResultCode(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspCdStr"));
                cashPubGatherDto.setResultMsg(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspMsgStr"));
                cashPubGatherDto.setTid(receivedLinkedMapData.getReceivedLinkedHashMap().get("ktrcNoStr"));
                cashPubGatherDto.setApplNum(receivedLinkedMapData.getReceivedLinkedHashMap().get("aprvNoStr"));
                cashPubGatherDto.setApplTime(receivedLinkedMapData.getReceivedLinkedHashMap().get("tsDateStr"));
                cashPubGatherDto.setApplSupplyAmt(Integer.parseInt(receivedLinkedMapData.getReceivedLinkedHashMap().get("dealAmtStr")));
                cashPubGatherDto.setApplVatAmt(Integer.parseInt(receivedLinkedMapData.getReceivedLinkedHashMap().get("vatStr")));
            } else {
                cashPubGatherDto.setResultCode(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspCdStr"));
                cashPubGatherDto.setResultMsg(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspMsgStr"));
            }

            stop();


        } catch ( Exception ex ) {
            log.error("[EXC] POS 발행 호출 오류", ex.getMessage());
            cashPubGatherDto.setResultCode("99");
            cashPubGatherDto.setResultMsg("[EXC] POS 발행 호출 오류");
        }
    }

    public void cashReciptIssueCancel(CashPublicationGatherDto cashPubGatherDto){
        log.info("===========================================================");
        log.info("================       IT&E POS 발행 취소 호출       ===================");
        log.info("===========================================================");
        try {
            String sendPos = null;
            String receivePos = null;
            StringBuilder sendSb = new StringBuilder();

            start(host,port);

            String staTx = new String(new byte[] {(byte)0x02});
            String endTx = new String(new byte[] {(byte)0x03});
            String crTx = new String(new byte[] {(byte)0x0D});

            sendSb.append(fillLengthEUCKRLeft(stx,staTx,false));																	//STX(전문시작)
            sendSb.append(fillLengthEUCKRLeft(msgType,"0420",false)); 															//MSG_TYPE(거래구분) - 0420 취소요청 고정
            sendSb.append(fillLengthEUCKRLeft(tsDate,"",false)); 																	//TS_DATE(전문생성일자) - 공란
            sendSb.append(fillLengthEUCKRLeft(saleDate,cashPubGatherDto.getCsrcPblcCnclDt(),false)); 					//SALE_DATE(거래일자) - TARGET_CANCEL_DT
            if("Y".equals(cashPubGatherDto.getClxpItdObjYn())){
                sendSb.append(fillLengthEUCKRLeft(termId,"DTC4000102",false)); 								//TERM_ID(단말기번호) - 쿼리문 및 필드 추가 필요
            }else{
                sendSb.append(fillLengthEUCKRLeft(termId,"DTC0000102",false)); 								//TERM_ID(단말기번호) - 쿼리문 및 필드 추가 필요
            }
            sendSb.append(fillLengthEUCKRLeft(issueTp,cashPubGatherDto.getCsrcPblcUsgCd(),false)); 								//ISSUE_TP(거래자구분) - USE_OPT
            String inputTpStr = (cashPubGatherDto.getEncCsrcPblcNo().getBytes().length > 12)?"0":"1";

            sendSb.append(fillLengthEUCKRLeft(inputTp,inputTpStr,false)); 								//INPUT_TP(입력구분) - ISSUE_NUM 복호화후 12자리 이상 여부로 판단
            sendSb.append(fillLengthEUCKRLeft(inData,cashPubGatherDto.getEncCsrcPblcNo(),false)); 								//IN_DATA(신분확인) - ISSNU_NUM


            sendSb.append(fillLengthEUCKRRight(dealAmt,String.valueOf(cashPubGatherDto.getCsrcSplPrc()),true)); 	//DEAL_AMT(공급가액) - SUPPLY_AMT
            sendSb.append(fillLengthEUCKRRight(vat,String.valueOf(cashPubGatherDto.getCsrcVatAmt()),true)); 				//VAT(부가세) - VAT_AMT
            sendSb.append(fillLengthEUCKRRight(svcFee,"0",true)); 																	//SVC_FEE(봉사료) - 0 고정
            sendSb.append(fillLengthEUCKRRight(saleAmt,String.valueOf(cashPubGatherDto.getCsrcGdsPblcAmt()),true)); 	//SALE_AMT(거래금액) - REAL_PAY_AMT
            sendSb.append(fillLengthEUCKRLeft(coTp,"102",false)); 																	//CO_TP(회사구분) - 102 고정
            sendSb.append(fillLengthEUCKRLeft(bizNo,cashPubGatherDto.getCspBzrn(),false)); 								//BIZ_NO(가맹점사업자번호) - BSNS_NUM
            sendSb.append(fillLengthEUCKRLeft(ktrcNo,cashPubGatherDto.getCsrcPgApvNo(),false)); 							//KTRC_NO(거래고유번호) - RECEIPT_AUTH
            sendSb.append(fillLengthEUCKRLeft(aprvNo,"",false)); 																	//APRV_NO(승인번호) - 공란
            sendSb.append(fillLengthEUCKRLeft(rspCd,"",false)); 																	//RSP_CD(응답코드) - 공란
            sendSb.append(fillLengthEUCKRLeft(rspMsg,"",false)); 																	//RSP_MSG(응답MSG) - 공란
            sendSb.append(fillLengthEUCKRLeft(cancelCd,"1",false)); 																//CANCEL_CD(취소사유) - 1 주문취소로 고정
            sendSb.append(fillLengthEUCKRLeft(oriAprvNo,cashPubGatherDto.getApplNum(),false)); 							//ORI_APRV_NO(원거래승인번호) - RECEIPT_APPL_NUM
            sendSb.append(fillLengthEUCKRLeft(oriSaleDate,cashPubGatherDto.getCsrcPblcObjDt(),false)); 				//ORI_SALE_DATE(원거래일자) - TARGET_ISSUE_DT
            sendSb.append(fillLengthEUCKRLeft(itemNm,cashPubGatherDto.getGdsNm(),false)); 							//ITEM_NM(상품명) - GOODS_NM 30LEN SUBSTR
            sendSb.append(fillLengthEUCKRLeft(custNm,cashPubGatherDto.getCspNm(),false)); 								//CUST_NM(구매자명) - SND_NM
            sendSb.append(fillLengthEUCKRLeft(filler1,cashPubGatherDto.getOrdNo(),false)); 								//FILLER1(예비필드1) - ORDER_NUM
            sendSb.append(fillLengthEUCKRLeft(filler2,(cashPubGatherDto.getAspOrdNo()==null)?"":cashPubGatherDto.getAspOrdNo(),false));							//FILLER2(예비필드2) - ASP_ORDER_NUM
            String filter3Str = "ERM-" + cashPubGatherDto.getOrdNo() +"-"+ cashPubGatherDto.getOrdGdsSeq() +"-"+ cashPubGatherDto.getClmNo() +"-"+ "1002";
            sendSb.append(fillLengthEUCKRLeft(filler3,filter3Str,false)); 									//FILLER3(예비필드3) - ERM-ORDER_NUM-ORDER_GOODS_SEQ-DB_TYPE
            sendSb.append(fillLengthEUCKRLeft(etx,endTx,false)); 						            								//ETX(전문종료)
            sendSb.append(fillLengthEUCKRLeft(cr,crTx,false)); 						             									//CR(종료처리)

            sendPos = sendSb.toString();
            log.debug("\n[ Data Send ]\n" + sendPos);

            byte[] sendByte = new byte[leng];
            sendByte = sendPos.getBytes("EUC-KR");

            log.debug("\n[ Byte length ]\n" + sendByte.length);

            // 요청 패킷을 송신한다.
            send(sendByte);

            byte[] receiveByte = new byte[leng];
            // 응답 패킷을 수신한다
            receiveByte = receive(leng);
            receivePos = new String(receiveByte, 0, leng, "EUC-KR");
            log.debug("\n[ Data Received ]\n" + receivePos);
            log.debug("\n[ Byte length ]\n" + sendByte.length);

            ReceivedLinkedMapData receivedLinkedMapData = new ReceivedLinkedMapData();
            readPosReceive(receiveByte, receivedLinkedMapData);

            /************************************************************************************
             * IT&E POS 응답코드
             * 0000 정상완료(전문 메시지타입 오류)
             * 8003 FORMAT 오류 재시도 요망(전문 메시지타입 오류)
             * 8006 거래금액 너무 적음(1원이상 거래요망)
             * 8010 금액입력오류(금액입력오류)
             * 8016 해당서비스 미연계 가맹점(도서공연비 승인불가 가맹점)
             * 8414 승인 거래 없음(원거래 조회 실패)
             * 8433 이미 취소된 거래입니다(기취소 거래)
             * 8735 서비스미등록 가맹점(미등록 가맹점 TID)
             * 9951 휴/폐업 가맹점 사업자번호(BL 가맹점 사업자번호)
             * 9952 원거래 식별번호 불일치(원거래 식별번호 불일치)
             * 9953 미등록 사업자번호(미등록 사업자번호)
             * 9954 카드번호 복호화 오류(HSM복호화오류)
             * 9958 신분확인 오류(8003에 대해서 신분확인만 별도 추가 요청한 응답코드)
             * S101 승인처리 중 DB에러(DB SELECT 실패)
             * S411 전문 데이터 길이 오류(신분확인 데이터 길이 오류)
             * 3010 현금영수증 대외계 시스템 장애(현금영수증 시스템 담당자 확인 요망)
             ************************************************************************************/

            log.debug("==================================================================");
            log.debug("RSP_CD : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("rspCdStr") + "\'");
            log.debug("RSP_MSG : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("rspMsgStr") + "\'");
            log.debug("KTRC_NO : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("ktrcNoStr") + "\'");
            log.debug("APRV_NO : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("aprvNoStr") + "\'");
            log.debug("TS_DATE : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("tsDateStr") + "\'");
            log.debug("FILLER3 : " + "\'" + receivedLinkedMapData.getReceivedLinkedHashMap().get("filler3Str") + "\'");
            log.debug("==================================================================");

            if(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspCdStr").equals("0000")) {
                cashPubGatherDto.setResultCode(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspCdStr"));
                cashPubGatherDto.setResultMsg(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspMsgStr"));
                cashPubGatherDto.setCancelNum(receivedLinkedMapData.getReceivedLinkedHashMap().get("aprvNoStr"));
                cashPubGatherDto.setCancelTime(receivedLinkedMapData.getReceivedLinkedHashMap().get("tsDateStr"));

            } else {
                cashPubGatherDto.setResultCode(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspCdStr"));
                cashPubGatherDto.setResultMsg(receivedLinkedMapData.getReceivedLinkedHashMap().get("rspMsgStr"));
            }

            stop();


        } catch ( Exception ex ) {
            log.error("[EXC] POS 발행 호출 오류", ex);
            cashPubGatherDto.setResultCode("99");
            cashPubGatherDto.setResultMsg("[EXC] POS 발행 호출 오류");
        }
    }

    private static void readPosReceive(byte[] receiveByte, ReceivedLinkedMapData receivedLinkedMapData) throws UnsupportedEncodingException {
        LinkedHashMap<String, String> linkedHashMap = new LinkedHashMap<String, String>();

        for (int i = 0; i < arrayId.length - 1; i++) {
            System.arraycopy(receiveByte, sumUpArray(arrayId, i), arrayByte[i + 1], 0, arrayId[i + 1]);
            String newStr = new String(arrayByte[i + 1], "EUC-KR");
            log.debug("\n[" + arrayStr[i + 1] + "] " + "\'" + newStr + "\'");
            linkedHashMap.put(arrayKey[i + 1], newStr.trim());
        }
        receivedLinkedMapData.setReceivedLinkedHashMap(linkedHashMap);
    }

    private static int sumUpArray(int[] arrayId, int i) {
        int sum = 0;
        for (int j = 0; j <= i; j++) {
            sum += arrayId[j];
        }
        return sum;
    }

    // 좌측 정렬 / 인코딩 EUC-KR
    public static String fillLengthEUCKRLeft(int length, String str, boolean fillZero) throws UnsupportedEncodingException {
        StringBuffer padded = new StringBuffer(str);
        while (padded.toString().getBytes("EUC-KR").length < length) {
            if (fillZero) {
                padded.append("0");
            } else {
                padded.append(" ");
            }
        }
        return padded.toString();
    }

    // 우측 정렬 / 인코딩 EUC-KR
    public static String fillLengthEUCKRRight(int length, String str, boolean fillZero) throws UnsupportedEncodingException {
        StringBuffer padded = new StringBuffer();
        while (padded.length() < length - str.getBytes("EUC-KR").length) {
            if (fillZero) {
                padded.append("0");
            } else {
                padded.append(" ");
            }
        }
        padded.append(str);
        return padded.toString();
    }

    public static   void send(byte[] sePackets) throws IOException{
        dos.write(sePackets, 0, sePackets.length);
        dos.flush();
    }

    public static byte[] receive(int len) throws IOException{
        byte[] bytes = null;
        try {
            bytes = new byte[len];
            dis.readFully(bytes, 0, bytes.length);
        } catch (SocketTimeoutException e) {
            log.error("중계서버로부터 데이터 수신 중 타임아웃 되었습니다.");
            throw e;
        } catch(SocketException se) {
            log.error("중계서버로부터 접속이 끊겼습니다.");
            throw se;
        }
        return bytes;
    }

    public static void stop(){
        if(dis != null){
            try {
                dis.close();
            } catch (IOException e) {
                log.error(e.getMessage(),e);
            }
            dis = null;
        }

        if(dos != null){
            try {
                dos.close();
            } catch (IOException e) {
                log.error(e.getMessage(),e);
            }
            dos = null;
        }

        if(socket != null){
            try {
                socket.close();
            } catch (IOException e) {
                log.error(e.getMessage(),e);
            }
            socket = null;
        }
    }

}
