package com.ezwelesp.batch.lgportal.order.job;

import com.ezwelesp.batch.lgportal.entity.ApiBatchSendEntity;
import com.ezwelesp.batch.lgportal.order.chunk.reader.OrderCancelOstkItemReader;
import com.ezwelesp.batch.lgportal.order.chunk.writer.OrderCancelOstkItemWriter;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.listener.CommonJobListener;
import com.ezwelesp.batch.listener.CommonStepListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class OrderCancelOstkJobConfig {

    private final CommonJobListener commonJobListener;

    @Value("${lgportal.chunk-size}")
    private int chunkSize;


    @Bean(LgConstants.JobName.JOB_ORDER_CANCEL_SOLDOUT)
    public Job orderCancelSoldout(JobRepository jobRepository, @Qualifier("orderCancelOstkStep") Step orderCancelSoldoutStep) {
        return new JobBuilder(LgConstants.JobName.JOB_ORDER_CANCEL_SOLDOUT, jobRepository)
                .listener(commonJobListener)
                .start(orderCancelSoldoutStep)
                .build();
    }

    @Bean(name = "orderCancelOstkStep")
    public Step orderCancelOstkStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager,
            OrderCancelOstkItemReader orderCancelOstkItemReader,
            OrderCancelOstkItemWriter orderCancelOstkItemWriter, CommonStepListener commonStepListener) {

        return new StepBuilder("orderCancelOstkStep", jobRepository)
                .allowStartIfComplete(true)
                .<ApiBatchSendEntity, ApiBatchSendEntity>chunk(chunkSize, transactionManager)
                .transactionManager(transactionManager)
                .reader(orderCancelOstkItemReader)
                .writer(orderCancelOstkItemWriter)
                .listener(commonStepListener)
                .build();

    }
}
