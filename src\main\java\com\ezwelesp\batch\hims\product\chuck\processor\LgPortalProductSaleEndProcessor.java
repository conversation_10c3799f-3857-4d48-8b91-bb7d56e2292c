package com.ezwelesp.batch.hims.product.chuck.processor;

import com.ezwelesp.batch.hims.product.domain.dto.LgPortalProductSaleEndDto;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import static com.ezwelesp.batch.lgportal.product.domain.enums.lgPortalSendKey.SOLD_OUT;

@Slf4j
@Component
@StepScope
@RequiredArgsConstructor
public class LgPortalProductSaleEndProcessor
        implements ItemProcessor<LgPortalProductSaleEndDto, LgPortalProductSaleEndDto> {

    @Override
    public LgPortalProductSaleEndDto process(@NonNull LgPortalProductSaleEndDto item) {
        return LgPortalProductSaleEndDto.builder()
                .certKey("u6;ShVow;kgR5=$")
                .productNo(item.getTargetGoodsCd())
                .sendKey(SOLD_OUT)
                .sendYn("N")
                .regId("esp_batch")
                .deliveryNo(String.valueOf(item.getGoodsCd()))
                .build();
    }

}

