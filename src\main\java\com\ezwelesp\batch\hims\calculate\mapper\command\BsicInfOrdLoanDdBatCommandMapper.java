package com.ezwelesp.batch.hims.calculate.mapper.command;

import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface BsicInfOrdLoanDdBatCommandMapper {
    /**
     * 데이터 이관 처리 프로시저 호출
     * @param parameter
     */
    void callBsicInfOrdLoanDdBatDataProc(Map<?, ?> parameter);
    /**
     * 고객사 사용내역, 소속분리, 카드정보, 특별포인트정보 이관 프로시저 호출
     */
    void callBsicInfOrdLoanDdBatEtcProc();
    /**
     * 온라인 정산 데이터 이관 프로시저 호출
     */
    void callBsicInfOrdLoanDdBatProcOnlnProc();
    
    /**
     * 오프라인 카드 정산 데이터 이관 프로시저 호출
     */
    void callBsicInfOrdLoanDdBatProcOflnProc();
    /**
     * 항공마일리지 데이터 처리 프로시저 호출
     */
    void callBsicInfOrdLoanDdBatPamProc();
    /**
     * 온라인 정산 데이터 처리(포인트직결제) 프로시저 호출
     */
    void callBsicInfOrdLoanDdBatPointProc();
    
    /**
     * 요약관리 / 고객사별 집계 데이터 생성 프로시저 호출
     */
    void callBsicInfOrdLoanDdBatSumProc();
    
    /**
     * 채권 데이터 처리 프로시저 호출
     */
    void callBsicInfOrdLoanDdBatLoanProc();
}