package com.ezwelesp.batch.lgportal.order.mapper.query;


import com.ezwelesp.batch.lgportal.entity.PdGdsCEntity;
import com.ezwelesp.batch.lgportal.entity.PdGdsOptCombBEntity;
import org.apache.ibatis.annotations.Param;


/**
 * The interface Product query mapper.
 */
public interface ProductQueryMapper {

    /**
     * Select goods pd gds c entity.
     *
     * @param gdsCd the gds cd
     * @return the pd gds c entity
     */
    PdGdsCEntity selectGoods(String gdsCd);

    /**
     * Select goods option base int.
     *
     * @param gdsCd the gds cd
     * @return the int
     */
    int selectGoodsOptionBase(String gdsCd);

    /**
     * Select goods option pd gds opt comb b entity.
     *
     * @param gdsCd the gds cd
     * @param optGdsCombSeq the opt gds comb seq
     * @return the pd gds opt comb b entity
     */
    PdGdsOptCombBEntity selectGoodsOption(String gdsCd, Long optGdsCombSeq);

}
