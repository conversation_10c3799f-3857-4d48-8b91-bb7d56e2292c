package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 하나투어예약API상세(ez_or.or_hanat_rsv_api_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrHanatRsvApiDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 하나투어사이트코드(hanat_site_cd)
     */
    private String hanatSiteCd;

    /**
     * 하나투어거래처코드(hanat_cust_cd)
     */
    private String hanatCustCd;

    /**
     * 하나투어상품코드(hanat_gds_cd)
     */
    private String hanatGdsCd;

    /**
     * 접속디바이스코드(acss_dvc_cd)
     */
    private String acssDvcCd;

    /**
     * 주문자명(ordr_nm)
     */
    private String ordrNm;

    /**
     * 주문자이메일주소(ordr_eml_adr)
     */
    private String ordrEmlAdr;

    /**
     * 예약자모바일유형코드(rsvr_mbl_typ_cd)
     */
    private String rsvrMblTypCd;

    /**
     * 주문자전화번호(ordr_telno)
     */
    private String ordrTelno;

    /**
     * 주문자생년월일일자(ordr_bymd_dt)
     */
    private String ordrBymdDt;

    /**
     * 외국인여부(frnr_yn) not null
     */
    private String frnrYn;

    /**
     * 주문요청메모(ord_req_memo)
     */
    private String ordReqMemo;

    /**
     * 선할인율(prdc_rt)
     */
    private Double prdcRt;

    /**
     * 하나투어예약코드(hanat_rsv_cd)
     */
    private String hanatRsvCd;

    /**
     * 하나투어API전송상태코드(hanat_api_trms_st_cd)
     */
    private String hanatApiTrmsStCd;

    /**
     * 재전송건수(rtrms_cnt)
     */
    private Integer rtrmsCnt;

    /**
     * 전송일시(trms_dtm)
     */
    private String trmsDtm;

    /**
     * 하나투어API오류코드(hanat_api_err_cd)
     */
    private String hanatApiErrCd;

    /**
     * 오류메시지내용(err_msg_cntn)
     */
    private String errMsgCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
