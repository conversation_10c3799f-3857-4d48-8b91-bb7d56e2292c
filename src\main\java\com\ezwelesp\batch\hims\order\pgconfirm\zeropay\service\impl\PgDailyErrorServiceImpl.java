package com.ezwelesp.batch.hims.order.pgconfirm.zeropay.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.stereotype.Service;

import com.ezwelesp.batch.hims.order.entity.PyPgPymtCmprErrMtrgLEntity;
import com.ezwelesp.batch.hims.order.pgconfirm.zeropay.mapper.command.PgDailyErrorCommandMapper;
import com.ezwelesp.batch.hims.order.pgconfirm.zeropay.mapper.query.PgDailyErrorQueryMapper;
import com.ezwelesp.batch.hims.order.pgconfirm.zeropay.service.PgDailyErrorService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class PgDailyErrorServiceImpl implements PgDailyErrorService{
	
	private final PgDailyErrorQueryMapper pgDailyErrorQueryMapper;
	private final PgDailyErrorCommandMapper pgDailyErrorCommandMapper;
	
	@Override
	public void callBatch() {
		List<HashMap> errList = new ArrayList<HashMap>();
		try {
			// PG(제로페이) 대사 기준날짜 조회
			HashMap<String, Object> zeroPayDateMap = new HashMap<String, Object>();
			zeroPayDateMap = this.getZeropayApplyDate();
			if(zeroPayDateMap.get("minConfirmDt") != null && !"".equals(zeroPayDateMap.get("minConfirmDt"))) {
				// PG(제로페이) 대사 오류 목록 조회(복지관기준)
				List<HashMap> cuserZeroPayDiffList = this.getCuserZeroPayDiffList(zeroPayDateMap);
				if(!cuserZeroPayDiffList.isEmpty()) {
					for(HashMap map : cuserZeroPayDiffList) {
						errList.add(map);
					}
				}
				
				// PG(제로페이) 대사 오류 목록 조회(제로페이 기준)
				List<HashMap> pgZeroPayDiffList = this.getPgZeroPayDiffList();
				if(!pgZeroPayDiffList.isEmpty()) {
					for(HashMap map : pgZeroPayDiffList) {
						errList.add(map);
					}
				}
			}else {
				// 제로페이 PG 누락 발생 시, 에러 메일 발송
			}

			// PG 일단위 승인 대사 오류건 DB 등록(EZ_EC_ORDER_PAY_ERR)
			if(!errList.isEmpty()) {
				errList.stream().distinct().forEach(item -> {
					this.insertPgDailyErrList(PyPgPymtCmprErrMtrgLEntity.builder()
							.pgPymtErrCd(String.valueOf(item.get("errType")))
							.ordNo(String.valueOf(item.get("orderNum")))
							.ordStCd(String.valueOf(item.get("orderStatus")))
							.aspOrdNo(String.valueOf(item.get("aspOrderNum")))
							.ordChNm(String.valueOf(item.get("orderType")))
							.pymtAmt(new BigDecimal(String.valueOf(item.get("recgPrice"))))
							.pgPymtApvAmt(new BigDecimal(String.valueOf(item.get("pgAmt"))))
							.pgKndCd(String.valueOf(item.get("pgType")))
							.pymtMnsNm(String.valueOf(item.get("payType")))
							.pgFrcsNo(String.valueOf(item.get("storeId")))
							.pgApvNo(String.valueOf(item.get("cancelTid")))
							.pgCrdcApvNo(String.valueOf(item.get("cardConfirmNum")))
							.pgFrcsOrdNo(String.valueOf(item.get("shopOrderNum")))
							.acssTeTypNm(String.valueOf(item.get("deviceType")))
							.mgrCnftCmptYn("N")
							.mgrMemo("")
							.build());
					
				});
			}
		} catch (Exception e) {
			log.error("처리중 에러가 발생했습니다.",e);
	//		TODO 알림 
	//		HashMap map = new HashMap();
	//		map.put("test", "111");
	//		errList.add(map);
	//		result = commonMonitorJobsService.getCommonMonitorJobs(jobStartDt, "PG_DAILY_ERROR_REPORT_001", errList, "[PG 일단위 승인 대사] 배치 오류 발생<br>" + e);
		
		} 
	}


	/**
	 * PG 시간단위 승인 대사 오류건 DB 등록(EZ_EC_ORDER_PAY_ERR)
	 * @param pyPgPymtCmprErrMtrgLEntity
	 */
	private void insertPgDailyErrList(PyPgPymtCmprErrMtrgLEntity pyPgPymtCmprErrMtrgLEntity) {
		this.pgDailyErrorCommandMapper.insertPgDailyErrList(pyPgPymtCmprErrMtrgLEntity);
	}


	/**
	 * // PG(제로페이) 대사 오류 목록 조회(제로페이 기준)
	 * @return
	 */
	private List<HashMap> getPgZeroPayDiffList() {
		return pgDailyErrorQueryMapper.selectPgZeroPayDiffList();
	}

	/**
	 * // PG(제로페이) 대사 오류 목록 조회(복지관기준)
	 * @param zeroPayDateMap
	 * @return
	 */
	private List<HashMap> getCuserZeroPayDiffList(HashMap<String, Object> zeroPayDateMap) {
		return pgDailyErrorQueryMapper.selectCuserZeroPayDiffList(zeroPayDateMap);
	}

	/**
	 * // PG(제로페이) 대사 기준날짜 조회
	 * @return
	 */
	private HashMap<String, Object> getZeropayApplyDate() {
		return pgDailyErrorQueryMapper.selectZeropayApplyDate();
	}

}
