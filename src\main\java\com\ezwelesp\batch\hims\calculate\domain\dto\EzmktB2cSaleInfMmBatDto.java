package com.ezwelesp.batch.hims.calculate.domain.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
public class EzmktB2cSaleInfMmBatDto {
    
    private String accountYm; // 정산년월
    private String chCd; // 채널코드
    private String orderNum; // 주문번호
    private Integer orderGoodsNum; // 주문상품순번
    private String orderCancelYn; // 주문취소여부
    private String orderDt; // 주문일시
    private String cancelDt; // 취소일시
    private String orderStatus; // 이지웰마켓B2C주문상태코드
    private String cspCd; // 협력사코드
    private String mkUserKey; // 사용자고유번호
    private String goodsCd; // 상품코드
    private String goodsNm; // 상품명
    private String taxYn; // 과세종류코드
    private Integer salePrice; // 판매가격
    private Integer orderQty; // 주문수량
    private Integer orderAmt; // 주문금액
    private Long point; // 일반포인트
    private Integer card; // 카드결제
    private Long virture; // 가상계좌
    private Integer reserve; // 적립금
    private Integer special; // 특별포인트
    private Integer trans; //계좌이체
    private Long coupon; // 쿠폰
    private Long zeropay; // 제로페이
    private Long naverPay; // 네이버페이
    private Long onnuriPay; // 온누리페이
    private Long digitalOnnuri; // 디지털온누리페이
//    private Integer request; // TODO 0원이라 미사용
    private Integer approval; // 승인금액
//    private String paylater; // TODO 0원이라 미사용
    private Long korchamAmt; // 대한상공회의소결제금액
    private String orderNm; //주문명
    private Long totalAmt; // 주문합계금액
    private Integer dlvrAmt; // 배송비용
}