<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.csp.mapper.query.CspOrderCurrentStateNotificationQueryMapper">

    <select id="selectCspOrderCurrentSateList" resultType="com.ezwelesp.batch.hims.order.csp.domain.CspOrderCurrentStateDto">
        with v_csp as (
            select ccb.csp_cd
                  , cmrncl.mgr_mbl_telno
              from ez_co.co_csp_b ccb
              join ez_cm.cm_mgr_r2nd_crtf_l cmrncl on cmrncl.csp_cd = ccb.csp_cd
             where ccb.ch_cd <![CDATA[<>]]> '120' /* WOW 제외 */
            /* and ccb.csp_st_cd = 'REGC' 등록완료 | 데이터 없어서 임시주석 */
               and cmrncl.tmsg_rcv_agr_yn = 'Y' /* 문자메시지수신동의여부 */
               and not exists (select 1 /* 협력사 휴일 제외 */
                                   from ez_co.co_hldy_b
                                  where csp_cd = ccb.csp_cd
                                    and use_yn = 'Y'
                                    and hldy_aply_obj_cd = 'CSP'
                                    and hldy_dt = to_char(now(), 'YYYYMMDD'))
               /*and not exists (select 1 /* 휴일 제외 */
                                   from ez_cm.cm_hldy_b
                                  where to_char(now(), 'YYYYMMDD') between strt_dtm and end_dtm */
               and extract(dow from now()) not in (0, 6) /* 토, 일 제외 */
               and exists (select 1 /* 3개월 이내 주문이 발생한 협력사 */
                              from ez_or.or_ord_b oob
                              join ez_or.or_ord_gds_d oogd on oogd.ord_no = oob.ord_no
                             where oogd.csp_cd = ccb.csp_cd
                               and oob.ord_dtm > TO_CHAR(NOW() - INTERVAL '3 month', 'YYYYMMDDHH24MISS'))
            group by ccb.csp_cd, cmrncl.mgr_mbl_telno
        )
        select T.csp_cd
              , T.mgr_mbl_telno
              , T.ord_cnt
              , T.dlv_dlay_cnt
              , T.dlv_wait_cnt
              , T.today_obnd_clsg
              , (select count(*) from ez_cm.bb_gds_inq_b where csp_cd = T.csp_cd and answ_cmpt_yn = 'N' and del_yn = 'N') as gds_inq_wait_cnt
              , (select count(*) from ez_cm.bb_gds_inq_b where csp_cd = T.csp_cd and answ_cmpt_yn = 'N' and del_yn = 'N') as gds_inq_over_wait_cnt
              , (select count(*) from ez_cm.bb_csp_inq_b where csp_cd = T.csp_cd and csp_inq_st_cd = '1001' and emgy_inq_yn = 'Y') as csp_inq_cnt
              , (select count(*) from ez_cm.bb_csp_inq_b where csp_cd = T.csp_cd and csp_inq_st_cd = '1001' and emgy_inq_yn = 'N') as csp_emgy_inq_cnt
              , (select count(*)
                   from ez_or.cl_clm_b ccb
                   join ez_or.cl_clm_gds_d ccgd on ccgd.clm_no = ccb.clm_no
                   join ez_or.or_ord_gds_d oogd on oogd.ord_no = ccgd.ord_no and oogd.ord_gds_seq = ccgd.ord_gds_seq
                  where ccb.clm_knd_cd = 'EXCH'
                    and ccb.clm_st_cd = 'APL'
                    and oogd.csp_cd = T.csp_cd) as exch_apl_cnt
              , (select count(*)
                   from ez_or.cl_clm_b ccb
                   join ez_or.cl_clm_gds_d ccgd on ccgd.clm_no = ccb.clm_no
                   join ez_or.or_ord_gds_d oogd on oogd.ord_no = ccgd.ord_no and oogd.ord_gds_seq = ccgd.ord_gds_seq
                  where ccb.clm_knd_cd = 'RTP'
                    and ccb.clm_st_cd = 'APL'
                    and oogd.csp_cd = T.csp_cd) as rtp_apl_cnt
              , (select count(*)
                   from ez_or.cl_clm_b ccb
                   join ez_or.cl_clm_gds_d ccgd on ccgd.clm_no = ccb.clm_no
                   join ez_or.or_ord_gds_d oogd on oogd.ord_no = ccgd.ord_no and oogd.ord_gds_seq = ccgd.ord_gds_seq
                  where ccb.clm_knd_cd = 'EXCH'
                    and ccb.clm_st_cd not in ('CMPT', 'WTDR')
                    and oogd.csp_cd = T.csp_cd
                    and exists (select 1
                                   from ez_or.cl_clm_gds_dlay_d
                                  where clm_no = ccgd.clm_no and clm_gds_seq = ccgd.clm_gds_seq and clm_dlay_st_cd = '2005')
                  ) as exch_dlay_cnt
              , (select count(*)
                   from ez_or.cl_clm_b ccb
                   join ez_or.cl_clm_gds_d ccgd on ccgd.clm_no = ccb.clm_no
                   join ez_or.or_ord_gds_d oogd on oogd.ord_no = ccgd.ord_no and oogd.ord_gds_seq = ccgd.ord_gds_seq
                  where ccb.clm_knd_cd = 'RTP'
                    and clm_rfnd_st_cd <![CDATA[<>]]> 'CMPT'
                    and oogd.csp_cd = T.csp_cd
                    and exists (select 1
                                   from ez_or.cl_clm_gds_dlay_d
                                  where clm_no = ccgd.clm_no and clm_gds_seq = ccgd.clm_gds_seq and clm_dlay_st_cd = '1008')
                  ) as rtp_rfnd_dlay_cnt
              , (select count(*)
                   from ez_or.cl_clm_b ccb
                   join ez_or.cl_clm_gds_d ccgd on ccgd.clm_no = ccb.clm_no
                   join ez_or.or_ord_gds_d oogd on oogd.ord_no = ccgd.ord_no and oogd.ord_gds_seq = ccgd.ord_gds_seq
                  where ccb.clm_knd_cd = 'EXCH'
                    and clm_st_cd not in ('CMPT', 'WTDR')
                    and oogd.csp_cd = T.csp_cd
                    and exists (select 1
                                   from ez_or.cl_clm_gds_dlay_d
                                  where clm_no = ccgd.clm_no and clm_gds_seq = ccgd.clm_gds_seq and clm_dlay_st_cd = '2003')
                  ) as exch_pkup_dlay_cnt
              , (select count(*)
                   from ez_or.cl_clm_b ccb
                   join ez_or.cl_clm_gds_d ccgd on ccgd.clm_no = ccb.clm_no
                   join ez_or.or_ord_gds_d oogd on oogd.ord_no = ccgd.ord_no and oogd.ord_gds_seq = ccgd.ord_gds_seq
                  where ccb.clm_knd_cd = 'RTP'
                    and clm_st_cd not in ('CMPT', 'WTDR')
                    and oogd.csp_cd = T.csp_cd
                    and exists (select 1
                                   from ez_or.cl_clm_gds_dlay_d
                                  where clm_no = ccgd.clm_no and clm_gds_seq = ccgd.clm_gds_seq and clm_dlay_st_cd = '1001')
                  ) as rtp_pkup_dlay_cnt
         from (select v.csp_cd
                     , v.mgr_mbl_telno
                     , count(ddb.dlv_st_cd = 'STBY')    as ord_cnt
                     , count(ddb.all_dlv_dlay_dcnt > 0) as dlv_dlay_cnt
                     , sum(case
                               when ccdpb.dlv_plcy_cd in ('1001', '1002', '1003', '1004') then
                                   case
                                       when ddb.dlv_strt_clsg_dt >= to_char(now(), 'YYYYMMDD') then
                                           case when ddb.dlv_st_cd in ('RDY', 'OBND') then 1 else 0 end
                                       else 0 end
                               else case
                                        when ddb.dlv_due_dt >= to_char(now(), 'YYYYMMDD') then
                                            case when ddb.dlv_st_cd in ('RDY', 'OBND') then 1 else 0 end
                                        else 0 end
                             end) as dlv_wait_cnt
                     , sum(case
                               when ccdpb.dlv_plcy_cd in ('1001', '1002', '1003', '1004') then
                                   case when ddb.dlv_strt_clsg_dt = to_char(now(), 'YYYYMMDD') then 1 else 0 end
                               else
                                   case when ddb.dlv_due_dt = to_char(now(), 'YYYYMMDD') then 1 else 0 end
                             end) as today_obnd_clsg
                 from v_csp v
                 join ez_or.dl_dlv_b ddb on ddb.csp_cd = v.csp_cd
                 join ez_co.co_csp_dlv_plcy_b ccdpb on ccdpb.csp_cd = ddb.csp_cd
                 join ez_or.or_ord_b oob on oob.ord_no = ddb.ord_no
                 join ez_or.dl_dlv_gds_d ddgd on ddgd.dlv_no = ddb.dlv_no
                 join ez_or.or_ord_gds_d oogd on oogd.ord_no = ddgd.ord_no and oogd.ord_gds_seq = ddgd.ord_gds_seq
                where ddb.clm_no is null
                  and ddb.dlv_obnd_typ_cd = 'ORD'
                  and ddgd.dlv_gds_qty - ddgd.dlv_cncl_gds_qty > 0
                /* and oogd.ch_cd in ('100', '101') */
                group by v.csp_cd, v.mgr_mbl_telno
         ) T
    </select>
</mapper>
