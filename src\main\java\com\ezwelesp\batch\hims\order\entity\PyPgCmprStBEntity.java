package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * PG대사상태기본(ez_or.py_pg_cmpr_st_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyPgCmprStBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * PG종류코드(pg_knd_cd) not null
     */
    private String pgKndCd;

    /**
     * PG대사거래종류코드(pg_cmpr_trd_knd_cd) not null
     */
    private String pgCmprTrdKndCd;

    /**
     * PG승인번호(pg_apv_no) not null
     */
    private String pgApvNo;

    /**
     * 취소PG승인번호(cncl_pg_apv_no) not null
     */
    private String cnclPgApvNo;

    /**
     * PG대사거래대상코드(pg_cmpr_trd_obj_cd)
     */
    private String pgCmprTrdObjCd;

    /**
     * PG대사거래비교구분코드(pg_cmpr_trd_comp_div_cd)
     */
    private String pgCmprTrdCompDivCd;

    /**
     * PG대사비교상태코드(pg_cmpr_comp_st_cd)
     */
    private String pgCmprCompStCd;

    /**
     * 결과메시지내용(rslt_msg_cntn)
     */
    private String rsltMsgCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
