package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 주문발송상품상세(ez_or.dl_ord_snd_gds_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlOrdSndGdsDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문발송순번(ord_snd_seq) not null
     */
    private Long ordSndSeq;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 주문발송상품상세순번(ord_snd_gds_dtl_seq) not null
     */
    private Long ordSndGdsDtlSeq;

    /**
     * 옵션상품조합순번(opt_gds_comb_seq)
     */
    private Long optGdsCombSeq;

    /**
     * 주문발송상태코드(ord_snd_st_cd)
     */
    private String ordSndStCd;

    /**
     * 협력사발행난수포함고정문자값(csp_pblc_rdno_incl_fix_cval)
     */
    private String cspPblcRdnoInclFixCval;

    /**
     * 무형상품난수번호(intg_gds_rdno_no)
     */
    private String intgGdsRdnoNo;

    /**
     * 무형상품난수발행차수(intg_gds_rdno_pblc_nos)
     */
    private Long intgGdsRdnoPblcNos;

    /**
     * 주문발송상품사용상태코드(ord_snd_gds_use_st_cd)
     */
    private String ordSndGdsUseStCd;

    /**
     * 주문발송상품난수상태코드(ord_snd_gds_rdno_st_cd)
     */
    private String ordSndGdsRdnoStCd;

    /**
     * 주문발송상품정산상태코드(ord_snd_gds_stl_st_cd)
     */
    private String ordSndGdsStlStCd;

    /**
     * 정산년월(stl_ym)
     */
    private String stlYm;

    /**
     * 세금계산서수신종류코드(txin_rcv_knd_cd)
     */
    private String txinRcvKndCd;

    /**
     * 발송일시(snd_dtm)
     */
    private String sndDtm;

    /**
     * 발송준비일시(snd_rdy_dtm)
     */
    private String sndRdyDtm;

    /**
     * 사용일시(use_dtm)
     */
    private String useDtm;

    /**
     * 사용취소일시(use_cncl_dtm)
     */
    private String useCnclDtm;

    /**
     * 발송취소일시(snd_cncl_dtm)
     */
    private String sndCnclDtm;

    /**
     * 주문취소사용자ID(ord_cncl_usr_id)
     */
    private String ordCnclUsrId;

    /**
     * 발송주문취소주체코드(snd_ord_cncl_magn_cd)
     */
    private String sndOrdCnclMagnCd;

    /**
     * 파기시작일시(dstr_strt_dtm)
     */
    private String dstrStrtDtm;

    /**
     * 파기종료일시(dstr_end_dtm)
     */
    private String dstrEndDtm;

    /**
     * 최종만료일시(last_expy_dtm)
     */
    private String lastExpyDtm;

    /**
     * 유효기간수정건수(vlid_term_mod_cnt)
     */
    private Integer vlidTermModCnt;

    /**
     * E쿠폰협력사요청고유번호(ecpn_csp_req_innt_no)
     */
    private String ecpnCspReqInntNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
