<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.delivery.mapper.command.DeliveryDelayCommandMapper">
    <update id="updateDeliveryDelayDays">
        update ez_or.dl_dlv_b
           set dlv_dlay_dcnt = #{dlvDlayDcnt}
             , all_dlv_dlay_dcnt = coalesce(dlv_due_dlay_dcnt, 0) + #{dlvDlayDcnt}
             , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
             , last_mod_usr_id = 'BATCH'
             , last_mod_pgm_id = 'BATCH'
        where dlv_no = #{dlvNo}
    </update>

    <update id="updateDeliveryDueDelayDays">
        update ez_or.dl_dlv_b
           set dlv_due_dlay_dcnt = #{dlvDueDlayDcnt}
             , all_dlv_dlay_dcnt = coalesce(dlv_dlay_dcnt, 0) + #{dlvDueDlayDcnt}
             , last_mod_dtm = to_char(now(), 'YYYYMMDDHH24MISS')
             , last_mod_usr_id = 'BATCH'
             , last_mod_pgm_id = 'BATCH'
        where dlv_no = #{dlvNo}
    </update>
</mapper>
