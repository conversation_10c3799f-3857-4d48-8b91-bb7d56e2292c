package com.ezwelesp.batch.hims.order.config;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

public class EcpnConstants {
    public static final String Y_STR = "Y";
    public static final String N_STR = "N";


    @Getter
    @RequiredArgsConstructor
    public enum BcdTypCdEnum {
        CODE_128("01", "CODE-128"),
        CODE_39("02", "CODE-39"),
        EAN_13("03", "EAN-13"),
        ITF("04", "ITF"),
        QRCODE("05", "QRCODE"),
        ;

        private final String code;
        private final String name;
    }

    @Getter
    @RequiredArgsConstructor
    public enum OrdSndStCdEnum {
        WAIT("01", "발송대기"),
        CMPT("02", "발송완료"),
        ERROR("03", "발송오류"),
        RDY("06", "발송준비중"),
        CNCL("07", "발송취소"),
        OBND("08", "발송진행"),
        ;

        private final String code;
        private final String name;
    }


    /**
     * 쿠폰발송 요청 시 발송번호
     *
     * default: 02-3282-0579
     * 이지웰(울산몰) : 1644-0559
     * 육군복지몰 : 02-2161-0900
     *
     * @param clientCd
     * @return
     */
    public static String getCallBackNum(String clientCd) {
        String callBack = "0232820579";
        if ("ulsanmall".equals(clientCd)
                || "dongbaekmall".equals(clientCd)
                || "ezlocal".equals(clientCd)
                || "ontongdaejeon".equals(clientCd)
                || clientCd.indexOf("dongbaekmall") > -1
        ) {
            callBack = "16440559";
        }else if ("mili".equals(clientCd) || "milifamily".equals(clientCd)) {
            callBack = "0221610900";
        }

        return callBack;
    }

    /**
     * 쿠폰발송 요청 시 발송번호
     *
     * default: 02-3282-0579
     * 이지웰(울산몰) : 1644-0559
     *
     * @param clientCd
     * @return
     */
    public static String getDefaultMsg(String clientCd) {
        String msg = "";
        if ("ulsanmall".equals(clientCd)
                || "dongbaekmall".equals(clientCd)
                || "ezlocal".equals(clientCd)
                || "ontongdaejeon".equals(clientCd)
                || clientCd.indexOf("dongbaekmall") > -1
        ) {
            msg = "<취소환불규정>\n";
            msg = msg + "- 취소/환불 요청은 구매자만 가능하며, 구매자에게 환불 처리\n";
            msg = msg + "- 유효기간 내에는 마이페이지에서 직접 취소 100% 처리 가능, 유효기간 만료 후 취소 시 구매금액의 90% 적립금 환불 처리\n";
            msg = msg + "- 취소/환불 관련 문의 : 현대이지웰(지역몰) -> 1644-0559\n";
        }
        return msg;
    }

    public static String getRefundMsg(String clientCd) {
        String msg = "";
        if ("ulsanmall".equals(clientCd)
                || "dongbaekmall".equals(clientCd)
                || "ezlocal".equals(clientCd)
                || "ontongdaejeon".equals(clientCd)
                || clientCd.indexOf("dongbaekmall") > -1
        ) {
            msg = "<취소환불규정>\n";
            msg = msg + "- 취소/환불 요청은 구매자만 가능하며, 구매자에게 환불 처리\n";
            msg = msg + "- 유효기간 내에는 마이페이지에서 직접 취소 100% 처리 가능, 유효기간 만료 후 취소 시 구매금액의 90% 적립금 환불 처리\n";
            msg = msg + "- 취소/환불 관련 문의 : 이지웰(지역몰) -> 1644-0559\n";

        }else if("mili".equals(clientCd)){

            msg = "<취소/환불 규정>\n";
            msg = msg + "- 취소/환불 요청은 구매자만 가능하며, 구매자에게 환불\n";
            msg = msg + "- 유효기간 내 취소 : 마이페이지에서 직접 취소, 100% 환불 가능\n";
            msg = msg + "- 유효기간 만료 후 취소 : 구매금액의 90% 적립금으로 환불 (일반 e쿠폰)\n";
            msg = msg + "- 유효기간 만료 후 취소 : 취소/환불 및 기간연장 불가 (생일축하금 e쿠폰)\n";
            msg = msg + "- 상품이용 문의 : (주)즐거운 1661-8191\n";
            msg = msg + "- 취소/환불 문의 : (주)현대이지웰 02-3282-0579 (일반 e쿠폰)\n";
            msg = msg + "- 취소/환불 문의 : (주)현대이지웰 02-2161-0900 (생일축하금 e쿠폰)\n";
        }

        return msg;
    }
}
