<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hpas.alarm.mapper.query.ReturnExchangeMailMainQueryMapper">

    <select id="selectReturnExchangeMailMainExchList">
        select oogd.csp_cd
             , csp.csp_nm
             , (select chrgr_eml_adr
                  from ez_co.co_csp_chrgr_d
                 where csp_chrgr_tsk_cd = 'OPER'
                   and csp_cd = oogd.csp_cd
                 limit 1) as email
             , oob.ord_no
             , oob.ord_dtm
             , ccb.clm_acpt_dtm
             , oogd.gds_cd 
             , oogd.gds_nm
          from ez_or.cl_clm_b ccb             -- 클레임기본
          join ez_or.or_ord_b oob             -- 주문기본
            on oob.ord_no = ccb.ord_no
          join ez_or.cl_clm_gds_d ccgd        -- 클레임상품상세
            on ccgd.clm_no = ccb.clm_no
          join ez_or.or_ord_gds_d oogd        -- 주문상품상세
            on oogd.ord_no = ccgd.ord_no
           and oogd.ord_gds_seq = ccgd.ord_gds_seq
          join ez_or.cl_clm_gds_dlay_d ccgdd  -- 클레임상품지연상세
            on ccgdd.clm_no = ccgd.clm_no
           and ccgdd.clm_gds_seq = ccgd.clm_gds_seq
          join ez_co.co_csp_b csp             -- 협력사기본
            on csp.csp_cd = oogd.csp_cd
         where oob.ord_dtm between to_char(current_date-90, 'yyyymmdd') || '000000' and to_char(current_date-7, 'yyyymmdd') || '235959'
           and ccb.clm_knd_cd = 'EXCH'  -- 클레임종류코드:교환
           and ccb.clm_st_cd = 'APL'    -- 클레임상태코드:신청
    </select>

    <select id="selectReturnExchangeMailMainRtpList">
        select oogd.csp_cd
             , csp.csp_nm
             , (select chrgr_eml_adr
                  from ez_co.co_csp_chrgr_d
                 where csp_chrgr_tsk_cd = 'OPER'
                   and csp_cd = oogd.csp_cd
                 limit 1) as email
             , oob.ord_no
             , oob.ord_dtm
             , ccb.clm_acpt_dtm
             , oogd.gds_cd 
             , oogd.gds_nm
          from ez_or.cl_clm_b ccb             -- 클레임기본
          join ez_or.or_ord_b oob             -- 주문기본
            on oob.ord_no = ccb.ord_no
          join ez_or.cl_clm_gds_d ccgd        -- 클레임상품상세
            on ccgd.clm_no = ccb.clm_no
          join ez_or.or_ord_gds_d oogd        -- 주문상품상세
            on oogd.ord_no = ccgd.ord_no
           and oogd.ord_gds_seq = ccgd.ord_gds_seq
          join ez_or.cl_clm_gds_dlay_d ccgdd  -- 클레임상품지연상세
            on ccgdd.clm_no = ccgd.clm_no
           and ccgdd.clm_gds_seq = ccgd.clm_gds_seq
          join ez_co.co_csp_b csp             -- 협력사기본
            on csp.csp_cd = oogd.csp_cd
         where oob.ord_dtm between to_char(current_date-90, 'yyyymmdd') || '000000' and to_char(current_date-7, 'yyyymmdd') || '235959'
           and ccb.clm_knd_cd = 'RTP'   -- 클레임종류코드:반품
           and ccb.clm_st_cd = 'APL'    -- 클레임상태코드:신청
    </select>

    <select id="selectReturnExchangeMailMainOrdList">
        select ogd.csp_cd
             , csp.csp_nm
             , (select chrgr_eml_adr
                  from ez_co.co_csp_chrgr_d
                 where csp_chrgr_tsk_cd = 'oper'
                   and csp_cd = ogd.csp_cd
                 limit 1) as email
             , oob.ord_no
             , oob.ord_dtm
             , null as clm_acpt_dtm
             , ogd.gds_cd 
             , ogd.gds_nm
          from ez_or.or_ord_b oob
          join ez_or.or_ord_gds_d ogd
            on oob.ord_no = ogd.ord_no
          join ez_co.co_csp_b csp on ogd.csp_cd = csp.csp_cd
         where oob.ord_dtm between to_char(current_date-90, 'yyyymmdd') || '000000' and to_char(current_date-7, 'yyyymmdd') || '235959'
           and oob.ord_st_cd in ('GDS_RDY', 'ORD_CMPT')  -- 상품준비중, 주문완료
    </select>

</mapper>
