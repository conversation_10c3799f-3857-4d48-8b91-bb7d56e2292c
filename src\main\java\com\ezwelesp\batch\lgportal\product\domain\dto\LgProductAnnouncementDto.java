package com.ezwelesp.batch.lgportal.product.domain.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

import java.util.List;

@Alias("LgProductAnnouncementDto")
@Getter
@SuperBuilder
public class LgProductAnnouncementDto {
    private String productMappingCode; // 이지웰상품 코드 gdsCd

    private List<ProductAnnouncement> items;


    public record ProductAnnouncement(String itemName, String itemData) {
    }
}


