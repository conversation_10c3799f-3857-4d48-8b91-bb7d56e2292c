package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 외부결제제휴사카테고리기본(ez_or.py_osd_pymt_asp_ctgr_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyOsdPymtAspCtgrBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 외부결제제휴사카테고리순번(osd_pymt_asp_ctgr_seq) not null
     */
    private Long osdPymtAspCtgrSeq;

    /**
     * 표준카테고리코드1(std_ctgr_cd1)
     */
    private String stdCtgrCd1;

    /**
     * 표준카테고리명1(std_ctgr_nm1)
     */
    private String stdCtgrNm1;

    /**
     * 표준카테고리코드2(std_ctgr_cd2)
     */
    private String stdCtgrCd2;

    /**
     * 표준카테고리명2(std_ctgr_nm2)
     */
    private String stdCtgrNm2;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
