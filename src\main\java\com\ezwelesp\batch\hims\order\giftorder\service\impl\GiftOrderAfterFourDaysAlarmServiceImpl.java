package com.ezwelesp.batch.hims.order.giftorder.service.impl;

import com.ezwelesp.batch.hims.order.config.OrderConstants;
import com.ezwelesp.batch.hims.order.entity.OrGvgftOrdBEntity;
import com.ezwelesp.batch.hims.order.giftorder.domain.GiftOrderDto;
import com.ezwelesp.batch.hims.order.giftorder.mapper.command.GiftOrderCommandMapper;
import com.ezwelesp.batch.hims.order.giftorder.mapper.query.GiftOrderQueryMapper;
import com.ezwelesp.batch.hims.order.giftorder.service.GiftOrderAfterFourDaysAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.service.impl
 * @since 2025.05.15
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class GiftOrderAfterFourDaysAlarmServiceImpl implements GiftOrderAfterFourDaysAlarmService {
    private final GiftOrderQueryMapper giftOrderQueryMapper;
    private final GiftOrderCommandMapper giftOrderCommandMapper;

    @Override
    public List<GiftOrderDto> getGiftOrderAfterFourDaysList() {
        return giftOrderQueryMapper.selectGiftOrderAfterFourDaysList();
    }

    @Override
    public void sendAlarmAndSaveStatus(GiftOrderDto dto) {
        // TODO 알림톡 발송
        log.info("알림톡 발송 내용 ---->>> {}", makMsg(dto));

        // 선물하기 주문 이력등록 및 상태 업데이트
        OrGvgftOrdBEntity orGvgftOrdBEntity = OrGvgftOrdBEntity.builder()
                .gvgftOrdNo(dto.getGvgftOrdNo())
                .build();
        giftOrderCommandMapper.insertGvgftOrdBHistory(orGvgftOrdBEntity);
        giftOrderCommandMapper.updateGvgftOrdB(orGvgftOrdBEntity.toBuilder()
                .gvgftOrdStCd(OrderConstants.GiftStatusCodeEnum.ENCRG_GIFT.getCode())
                .build());
    }

    private String makMsg(GiftOrderDto dto) {
        String callFrom = "";
        if ("WRM".equals(dto.getClntTypCd())) {
            callFrom = "02-3282-0505";
        } else if("PRM".equals(dto.getClntTypCd())) {
            callFrom = "02-3282-0570";
        } else {
            callFrom = "02-3282-0579";
            if("seheunghub".equals(dto.getClntCd()) || "sogong".equals(dto.getClntCd()) || "rotary".equals(dto.getClntCd())
                    || "daoupartner".equals(dto.getClntCd()) || "pask".equals(dto.getClntCd()) || "akom".equals(dto.getClntCd())
                    || "sf".equals(dto.getClntCd()) || "wtgm".equals(dto.getClntCd()) || "naver".equals(dto.getClntCd())
                    || "spcu".equals(dto.getClntCd()) || "spi".equals(dto.getClntCd()) || "chosunmembers".equals(dto.getClntCd())
                    || "viphyungji".equals(dto.getClntCd()) || "amorepartner".equals(dto.getClntCd()) || "kwcu".equals(dto.getClntCd())
                    || "sangdam".equals(dto.getClntCd()) || "8899".equals(dto.getClntCd()) || "syngentadream".equals(dto.getClntCd())
                    || "docple".equals(dto.getClntCd()) || "dongbupa".equals(dto.getClntCd()) || "newlife".equals(dto.getClntCd())
                    || "schs".equals(dto.getClntCd()) || "scard".equals(dto.getClntCd()) || "scourt".equals(dto.getClntCd())
                    || "gulumma".equals(dto.getClntCd()) || "metlifecafe".equals(dto.getClntCd()) || "aialife".equals(dto.getClntCd())
                    || "mb-race".equals(dto.getClntCd()) || "cowaymembers".equals(dto.getClntCd()) || "mb-race-service".equals(dto.getClntCd())
                    || "ktl1".equals(dto.getClntCd()) || "korva".equals(dto.getClntCd())  || "kpartners".equals(dto.getClntCd())){
                callFrom = "02-3282-0586";
            } else if("nh-ihappymall".equals(dto.getClntCd())) {
                callFrom = "02-6900-5987";
            } else if("kpointmall".equals(dto.getClntCd())) {
                callFrom = "02-3282-0586";
            } else if("kencafe".equals(dto.getClntCd())) {
                callFrom = "02-2161-0900";
            } else if(dto.getClntCd().indexOf("lg_") > -1) {
                callFrom = "02-3282-0556";
            } else if("obcafe".equals(dto.getClntCd())) {
                callFrom = "02-3282-0574";
            } else if("mili".equals(dto.getClntCd()) || "milifamily".equals(dto.getClntCd())) {
                callFrom = "02-2161-0900";
            }
        }

        // 배송지 입력기한 주문일 + 3일
        LocalDateTime datetime = LocalDateTime.parse(dto.getOrdDtm(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss")).plusDays(3);
        String date = datetime.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String dow = datetime.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.KOREAN);

        StringBuilder sb = new StringBuilder();
        sb.append(String.format("안녕하세요. %s 고객님!\n", dto.getRcvrNm()));
        sb.append(String.format("%s님이 보낸 선물 수락 기한이 1일 남았습니다.\n", dto.getOrdrNm()));
        sb.append("선물을 거절하거나 기한 내에 수락하지 않으면 주문 취소되어 보내신 분께 자동으로 환불처리 됩니다.\n\n");
        sb.append(String.format("- 상품명 : %s\n", dto.getGdsNm()));
        sb.append(String.format("- 주문번호 : %s\n", dto.getOrdNo()));
        sb.append(String.format("- 배송지입력기한 : %s(%s)\n", date, dow));
        sb.append(String.format("- 선물 확인하기 : %s\n\n", "선물하기 URL 확인"));
        sb.append("※ 선물을 수락하고 배송지를 입력하면 배송이 시작됩니다.\n\n");
        sb.append(String.format("고객센터 : %s(평일:9시~18시)", callFrom));

        return sb.toString();
    }

}
