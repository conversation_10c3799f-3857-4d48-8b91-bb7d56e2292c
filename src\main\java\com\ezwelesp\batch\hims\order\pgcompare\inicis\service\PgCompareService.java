package com.ezwelesp.batch.hims.order.pgcompare.inicis.service;

import com.ezwelesp.batch.hims.order.entity.external.EzEcOrderPayLogEntity;

public interface PgCompareService {



    /**
     * 거래대사 정보 입력
     * @param orderPayLog
     * @return
     */
    int createEzEcOrderPayLog(EzEcOrderPayLogEntity orderPayLog);

    /**
     * 이니시스 대사정보를 수집한다
     * @return
     */
    void connectInicisPgCompareData();

}
