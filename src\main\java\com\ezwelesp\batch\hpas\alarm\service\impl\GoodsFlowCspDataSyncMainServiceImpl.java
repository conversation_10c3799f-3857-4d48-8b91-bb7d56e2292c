package com.ezwelesp.batch.hpas.alarm.service.impl;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ezwelesp.batch.hpas.alarm.dto.GoodsFlowCspDataSyncMainDto;
import com.ezwelesp.batch.hpas.alarm.mapper.command.GoodsFlowCspDataSyncMainCommandMapper;
import com.ezwelesp.batch.hpas.alarm.service.GoodsFlowCspDataSyncMainService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class GoodsFlowCspDataSyncMainServiceImpl implements GoodsFlowCspDataSyncMainService {
    private static final String GOODSFLOW_API_KEY = "ab08be33-1e54-4a9b-9b8f-84854e11720a";

    @Value("${ezwel.apim.host}")
    private String host;
    private final GoodsFlowCspDataSyncMainCommandMapper goodsFlowCspDataSyncMainCommandMapper;

    @Override
    public int procGoodsFlowCspDataSyncMain() {
        int count = 0;
        List<List<GoodsFlowCspDataSyncMainDto>> data = this.getDlvrContratsCspList();

        // 계약건이 존재할 경우
        if (data != null) {
            for (List<GoodsFlowCspDataSyncMainDto> list : data) {
                for (GoodsFlowCspDataSyncMainDto item : list) {
                    //복지샵 굿스플로 계약관련 테이블 동기화
                    //this.setUpdCspDlvrContract(item);
                    count++;
                }
            }
        }

        return count;
    }

    private List<List<GoodsFlowCspDataSyncMainDto>> getDlvrContratsCspList() {
        List<List<GoodsFlowCspDataSyncMainDto>> resultList = new ArrayList<List<GoodsFlowCspDataSyncMainDto>>();
        List<GoodsFlowCspDataSyncMainDto> cspDtlList = new ArrayList<GoodsFlowCspDataSyncMainDto>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);

        String startDay = sdf.format(cal.getTime()); //어제 일자
        String endDay = sdf.format(cal.getTime());

        String url = "";
        if (host == "https://apim.ezwel.com") {
            url = "https://ds.goodsflow.com/delivery/api/v2/contracts/verify-results/" + startDay + "/" + endDay;
        }
        else {
            url = "https://test.goodsflow.com/delivery/api/v2/contracts/verify-results/" + startDay + "/" + endDay;
        }

        try {
            HttpClient httpClient = HttpClient.newHttpClient();
            HttpRequest getMehtod = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json;")
                    .header("goodsFLOW-Api-Key", GOODSFLOW_API_KEY)
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(getMehtod, HttpResponse.BodyHandlers.ofString());
            String rtnJson = response.body();

            Gson gson = new Gson();

            // 전체 JSON -> Map
            Map<String, Object> rtnMap = gson.fromJson(rtnJson, new TypeToken<Map<String, Object>>() {}.getType());

            if ("true".equals(String.valueOf(rtnMap.get("success")))) {
                Map<String, Object> dataMap = (Map<String, Object>) rtnMap.get("data");

                List itemList = new ArrayList();
                Set<Integer> set = new HashSet<Integer>();

                itemList = (List) dataMap.get("items");

                for (Object itemObj : itemList) {
                    Map<String, Object> item = (Map<String, Object>) itemObj;

                    log.info("### item : " + item);

                    if ("Y".equals((String) item.get("verifiedResult"))) {
                        // csp 중복제거 (csp 1개씩만 담기)
                        set.add(Integer.parseInt(item.get("partnerCode").toString()));
                    }
                }

                log.info("### set : " + set);

                for (int pCode : set) {
                    cspDtlList = this.getDlvrContratsCodeList(pCode);

                    //굿스플로에 등록된 csp의 모든 서비스이용 정보 조회
                    resultList.add(cspDtlList);
                }
            }
            else {
                Map<String, Object> errorMap = (Map<String, Object>) rtnMap.get("error");
                log.info("### error - status : " + errorMap.get("status"));
                log.info("### error - message : " + errorMap.get("message"));
                log.info("### error - detail : " + errorMap.get("detail"));
            }

        } catch (Exception e) {
            log.error("###########################################");
            log.error("## [협력사 굿스플로 계약정보 동기화] 처리 중 에러 1 ###");
            log.error("###########################################");
            log.error(e.toString());
        }

        return resultList;
    }

    private List<GoodsFlowCspDataSyncMainDto> getDlvrContratsCodeList(int cspCd) {
        List<GoodsFlowCspDataSyncMainDto> resultList = new ArrayList<GoodsFlowCspDataSyncMainDto>();

        String url = "";
        if (host == "https://apim.ezwel.com") {
            url = "https://ds.goodsflow.com/delivery/api/v2/contracts/partner/" + cspCd;
        }
        else {
            url = "https://test.goodsflow.com/delivery/api/v2/contracts/partner/" + cspCd;
        }

        try {
            HttpClient httpClient = HttpClient.newHttpClient();
            HttpRequest getMehtod = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json;")
                    .header("goodsFLOW-Api-Key", GOODSFLOW_API_KEY)
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(getMehtod, HttpResponse.BodyHandlers.ofString());
            String rtnJson = response.body();

            Gson gson = new Gson();

            // 전체 JSON -> Map
            Map<String, Object> rtnMap = gson.fromJson(rtnJson, new TypeToken<Map<String, Object>>() {}.getType());

            if ("true".equals(String.valueOf(rtnMap.get("success")))) {
                Map<String, Object> dataMap = (Map<String, Object>) rtnMap.get("data");

                List itemList = new ArrayList();
                itemList = (List) dataMap.get("items");

                for (Object itemObj : itemList) {
                    Map<String, Object> item = (Map<String, Object>) itemObj;

                    log.info("### item : " + item);

                    if ("Y".equals((String) item.get("verifiedResult"))) {
                        GoodsFlowCspDataSyncMainDto contract = new GoodsFlowCspDataSyncMainDto();
                        contract = this.settingCspDlvrContract(item);

                        resultList.add(contract);
                    }
                }
            }
            else {
                Map<String, Object> errorMap = (Map<String, Object>) rtnMap.get("error");
                log.info("### error - status : " + errorMap.get("status"));
                log.info("### error - message : " + errorMap.get("message"));
                log.info("### error - detail : " + errorMap.get("detail"));
            }

        } catch (Exception e) {
            log.error("###########################################");
            log.error("## [협력사 굿스플로 계약정보 동기화] 처리 중 에러 2 ###");
            log.error("###########################################");
            log.error(e.toString());
        }

        return resultList;
    }

    private GoodsFlowCspDataSyncMainDto settingCspDlvrContract(Map<String, Object> item) {
        GoodsFlowCspDataSyncMainDto result = new GoodsFlowCspDataSyncMainDto();

        result.setRequestKey((String) item.get("requestKey")); // 요청키
        result.setPartnerCode((String) item.get("partnerCode")); // 연동판매자코드
        result.setCenterCode((String) item.get("centerCode")); // 발송지 코드
        result.setDeliverCode((String) item.get("deliverCode")); // 택배사코드
        result.setDeliverNm((String) item.get("deliverCode")); // 택배사명
        result.setVerificationType((String) item.get("verificationType")); // 승인방법
        result.setBizNo((String) item.get("bizNo")); // 사업자번호
        result.setContractNo((String) item.get("contractNo")); // 택배사 계약코드
        result.setContractCustNo((String) item.get("contractCustNo")); // 택배사 업체코드
        result.setMallName((String) item.get("mallName")); // 판매자 업체명
        result.setMallUserName((String) item.get("mallUserName")); // 판매자 명
        result.setMallUserTel1((String) item.get("mallUserTel1")); // 판매자 전화1
        result.setMallUserTel2((String) item.get("mallUserTel2")); // 판매자 전화2
        result.setCenterName((String) item.get("centerName")); // 발송지명
        result.setCenterZipCode((String) item.get("centerZipCode")); // 발송지 우편번호
        result.setCenterAddr1((String) item.get("centerAddr1")); // 발송지 기본주소
        result.setCenterAddr2((String) item.get("centerAddr2")); // 발송지 상세주소
        result.setCenterTel1((String) item.get("centerTel1")); // 발송지 전화1
        result.setCenterTel2((String) item.get("centerTel2")); // 발송지 전화2
        result.setShippingFee((Integer.parseInt(item.get("shippingFee").toString())) + ""); // 도선료
        result.setFlightFee((Integer.parseInt(item.get("flightFee").toString())) + ""); // 항공료
        result.setVerifiedDatetime((String) item.get("verifiedDatetime")); // 승인처리일시
        result.setVerifiedResultCode((String) item.get("verifiedResultCode")); // 승인거부사유코드
        result.setVerifiedResult((String) item.get("verifiedResult")); // 승인결과

        List<Map<String, Object>> boxRateList = (ArrayList<Map<String, Object>>) item.get("contractRates");

        List<GoodsFlowCspDataSyncMainDto> boxRateListTemp = new ArrayList<GoodsFlowCspDataSyncMainDto>();

        for (int i = 0; i < boxRateList.size(); i++) {
            Map<String, Object> rate = boxRateList.get(i);
            GoodsFlowCspDataSyncMainDto rateTemp = new GoodsFlowCspDataSyncMainDto();
            rateTemp.setRequestKey((String) item.get("requestKey")); // 요청키
            rateTemp.setPartnerCode((String) item.get("partnerCode")); // 연동판매자코드
            rateTemp.setCenterCode((String) item.get("centerCode")); // 발송지코드
            rateTemp.setDeliverCode((String) item.get("deliverCode")); // 택배사코드
            rateTemp.setBoxSize(rate.get("boxSize").toString().trim()); // 박스규격

            if (rate.get("shFare") != null)
                rateTemp.setShFare((Integer.parseInt(rate.get("shFare").toString())) + ""); // 선불배송료
            if (rate.get("scFare") != null)
                rateTemp.setScFare((Integer.parseInt(rate.get("scFare").toString())) + ""); // 신용배송료
            if (rate.get("bhFare") != null)
                rateTemp.setBhFare((Integer.parseInt(rate.get("bhFare").toString())) + ""); // 착불배송료
            if (rate.get("rtFare") != null)
                rateTemp.setRtFare((Integer.parseInt(rate.get("rtFare").toString())) + ""); // 반불배송료

            boxRateListTemp.add(rateTemp);
        }

        result.setBoxRateList(boxRateListTemp);

        return result;
    }

    private void setUpdCspDlvrContract(GoodsFlowCspDataSyncMainDto goodsFlowCspDataSyncMainDto) {
        try {
            // 기존 데이터 사용안함 처리
            Map<String, String> map = new HashMap<String, String>();
            map.put("cspCd", goodsFlowCspDataSyncMainDto.getPartnerCode());
            goodsFlowCspDataSyncMainCommandMapper.setCspReturnDlvrUseYnN(map);

            goodsFlowCspDataSyncMainDto.setStatus("Y"); //신청완료
            goodsFlowCspDataSyncMainCommandMapper.setCspReturnDlvrUseYnN(goodsFlowCspDataSyncMainDto);

            if (goodsFlowCspDataSyncMainDto.getBoxRateList() != null
                    && goodsFlowCspDataSyncMainDto.getBoxRateList().size() > 0) {
                for (GoodsFlowCspDataSyncMainDto temp : goodsFlowCspDataSyncMainDto.getBoxRateList()) {
                    temp.setRequestKey(goodsFlowCspDataSyncMainDto.getRequestKey());
                    temp.setPartnerCode(goodsFlowCspDataSyncMainDto.getPartnerCode());
                    temp.setCenterCode(goodsFlowCspDataSyncMainDto.getCenterCode());
                    temp.setDeliverCode(goodsFlowCspDataSyncMainDto.getDeliverCode());

                    goodsFlowCspDataSyncMainCommandMapper.delCspDlvrContractBoxRate(temp);
                    goodsFlowCspDataSyncMainCommandMapper.setCspDlvrContractBoxRate(temp);
                }
            }

            //복지샵 CP 반품 택배(배송업체) MERGE INTO
            goodsFlowCspDataSyncMainDto.setRegId("BATCH");
            goodsFlowCspDataSyncMainCommandMapper.setCspReturnDlvr(goodsFlowCspDataSyncMainDto);

        } catch (Exception e) {
            log.error("###########################################");
            log.error("## [협력사 굿스플로 계약정보 동기화] 처리 중 에러 3 ###");
            log.error("###########################################");
            log.error(e.toString());
        }

    }
}
