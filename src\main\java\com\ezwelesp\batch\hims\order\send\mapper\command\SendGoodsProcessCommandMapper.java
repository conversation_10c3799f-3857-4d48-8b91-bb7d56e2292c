package com.ezwelesp.batch.hims.order.send.mapper.command;

import com.ezwelesp.batch.hims.order.entity.DlOrdSndGdsDEntity;
import com.ezwelesp.batch.hims.order.entity.DlOrdSndGdsDHEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SendGoodsProcessCommandMapper {
    /**
     * 주문발송상품상세 상태 수정
     */
    void updateSendGoodsDetailStatus(DlOrdSndGdsDEntity entity);

    /**
     * 주문발송상품상세이력 등록
     */
    void insertSendGoodsHistory(DlOrdSndGdsDHEntity entity);
}
