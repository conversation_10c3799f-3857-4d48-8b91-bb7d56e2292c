package com.ezwelesp.batch.hims.order.config;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

public class GoodsFlowConstants {
    public static final String EMPTY_STRING = "";
    public static final String SERVER_TYPE_ZKM = "zkm"; // 배송지키미 api (배송추적)
    public static final String SERVER_TYPE_RETURN = "return"; // 배송서비스 api (반품)

    @Getter
    @RequiredArgsConstructor
    @ToString
    public enum GoodsFlowApiEnum {
        SEND_TRACE_REQUEST_URL(SERVER_TYPE_ZKM,"/gws/api/Member/v3/SendTraceRequest/ezwelfare", "배송추적 요청"),
        RECEIVE_TRACE_RESULT_URL(SERVER_TYPE_ZKM,"/gws/api/Member/v3/ReceiveTraceResult/ezwelfare", "배송결과수신"),
        SEND_TRACE_RESULT_RESPONSE_URL(SERVER_TYPE_ZKM,"/gws/api/Member/v3/SendTraceResultResponse/ezwelfare", "배송결과수신 응답처리"),
        RETURN_REGISTER_URL(SERVER_TYPE_RETURN, "/delivery/api/v2/returns/partner/%s", "반품등록"),
        RETURN_CANCEL_URL(SERVER_TYPE_RETURN, "/delivery/api/v2/returns/%s/cancel", "반품취소"),
        ;

        private final String type;
        private final String url;
        private final String name;

    }

    @Getter
    @RequiredArgsConstructor
    public enum MallCodeEnum {
        SHOP("ezwelfare"),
        ;

        private final String memberCode;
    }
}
