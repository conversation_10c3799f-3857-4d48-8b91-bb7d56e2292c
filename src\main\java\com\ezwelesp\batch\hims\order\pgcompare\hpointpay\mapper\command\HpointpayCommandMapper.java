package com.ezwelesp.batch.hims.order.pgcompare.hpointpay.mapper.command;

import com.ezwelesp.batch.hims.order.entity.PyPgPymtCmprErrMtrgLEntity;
import com.ezwelesp.batch.hims.order.entity.external.ApiPgPrsnlPayEntity;
import com.ezwelesp.batch.hims.order.entity.external.EzEcHpointpayEntity;

public interface HpointpayCommandMapper {

	void insertPgApproveErrList(PyPgPymtCmprErrMtrgLEntity pyPgPymtCmprErrMtrgLEntity);

	void insertTossTransaction(EzEcHpointpayEntity ezEcHpointpayEntity);

	void insertApiPgPrsnlPay(ApiPgPrsnlPayEntity apiPgPrsnlPayEntity);

}
