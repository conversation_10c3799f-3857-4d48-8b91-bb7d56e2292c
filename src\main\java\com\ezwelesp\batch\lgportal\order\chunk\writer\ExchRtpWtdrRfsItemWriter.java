package com.ezwelesp.batch.lgportal.order.chunk.writer;

import com.ezwelesp.batch.lgportal.entity.ApiBatchChgReturnEntity;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.ClaimWtdrRfsReqDto;
import com.ezwelesp.batch.lgportal.order.mapper.command.ApiBatchCommandMapper;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 교환,반품 완료처리
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order
 * @since 2025.03.05
 */
@Component
public class ExchRtpWtdrRfsItemWriter implements ItemWriter<ClaimWtdrRfsReqDto> {

    @Value("${lgportal.server.host}")
    private String serverHost;

    @Value("${lgportal.server.timeout}")
    private int timeout;

    @Value("${lgportal.api-key}")
    private String apiKey;



    private final SqlSessionTemplate sqlSessionTemplate;

    /**
     * Instantiates a new Reg delivery loc item writer.
     *
     * @param sqlSessionFactory the sql session factory
     */
    public ExchRtpWtdrRfsItemWriter(@Qualifier("primaryCommandSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory);
    }

    @Override
    public void write(Chunk<? extends ClaimWtdrRfsReqDto> chunks) {
        chunks.forEach(apiBatchChgReturnEntity ->
                this.sqlSessionTemplate.update(ApiBatchCommandMapper.class.getName() + ".updateApiChgReturn",
                        this.exchRtpWtdrRfs(apiBatchChgReturnEntity)));
    }

    private ApiBatchChgReturnEntity exchRtpWtdrRfs(ClaimWtdrRfsReqDto claimWtdrRfsReqDto) {

        try {
            AjaxResult result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_SET_EXCEPTION_CANCEL, this.timeout,
                    JsonObjectConverter.serialize(
                            Map.of("apiKey", this.apiKey,
                            "exceptStatus", claimWtdrRfsReqDto.getExceptStatus(),
                            "salesOrderExceptNo", claimWtdrRfsReqDto.getSaleOrderExceptNo(),
                                    "memo", claimWtdrRfsReqDto.getMemo())
                    ));

            return ApiBatchChgReturnEntity.builder()
                    .apiNo(claimWtdrRfsReqDto.getApiNo())
                    .returnCd(result.get("result").toString())
                    .returnMsg(result.get("resultMessage").toString())
                    .sendYn(StringUtils.equals(String.valueOf(HttpStatus.OK.value()), result.get("result").toString()) ?
                            "Y" : "N")
                    .build();

        } catch (JsonProcessingException e) {
            throw new RuntimeException("LG portal api request failed");
        }
    }
}
