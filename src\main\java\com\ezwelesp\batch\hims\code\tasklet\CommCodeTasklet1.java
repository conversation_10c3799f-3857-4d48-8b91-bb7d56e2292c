package com.ezwelesp.batch.hims.code.tasklet;

import com.ezwelesp.batch.hims.code.mapper.command.CommCodeCommandMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> CommCodeTasklet1
 * @date : 2025-02-14
 * @see : com.ezwelesp.batch.aasample.test.tasklet
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CommCodeTasklet1 implements Tasklet {
    private final CommCodeCommandMapper mapper;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            // 1. cm_comm_c 테이블 DELETE.
            mapper.deleteCommCode();

            // 2. cm_comm_c 테이블 INSERT.
            mapper.insertCommCode();

            // 3. cm_comm_dtl_c 테이블 DELETE.
            mapper.deleteCommCodeDtl();

            // 4. cm_comm_dtl_c 테이블 INSERT.
            mapper.insertCommCodeDtl();
        } catch (Exception e) {
            log.error("CommCodeTasklet1 Failed: {}", e.getMessage(), e);
        }
        return RepeatStatus.FINISHED;
    }
}
