package com.ezwelesp.batch.lgportal.product.chunk.processor.handler;

import com.ezwelesp.batch.lgportal.product.api.LgApiClient;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgUpdateProductSoldOutRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class LgPortalEditProductSaleHandler implements LgPortalEditProductProcessorHandler {

    private final LgApiClient lgApiClient;

    @Override
    public LgProductApiResponseDto handle(LgEditProductTargetVo item) {

        val requestDto = LgUpdateProductSoldOutRequestDto.builder()
                .productNo(item.getProductNo())
                .build();

        val responseDto = lgApiClient.sendLgUpdateProductSaleApi(
                requestDto,
                item.getGdsCd(),
                item.getApiSeq()
        );

        if (responseDto != null && !"200".equals(responseDto.getResult())) {
            log.debug("sendLgUpdateProductSaleApi Fail : {},{}", item.getGdsCd(), responseDto.getResult());
        }

        return responseDto;
    }

}
