package com.ezwelesp.batch.hims.customer.tasklet;

import com.ezwelesp.batch.hims.customer.dto.PointDupRstrSmsSendDto;
import com.ezwelesp.batch.hims.customer.mapper.query.PointDupRstrMonitoringQueryMapper;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
@RequiredArgsConstructor
public class PointDupRstrMonitoringTasklet implements Tasklet {
    private final PointDupRstrMonitoringQueryMapper pointDupRstrMonitoringQueryMapper;

    @Override
    public RepeatStatus execute(
            @NonNull StepContribution contribution
            , @NonNull ChunkContext chunkContext
    ) {
        try {
            val userPointDupRstrCount = pointDupRstrMonitoringQueryMapper.selectUserPointDupRstr();

            val specialPointDupRstrCount = pointDupRstrMonitoringQueryMapper.selectSpecialPointDupRstr();


            log.info("[선복 중복사용여부] : " + userPointDupRstrCount);
            log.info("####################################");
            log.info("[특복 중복사용여부] : " + specialPointDupRstrCount);

            // todo 문자발송기능 개발 안되서 ASIS 기준으로 설정해주는 값들까지 개발하였으며,
            //  문자발송 기능에 맞춰서 해당변수값 수정 가능
            if (userPointDupRstrCount > 0 || specialPointDupRstrCount > 0) {
                //문자 받는 사람
                val callToList = List.of(
                        "010-8536-6320"     //오혜영
                        , "010-3849-5845"   //강지훈
                );

                // 문자 보내는 내용
                val smsText = "포인트 이중복구 확인 필요!!\n선복("
                        + userPointDupRstrCount
                        + " 건) / 특복("
                        + specialPointDupRstrCount
                        + " 건)";

                // todo 메세지 보내는 PointDupRstrSmsSendDto 레코드 리스트 생성
                //  발송번호 / 서비스코드 / 발송타입 전부 ASIS 와 동일하게 하드코딩 해놨습니다.
                val pointDupRstrSmsSendDtoList = callToList.stream()
                        .map(callTo -> pointDupRstrSmsSendBuilder(callTo, smsText))
                        .toList();

                log.info("PointDupRstrSmsSendDtoList : " + pointDupRstrSmsSendDtoList);


                // todo 문자 발송기능 추가


            }

        } catch (Exception e) {
            log.error("처리중 에러가 발생했습니다.", e);
        }


        return RepeatStatus.FINISHED;
    }

    private PointDupRstrSmsSendDto pointDupRstrSmsSendBuilder(
            String callTo
            , String smsText
    ) {
        return PointDupRstrSmsSendDto.builder()
                .callTo(callTo)
                .callFrom("16600011")   // 발송번호
                .svcType("1001")        // ASIS 서비스 코드
                .smsText(smsText)
                .msgType("4")           // 발송타입
                .build();
    }
}
