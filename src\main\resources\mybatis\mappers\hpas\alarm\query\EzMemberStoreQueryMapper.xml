<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hpas.alarm.mapper.query.EzMemberStoreQueryMapper">

    <select id="selectEzMembersStoreUnregisterList" resultType="com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreUnregisterDto">
		SELECT
		     coalesce(A.crd_sale_no,'N')         
		    ,coalesce(A.crd_apv_no,'N')          
		    ,to_char(use_dt::date, 'YYYY-MM-DD') 
		    ,A.crdc_frcs_biztp_cd                
		    ,coalesce(A.crdc_frcs_biztp_nm,'N')  
		    ,coalesce(A.crdc_frcs_no,'N')        
		    ,coalesce(A.crdc_frcs_nm,'N')        
		    ,A.ezmbrs_brnd_nm 					
		    ,A.orgl_crd_sale_no                  
		    ,A.cncl_yn
		    ,case when A.cncl_yn='N' then '청구'
		     when A.cncl_yn='Y'then '취소'
			else '' end                    			 AS CANCELYN
		FROM (
		    SELECT X.*, Y.ezmbrs_brnd_nm FROM
		    (SELECT * FROM ez_ct.et_offcrd_blng_dc_l) X,
		    (SELECT A.crdc_bsic_ofln_frcs_no, MAX(C.ezmbrs_brnd_nm) AS ezmbrs_brnd_nm FROM ez_co.co_ezmbrs_ofln_frcs_d A, ez_co.co_ezmbrs_frcs_b B, ez_co.co_ezmbrs_brnd_b C
		    WHERE  A.ezmbrs_frcs_no = B.ezmbrs_frcs_no
		    AND B.ezmbrs_brnd_no = C.ezmbrs_brnd_no
		    GROUP BY A.crdc_bsic_ofln_frcs_no) Y
		    WHERE X.crdc_frcs_no = Y.crdc_bsic_ofln_frcs_no
		UNION ALL
		    SELECT X.*, Y.ezmbrs_brnd_nm FROM
		    (
		        SELECT X.* FROM
		    	(SELECT * FROM ez_ct.et_offcrd_blng_dc_l ) X,
		        (SELECT A.crdc_bsic_ofln_frcs_no, MAX(C.ezmbrs_brnd_nm) AS ezmbrs_brnd_nm FROM ez_co.co_ezmbrs_ofln_frcs_d A, ez_co.co_ezmbrs_frcs_b B, ez_co.co_ezmbrs_brnd_b C
		        WHERE  A.ezmbrs_frcs_no = B.ezmbrs_frcs_no
		        AND B.ezmbrs_brnd_no = C.ezmbrs_brnd_no
		        GROUP BY A.crdc_bsic_ofln_frcs_no) Y
		        WHERE X.crdc_frcs_no = Y.crdc_bsic_ofln_frcs_no
		        AND Y.ezmbrs_brnd_nm IS NULL
		         ) X right outer JOIN
		        (SELECT LPAD(TRIM(A.crdc_bsic_ofln_frcs_no),10,'0') AS crdc_bsic_ofln_frcs_no, MAX(C.ezmbrs_brnd_nm) AS ezmbrs_brnd_nm FROM ez_co.co_ezmbrs_ofln_frcs_d A, ez_co.co_ezmbrs_frcs_b B, ez_co.co_ezmbrs_brnd_b C
		        WHERE  A.ezmbrs_frcs_no = B.ezmbrs_frcs_no
		        AND B.ezmbrs_brnd_no = C.ezmbrs_brnd_no
		        GROUP BY A.crdc_bsic_ofln_frcs_no) Y									
		    ON X.crdc_frcs_no = Y.crdc_bsic_ofln_frcs_no
		) A
		WHERE 1=1
		AND A.ezmbrs_brnd_nm IS NULL
		AND	SUBSTR(a.frst_reg_dtm,0,8) = TO_CHAR(now(), 'YYYYMMDD')
	
	</select>
	<select id="selectMonitorJobList" resultType="com.ezwelesp.batch.hpas.alarm.dto.MonitorJobsDto">
		SELECT 
				job_id as jobId
				,job_nm as jobNm
				,job_desc as jobDesc
				,job_cycle as jobCycle
				,sys_cd as sysCd
				,type_cd as typeCd
				,alram_yn as alramYn
				,alram_cnt_max as alramCntMax
				,critical_value as criticalValue
				,critical_value_unit as criticalValueUnit
				,reg_id as regId
				,modi_id as modiId
			FROM 
			ez_cm.sc_mnt_monitor_jobs
			WHERE 1=1
			AND job_id = #{jobId}
	</select>
	<select id="selectEzMembersStoreClosedList" resultType="com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreClosedDto">
	 	   SELECT             
        		A.crdc_frcs_no	
        		,A.crdc_frcs_nm 
	        FROM (
		        SELECT X.*, Y.ezmbrs_brnd_nm, Y.ezmbrs_frcs_no, exps_yn, frcs_intl_st_cd FROM
			        (SELECT * FROM ez_ct.et_offcrd_blng_dc_l) X,
			        (SELECT A.crdc_bsic_ofln_frcs_no, MAX(C.ezmbrs_brnd_nm) AS ezmbrs_brnd_nm, MAX(B.ezmbrs_frcs_no) AS ezmbrs_frcs_no, MAX(B.exps_yn) AS exps_yn, MAX(B.frcs_intl_st_cd) AS frcs_intl_st_cd 
			        FROM ez_co.co_ezmbrs_ofln_frcs_d A, ez_co.co_ezmbrs_frcs_b B, ez_co.co_ezmbrs_brnd_b C
			        WHERE A.ezmbrs_frcs_no = B.ezmbrs_frcs_no
			        AND B.ezmbrs_brnd_no = C.ezmbrs_brnd_no
			        GROUP BY A.crdc_bsic_ofln_frcs_no) Y
			        WHERE X.crdc_frcs_no = Y.crdc_bsic_ofln_frcs_no
		        UNION ALL
		        SELECT X.*, Y.ezmbrs_brnd_nm, Y.ezmbrs_frcs_no, exps_yn, frcs_intl_st_cd FROM
		        (
		            SELECT X.* FROM
			        (SELECT * FROM ez_ct.et_offcrd_blng_dc_l ) X right outer JOIN
			        (SELECT A.crdc_bsic_ofln_frcs_no, MAX(C.ezmbrs_brnd_nm) AS ezmbrs_brnd_nm, MAX(B.ezmbrs_frcs_no) AS ezmbrs_frcs_no, MAX(B.exps_yn) AS exps_yn, MAX(B.frcs_intl_st_cd) AS frcs_intl_st_cd 
			        FROM ez_co.co_ezmbrs_ofln_frcs_d A, ez_co.co_ezmbrs_frcs_b B, ez_co.co_ezmbrs_brnd_b C
			        WHERE A.ezmbrs_frcs_no = B.ezmbrs_frcs_no
			        AND B.ezmbrs_brnd_no = C.ezmbrs_brnd_no
			        GROUP BY A.crdc_bsic_ofln_frcs_no) Y
			        ON X.crdc_frcs_no = Y.crdc_bsic_ofln_frcs_no
			        AND Y.ezmbrs_brnd_nm IS NULL
			         ) X right outer JOIN
			        (SELECT LPAD(TRIM(A.crdc_bsic_ofln_frcs_no),10,'0') AS crdc_bsic_ofln_frcs_no, MAX(C.ezmbrs_brnd_nm) AS ezmbrs_brnd_nm, MAX(B.ezmbrs_frcs_no) AS ezmbrs_frcs_no, MAX(B.exps_yn) AS exps_yn, MAX(B.frcs_intl_st_cd) AS frcs_intl_st_cd
			        FROM ez_co.co_ezmbrs_ofln_frcs_d A, ez_co.co_ezmbrs_frcs_b B, ez_co.co_ezmbrs_brnd_b C
			        WHERE A.ezmbrs_frcs_no = B.ezmbrs_frcs_no
			        AND B.ezmbrs_brnd_no = C.ezmbrs_brnd_no
			        GROUP BY A.crdc_bsic_ofln_frcs_no) Y
		        ON X.crdc_frcs_no = Y.crdc_bsic_ofln_frcs_no
	        ) A
	        WHERE 1=1
	        <if test="expsYn != null and expsYn != ''">
	        	AND A.exps_yn = #{expsYn}
	        </if>
	        <if test="frcsIntlStCd != null and frcsIntlStCd != ''">
	        	AND A.frcs_intl_st_cd = #{frcsIntlStCd}
	        </if>
	        AND	SUBSTR(A.frst_reg_dtm,0,8) = TO_CHAR(now(), 'YYYYMMDD')
	        GROUP BY A.crdc_frcs_no, A.crdc_frcs_nm
	</select>
	<select id="selectEzMembersStoreBenefitList" resultType="com.ezwelesp.batch.hpas.alarm.dto.EzMemberStoreBenefitDto">
		  SELECT
				Z.crdc_frcs_no 			
				,Z.crdc_frcs_nm  		
				,COUNT(Z.crdc_frcs_no) 	AS count
			FROM(
				SELECT
		        	A.crd_sale_no                                             			 	AS crd_sale_no
		            ,A.crd_apv_no                                                   	AS crd_apv_no
		            ,TO_CHAR(TO_DATE(A.use_dt,'YYYYMMDD'),'YYYY-MM-DD')                         	AS use_dt
		            ,(CASE WHEN tlgm_cd = 'SE06' AND (offcrd_sale_knd_cd::numeric = 15 OR offcrd_sale_knd_cd::numeric = 18) THEN A.use_amt
		            	WHEN tlgm_cd = 'ET14' AND (offcrd_sale_knd_cd::numeric = 15 OR offcrd_sale_knd_cd::numeric = 18) THEN -A.use_amt
		                WHEN tlgm_cd = 'EW14' AND (offcrd_sale_knd_cd::numeric = 15 OR offcrd_sale_knd_cd::numeric = 18) THEN -A.use_amt
		                WHEN tlgm_cd = 'KB11' AND (offcrd_sale_knd_cd::numeric = 3) THEN -A.use_amt
		                WHEN tlgm_cd = 'KB51' AND (offcrd_sale_knd_cd::numeric = 3) THEN -A.use_amt
		            ELSE A.use_amt END) AS USEPRICE
		            ,(CASE WHEN tlgm_cd = 'SE06' AND (offcrd_sale_knd_cd::numeric = 15 OR offcrd_sale_knd_cd::numeric = 18) THEN A.dc_amt
		            	WHEN tlgm_cd = 'ET14' AND (offcrd_sale_knd_cd::numeric = 15 OR offcrd_sale_knd_cd::numeric = 18) THEN -A.dc_amt
		                WHEN tlgm_cd = 'EW14' AND (offcrd_sale_knd_cd::numeric = 15 OR offcrd_sale_knd_cd::numeric = 18) THEN -A.dc_amt
		                WHEN tlgm_cd = 'KB11' AND (offcrd_sale_knd_cd::numeric = 3) THEN -A.dc_amt
		                WHEN tlgm_cd = 'KB51' AND (offcrd_sale_knd_cd::numeric = 3) THEN -A.dc_amt
		            ELSE A.dc_amt END) AS DCPRICE
		            ,(CASE when A.offcrd_sale_knd_cd='05' then '일시불'
		             when A.offcrd_sale_knd_cd='08' then '할부'
		             when A.offcrd_sale_knd_cd='1' then '정상'
		             when A.offcrd_sale_knd_cd='2' then '취소'
		             when A.offcrd_sale_knd_cd='3' then '취소'
		            else '취소' end)  AS offcrd_sale_knd_cd
		            ,A.crdc_frcs_biztp_cd                                               
		            ,A.crdc_frcs_biztp_nm                                                 	
		            ,A.crdc_frcs_nm                                                      	
		            ,A.ezmbrsBrndNm 													 	
		            ,A.alli_cd                                                       	
		            ,A.crdc_frcs_no                                                      	
		            ,A.bnftDcRt                											
		        FROM (
				        SELECT X.*, Y.ezmbrsBrndNm, Y.bnftDcRt FROM
				        (SELECT * FROM ez_ct.et_offcrd_blng_dc_l) X,
				        (SELECT A.crdc_bsic_ofln_frcs_no, MAX(C.ezmbrs_brnd_nm) AS ezmbrsBrndNm, MAX(CASE WHEN C.dc_rt_cd = 'DI' THEN C.drct_insr_dc_rt ELSE C.dc_rt_cd::numeric END) AS bnftDcRt 
				        FROM ez_co.co_ezmbrs_ofln_frcs_d A, ez_co.co_ezmbrs_frcs_b B, ez_co.co_ezmbrs_brnd_b C
				        WHERE A.ezmbrs_frcs_no = B.ezmbrs_frcs_no
				        AND B.ezmbrs_brnd_no = C.ezmbrs_brnd_no
				        GROUP BY A.crdc_bsic_ofln_frcs_no) Y
				        WHERE X.crdc_frcs_no = Y.crdc_bsic_ofln_frcs_no
			        UNION ALL
				        SELECT X.*, Y.ezmbrsBrndNm, Y.bnftDcRt FROM
				        (
				            SELECT X.* FROM
				        (SELECT * FROM ez_ct.et_offcrd_blng_dc_l) X right OUTER JOIN
				        (SELECT A.crdc_bsic_ofln_frcs_no, MAX(C.ezmbrs_brnd_nm) AS ezmbrsBrndNm, MAX(CASE WHEN C.dc_rt_cd = 'DI' THEN C.drct_insr_dc_rt ELSE C.dc_rt_cd::numeric END) AS bnftDcRt 
				        FROM ez_co.co_ezmbrs_ofln_frcs_d A, ez_co.co_ezmbrs_frcs_b B, ez_co.co_ezmbrs_brnd_b C
				        WHERE A.ezmbrs_frcs_no = B.ezmbrs_frcs_no
				        AND B.ezmbrs_brnd_no = C.ezmbrs_brnd_no
				        GROUP BY A.crdc_bsic_ofln_frcs_no) Y
				        ON X.crdc_frcs_no = Y.crdc_bsic_ofln_frcs_no
				        AND Y.ezmbrsBrndNm IS NULL
				         ) X right OUTER JOIN
				        (SELECT LPAD(TRIM(A.crdc_bsic_ofln_frcs_no),10,'0') AS crdcBsicOflnFrcsNo, MAX(C.dc_rt_cd) AS ezmbrsBrndNm, MAX(CASE WHEN C.dc_rt_cd = 'DI' THEN C.drct_insr_dc_rt ELSE C.dc_rt_cd::numeric END) AS bnftDcRt 
				        FROM ez_co.co_ezmbrs_ofln_frcs_d A, ez_co.co_ezmbrs_frcs_b B, ez_co.co_ezmbrs_brnd_b C
				        WHERE  A.ezmbrs_frcs_no = B.ezmbrs_frcs_no
				        AND B.ezmbrs_brnd_no = C.ezmbrs_brnd_no
				        GROUP BY A.crdc_bsic_ofln_frcs_no) Y
			        ON X.crdc_frcs_no = Y.crdcBsicOflnFrcsNo
			        ) A
			        WHERE 1=1
			        AND	SUBSTR(A.frst_reg_dtm,0,8) = TO_CHAR(now(), 'YYYYMMDD')
				)Z
				WHERE ABS((ABS(Z.USEPRICE) * (Z.bnftDcRt / 100)) - ABS(Z.DCPRICE)) > 20
				GROUP BY Z.crdc_frcs_no, Z.crdc_frcs_nm
	</select>
</mapper>