package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * [2차]주문결제수단상품관계(ez_or.or_ord_pymt_mns_gds_r)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrOrdPymtMnsGdsREntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 결제차수(pymt_nos) not null
     */
    private Long pymtNos;

    /**
     * 주문결제수단조건순번(ord_pymt_mns_cond_seq) not null
     */
    private Long ordPymtMnsCondSeq;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 사용가능금액(use_poss_amt) not null
     */
    private BigDecimal usePossAmt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
