package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 주문발송내역(ez_or.dl_ord_snd_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlOrdSndLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문발송순번(ord_snd_seq) not null
     */
    private Long ordSndSeq;

    /**
     * 주문발송차수(ord_snd_nos) not null
     */
    private Long ordSndNos;

    /**
     * 수신자모바일전화번호(rcvr_mbl_telno) not null
     */
    private String rcvrMblTelno;

    /**
     * 발송예약일자(snd_rsv_dt)
     */
    private String sndRsvDt;

    /**
     * 발송일시(snd_dtm) not null
     */
    private String sndDtm;

    /**
     * 주문발송처리관리자ID(ord_snd_prcs_mgr_id)
     */
    private String ordSndPrcsMgrId;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
