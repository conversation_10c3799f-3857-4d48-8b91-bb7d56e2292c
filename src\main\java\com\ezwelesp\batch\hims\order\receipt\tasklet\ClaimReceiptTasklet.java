package com.ezwelesp.batch.hims.order.receipt.tasklet;


import com.ezwelesp.batch.hims.order.receipt.domain.CashReceiptParamDto;
import com.ezwelesp.batch.hims.order.receipt.domain.OrderClaimDto;
import com.ezwelesp.batch.hims.order.receipt.service.command.CashReceiptCommandService;
import com.ezwelesp.batch.hims.order.receipt.service.query.CashReceiptQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ClaimReceiptTasklet implements Tasklet {
    private final CashReceiptQueryService cashReceiptQueryService;
    private final CashReceiptCommandService cashReceiptCommandService;


    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            int repeat = 0;
            for(;;) {
                repeat++;
                List<OrderClaimDto> list = cashReceiptQueryService.getClaimCashReceiptTarget();
                if(!list.isEmpty()&&repeat<999){// 횟수제한 고민
                    for (OrderClaimDto dto : list) {
                        CashReceiptParamDto
                                param = CashReceiptParamDto.builder().ordNo(dto.getOrdNo()).clmNo(dto.getClmNo()).prevClmNo(dto.getPrevClmNo()).build();
                        cashReceiptCommandService.createCashReceipt(param);
                    }
                }else{
                    break;
                }

            }

            log.debug("!######################## ClaimReceiptTasklet method end");

        } catch (Exception e) {
            log.error("ClaimReceiptTasklet Failed: {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }

        log.debug("!######################## ClaimReceiptTasklet method finish");




        return RepeatStatus.FINISHED;
    }
}
