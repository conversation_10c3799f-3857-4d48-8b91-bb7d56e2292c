package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * EZ멤버스매출내역(ez_or.or_ezmbrs_sale_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrEzmbrsSaleLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * EZ멤버스매출내역순번(ezmbrs_sale_lst_seq) not null
     */
    private Long ezmbrsSaleLstSeq;

    /**
     * EZ멤버스정산유형코드(ezmbrs_stl_typ_cd) not null
     */
    private String ezmbrsStlTypCd;

    /**
     * EZ멤버스전문종류코드(ezmbrs_tlgm_knd_cd) not null
     */
    private String ezmbrsTlgmKndCd;

    /**
     * 정산년월(stl_ym) not null
     */
    private String stlYm;

    /**
     * 사용금액(use_amt) not null
     */
    private BigDecimal useAmt;

    /**
     * 할인금액(dc_amt) not null
     */
    private BigDecimal dcAmt;

    /**
     * 집계건수(sum_cnt)
     */
    private Integer sumCnt;

    /**
     * 메모(memo)
     */
    private String memo;

    /**
     * 첨부파일명(atch_file_nm)
     */
    private String atchFileNm;

    /**
     * 첨부파일경로(atch_file_path)
     */
    private String atchFilePath;

    /**
     * 파일크기문자값(file_size_cval)
     */
    private String fileSizeCval;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
