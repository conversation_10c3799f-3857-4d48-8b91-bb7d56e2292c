package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 협력사출고반품위치기본(ez_co.co_csp_obnd_rtp_loc_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class CoCspObndRtpLocBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 협력사코드(csp_cd) not null
     */
    private String cspCd;

    /**
     * 협력사출고위치번호(csp_obnd_loc_no) not null
     */
    private String cspObndLocNo;

    /**
     * 협력사출고위치명(csp_obnd_loc_nm) not null
     */
    private String cspObndLocNm;

    /**
     * 출고위치기본주소(obnd_loc_bas_adr)
     */
    private String obndLocBasAdr;

    /**
     * 출고위치상세주소(obnd_loc_dtl_adr)
     */
    private String obndLocDtlAdr;

    /**
     * 출고위치전화번호(obnd_loc_telno)
     */
    private String obndLocTelno;

    /**
     * 출고위치우편번호(obnd_loc_zipcd)
     */
    private String obndLocZipcd;

    /**
     * 반품위치기본주소(rtp_loc_bas_adr)
     */
    private String rtpLocBasAdr;

    /**
     * 반품위치상세주소(rtp_loc_dtl_adr)
     */
    private String rtpLocDtlAdr;

    /**
     * 반품위치전화번호(rtp_loc_telno)
     */
    private String rtpLocTelno;

    /**
     * 반품위치우편번호(rtp_loc_zipcd)
     */
    private String rtpLocZipcd;

    /**
     * 조건부무료배송여부(cndl_nchg_dlv_yn) not null
     */
    private String cndlNchgDlvYn;

    /**
     * 조건부무료배송비용(cndl_nchg_dlv_exp)
     */
    private BigDecimal cndlNchgDlvExp;

    /**
     * 대표주소여부(rps_adr_yn) not null
     */
    private String rpsAdrYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
