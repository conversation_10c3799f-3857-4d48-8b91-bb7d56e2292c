package com.ezwelesp.batch.lgportal.order.job;

import com.ezwelesp.batch.lgportal.entity.ApiBatchDlvrEntity;
import com.ezwelesp.batch.lgportal.order.chunk.reader.DeliveryCompleteItemReader;
import com.ezwelesp.batch.lgportal.order.chunk.writer.DeliveryCompleteItemWriter;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.listener.CommonJobListener;
import com.ezwelesp.batch.listener.CommonStepListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class DeliveryCompleteJobConfig {

    private final String DELIVERY_COMPLETE_STEP = LgConstants.JobName.JOB_DELIVERY_COMPLETE + "_STEP";

    private final CommonJobListener commonJobListener;

    @Value("${lgportal.chunk-size}")
    private int chunkSize;


    @Bean(LgConstants.JobName.JOB_DELIVERY_COMPLETE)
    public Job deliveryCompleteJob(JobRepository jobRepository, @Qualifier(DELIVERY_COMPLETE_STEP) Step deliveryCompleteStep) {
        return new JobBuilder(LgConstants.JobName.JOB_DELIVERY_COMPLETE, jobRepository)
                .listener(commonJobListener)
                .start(deliveryCompleteStep)
                .build();
    }

    @Bean(name = DELIVERY_COMPLETE_STEP)
    public Step deliveryCompleteStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager,
            DeliveryCompleteItemReader deliveryCompleteItemReader,
            DeliveryCompleteItemWriter deliveryCompleteItemWriter, CommonStepListener commonStepListener) {

        return new StepBuilder(DELIVERY_COMPLETE_STEP, jobRepository)
                .allowStartIfComplete(true)
                .<ApiBatchDlvrEntity, ApiBatchDlvrEntity>chunk(chunkSize, transactionManager)
                .transactionManager(transactionManager)
                .reader(deliveryCompleteItemReader)
                .writer(deliveryCompleteItemWriter)
                .listener(commonStepListener)
                .build();

    }
}
