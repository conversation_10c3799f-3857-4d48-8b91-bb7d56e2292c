package com.ezwelesp.batch.hims.order.pgconfirm.zeropay.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.order.pgconfirm.zeropay.tasklet.PgDailyErrorTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class PgDailyErrorAlarmJobConfig {
	private final CommonJobListener commonJobListener;
	private final PgDailyErrorTasklet pgDailyErrorTasklet;
	
	@Bean("BA_HIOR00132")
	public Job inicisPgCompareJob(JobRepository jobRepository, @Qualifier("BA_HIOR00132_STEP") Step step) {
	    return new JobBuilder("BA_HIOR00132", jobRepository)
	            .listener(commonJobListener)
	            .start(step)
	            .build();
	}
	
	@Bean("BA_HIOR00132_STEP")
	public Step inicisPgCompareStep1(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
	    return new StepBuilder("BA_HIOR00132_STEP", jobRepository)
	            .allowStartIfComplete(true)
	            .tasklet(pgDailyErrorTasklet, transactionManager)
	            .build();
	}
}
