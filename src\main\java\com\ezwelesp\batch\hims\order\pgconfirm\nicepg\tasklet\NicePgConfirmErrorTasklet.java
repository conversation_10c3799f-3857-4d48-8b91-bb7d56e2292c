package com.ezwelesp.batch.hims.order.pgconfirm.nicepg.tasklet;

import com.ezwelesp.batch.hims.order.pgconfirm.nicepg.domain.NicePgErrorDto;
import com.ezwelesp.batch.hims.order.pgconfirm.nicepg.service.NicePgConfirmErrorService;
import com.ezwelesp.batch.hims.order.utils.OrderJsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class NicePgConfirmErrorTasklet implements Tasklet {

    private final NicePgConfirmErrorService nicePgConfirmErrorService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {


            log.debug("!######################## NicePgConfirmErrorTasklet method start");
            List<NicePgErrorDto> list = nicePgConfirmErrorService.getNicePgConfirmErrorList();

            log.info("nicePgConfirmErrorService:{}", OrderJsonUtils.convertToJson(nicePgConfirmErrorService));

            log.debug("!######################## NicePgConfirmErrorTasklet method start2");

        } catch (Exception e) {
            log.error("NicePgConfirmErrorTasklet Failed: {}", e.getMessage(), e);
            contribution.setExitStatus(ExitStatus.FAILED);
        }


        log.debug("!######################## NicePgConfirmErrorTasklet method finish");

        return RepeatStatus.FINISHED;
    }
}
