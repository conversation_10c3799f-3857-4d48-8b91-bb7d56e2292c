package com.ezwelesp.batch.hims.order.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 통합장바구니옵션상세(ez_or.or_untd_bskt_opt_d)
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OrUntdBsktOptDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 장바구니번호(bskt_no) not null
     */
    private Long bsktNo;

    /**
     * 옵션상품조합순번(opt_gds_comb_seq) not null
     */
    private Long optGdsCombSeq;

    /**
     * 주문상품수량(ord_gds_qty) not null
     */
    private Integer ordGdsQty;

    /**
     * 필수선택여부(mndr_choc_yn) not null
     */
    private String mndrChocYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
