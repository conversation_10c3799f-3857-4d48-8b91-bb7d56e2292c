<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.lgportal.product.mapper.command.ProductLGPortalCommandMapper">

    <insert id="insertApiBatchGoodsBeforeSend">
        MERGE INTO ez_if.api_batch_goods AS target
            USING (SELECT #{gdsCd}        AS gds_cd,
                          #{targetDiv}    AS target_div,
                          #{cspCd}        AS csp_cd,
                          #{cspObndLocNo} AS csp_dlvr_id) AS source
            ON (target.goods_cd = source.gds_cd AND target.target_div = source.target_div)
            WHEN MATCHED THEN
                UPDATE SET
                    error_code = NULL,
                    error_message = NULL,
                    csp_cd = source.csp_cd,
                    csp_dlvr_id = source.csp_dlvr_id,
                    reg_dt = to_char(NOW(), 'YYYYMMDDHH24MISS')
            WHEN NOT MATCHED THEN
                INSERT (goods_cd, csp_cd, csp_dlvr_id, target_div, reg_dt)
                    VALUES (source.goods_cd, source.csp_cd, source.csp_dlvr_id, source.target_div,
                            to_char(NOW(), 'YYYYMMDDHH24MISS'));
    </insert>

    <update id="updateApiBatchGoodsAfterSend">
        UPDATE ez_if.api_batch_goods
        SET target_goods_cd = cast(#{productNo} AS text),
            error_code      = #{result},
            error_messege   = #{resultMessage}
        WHERE goods_cd = #gdsCd#
    </update>

    <update id="updateListApiBatchSendStatus">
        <foreach collection="list" item="item" separator=";">
            UPDATE ez_if.api_batch_send
            SET
            error_messege = #{item.resultMessage},
            send_yn = case when #{item.result} = '200' then 'Y' else 'F' end
            WHERE
            api_seq = #{item.apiSeq}
        </foreach>
    </update>

    <update id="updateApiBatchSendStatus">
        UPDATE ez_if.api_batch_send
        SET error_messege = #{item.resultMessage},
            send_yn       = case when #{item.result} = '200' then 'Y' else 'F' end
        WHERE api_seq = #{item.apiSeq}
    </update>

    <insert id="insertApiBatchSKUStatus">
        INSERT INTO ez_if.api_batch_sku
        (api_seq,
        goods_cd,<!-- todo db 형 바뀌면 수정해야됨-->
        sku_cd,
        sku_value,
        option_add_amt,
        option_add_price,
        option_add_buy_price,
        send_yn,
        return_code,
        return_message,
        reg_dt,
        reg_id,
        product_no)
        VALUES (
        (nextval('ez_if.sq_api_batch_sku')
        , #{requestItem.productMappingCode}
        , #{requestItem.sKUCode}
        , #{requestItem.sKUVal}
        , #{requestItem.controlSalesPrice}
        , #{requestItem.optionAddPrice}
        , #{requestItem.controlVendorPrice}
        , CASE WHEN #{responseItem.result} = '200' THEN 'Y' ELSE 'F' END
        , #{responseItem.result}
        , #{responseItem.resultMessage}
        , to_char(now(), 'yyyyMMddHH24miss')
        , 'batch'
        , #{responseItem.productNo})::text
    </insert>

    <insert id="insertApiProductQna">
        INSERT INTO ez_cm.bb_gds_inq_b
        ( gds_inq_seq
        , ttl
        , inq_cntn
        , gds_cd
        , gds_nm
        , csp_cd
        , cmnt_yn
        , answ_cmpt_yn
        , del_yn
        , high_gds_inq_seq
        , user_key
        , lgp_bbs_no
        , bbc_opbl_yn
        , frst_reg_dtm
        , frst_reg_usr_id
        , frst_reg_pgm_id -- 최초등록프로그램
        , last_mod_dtm
        , last_mod_usr_id
        , last_mod_pgm_id)
        VALUES ( nextval('ez_cm.sq_bb_gds_inq_b')
               , #{ttl}
               , #{inqCntn}
               , #{gdsCd}
               , #{gdsNm}
               , #{cspCd}
               , 'N'
               , 'N'
               , 'Y'
               , 0
               , **********
               , #{lgpBbsNo}
               , 'N'
               , to_char(now(), 'yyyyMMddhh24miss')
               , 'batch'
               , 'LgPortalGetProductQnaJobConfig'
               , to_char(now(), 'yyyyMMddhh24miss')
               , 'batch'
               , 'LgPortalGetProductQnaJobConfig')
    </insert>

    <insert id="insertApiProductLgQnaAutoAnswer">
        INSERT INTO ez_if.api_batch_send
        ( api_seq
        , provider_no
        , certkey
        , product_no
        , title_arr
        , value_arr
        , board_no
        , send_key
        , send_yn
        , error_messege
        , reg_dt
        , reg_id)
        VALUES ( nextval('ez_if.sq_api_batch_send')
               , #{providerNo}
               , #{certkey}
               , #{productNo}
               , #{titleArr}
               , #{valueArr}
               , #{boardNo}
               , #{sendKey}
               , #{result}
               , #{resultMessage}
               , to_char(now(), 'yyyymmddhh24miss')
               , 'batch')
    </insert>

    <!--날짜를 now가 아닌 current_date를 쓴 이유는 배치 시간과 상관없이
    00시 기준으로 명확하게 데이터를 삭제하기 위해서 입니다. -->
    <delete id="deleteApiBatchSendBefore30Days">
        DELETE
        FROM ez_if.api_batch_send
        WHERE reg_dt <![CDATA[ <= ]]> to_char(current_date - interval '30 days', 'yyyymmddhh24miss')
    </delete>

    <delete id="deleteApiBatchSkuBefore30Days">
        DELETE
        FROM ez_if.api_batch_sku
        WHERE reg_dt <![CDATA[ <= ]]> to_char(current_date - interval '30 days', 'yyyymmddhh24miss')
    </delete>


</mapper>
