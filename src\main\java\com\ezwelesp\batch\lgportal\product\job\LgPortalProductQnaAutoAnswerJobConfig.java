package com.ezwelesp.batch.lgportal.product.job;


import com.ezwelesp.batch.lgportal.product.chunk.processor.LgPortalProductQnaAutoAnswerProcessor;
import com.ezwelesp.batch.lgportal.product.chunk.reader.LgPortalProductQnaAutoAnswerReader;
import com.ezwelesp.batch.lgportal.product.chunk.writer.LgPortalProductQnaAutoAnswerWriter;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductQnaAutoAnswerListDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgQnaAutoAnswerTargetVo;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;



/**
 * LG_API QnA 미답변건 자동 답변 배치
 */
//Scheduled(cron = "00 00 */6 * * *") 6시간 마다 실행?
// 일반 답변의 경우 상품 수정 배치에서 같이 진행 되나(processor 같이 사용하면서 handler 로 구분 )
// 자동 답변의 경우 db insert 가 필요하여 별도 processor 생성

@Configuration
public class LgPortalProductQnaAutoAnswerJobConfig {
    private final int CHUNK_SIZE = 1000;

    @Bean("BA_HIPO00036")
    public Job lgPortalProductQnaAutoAnswerJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPO00036_STEP") Step lgPortalProductQnaAutoAnswerStep
    ) {
        return new JobBuilder("BA_HIPO00036", jobRepository)
                .start(lgPortalProductQnaAutoAnswerStep)
                .build();
    }

    @Bean("BA_HIPO00036_STEP")
    public Step lgPortalProductQnaAutoAnswerStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
            , LgPortalProductQnaAutoAnswerReader lgPortalProductQnaAutoAnswerReader
            , LgPortalProductQnaAutoAnswerProcessor lgPortalProductQnaAutoAnswerProcessor
            , LgPortalProductQnaAutoAnswerWriter lgPortalProductQnaAutoAnswerWriter
    ) {
        return new StepBuilder("BA_HIPO00036_STEP", jobRepository)
                .allowStartIfComplete(true)
                .<LgQnaAutoAnswerTargetVo, LgProductQnaAutoAnswerListDto>chunk(CHUNK_SIZE, transactionManager)
                .reader(lgPortalProductQnaAutoAnswerReader)
                .processor(lgPortalProductQnaAutoAnswerProcessor)
                .writer(lgPortalProductQnaAutoAnswerWriter)
                .build();

    }
}
