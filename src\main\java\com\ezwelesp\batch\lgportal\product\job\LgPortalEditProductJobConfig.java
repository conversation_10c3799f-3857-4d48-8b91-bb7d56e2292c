package com.ezwelesp.batch.lgportal.product.job;


import com.ezwelesp.batch.lgportal.product.chunk.processor.LgPortalEditProductProcessor;
import com.ezwelesp.batch.lgportal.product.chunk.reader.LgPortalEditProductReader;
import com.ezwelesp.batch.lgportal.product.chunk.writer.LgPortalEditProductWriter;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;



/**
 * LG_API 상품 관련 수정
 */
//Scheduled(cron = "00 */10 * * * *") 매 10분마다 실행

@Configuration
public class LgPortalEditProductJobConfig {
    private final int CHUNK_SIZE = 1000;

    @Bean("BA_HIPO00031")
    public Job lgPortalEditProductJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPO00031_STEP") Step lgPortalEditProductStep
    ) {
        return new JobBuilder("BA_HIPO00031", jobRepository)
                .start(lgPortalEditProductStep)
                .build();
    }

    @Bean("BA_HIPO00031_STEP")
    public Step lgPortalEditProductStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
            , LgPortalEditProductReader lgPortalEditProductReader
            , LgPortalEditProductProcessor lgPortalEditProductProcessor
            , LgPortalEditProductWriter lgPortalEditProductWriter
    ) {
        return new StepBuilder("BA_HIPO00031_STEP", jobRepository)
                .allowStartIfComplete(true)
                .<LgEditProductTargetVo, LgProductApiResponseDto>chunk(CHUNK_SIZE, transactionManager)
                .reader(lgPortalEditProductReader)
                .processor(lgPortalEditProductProcessor)
                .writer(lgPortalEditProductWriter)
                .build();

    }
}
