package com.ezwelesp.batch.hims.order.send.job;

import com.ezwelesp.batch.hims.order.send.tasklet.SendGoodsProcessTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * SMS 발송 [BA_HIOR00068]
 * 발송상품처리 배치
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class SendGoodsProcessJobConfig {
    private final CommonJobListener commonJobListener;
    private final SendGoodsProcessTasklet sendGoodsProcessTasklet;

    @Bean("BA_HIOR00068")
    public Job sendGoodsProcessJob(JobRepository jobRepository, @Qualifier("BA_HIOR00068_STEP") Step sendGoodsProcessStep) {
        return new JobBuilder("BA_HIOR00068", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(sendGoodsProcessStep)
                .build();
    }

    @Bean("BA_HIOR00068_STEP")
    public Step sendGoodsProcessStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00068_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(sendGoodsProcessTasklet, transactionManager)
                .build();
    }

}
