package com.ezwelesp.batch.hpas.alarm.dto;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 협력사 굿스플로 계약정보 동기화 DTO
 * 
 * <AUTHOR>
 * @since 2025.05.30
 */
@Data
public class GoodsFlowCspDataSyncMainDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String requestKey = "";
    private String partnerCode = "";
    private String centerCode = "";
    private String deliverCode = "";
    private String deliverNm = "";
    private String verificationType = "";
    private String bizNo = "";
    private String contractNo = "";
    private String contractCustNo = "";
    private String mallName = "";
    private String mallUserName = "";
    private String mallUserTel1 = "";
    private String mallUserTel2 = "";
    private String mallUserEmail = "";
    private String centerName = "";
    private String centerZipCode = "";
    private String centerAddr1 = "";
    private String centerAddr2 = "";
    private String centerTel1 = "";
    private String centerTel2 = "";
    private String shippingFee = "";
    private String flightFee = "";
    private String status = "";
    private String mgrId = "";
    private String cspCd = ""; // 협력사코드
    private String contractRatesSeq = "";
    private String boxSize = "";
    private String shFare = "0";
    private String scFare = "0";
    private String shOrcsGubn = "";
    private String shOrcsFare = "";
    private String bhFare = "0";
    private String rtFare = "0";
    private List<GoodsFlowCspDataSyncMainDto> boxRateList = null;

    private String memo = "";
    private String regDt = "";
    private String regId = "";
    private String regNm = "";
    private String searchGubn = "";
    private String searchVal = "";
    private String useYn = "";
    private String verifiedDatetime = "";
    private String verifiedResultCode = "";
    private String verifiedResult = "";
    private final Map<String, String> dlvrMap = new HashMap<String, String>() {
        {
            put("EPOST", "우체국택배");
            put("CJGLS", "CJ대한통운");
            put("KGB", "로젠택배");
            put("DONGBU", "드림택배");
            put("KGSL", "KGSL");
            put("HANJIN", "한진택배");
            put("HYUNDAI", "롯데택배");
            put("ILYANG", "일양로지스");
            put("BGF", "CU편의점 택배");
        }
    };

    private Map<String, Object> goodFlowResultMap;
    private String errorMsg = "";
    private String alertMsg = "";
}
