package com.ezwelesp.batch.hpas.alarm.tasklet;

import java.util.ArrayList;
import java.util.List;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.hpas.alarm.dto.EntryCspMainDto;
import com.ezwelesp.batch.hpas.alarm.service.EntryCspMainService;
import com.ezwelesp.batch.hpas.util.HpasUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class EntryCspMainTasklet implements Tasklet {
    private final EntryCspMainService entryCspMainService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) {
        try {
            log.info("#############################################");
            log.info("### [입점 승인 후 3일간 시스템 미접속 / 7일간 상품 미등록 안내메일 발송] 시작 일시 : " + HpasUtils.getCurrentDtm() + " ###");
            log.info("#############################################");

            /*
             * 대상업체 조회하여 메일발송
             * To 업체 - 협력사코드 / 협력사명 / 협력사ID / 초기비밀번호 / 담당MD명 / 담당MD연락처 / 담당MD이메일
             * To 담당MD - 협력사코드 / 협력사명 / 입점승인일 / 협력사담당자명 / 협력사담당자연락처 / 협력사담당자이메일
            */
            try {
                int baseCount = 10000;
                int entryCspMainCount = entryCspMainService.getEntryCspMainLoginCount();
                int pageInt = 1;
                if (entryCspMainCount > baseCount) {
                    pageInt = (entryCspMainCount / baseCount) + 1;
                }

                List<EntryCspMainDto> entryCspMainList = new ArrayList<EntryCspMainDto>();

                if (entryCspMainCount > 0) {
                    for (int i = 1; i <= pageInt; i++) {
                        entryCspMainList = entryCspMainService.getEntryCspMainLoginList(pageInt);

                        for (EntryCspMainDto entryCspMain : entryCspMainList) {
                            // 이메일발송
                            entryCspMainService.sendEmailEntryCspMainLogin(entryCspMain);
                        }
                    }

                }
            } catch (Exception e) {
                log.error("###########################################");
                log.error("## [입점 승인 후 3일간 시스템 미접속 안내메일 발송] 처리 중 에러 ###");
                log.error("###########################################");
                log.error(e.toString());
            }

            try {
                int baseCount = 10000;
                int entryCspMainCount = entryCspMainService.getEntryCspMainGoodsCount();
                int pageInt = 1;
                if (entryCspMainCount > baseCount) {
                    pageInt = (entryCspMainCount / baseCount) + 1;
                }

                List<EntryCspMainDto> entryCspMainList = new ArrayList<EntryCspMainDto>();

                if (entryCspMainCount > 0) {
                    for (int i = 1; i <= pageInt; i++) {
                        entryCspMainList = entryCspMainService.getEntryCspMainGoodsList(pageInt);

                        for (EntryCspMainDto entryCspMain : entryCspMainList) {
                            // 이메일발송
                            entryCspMainService.sendEmailEntryCspMainGoods(entryCspMain);
                        }
                    }

                }
            } catch (Exception e) {
                log.error("###########################################");
                log.error("## [입점 승인 후 7일간 상품 미등록 안내메일 발송] 처리 중 에러 ###");
                log.error("###########################################");
                log.error(e.toString());
            }

            log.info("#############################################");
            log.info("### [입점 승인 후 3일간 시스템 미접속 / 7일간 상품 미등록 안내메일 발송] 종료 일시 : " + HpasUtils.getCurrentDtm() + " ###");
            log.info("#############################################");

        } catch (Exception e) {
            log.error("처리중 에러가 발생했습니다.", e);
        }

        return RepeatStatus.FINISHED;
    }
}
