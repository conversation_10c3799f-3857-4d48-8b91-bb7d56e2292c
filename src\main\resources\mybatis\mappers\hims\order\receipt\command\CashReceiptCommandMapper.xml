<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.receipt.mapper.command.CashReceiptCommandMapper">

    <insert id="insertCashReceipt">
        insert into ez_or.py_csrc_b(
              ord_no
            , clm_no
            , ord_gds_seq
            , csrc_hndw_prcs_nos
            , asp_ord_no
            , all_pymt_amt
            , csrc_pblc_obj_dt
            , csrc_pblc_usg_cd
            , enc_csrc_pblc_no
            , pg_frcs_no
            , csrc_pblc_typ_cd
            , user_key
            , ordr_nm
            , ordr_eml_adr
            , ordr_mbl_telno
            , csrc_pg_apv_no
            , csrc_apv_no
            , csrc_apv_dtm
            , csrc_pblc_cncl_dt
            , cncl_csrc_pg_apv_no
            , csrc_cncl_apv_dtm
            , gds_cd
            , gds_nm
            , csp_cd
            , csp_nm
            , gds_amt_rt
            , gds_spl_prc
            , csrc_gds_pblc_amt
            , csrc_spl_prc
            , csrc_vat_amt
            , gds_vat_amt
            , csp_csrc_pblc_yn
            , csp_csrc_pblc_magn_cd
            , csrc_bzrn_obj_cd
            , csrc_pblc_knd_cd
            , csrc_pblc_obj_gds_yn
            , csp_bzrn
            , taxn_knd_cd
            , std_menu_cd
            , rgm_clsg_cncl_yn
            , csrc_pblc_yn
            , rmrk
            , wfp_csrc_pblc_amt
            , wgs_sbtr_csrc_pblc_amt
            , spp_csrc_pblc_amt
            , acnt_trns_csrc_pblc_amt
            , vtac_csrc_pblc_amt
            , lbv_csrc_pblc_amt
            , kcci_pnt_csrc_pblc_amt
            , vndp_csrc_pblc_amt
            , vnpp_csrc_pblc_amt
            , hpnt_csrc_pblc_amt
            , nvpay_csrc_pblc_amt
            , nhpnt_csrc_pblc_amt
            , wfp_yn
            , spp_yn
            , clxp_itd_obj_yn
            , frst_reg_dtm
            , frst_reg_usr_id
            , frst_reg_pgm_id
            , last_mod_dtm
            , last_mod_usr_id
            , last_mod_pgm_id
       )values (
             #{ordNo,jdbcType=VARCHAR}
           , #{clmNo,jdbcType=VARCHAR}
           , #{ordGdsSeq,jdbcType=NUMERIC}
           , #{csrcHndwPrcsNos,jdbcType=NUMERIC}
           , #{aspOrdNo,jdbcType=VARCHAR}
           , #{allPymtAmt,jdbcType=NUMERIC}
           , #{csrcPblcObjDt,jdbcType=VARCHAR}
           , #{csrcPblcUsgCd,jdbcType=VARCHAR}
           , #{encCsrcPblcNo,jdbcType=VARCHAR}
           , #{pgFrcsNo,jdbcType=VARCHAR}
           , #{csrcPblcTypCd,jdbcType=VARCHAR}
           , #{userKey,jdbcType=VARCHAR}
           , #{ordrNm,jdbcType=VARCHAR}
           , #{ordrEmlAdr,jdbcType=VARCHAR}
           , #{ordrMblTelno,jdbcType=VARCHAR}
           , #{csrcPgApvNo,jdbcType=VARCHAR}
           , #{csrcApvNo,jdbcType=VARCHAR}
           , #{csrcApvDtm,jdbcType=VARCHAR}
           , #{csrcPblcCnclDt,jdbcType=VARCHAR}
           , #{cnclCsrcPgApvNo,jdbcType=VARCHAR}
           , #{csrcCnclApvDtm,jdbcType=VARCHAR}
           , #{gdsCd,jdbcType=VARCHAR}
           , #{gdsNm,jdbcType=VARCHAR}
           , #{cspCd,jdbcType=VARCHAR}
           , #{cspNm,jdbcType=VARCHAR}
           , #{gdsAmtRt,jdbcType=NUMERIC}
           , #{gdsSplPrc,jdbcType=NUMERIC}
           , #{csrcGdsPblcAmt,jdbcType=NUMERIC}
           , #{csrcSplPrc,jdbcType=NUMERIC}
           , #{csrcVatAmt,jdbcType=NUMERIC}
           , #{gdsVatAmt,jdbcType=NUMERIC}
           , #{cspCsrcPblcYn,jdbcType=VARCHAR}
           , #{cspCsrcPblcMagnCd,jdbcType=VARCHAR}
           , #{csrcBzrnObjCd,jdbcType=VARCHAR}
           , #{csrcPblcKndCd,jdbcType=VARCHAR}
           , #{csrcPblcObjGdsYn,jdbcType=VARCHAR}
           , #{cspBzrn,jdbcType=VARCHAR}
           , #{taxnKndCd,jdbcType=VARCHAR}
           , #{stdMenuCd,jdbcType=VARCHAR}
           , #{rgmClsgCnclYn,jdbcType=VARCHAR}
           , #{csrcPblcYn,jdbcType=VARCHAR}
           , #{rmrk,jdbcType=VARCHAR}
           , #{wfpCsrcPblcAmt,jdbcType=NUMERIC}
           , #{wgsSbtrCsrcPblcAmt,jdbcType=NUMERIC}
           , #{sppCsrcPblcAmt,jdbcType=NUMERIC}
           , #{acntTrnsCsrcPblcAmt,jdbcType=NUMERIC}
           , #{vtacCsrcPblcAmt,jdbcType=NUMERIC}
           , #{lbvCsrcPblcAmt,jdbcType=NUMERIC}
           , #{kcciPntCsrcPblcAmt,jdbcType=NUMERIC}
           , #{vndpCsrcPblcAmt,jdbcType=NUMERIC}
           , #{vnppCsrcPblcAmt,jdbcType=NUMERIC}
           , #{hpntCsrcPblcAmt,jdbcType=NUMERIC}
           , #{nvpayCsrcPblcAmt,jdbcType=NUMERIC}
           , #{nhpntCsrcPblcAmt,jdbcType=NUMERIC}
           , #{wfpYn,jdbcType=VARCHAR}
           , #{sppYn,jdbcType=VARCHAR}
           , #{clxpItdObjYn,jdbcType=VARCHAR}
           , to_char(now(), 'YYYYMMDDHH24MISS')
           , #{userKey,jdbcType=VARCHAR}
           , #{frstRegPgmId}
           , to_char(now(), 'YYYYMMDDHH24MISS')
           , #{userKey,jdbcType=VARCHAR}
           , #{frstRegPgmId}
        )
    </insert>

    <insert id="insertCashReceiptWithdrawal">
        insert into ez_or.py_csrc_b (
              ord_no
            , clm_no
            , ord_gds_seq
            , csrc_hndw_prcs_nos
            , asp_ord_no
            , all_pymt_amt
            , csrc_pblc_obj_dt
            , csrc_pblc_usg_cd
            , enc_csrc_pblc_no
            , pg_frcs_no
            , csrc_pblc_typ_cd
            , user_key
            , ordr_nm
            , ordr_eml_adr
            , ordr_mbl_telno
            , csrc_pg_apv_no
            , csrc_apv_no
            , csrc_apv_dtm
            , csrc_pblc_cncl_dt
            , cncl_csrc_pg_apv_no
            , csrc_cncl_apv_dtm
            , gds_cd
            , gds_nm
            , csp_cd
            , csp_nm
            , gds_amt_rt
            , gds_spl_prc
            , csrc_gds_pblc_amt
            , csrc_spl_prc
            , csrc_vat_amt
            , gds_vat_amt
            , csp_csrc_pblc_yn
            , csp_csrc_pblc_magn_cd
            , csrc_bzrn_obj_cd
            , csrc_pblc_knd_cd
            , csrc_pblc_obj_gds_yn
            , csp_bzrn
            , taxn_knd_cd
            , std_menu_cd
            , rgm_clsg_cncl_yn
            , csrc_pblc_yn
            , rmrk
            , wfp_csrc_pblc_amt
            , wgs_sbtr_csrc_pblc_amt
            , spp_csrc_pblc_amt
            , acnt_trns_csrc_pblc_amt
            , vtac_csrc_pblc_amt
            , lbv_csrc_pblc_amt
            , kcci_pnt_csrc_pblc_amt
            , vndp_csrc_pblc_amt
            , vnpp_csrc_pblc_amt
            , hpnt_csrc_pblc_amt
            , nvpay_csrc_pblc_amt
            , nhpnt_csrc_pblc_amt
            , wfp_yn
            , spp_yn
            , clxp_itd_obj_yn
            , frst_reg_dtm
            , frst_reg_usr_id
            , frst_reg_pgm_id
            , last_mod_dtm
            , last_mod_usr_id
            , last_mod_pgm_id
        )
        select
            ord_no
             , #{clmNo}
             , ord_gds_seq
             , (select max(csrc_hndw_prcs_nos) from  ez_or.py_csrc_b where ord_no = a.ord_no and ord_gds_seq = a.ord_gds_seq)+(row_number() over(partition by a.ord_no,a.ord_gds_seq))
             , asp_ord_no
             , all_pymt_amt
             , csrc_pblc_obj_dt
             , csrc_pblc_usg_cd
             , enc_csrc_pblc_no
             , pg_frcs_no
             , csrc_pblc_typ_cd
             , user_key
             , ordr_nm
             , ordr_eml_adr
             , ordr_mbl_telno
             , csrc_pg_apv_no
             , csrc_apv_no
             , csrc_apv_dtm
             , #{csrcPblcCnclDt,jdbcType=VARCHAR}
             , cncl_csrc_pg_apv_no
             , csrc_cncl_apv_dtm
             , gds_cd
             , gds_nm
             , csp_cd
             , csp_nm
             , gds_amt_rt
             , gds_spl_prc
             , csrc_gds_pblc_amt
             , csrc_spl_prc
             , csrc_vat_amt
             , gds_vat_amt
             , csp_csrc_pblc_yn
             , csp_csrc_pblc_magn_cd
             , csrc_bzrn_obj_cd
             , csrc_pblc_knd_cd
             , csrc_pblc_obj_gds_yn
             , csp_bzrn
             , taxn_knd_cd
             , std_menu_cd
             , rgm_clsg_cncl_yn
             , csrc_pblc_yn
             , rmrk
             , wfp_csrc_pblc_amt
             , wgs_sbtr_csrc_pblc_amt
             , spp_csrc_pblc_amt
             , acnt_trns_csrc_pblc_amt
             , vtac_csrc_pblc_amt
             , lbv_csrc_pblc_amt
             , kcci_pnt_csrc_pblc_amt
             , vndp_csrc_pblc_amt
             , vnpp_csrc_pblc_amt
             , hpnt_csrc_pblc_amt
             , nvpay_csrc_pblc_amt
             , nhpnt_csrc_pblc_amt
             , wfp_yn
             , spp_yn
             , clxp_itd_obj_yn
             , to_char(now(), 'YYYYMMDDHH24MISS')
             , user_key
             , frst_reg_pgm_id
             , to_char(now(), 'YYYYMMDDHH24MISS')
             , user_key
             , frst_reg_pgm_id
        from
            ez_or.py_csrc_b a
        where
            ord_no = #{ordNo}
            and (csrc_pblc_cncl_dt is null or csrc_pblc_cncl_dt = '')
            <choose>
                <when test="prevClmNo != null and prevClmNo != ''">
                    and clm_no = #{prevClmNo}
                </when>
                <otherwise>
                    and clm_no = '0'
                </otherwise>
            </choose>
    </insert>

    <select id="selectClaimCashReceiptNextNumber" flushCache="true" resultType="Long">
        select coalesce(max(csrc_hndw_prcs_nos),0)
        from  ez_or.py_csrc_b
        where
            ord_no =  #{ordNo,jdbcType=VARCHAR}
          and ord_gds_seq =  #{ordGdsSeq,jdbcType=NUMERIC}
    </select>

    <select id="selectWithdrawalReceipt" flushCache="true" resultType="java.lang.Integer">
        select
            count(1)
        from
            ez_or.py_csrc_b a
        where
            ord_no = #{ordNo}
          and clm_no = #{clmNo}
          and coalesce(csrc_pblc_cncl_dt,'') != ''
    </select>

    <update id="updatePublicationReceipt">
        update ez_or.py_csrc_b
        set csrc_pg_apv_no = #{tid,jdbcType=VARCHAR}
            ,csrc_apv_no = #{applNum,jdbcType=VARCHAR}
            ,csrc_apv_dtm = #{csrcApvDtm,jdbcType=VARCHAR}
            ,csrc_spl_prc = #{csrcSplPrc,jdbcType=NUMERIC}
            ,csrc_vat_amt = #{csrcVatAmt,jdbcType=NUMERIC}
        WHERE ord_no =  #{ordNo,jdbcType=VARCHAR}
          AND clm_no = #{clmNo,jdbcType=VARCHAR}
          AND ord_gds_seq = #{ordGdsSeq,jdbcType=NUMERIC}
          AND csrc_hndw_prcs_nos = #{csrcHndwPrcsNos,jdbcType=NUMERIC}
    </update>

    <update id="updatePublicationReceiptError">
        update ez_or.py_csrc_b
        set csrc_pblc_obj_dt = (CASE WHEN to_char(now(), 'YYYYMMDD') = #{csrcPblcObjDt,jdbcType=VARCHAR}
                                              THEN #{csrcPblcObjDt,jdbcType=VARCHAR}
                                          ELSE to_char(now(), 'YYYYMMDD') END)
        WHERE ord_no =  #{ordNo,jdbcType=VARCHAR}
          AND clm_no = #{clmNo,jdbcType=VARCHAR}
          AND ord_gds_seq = #{ordGdsSeq,jdbcType=NUMERIC}
          AND csrc_hndw_prcs_nos = #{csrcHndwPrcsNos,jdbcType=NUMERIC}
    </update>

    <update id="updatePublicationReceiptNumError">
        update ez_or.py_csrc_b
        set csrc_pblc_obj_dt = (CASE WHEN to_char(now(), 'YYYYMMDD') = #{csrcPblcObjDt,jdbcType=VARCHAR}
                                         THEN #{csrcPblcObjDt,jdbcType=VARCHAR}
                                     ELSE to_char(now(), 'YYYYMMDD') END)
          , enc_csrc_pblc_no = #{encCsrcPblcNo,jdbcType=VARCHAR}
          , csrc_pblc_usg_cd = #{csrcPblcUsgCd}
          , ordr_mbl_telno = #{ordrMblTelno,jdbcType=VARCHAR}
        WHERE ord_no =  #{ordNo,jdbcType=VARCHAR}
          AND clm_no = #{clmNo,jdbcType=VARCHAR}
          AND ord_gds_seq = #{ordGdsSeq,jdbcType=NUMERIC}
          AND csrc_hndw_prcs_nos = #{csrcHndwPrcsNos,jdbcType=NUMERIC}
    </update>

    <delete id="deletePublicationReceiptError">
        delete from ez_or.py_csrc_b
        WHERE ord_no =  #{ordNo,jdbcType=VARCHAR}
          AND clm_no = #{clmNo,jdbcType=VARCHAR}
          AND ord_gds_seq = #{ordGdsSeq,jdbcType=NUMERIC}
          AND csrc_hndw_prcs_nos = #{csrcHndwPrcsNos,jdbcType=NUMERIC}
          AND csrc_pg_apv_no is null
    </delete>






    <update id="updatePublicationReceiptCancel">
        update ez_or.py_csrc_b
        set cncl_csrc_pg_apv_no = #{cancelNum,jdbcType=VARCHAR}
            ,csrc_cncl_apv_dtm = #{cancelTime}
        WHERE ord_no =  #{ordNo,jdbcType=VARCHAR}
          AND clm_no = #{clmNo,jdbcType=VARCHAR}
          AND ord_gds_seq = #{ordGdsSeq,jdbcType=NUMERIC}
          AND csrc_hndw_prcs_nos = #{csrcHndwPrcsNos,jdbcType=NUMERIC}
    </update>

    <update id="updatePublicationReceiptCancelError">
        update ez_or.py_csrc_b
        SET csrc_pblc_cncl_dt = (CASE WHEN to_char(now(), 'YYYYMMDD') = #{csrcPblcCnclDt,jdbcType=VARCHAR}
                                         THEN #{csrcPblcCnclDt,jdbcType=VARCHAR}
                                     ELSE to_char(now(), 'YYYYMMDD') END)
        WHERE ord_no =  #{ordNo,jdbcType=VARCHAR}
          AND clm_no = #{clmNo,jdbcType=VARCHAR}
          AND ord_gds_seq = #{ordGdsSeq,jdbcType=NUMERIC}
          AND csrc_hndw_prcs_nos = #{csrcHndwPrcsNos,jdbcType=NUMERIC}
    </update>


    
    <insert id="insertPublicationReceiptError">
        insert into ez_or.py_csrc_err_g(
            csrc_err_log_seq
            ,ord_no
            ,ord_gds_seq
            ,csrc_hndw_prcs_nos
            ,pymt_nos
            ,csrc_err_cd
            ,csrc_err_cntnj
            ,frst_reg_dtm
            ,frst_reg_usr_id
            ,frst_reg_pgm_id
            ,last_mod_dtm
            ,last_mod_usr_id
            ,last_mod_pgm_id
        )values (
            nextval('ez_or.sq_py_csrc_err_g')
            ,#{ordNo,jdbcType=VARCHAR}
            , #{ordGdsSeq,jdbcType=NUMERIC}
            , #{csrcHndwPrcsNos,jdbcType=NUMERIC}
            , 1
            , #{resultCode,jdbcType=VARCHAR}
            , to_json(#{jsonStr,jdbcType=VARCHAR})
            , to_char(now(), 'YYYYMMDDHH24MISS')
            , #{userKey,jdbcType=VARCHAR}
            , 'test'
            , to_char(now(), 'YYYYMMDDHH24MISS')
            , #{userKey,jdbcType=VARCHAR}
            , 'test'
        )
    </insert>
</mapper>

