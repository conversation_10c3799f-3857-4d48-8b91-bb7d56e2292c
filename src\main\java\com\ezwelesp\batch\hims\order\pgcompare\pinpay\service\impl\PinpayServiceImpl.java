package com.ezwelesp.batch.hims.order.pgcompare.pinpay.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.ezwelesp.batch.hims.order.entity.external.EzEcOrderPayLogEntity;
import com.ezwelesp.batch.hims.order.pgcompare.pinpay.mapper.command.PinpayCommandMapper;
import com.ezwelesp.batch.hims.order.pgcompare.pinpay.mapper.query.PinpayQueryMapper;
import com.ezwelesp.batch.hims.order.pgcompare.pinpay.service.PinpayService;
import com.ezwelesp.batch.hims.order.utils.RestClientUtil;
import com.ezwelesp.framework.utils.DateUtils;
import com.ezwelesp.framework.utils.StringUtils;
import com.ezwelesp.framework.utils.request.dto.Header;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service 
@RequiredArgsConstructor
public class PinpayServiceImpl implements PinpayService {
	
	private final PinpayQueryMapper pinpayQueryMapper;
	private final PinpayCommandMapper pinpayCommandMapper;
	
	@Value(("${pinpay.api.timeout}"))
	private int timeout;
	
	@Value(("${pinpay.api.history}"))
	private String apiUrl;
	
	@Value(("${pinpay.api.mchId}"))
	private String mchId;
	
	@Value(("${pinpay.api.limit}"))
	private String limit;
	
	@Override
	public void callPinpayApi(String searchDtm) {
		log.info("Pinpay Batch START");
		try {
			this.getPinpayOrderList(1);
		} catch (Exception e) {
			log.error("Pinpay Batch ERROR");
			log.error(e.getMessage());
			e.printStackTrace();
		} finally {
			log.info("Pinpay Batch FINISHED");
		}
	}
	
	
	private void getPinpayOrderList(int page) throws Exception {
		String searchDate = DateUtils.dateTimeNow("yyyyMMddHH");
		
		log.info("getPinpayOrderList page [" + page + "]");
		log.info("getPinpayOrderList time [" + searchDate + "]");

		JSONObject sendObject = new JSONObject();
		sendObject.put("startDateTime", searchDate + "0000"); //"20220627000000"
		sendObject.put("endDateTime", searchDate + "5959"); // "20220627235959"
		sendObject.put("page", page);
		sendObject.put("limit", this.limit);
		sendObject.put("sort", "ASC");
		sendObject.put("mchId", this.mchId);

		JSONObject jsonObject = this.getPinPayRequest(sendObject.toString());

		log.info("getPinpayOrderList sendObject:" + sendObject.toString());
		log.info("getPinpayOrderList jsonObject:" + jsonObject.toString());

		String resultCode = (String) jsonObject.get("resultCode");

		log.info("getPinpayOrderList resultCode [" + resultCode + "]");

		if("0000".equals(resultCode) ) {
			int total = Integer.parseInt(String.valueOf(jsonObject.get("total")));
			int next = Integer.parseInt(String.valueOf(jsonObject.get("next")));

			log.info("getPinpayOrderList resultCode:" + resultCode + ", total:" + total + ", next:" + next);
			if(total > 0) {
				this.insertPinpayData((JSONArray) jsonObject.get("list"));
				if(next > 0) {
					getPinpayOrderList(next);
				}
			}

		}

	}
	
	private JSONObject getPinPayRequest(String sendJson) throws Exception {
		JSONObject jsonObject = new JSONObject();
		log.info("pinPayRequest sendJson " + sendJson);
		log.info("pinPayRequest pinpayApiUrl " + this.apiUrl);

		try {
			String pinpayAuthKey = this.pinpayQueryMapper.selectPinpayAuthKey();
			
			List<Header> headerList = new ArrayList<>();
			headerList.add(new Header("Accept", "application/json"));
			headerList.add(new Header("Authorization", "Basic " + pinpayAuthKey));
			
			
			Map<String, Object> result = RestClientUtil.requestApi(this.apiUrl, this.timeout, headerList, sendJson);
			if(!StringUtils.equals("0000", result.get("resultCode").toString())) {
				log.info("Remote Server Receive Failed(Internal Error Http Status Code:[" + result.get("code") + "])");
				throw new Exception("[5002]서버 수신오류(Server Internal Error)");
			}
			
			jsonObject = new JSONObject(new ObjectMapper().writeValueAsString(result));
			log.info("pinPayRequest jsonObject " + jsonObject);

		} catch (Exception e) {
			log.error("pinPayRequest catch " + e.getMessage());
			e.printStackTrace();
		} 

		return jsonObject;
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertPinpayData(JSONArray data) throws Exception {
		try {
			log.info("insertPinpayData JSONArray data [" + data.length() + "]");
			for(int i = 0; i < data.length(); i++) {
				JSONObject dataObject = (JSONObject) data.get(i);

				boolean checkPayStatus = false;
				String dealStatus = "";

				//10 :인증요청, 19: 인증실패, 11: 인증성공
				//20: 승인요청, 29: 승인실패, 21 승인성공
				//80: 취소요청, 81: 취소성공, 89:취소실패
				//90: 망취소요청, 99:망취소실패, 91:망취소성공
				if("21".equals(dataObject.get("payStatus"))) {
					dealStatus = "승인";
					checkPayStatus = true;
				}else if("81".equals(dataObject.get("payStatus"))) {
					dealStatus = "취소";
					checkPayStatus = false;
				}else if("91".equals(dataObject.get("payStatus"))) {
					dealStatus = "망취소";
					checkPayStatus = false;
				}else {
					continue;
				}

				String confirmDd = dataObject.get("payCplnDttm") != null ? String.valueOf(dataObject.get("payCplnDttm")).substring(0, 8) : ""; //payReqDtm
				String confirmTm = dataObject.get("payCplnDttm") != null ? String.valueOf(dataObject.get("payCplnDttm")).substring(8, 14) : "";
				Long amount = Long.parseLong(String.valueOf(dataObject.get("amount")));
				String payId = String.valueOf(dataObject.get("payId"));
				EzEcOrderPayLogEntity ezEcOrderPayLogEntity = EzEcOrderPayLogEntity.builder()
					.tid(payId)
					.statusCd(checkPayStatus ? "0001" : "0002")
					.orderNum(String.valueOf(dataObject.get("orderId")))
					.ezwelOrderNum(Long.parseLong(String.valueOf(dataObject.get("orderId")).replace("MK_", "")))
					.storeId(this.mchId)
					.dealType("C")
					.deviceType("구분없음")
					.payType("신용카드")
					.dealStatus(dealStatus)
					.confirmDd(confirmDd)
					.confirmTm(confirmTm)
					.payDt(confirmDd + confirmTm)
					.dealAmt(new BigDecimal(amount))
					.cancelTid(payId)
					.cardAmt(new BigDecimal(amount))
					.instMm("")
					.confirmNum(dataObject.get("approvalNo") != null ? String.valueOf(dataObject.get("approvalNo")) : "")
					.pgType("P")
					.build();

				//취소일때
				if(!checkPayStatus) {
					ezEcOrderPayLogEntity = ezEcOrderPayLogEntity.toBuilder()
							.cancelDd(confirmDd)
							.cancelTm(confirmTm)
							.cancelAmt(new BigDecimal(String.valueOf(dataObject.get("amount"))))
							.build();
				}


				this.pinpayCommandMapper.insertPinpayOrderLog(ezEcOrderPayLogEntity);

				log.info("insertPinpayData insertData[" + ezEcOrderPayLogEntity.toString() + "]");
			}
		} catch(DuplicateKeyException e) {
			log.error("insertNaverpayData catch " + e.getMessage());
			e.printStackTrace();
		} catch (Exception e) {
			log.error("insertNaverpayData catch " + e.getMessage());
			e.printStackTrace();
		}
	}

}
