package com.ezwelesp.batch.hims.calculate.util;

import java.time.YearMonth;

import org.joda.time.LocalDate;

public class CalculateBatchUtil {
    /**
     * 현재 기준 이전 달 yyyyMM 문자열 형식 return
     * @return
     */
    public static String getCurrentBasicBeforeMonth() {
        // 현재 날짜 가져오기
        LocalDate currentDate = LocalDate.now();
        // 1. 현재 기준 이전 달 구하기 (yyyyMM 포맷)
        YearMonth currentYearMonth = YearMonth.of(currentDate.getYear(), currentDate.getMonthOfYear());
        YearMonth previousYearMonth = currentYearMonth.minusMonths(1);
        int prevYear = previousYearMonth.getYear();
        int prevMonth = previousYearMonth.getMonthValue();
        String previousMonthFormatted = String.format("%04d%02d", prevYear, prevMonth);
        return previousMonthFormatted;
    }
    
    /**
     * 현재 기준 이전 달의 마지막 날짜 yyyyMMdd 문자열 형식 return
     * @return
     */
    public static String getCurrentBasicBeforeMonthLastDay() {
        // 현재 날짜 가져오기
        LocalDate currentDate = LocalDate.now();
        // 1. 현재 기준 이전 달 구하기 (yyyyMM 포맷)
        YearMonth currentYearMonth = YearMonth.of(currentDate.getYear(), currentDate.getMonthOfYear());
        YearMonth previousYearMonth = currentYearMonth.minusMonths(1);
        // 2. 현재 기준 이전 달의 마지막 날짜 구하기 (yyyyMMdd 포맷)
        java.time.LocalDate lastDayOfPreviousMonth = previousYearMonth.atEndOfMonth();
        int lastYear = lastDayOfPreviousMonth.getYear();
        int lastMonth = lastDayOfPreviousMonth.getMonthValue();
        int lastDay = lastDayOfPreviousMonth.getDayOfMonth();
        String lastDayFormatted = String.format("%04d%02d%02d", lastYear, lastMonth, lastDay);
        return lastDayFormatted;
    }
    
    /**
     * yyyyMMdd 형식의 문자열을 받아 해당 년월의 마지막 일자를 return
     * @param yearMonth
     * @return
     */
    public static String getLastDayOfMonth(String yearMonth) {
        return yearMonth + YearMonth.parse(yearMonth, java.time.format.DateTimeFormatter.ofPattern("yyyyMM"))
                                    .atEndOfMonth()
                                    .getDayOfMonth();
    }
}
