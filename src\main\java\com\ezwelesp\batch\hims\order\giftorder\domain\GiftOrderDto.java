package com.ezwelesp.batch.hims.order.giftorder.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.domain
 * @since 2025.05.15
 */
@Data
public class GiftOrderDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5843309772784563641L;

    private long gvgftOrdNo;

    private String ordNo;

    private String aspOrdNo;

    private String gdsNm;

    private String ordrNm;

    private String rcvrNm;

    private String rcvrMblTelno;

    private String ordDtm;

    private String clntCd;

    private String clntTypCd;
}
