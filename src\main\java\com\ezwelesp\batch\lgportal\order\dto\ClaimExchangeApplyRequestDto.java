package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 교환 신청 DTO
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto
 * @since 2025.04.30
 */
@Getter
@Jacksonized
@SuperBuilder
public class ClaimExchangeApplyRequestDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 3117496239075868039L;

    // 주문번호
    private String orderNumber;

    // 협력사코드
    private String cspCd;

    // 협력사명
    private String cspNm;

    // 협력사출고위치번호
    private String cspObndLocNo;

    // 클레임종류코드
    private String clmKndCd;

    // 주문상품 리스트
    private List<ClaimSaveGoodsDto> ordGdsList;

    // 배송지순번
    private Long dlvAdrNo;

    // 클레임배송비용
    private String clmDlvExp;

    // 클레임추가비용결제수단코드
    private String clmAddExpPrcsPymtMnsCd;

    // 배송정보
    private List<ClaimDeliveryAddressDto> deliveryAddressList;

    // 수거방법
    private String pickupMthd;

    /*
     * Batch 에서 추가되는 파라미터
     */

    // 클레임 일시
    private String clmDtm;
}
