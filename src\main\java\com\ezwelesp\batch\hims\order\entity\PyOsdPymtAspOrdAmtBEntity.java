package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 외부결제제휴사주문금액기본(ez_or.py_osd_pymt_asp_ord_amt_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyOsdPymtAspOrdAmtBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 결제차수(pymt_nos) not null
     */
    private Long pymtNos;

    /**
     * 외부결제금액(osd_pymt_amt) not null
     */
    private BigDecimal osdPymtAmt;

    /**
     * 외부결제정산대상금액(osd_pymt_stl_obj_amt) not null
     */
    private BigDecimal osdPymtStlObjAmt;

    /**
     * 외부결제정산제외금액(osd_pymt_stl_excld_amt) not null
     */
    private BigDecimal osdPymtStlExcldAmt;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
