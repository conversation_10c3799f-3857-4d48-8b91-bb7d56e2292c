package com.ezwelesp.batch.lgportal.order.chunk.writer;

import com.ezwelesp.batch.lgportal.entity.ApiBatchCspDlvrEntity;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.CspDlvrLocDto;
import com.ezwelesp.batch.lgportal.order.dto.DeliveryLocListReqDto;
import com.ezwelesp.batch.lgportal.order.dto.RegDeliveryLocReqDto;
import com.ezwelesp.batch.lgportal.order.mapper.command.ApiBatchCommandMapper;
import com.ezwelesp.batch.lgportal.util.RestClientUtil;
import com.ezwelesp.framework.apiresponse.AjaxResult;
import com.ezwelesp.framework.utils.JsonObjectConverter;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 출고/반품/교환지 등록 처리
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order
 * @since 2025.03.05
 */
@Component
public class RegDeliveryLocItemWriter implements ItemWriter<CspDlvrLocDto> {

    @Value("${lgportal.server.host}")
    private String serverHost;

    @Value("${lgportal.server.timeout}")
    private int timeout;

    @Value("${lgportal.api-key}")
    private String apiKey;



    private final SqlSessionTemplate sqlSessionTemplate;

    /**
     * Instantiates a new Reg delivery loc item writer.
     *
     * @param sqlSessionFactory the sql session factory
     */
    public RegDeliveryLocItemWriter(@Qualifier("primaryCommandSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory);
    }

    @Override
    public void write(Chunk<? extends CspDlvrLocDto> chunks) {
        chunks.forEach(cspDlvrLocDto ->
                this.sqlSessionTemplate.insert(ApiBatchCommandMapper.class.getName() + ".insertApiBatchCspDlvr",
                        this.regDlvrLoc(cspDlvrLocDto)));
    }

    private ApiBatchCspDlvrEntity regDlvrLoc(CspDlvrLocDto entity) {
        RegDeliveryLocReqDto regDeliveryLocReqDto = new RegDeliveryLocReqDto(this.apiKey);

        regDeliveryLocReqDto.setDeliveryAddrTypeCode(entity.getDlvrTypeCd());
        regDeliveryLocReqDto.setDeliveryName(entity.getCspDlvrNm());
        regDeliveryLocReqDto.setBaseSettingYN(entity.getBaseSettingYn());
        regDeliveryLocReqDto.setPostCode(entity.getZipCd());
        regDeliveryLocReqDto.setDeliveryAddr(entity.getDlvrAddr());
        regDeliveryLocReqDto.setDeliveryAddrDetail(entity.getDlvrAddrDetail());
        regDeliveryLocReqDto.setTelNo1(StringUtils.isEmpty(entity.getTelno()) ? "-" : entity.getTelno());
        regDeliveryLocReqDto.setDeliveryComNo("COM1102");   // 대한통운
        regDeliveryLocReqDto.setDeliveryCostTypeCode(entity.getDlvrCostTypeCd());
        regDeliveryLocReqDto.setDeliveryMappingCode(entity.getCspDlvrId() + "_" + entity.getDlvrPrice());

        regDeliveryLocReqDto.setEntEntryComDeliveryCostList(Collections.singletonList(RegDeliveryLocReqDto.DeliveryCost.builder()
            .deliveryCost(String.valueOf(entity.getDlvrPrice()))
            .build()));

        try {
            AjaxResult result = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_ADD_VENDOR, this.timeout, JsonObjectConverter.serialize(regDeliveryLocReqDto));

            ApiBatchCspDlvrEntity apiBatchCspDlvr = ApiBatchCspDlvrEntity.builder()
                    .cspCd(entity.getCspCd())
                    .cspDlvrId(entity.getCspDlvrId())
                    .dlvrTypeCd(entity.getDlvrTypeCd())
                    .dlvrCostTypeCd(entity.getDlvrCostTypeCd())
                    .dlvrPrice(entity.getDlvrPrice())
                    .reqParam(JsonObjectConverter.serialize(regDeliveryLocReqDto))
                    .build();

            if (StringUtils.equals(String.valueOf(HttpStatus.OK.value()), result.get("result").toString())) {

                DeliveryLocListReqDto deliveryLocListReqDto = DeliveryLocListReqDto.builder()
                        .apiKey(this.apiKey)
                        .deliveryMappingCode(entity.getCspDlvrId() + "_" + entity.getDlvrPrice())
                        .deliveryAddrTypeCode(entity.getDlvrTypeCd())
                        .deliveryCostTypeCode(entity.getDlvrCostTypeCd())
                        .build();

                AjaxResult deliveryLocListResult = RestClientUtil.requestApi(this.serverHost + LgConstants.TARGET_VENDOR_LIST, this.timeout, JsonObjectConverter.serialize(deliveryLocListReqDto));

                Integer lgDlvrNo = null;
                if (StringUtils.equals(String.valueOf(HttpStatus.OK.value()), deliveryLocListResult.get("result").toString()) &&
                        ObjectUtils.isNotEmpty(deliveryLocListResult.get("items"))) {
                    // '배송지정보순번' 조회
                    lgDlvrNo = Integer.parseInt(((List<Map<String, Object>>)deliveryLocListResult.get("items")).get(0).get("entryComDeliveryNo").toString());
                }

                return apiBatchCspDlvr.toBuilder()
                        .sendYn("Y")
                        .resultCd(result.get("result").toString())
                        .resultMsg(result.get("resultMessage").toString())
                        .lgDlvrNo(lgDlvrNo)
                        .build();

            } else {
                return apiBatchCspDlvr.toBuilder()
                        .sendYn("F")
                        .resultCd(result.get("result").toString())
                        .resultMsg(result.get("resultMessage").toString())
                        .build();
            }

        } catch (JsonProcessingException e) {
            throw new RuntimeException("LG portal api request failed");
        }
    }
}
