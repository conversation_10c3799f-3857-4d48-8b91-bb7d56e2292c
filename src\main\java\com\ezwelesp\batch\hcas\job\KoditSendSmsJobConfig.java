package com.ezwelesp.batch.hcas.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hcas.tasklet.KoditSendSmsTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class KoditSendSmsJobConfig {
    private final CommonJobListener commonJobListener;
    private final KoditSendSmsTasklet tasklet;
    
    /**
     * 신용보증기금 잔여 포인트 sms 발송
     * 주기 : 매주 금요일 10시
     * <AUTHOR>
     * @since 2025. 4. 4.
     * @param jobRepository
     * @param koditSendSmsTaskletStep
     * @return
     */
    @Bean("koditSendSmsJob")
    public Job koditSendSmsJob(JobRepository jobRepository, Step koditSendSmsTaskletStep) {
        return new JobBuilder("koditSendSmsJob", jobRepository)
                .listener(commonJobListener)
                .start(koditSendSmsTaskletStep)
                .build();
    }
    
    /**
     * <AUTHOR>
     * @since 2025. 4. 4.
     * @param jobRepository
     * @param transactionManager
     * @return
     */
    @Bean("koditSendSmsTaskletStep")
    public Step koditSendSmsTaskletStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("koditSendSmsTaskletStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(tasklet, transactionManager)
                .build();
    }

}