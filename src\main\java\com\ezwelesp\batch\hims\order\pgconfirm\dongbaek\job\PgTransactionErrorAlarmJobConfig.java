package com.ezwelesp.batch.hims.order.pgconfirm.dongbaek.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.ezwelesp.batch.hims.order.pgconfirm.dongbaek.tasklet.PgTransactionErrorTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class PgTransactionErrorAlarmJobConfig {
	private final CommonJobListener commonJobListener;
	private final PgTransactionErrorTasklet pgTransactionErrorTasklet;
	
	@Bean("BA_HIOR00059")
	public Job inicisPgCompareJob(JobRepository jobRepository, @Qualifier("BA_HIOR00059_STEP") Step step) {
	    return new JobBuilder("BA_HIOR00059", jobRepository)
	            .listener(commonJobListener)
	            .start(step)
	            .build();
	}
	
	@Bean("BA_HIOR00059_STEP")
	public Step inicisPgCompareStep1(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
	    return new StepBuilder("BA_HIOR00059_STEP", jobRepository)
	            .allowStartIfComplete(true)
	            .tasklet(pgTransactionErrorTasklet, transactionManager)
	            .build();
	}
}
