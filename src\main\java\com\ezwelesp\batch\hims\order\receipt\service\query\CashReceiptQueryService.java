package com.ezwelesp.batch.hims.order.receipt.service.query;


import com.ezwelesp.batch.hims.order.receipt.domain.*;

import java.util.List;

public interface CashReceiptQueryService {
    List<OrderClaimDto> getOrderCashReceiptTarget();

    List<OrderClaimDto> getClaimCashReceiptTarget();

    List<OrderInfoDto> getOrderInfo(String ordNo);

    int getCountCashReceipt(CashReceiptParamDto param);

    List<CashPublicationGatherDto> getCashReceiptPublicationGather(CashPublicationGatherDto dto);

    int getCashReceiptPublicationGatherCount(CashPublicationGatherDto dto);

    ContentServiceProviderDto getUnitedContentServiceProviderInfo(String cspCd);

}
