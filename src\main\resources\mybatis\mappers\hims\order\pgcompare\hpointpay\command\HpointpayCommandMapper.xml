<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgcompare.hpointpay.mapper.command.HpointpayCommandMapper">

	<insert id="insertPgApproveErrList">
		<selectKey resultType="String" keyProperty="errSeq" order="BEFORE"> 
	      	SELECT coalesce(max(pg_pymt_cmpr_err_mtrg_lst_seq),0)+1 FROM ez_or.py_pg_pymt_cmpr_err_mtrg_l
	    </selectKey>
   		
	   		insert into ez_or.py_pg_pymt_cmpr_err_mtrg_l (
	   			pg_pymt_cmpr_err_mtrg_lst_seq
	   			, pg_pymt_err_cd
	   			, ord_no
	   			, ord_st_cd
	   			, asp_ord_no
	   			, ord_ch_nm
	   			, pymt_amt
	   			, pg_pymt_apv_amt
	   			, pg_knd_cd
	   			, pymt_mns_nm
	   			, pg_frcs_no
	   			, pg_apv_dtm
	   			, pg_apv_no
	   			, pg_crdc_apv_no
	   			, pg_frcs_ord_no
	   			, acss_te_typ_nm
	   			, mgr_cnft_cmpt_yn
	   			, mgr_memo
	   			, frst_reg_dtm
	   			, frst_reg_usr_id
	   			, frst_reg_pgm_id
	   			, last_mod_dtm
	   			, last_mod_usr_id
	   			, last_mod_pgm_id
	   		) values (
	   			#{errSeq}
				, #{pgPymtErrCd}
				, #{ordNo}
				, #{ordStCd}
				, #{aspOrdNo}
				, #{ordChNm}
				, #{pymtAmt}
				, #{pgPymtApvAmt}
				, #{pgKndCd}
				, #{pymtMnsNm}
				, #{pgFrcsNo}
				, #{pgApvNo}
				, #{pgCrdcApvNo}
				, #{pgFrcsOrdNo}
				, #{acssTeTypNm}
				, #{mgrCnftCmptYn}
				, #{mgrMemo}
				, to_char(now(), 'YYYYMMDDHH24MISS')
				, 'BATCH'
				, 'BATCH'
				, to_char(now(), 'YYYYMMDDHH24MISS')
				, 'BATCH'
				, 'BATCH'
	   		) 		
	</insert>
	
	
	<insert id="insertTossTransaction">
		
		insert into ez_if.ez_ec_hpointpay (
			toss_key,
			authcode,
			store_id,
			toss_order_num,
			pay_type,
			pay_method,
			pay_amt,
			confirm_dt,
			reg_dt
		) values (
			#{tossKey}
			, #{authcode}
			, #{storeId}
			, #{tossOrderNum}
			, #{payType}
			, #{payMethod}
			, #{payAmt}
			, #{confirmDt}
			, to_char(now(), 'yyyymmddhh24miss')
		)
		
	</insert>
	
	<insert id="insertApiPgPrsnlPay">
		merge into
			ez_if.api_pg_prsnl_pay 
		using
			(select 1)
		on
			(tid= #{tid} and status_cd= #{statusCd} and pg_type = #{pgType})
		when matched then 
			update set
				deal_status = deal_status
				, pay_cancel_dt = #{cancelDt}
		when not matched then 
			insert 
			(
				tid
				, status_cd
				, ezwel_order_num
				, store_id
				, pay_type
				, deal_status
				, confirm_dt
				, deal_amt
				, cancel_tid
				<if test="cancelDt != null and cancelDt != ''">
				, cancel_dt
				</if>
				<if test="cancelAmt != null and cancelAmt != ''">
				, cancel_amt
				</if>
				<if test="cardAmt != null and cardAmt != ''">
				, card_amt
				</if>
				<if test="instMm != null and instMm != ''">
				, inst_mm
				</if>
				<if test="confirmNum != null and confirmNum != ''">
				, confirm_num
				</if>
				, pay_dt
				, pg_type
				, pay_cd
			)values(
				#{tid}
				, #{statusCd}
				, #{ezwelOrderNum}
				, #{storeId}
				, #{payType}
				, #{dealStatus}
				, #{confirmDt}
				, #{dealAmt}
				, #{cancelTid}
				<if test="cancelDt != null and cancelDt != ''">
				, #{cancelDt}
				</if>
				<if test="cancelAmt != null and cancelAmt != ''">
				, #{cancelAmt}
				</if>
				<if test="cardAmt != null and cardAmt != ''">
				, #{cardAmt}
				</if>
				<if test="instMm != null and instMm != ''">
				, #{instMm}
				</if>
				<if test="confirmNum != null and confirmNum != ''">
				, #{confirmNum}
				</if>
				, #{payDt}
				, #{pgType}
				, #{payCd}
			)
		
	</insert>
</mapper>