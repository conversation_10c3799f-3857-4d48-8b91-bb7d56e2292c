package com.ezwelesp.batch.lgportal.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 주문기본(ez_or.or_ord_b)
 */
@SuperBuilder(toBuilder = true)
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class OrOrdBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 고객사주문번호(clnt_ord_no)
     */
    private String clntOrdNo;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 고객사코드(clnt_cd) not null
     */
    private String clntCd;

    /**
     * 주문일시(ord_dtm) not null
     */
    private String ordDtm;

    /**
     * 주문상태코드(ord_st_cd) not null
     */
    private String ordStCd;

    /**
     * 주문유형코드(ord_typ_cd) not null
     */
    private String ordTypCd;

    /**
     * 주문배송종류코드(ord_dlv_knd_cd) not null
     */
    private String ordDlvKndCd;

    /**
     * 접속디바이스코드(acss_dvc_cd)
     */
    private String acssDvcCd;

    /**
     * 배송준비시작일시(dlv_rdy_strt_dtm)
     */
    private String dlvRdyStrtDtm;

    /**
     * 배송시작일시(dlv_strt_dtm)
     */
    private String dlvStrtDtm;

    /**
     * 배송완료일시(dlv_cmpt_dtm)
     */
    private String dlvCmptDtm;

    /**
     * 취소일시(cncl_dtm)
     */
    private String cnclDtm;

    /**
     * 주문금액(ord_amt) not null
     */
    private BigDecimal ordAmt;

    /**
     * 주문자명(ordr_nm) not null
     */
    private String ordrNm;

    /**
     * 주문자전화번호(ordr_telno)
     */
    private String ordrTelno;

    /**
     * 주문자모바일전화번호(ordr_mbl_telno)
     */
    private String ordrMblTelno;

    /**
     * 주문자이메일주소(ordr_eml_adr)
     */
    private String ordrEmlAdr;

    /**
     * 가족회원번호(fam_mem_no)
     */
    private String famMemNo;

    /**
     * 실제주문자명(real_ordr_nm)
     */
    private String realOrdrNm;

    /**
     * 실제주문자모바일전화번호(real_ordr_mbl_telno)
     */
    private String realOrdrMblTelno;

    /**
     * 제휴사주문협력사코드(asp_ord_csp_cd)
     */
    private String aspOrdCspCd;

    /**
     * 제휴사주문완료연결URL(asp_ord_cmpt_conn_url)
     */
    private String aspOrdCmptConnUrl;

    /**
     * 취소수수료주문여부(cncl_cms_ord_yn) not null
     */
    private String cnclCmsOrdYn;

    /**
     * 원본주문번호(orgl_ord_no)
     */
    private String orglOrdNo;

    /**
     * 숙박주문취소수수료금액(ldg_ord_cncl_cms_amt) not null
     */
    private BigDecimal ldgOrdCnclCmsAmt;

    /**
     * 개인통관고유부호(pcc)
     */
    private String pcc;

    /**
     * 개인통관고유부호재사용여부(pcc_ruse_yn) not null
     */
    private String pccRuseYn;

    /**
     * 제3자정보제공동의일자(apitp_dt)
     */
    private String apitpDt;

    /**
     * 전자금융거래동의일자(aeftc_dt)
     */
    private String aeftcDt;

    /**
     * 노출여부(exps_yn) not null
     */
    private String expsYn;

    /**
     * 노출여부변경일시(exps_yn_chg_dtm)
     */
    private String expsYnChgDtm;

    /**
     * 노출여부변경관리자ID(exps_yn_chg_mgr_id)
     */
    private String expsYnChgMgrId;

    /**
     * 포인트제도마감일자(pnt_rgm_clsg_dt)
     */
    private String pntRgmClsgDt;

    /**
     * 표준메뉴코드(std_menu_cd)
     */
    private String stdMenuCd;

    /**
     * 주문상품요약명(ord_gds_smry_nm)
     */
    private String ordGdsSmryNm;

    /**
     * 주문고유난수번호(ord_innt_rdno_no)
     */
    private String ordInntRdnoNo;

    /**
     * 이관주문여부(trsf_ord_yn) not null
     */
    private String trsfOrdYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
