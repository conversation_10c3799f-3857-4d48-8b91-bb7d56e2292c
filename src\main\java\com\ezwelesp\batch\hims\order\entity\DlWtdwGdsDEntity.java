package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 회수상품상세(ez_or.dl_wtdw_gds_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class DlWtdwGdsDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송번호(dlv_no) not null
     */
    private String dlvNo;

    /**
     * 회수상품순번(wtdw_gds_seq) not null
     */
    private Long wtdwGdsSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 회수상품수량(wtdw_gds_qty) not null
     */
    private Integer wtdwGdsQty;

    /**
     * 회수상품상태코드(wtdw_gds_st_cd) not null
     */
    private String wtdwGdsStCd;

    /**
     * 회수취소일시(wtdw_cncl_dtm)
     */
    private String wtdwCnclDtm;

    /**
     * 회수유예일시(wtdw_pstp_dtm)
     */
    private String wtdwPstpDtm;

    /**
     * 회수거부일시(wtdw_rfs_dtm)
     */
    private String wtdwRfsDtm;

    /**
     * 회수완료일시(wtdw_cmpt_dtm)
     */
    private String wtdwCmptDtm;

    /**
     * LG포털API배송번호(lgp_api_dlv_no)
     */
    private String lgpApiDlvNo;

    /**
     * LG포털API배송상세번호(lgp_api_dlv_dtl_no)
     */
    private String lgpApiDlvDtlNo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
