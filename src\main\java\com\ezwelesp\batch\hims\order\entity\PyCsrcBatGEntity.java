package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 현금영수증배치로그(ez_or.py_csrc_bat_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyCsrcBatGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 현금영수증배치로그순번(csrc_bat_log_seq) not null
     */
    private Long csrcBatLogSeq;

    /**
     * 배치시작일시(bat_strt_dtm) not null
     */
    private String batStrtDtm;

    /**
     * 배치처리내용(bat_prcs_cntn) not null
     */
    private String batPrcsCntn;

    /**
     * 배치처리완료여부(bat_prcs_cmpt_yn) not null
     */
    private String batPrcsCmptYn;

    /**
     * 배치처리결과메시지내용(bat_prcs_rslt_msg_cntn) not null
     */
    private String batPrcsRsltMsgCntn;

    /**
     * 현금영수증발행처리배치코드(csrc_pblc_prcs_bat_cd) not null
     */
    private String csrcPblcPrcsBatCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
