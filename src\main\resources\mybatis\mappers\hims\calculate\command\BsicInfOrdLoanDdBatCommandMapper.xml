<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.calculate.mapper.command.BsicInfOrdLoanDdBatCommandMapper">
    <update id="callBsicInfOrdLoanDdBatDataProc">
        /* 데이터 이관 처리 프로시저 호출 */
        call ez_ca.sp_bsicinfordloan_dd_bat_data(#{iMoveYn})
    </update>
    
    <update id="callBsicInfOrdLoanDdBatEtcProc">
        /* 고객사 사용내역, 소속분리, 카드정보, 특별포인트정보 이관 프로시저 호출 */
        call ez_ca.sp_bsicinfordloan_dd_bat_etc()
    </update>
    
    <update id="callBsicInfOrdLoanDdBatProcOnlnProc">
        /* 온라인 정산 데이터 이관 프로시저 호출 */
        call ez_ca.sp_bsicinfordloan_dd_bat_onln()
    </update>
    
    <update id="callBsicInfOrdLoanDdBatProcOflnProc">
        /* 오프라인 카드 정산 데이터 이관 프로시저 호출 */
        call ez_ca.sp_bsicinfordloan_dd_bat_ofln()
    </update>
    
    <update id="callBsicInfOrdLoanDdBatPamProc">
        /* 항공마일리지 데이터 처리 프로시저 호출 */
        call ez_ca.sp_bsicinfordloan_dd_bat_pam()
    </update>
    
    <update id="callBsicInfOrdLoanDdBatPointProc">
        /* 온라인 정산 데이터 처리(포인트직결제) 프로시저 호출 */
        call ez_ca.sp_bsicinfordloan_dd_bat_point()
    </update>
    
    <update id="callBsicInfOrdLoanDdBatSumProc">
        /* 요약관리 / 고객사별 집계 데이터 생성 프로시저 호출 */
        call ez_ca.sp_bsicinfordloan_dd_bat_sum()
    </update>
    
    <update id="callBsicInfOrdLoanDdBatLoanProc">
        /* 채권 데이터 처리 프로시저 호출 */
        call ez_ca.sp_bsicinfordloan_dd_bat_loan()
    </update>
</mapper>