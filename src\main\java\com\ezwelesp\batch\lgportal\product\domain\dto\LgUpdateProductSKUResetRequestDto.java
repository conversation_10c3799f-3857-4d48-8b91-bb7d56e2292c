package com.ezwelesp.batch.lgportal.product.domain.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgUpdateProductSKUResetRequestDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgUpdateProductSKUResetRequestDto {

    private String productMappingCode;  // 이지웰 상품번호
    private Integer productNo;          // lg 상품번호

    private String sKUTypeCode; // SKU 유형 코드 (옵션 유형 구분)
    // 상품등록시 sKUAttrs 이나 해당 API 에서는 sKuAttrs로 되어있음
    private String sKuAttrs; // SKU 속성
}
