package com.ezwelesp.batch.lgportal.product.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

import java.io.File;

@Alias("LgProductQnaAnswerRequestDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgProductQnaAnswerRequestDto {

    private Integer productInquiryNo;   // 상품 문의 번호
    private Integer productAnswerNo;    // 상품 문의 답변 번호
    private String answerTitle;         // 답변 제목
    private String answerContents;      // 답변 내용
    private File attachFileNo;          // 첨부 파일 (File 타입)

}


