package com.ezwelesp.batch.lgportal.order.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
  * 교환/반품 대상 리스트 요청 시 사용 Dto
  *
  * <AUTHOR>
  * @since 2025.03.05
  * @see ClaimListReqDto
  */
@SuperBuilder(toBuilder = true)
@Getter
public class ClaimListReqDto {

    private String apiKey;

    private String exceptStatusCode;                    // 교환/반품/취소 상태코드(신청)
    private String exceptStatusUpdateDate_above;        // 교환/반품/취소 상태변경 시작일
    private String exceptStatusUpdateDate_below;        // 교환/반품/취소 상태변경 종료일

    @Builder.Default
    private int pageSize = 99999;                       // 페이지사이즈

}
