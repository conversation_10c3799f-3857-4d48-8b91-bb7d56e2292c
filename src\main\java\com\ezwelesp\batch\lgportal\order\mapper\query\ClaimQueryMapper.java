package com.ezwelesp.batch.lgportal.order.mapper.query;

import com.ezwelesp.batch.hims.order.entity.external.CoCspObndRtpLocBEntity;
import com.ezwelesp.batch.lgportal.order.dto.ClaimApplyGoodsInfoDto;
import com.ezwelesp.batch.lgportal.order.dto.ClaimWtdrRfsReqDto;
import com.ezwelesp.batch.lgportal.order.dto.ExchRtpGoodsInfoDto;
import com.ezwelesp.batch.lgportal.order.dto.ExchRtpOrderInfoDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The interface user query mapper.
 */
public interface ClaimQueryMapper {
    int selectWtdwGdsCount(String clientOrderNum);

    CoCspObndRtpLocBEntity selectCspObndRtpLocInfo(@Param("cspCd") String cspCd, @Param("cspObndLocNo") String cspObndLocNo);

    List<ClaimWtdrRfsReqDto> selectExchRtpWtdrRfsList();

    ExchRtpOrderInfoDto selectExchRtpOrderInfo(String clientOrderNum);

    ExchRtpGoodsInfoDto selectExchRtpOrderGoodsInfo(@Param("clientOrderNum") String clientOrderNum, @Param("dlvCoCd") String dlvCoCd, @Param("orderItemNo") String orderItemNo);

    int selectCancelOrderGoodsCount(@Param("ordNo") String ordNo, @Param("lgpApiDlvNo") String lgpApiDlvNo, @Param("lgpApiDlvDtlNo") String lgpApiDlvDtlNo);

    ClaimApplyGoodsInfoDto selectCancelOrderGoodsInfo(@Param("ordNo") String ordNo, @Param("lgpApiDlvNo") String lgpApiDlvNo, @Param("lgpApiDlvDtlNo") String lgpApiDlvDtlNo);
}
