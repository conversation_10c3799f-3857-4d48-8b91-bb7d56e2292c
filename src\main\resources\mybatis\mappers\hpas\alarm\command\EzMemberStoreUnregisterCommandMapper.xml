<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hpas.alarm.mapper.command.EzMemberStoreCommandMapper">
	<insert id="insertMonitorRes">
	INSERT INTO ez_cm.sc_mnt_monitor_res
			(
				res_seq
				,job_id
				,job_start_dt
				,job_end_dt
				,alram_yn
				,alram_cnt_max
				,alram_user
				,critical_value
				,critical_value_unit
				,res_value
				<if test="resStatus != null and resStatus != ''">
            	,delivery_item
				</if>
				,res_etc
				,res_etc2
				,res_ip
				,reg_dt
				,modi_dt
			)VALUES(
				(SELECT MAX(res_seq)+1 FROM ez_cm.sc_mnt_monitor_res)
				,#{jobId}
				,#{jobStartDt}
			  	,TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
				,#{alramYn}
				,#{alramCntMax}::integer
				,#{alramUser}
				,#{criticalValue}::integer
				,#{criticalValueUnit}
				,#{resValue}
				<if test="resStatus != null and resStatus != ''">
				,'T'
				</if>
				,#{resEtc}
				,#{resEtc2}
				,#{resIp}
				,TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
				,TO_CHAR(NOW(), 'YYYYMMDDHH24MISS')
			)
	
	</insert>
</mapper>
