package com.ezwelesp.batch.hims.order.handler;


import com.ezwelesp.batch.hims.order.utils.OrderJsonUtils;
import com.ezwelesp.framework.utils.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * snake case json object handler
 *
 * @param <T>
 */
public class ObjectSnakeTypeHandler<T extends Object> extends BaseTypeHandler<T> {

    private Class<T> clazz;

    public ObjectSnakeTypeHandler(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {

    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getObject(rs.getString(columnName));
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getObject(rs.getString(columnIndex));
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getObject(cs.getString(columnIndex));
    }

    public T getObject(String arr) {
        if (StringUtils.isNotEmpty(arr)) {
            return OrderJsonUtils.deserializeSnake(arr, clazz);
        }
        else {
            return null;
        }
    }
}
