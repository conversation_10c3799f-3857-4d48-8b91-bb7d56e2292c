package com.ezwelesp.batch.lgportal.order.mapper.query;

import com.ezwelesp.batch.lgportal.entity.ApiBatchChgReturnEntity;
import com.ezwelesp.batch.lgportal.entity.ApiBatchDlvrEntity;
import com.ezwelesp.batch.lgportal.entity.ApiBatchSendEntity;
import com.ezwelesp.batch.lgportal.order.dto.CspDlvrLocDto;

import java.util.List;


/**
 * The interface user query mapper.
 */
public interface ApiBatchQueryMapper {
    List<ApiBatchChgReturnEntity> selectExchRtpCompleteList();

    List<CspDlvrLocDto> selectRegCspDlvrList();

    List<ApiBatchDlvrEntity> selectDeliveryCompleteList();

    List<ApiBatchDlvrEntity> selectRegInvoiceNoList();

    String selectApiBatchGoods(String gdsCd);

    ApiBatchSendEntity selectOrderCancelGoodsOstkList();

}
