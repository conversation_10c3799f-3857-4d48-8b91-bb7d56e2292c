package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 배송지연기본(ez_or.cl_dlv_dlay_b)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClDlvDlayBEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송번호(dlv_no) not null
     */
    private String dlvNo;

    /**
     * 배송상품순번(dlv_gds_seq) not null
     */
    private Long dlvGdsSeq;

    /**
     * 배송지연사유코드(dlv_dlay_rsn_cd)
     */
    private String dlvDlayRsnCd;

    /**
     * 배송지연소명코드(dlv_dlay_expl_cd)
     */
    private String dlvDlayExplCd;

    /**
     * 배송지연소명상태코드(dlv_dlay_expl_st_cd)
     */
    private String dlvDlayExplStCd;

    /**
     * 배송지연소명대상종류코드(dlv_dlay_expl_obj_knd_cd)
     */
    private String dlvDlayExplObjKndCd;

    /**
     * 배송지연소명처리상태코드(dlv_dlay_expl_prcs_st_cd)
     */
    private String dlvDlayExplPrcsStCd;

    /**
     * 배송지연소명처리관리자ID(dlv_dlay_expl_prcs_mgr_id)
     */
    private String dlvDlayExplPrcsMgrId;

    /**
     * 배송지연소명내용(dlv_dlay_expl_cntn)
     */
    private String dlvDlayExplCntn;

    /**
     * 배송지연소명처리사유내용(dlv_dlay_expl_prcs_rsn_cntn)
     */
    private String dlvDlayExplPrcsRsnCntn;

    /**
     * 배송지연소명첨부파일경로1(dlv_dlay_expl_atch_file_path1)
     */
    private String dlvDlayExplAtchFilePath1;

    /**
     * 배송지연소명첨부파일명1(dlv_dlay_expl_atch_file_nm1)
     */
    private String dlvDlayExplAtchFileNm1;

    /**
     * 배송지연소명첨부파일경로2(dlv_dlay_expl_atch_file_path2)
     */
    private String dlvDlayExplAtchFilePath2;

    /**
     * 배송지연소명첨부파일명2(dlv_dlay_expl_atch_file_nm2)
     */
    private String dlvDlayExplAtchFileNm2;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
