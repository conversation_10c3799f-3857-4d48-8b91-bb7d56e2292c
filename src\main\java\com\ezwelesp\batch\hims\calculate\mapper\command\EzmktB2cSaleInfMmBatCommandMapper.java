package com.ezwelesp.batch.hims.calculate.mapper.command;

import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface EzmktB2cSaleInfMmBatCommandMapper {
    
    /**
     * 이지웰마켓B2C 상세 기존 데이터 삭제
     * @param parameter
     */
    void deleteCaMktB2cStlD(Map<?, ?> parameter);
    
    /**
     * 이지웰마켓B2C 기본 기존 데이터 삭제
     * @param parameter
     */
    void deleteCaMktB2cStlB(Map<?, ?> parameter);

    /**
     * 마켓B2C정산상세 입력
     * @param parameter
     */
    void insertCaMktB2cStlD(Map<?, ?> parameter);
    
    /**
     * 마켓B2C정산상세 갱신
     * @param parameter
     */
    void updateCaMktB2cStlD(Map<?, ?> parameter);
    
    /**
     * 마켓B2C정산기본 입력
     * @param parameter
     */
    void insertCaMktB2cStlB(Map<?, ?> parameter);
}