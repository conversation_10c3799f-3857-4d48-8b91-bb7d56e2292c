package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 선물하기주문기본이력(ez_or.or_gvgft_ord_b_h)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrGvgftOrdBHEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 선물하기주문번호(gvgft_ord_no) not null
     */
    private Long gvgftOrdNo;

    /**
     * 선물하기주문이력순번(gvgft_ord_his_seq) not null
     */
    private Long gvgftOrdHisSeq;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 선물하기주문종류코드(gvgft_ord_knd_cd)
     */
    private String gvgftOrdKndCd;

    /**
     * 선물하기주문상태코드(gvgft_ord_st_cd)
     */
    private String gvgftOrdStCd;

    /**
     * 선물하기주문취소주체코드(gvgft_ord_cncl_magn_cd)
     */
    private String gvgftOrdCnclMagnCd;

    /**
     * 수신자명(rcvr_nm)
     */
    private String rcvrNm;

    /**
     * 주문자명(ordr_nm)
     */
    private String ordrNm;

    /**
     * 수신자모바일전화번호(rcvr_mbl_telno)
     */
    private String rcvrMblTelno;

    /**
     * 선물메시지내용(prsn_msg_cntn)
     */
    private String prsnMsgCntn;

    /**
     * 이미지경로1(img_path1)
     */
    private String imgPath1;

    /**
     * 이미지경로2(img_path2)
     */
    private String imgPath2;

    /**
     * 제3자정보제공동의여부(apitp_yn) not null
     */
    private String apitpYn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
