package com.ezwelesp.batch.hims.order.giftorder.mapper.command;

import com.ezwelesp.batch.hims.order.entity.OrGvgftOrdBEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.mapper
 * @since 2025.05.15
 */
@Mapper
public interface GiftOrderCommandMapper {

    void updateGvgftOrdB(OrGvgftOrdBEntity orGvgftOrdBEntity);

    void insertGvgftOrdBHistory(OrGvgftOrdBEntity orGvgftOrdBEntity);
}
