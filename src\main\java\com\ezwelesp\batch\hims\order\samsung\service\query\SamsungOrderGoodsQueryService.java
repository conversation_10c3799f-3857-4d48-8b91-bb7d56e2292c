package com.ezwelesp.batch.hims.order.samsung.service.query;

public interface SamsungOrderGoodsQueryService {

    /**
     * 주문기본 테이블에서 특수문자가 포함된 주문상품요약명 건수 조회
     */
    int getOrdGdsSmryNmWithSpecialCharsCount();

    /**
     * 주문상품상세 테이블에서 특수문자가 포함된 상품명 건수 조회
     */
    int getGdsNmWithSpecialCharsCount();

    /**
     * 처리 대상 주문 건수 조회 (최근 2일)
     */
    int getTargetOrdersCount();

    /**
     * 특정 주문번호의 주문상품요약명 조회
     */
    String getOrdGdsSmryNmByOrdNo(String ordNo);

    /**
     * 특정 주문번호의 상품명 조회
     */
    String getGdsNmByOrdNo(String ordNo);

    /**
     * 처리 전 검증 - 처리할 데이터가 있는지 확인
     */
    boolean hasDataToProcess();
}
