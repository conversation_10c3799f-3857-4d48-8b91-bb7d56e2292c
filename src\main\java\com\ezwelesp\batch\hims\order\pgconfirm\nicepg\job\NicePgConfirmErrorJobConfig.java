package com.ezwelesp.batch.hims.order.pgconfirm.nicepg.job;

import com.ezwelesp.batch.hims.order.pgconfirm.nicepg.tasklet.NicePgConfirmErrorTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class NicePgConfirmErrorJobConfig {
    private final CommonJobListener commonJobListener;
    private final NicePgConfirmErrorTasklet nicePgConfirmErrorTasklet;


    @Bean("BA_HIOR00004")
    public Job nicePgConfirmErrorJob(JobRepository jobRepository, @Qualifier("BA_HIOR00004_STEP1") Step inicisPgCompareStep1) {
        return new JobBuilder("BA_HIOR00004", jobRepository)
                .listener(commonJobListener)
                .start(inicisPgCompareStep1)
                .build();
    }

    @Bean("BA_HIOR00004_STEP1")
    public Step nicePgConfirmErrorStep1(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00004_STEP1", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(nicePgConfirmErrorTasklet, transactionManager)
                .build();
    }


}
