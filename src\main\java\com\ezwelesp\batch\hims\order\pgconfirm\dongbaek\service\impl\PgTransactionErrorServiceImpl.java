package com.ezwelesp.batch.hims.order.pgconfirm.dongbaek.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.stereotype.Service;

import com.ezwelesp.batch.hims.order.pgconfirm.dongbaek.mapper.query.PgTransactionErrorQueryMapper;
import com.ezwelesp.batch.hims.order.pgconfirm.dongbaek.service.PgTransactionErrorService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class PgTransactionErrorServiceImpl implements PgTransactionErrorService {
	private final PgTransactionErrorQueryMapper pgTransactionErrorQueryMapper;
	
	/**
	 * 동백몰 거래대사(PgTransaction) 오류 알림
	 *
	 * 하루 전날 데이터 확인
	 * ex) D-1의 00:00:00 ~ 23:59:59 데이터 확인
	 * case1) 복지관에는 결재정보가 있지만 PG에는 없음
	 * case2) 복지관에도 PG에도 결재정보가 있지만 결제금액이 다름
	 * case3) PG에는 결재정보가 있지만 복지관에는 없음
	 */
	@Override
	public void callBatch() {
		
		List<HashMap> errList = new ArrayList<>();
		
		try {
			
			// 동백몰 대사 오류 목록 조회(복지관거래 기준)
			List<HashMap> cuserDiffList = this.getPgTransactionCuserDiffList();
			
			// 동백몰 대사 오류 목록 조회(동백몰거래 기준)
			List<HashMap> pgDiffList = this.getPgTransactionDongbaekDiffList();
			
		}catch(Exception e) {
			log.error("처리중 에러가 발생했습니다.",e);
			//TO-DO 알림발송 진행
		}
		
		
	}

	/**
	 * 동백몰 대사 오류 목록 조회(동백몰거래 기준)
	 * @return
	 */
	private List<HashMap> getPgTransactionDongbaekDiffList() {
		return this.pgTransactionErrorQueryMapper.selectPgTransactionDongbaekDiffList();
	}

	/**
	 * 동백몰 대사 오류 목록 조회(복지관거래 기준)
	 * @return
	 */
	private List<HashMap> getPgTransactionCuserDiffList() {
		return this.pgTransactionErrorQueryMapper.selectPgTransactionCuserDiffList();
	}
}
