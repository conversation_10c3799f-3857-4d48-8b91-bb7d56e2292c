package com.ezwelesp.batch.hpas.alarm.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class MonitorJobsDto {
    private String jobId;
    private String jobNm;
    private String jobDesc;
    private String jobCycle;
    private String sysCd;
    private String typeCd;
    private String alramYn;
    private String alramCntMaxYn;
    private String alramCntMax;
    private String criticalValueYn;
    private String criticalValue;
    private String criticalValueUnit;
    private String regId;
    private String modiId;
    private String regDt;
    private String modiDt;
}
