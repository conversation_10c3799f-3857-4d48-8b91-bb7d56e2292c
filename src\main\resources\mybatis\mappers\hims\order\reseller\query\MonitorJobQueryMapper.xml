<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.reseller.mapper.query.MonitorJobQueryMapper">

    <select id="selectScMntMonitorJobsByJobId" resultType="com.ezwelesp.batch.hims.order.entity.external.ScMntMonitorJobsEntity">
        SELECT job_id
             , job_nm
             , job_desc
             , job_cycle
             , sys_cd
             , type_cd
             , alram_yn
             , alram_cnt_max_yn
             , alram_cnt_max
             , critical_value_yn
             , critical_value
             , critical_value_unit
             , reg_id
             , modi_id
             , reg_dt
             , modi_dt
          FROM ez_cm.sc_mnt_monitor_jobs
         WHERE job_id = #{jobId}
    </select>
</mapper>
