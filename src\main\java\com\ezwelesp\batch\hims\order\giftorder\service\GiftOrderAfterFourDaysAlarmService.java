package com.ezwelesp.batch.hims.order.giftorder.service;

import com.ezwelesp.batch.hims.order.giftorder.domain.GiftOrderDto;

import java.util.List;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.giftorder.service
 * @since 2025.05.15
 */
public interface GiftOrderAfterFourDaysAlarmService {

    List<GiftOrderDto> getGiftOrderAfterFourDaysList();

    void sendAlarmAndSaveStatus(GiftOrderDto dto);
}
