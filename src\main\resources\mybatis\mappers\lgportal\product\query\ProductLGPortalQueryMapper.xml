<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.lgportal.product.mapper.query.ProductLGPortalQueryMapper">


    <!-- 상품추가 타겟 조회  -->
    <!-- selectAddProductTarget -->
    <select id="selectLgAddProductTarget"
            resultType="com.ezwelesp.batch.lgportal.product.domain.vo.LgAddProductTargetVo">
        <![CDATA[
        select g.gds_cd
             , g.gds_nm
             , acm.lg_ctg_cd
             , coalesce(g.gds_mdl_nm, '기타')          as gds_mdl_nm
             , g.mnfc_co_nm                          as mnfc_co_nm
             , g.orgn_nm                             as orgn_nm
             , coalesce(g.gds_simp_desc, g.gds_nm)   as gds_simp_desc
             , case
                   when g.taxn_knd_cd = '0' then 'N'
                   else 'Y'
            end                                      as taxn_knd_cd
             , g.nrml_sell_prc
             , g.real_sell_prc
             , g.t1_buy_poss_max_qty_use_yn
             , coalesce(g.t1_buy_poss_max_qty, 0)    as t1_buy_poss_max_qty
             , g.psn1_buy_poss_max_qty_use_yn
             , coalesce(g.psn1_buy_poss_max_qty, 0)  as psn1_buy_poss_max_qty
             , gi.img_path
             , g.gds_opt_use_yn
             , g.stck_qty
             , g.sell_qty
             , g.csp_cd
             , coalesce(g.csp_obnd_loc_no, g.csp_cd) as csp_obnd_loc_no
             , cd.lg_dlvr_no                         as shippingLoc
             , cdrt.lg_dlvr_no                       as returnLoc
             , cdex.lg_dlvr_no                       as exchangeLoc
        from ez_pd.pd_gds_c g
                 join ez_pd.pd_gds_img_d gi
                      on g.gds_cd = gi.gds_cd and gi.gds_img_seq = 1
                 join ez_co.co_csp_b ec
                      on g.csp_cd = ec.csp_cd
                          and ec.lgp_api_use_yn = 'Y'
                 join ez_dp.dp_std_ctgr_b ecg
                      on g.mng_std_ctgr_cd = ecg.std_ctgr_cd
                          and ecg.use_yn = 'Y'
                 join ez_if.api_ctg_mapping acm
                      on g.mng_std_ctgr_cd = acm.ez_ctg_cd::text
            /* LG쪽에 배송위치 정보 중 배송 */
                 join (select csp_cd, csp_dlvr_id, dlvr_type_cd, dlvr_cost_type_cd, dlvr_price, lg_dlvr_no
                       from ez_if.api_batch_csp_dlvr
                       where dlvr_type_cd = '01'
                         and result_cd = '200') cd
        on g.csp_cd = cd.csp_cd::text
            and coalesce (g.csp_obnd_loc_no, g.csp_cd) = cd.csp_dlvr_id
            and coalesce (g.dlv_exp, 0) = cd.dlvr_price
            /* LG쪽에 배송위치 정보 중 반품 */
            join (select csp_cd, csp_dlvr_id, dlvr_type_cd, dlvr_cost_type_cd, dlvr_price, lg_dlvr_no
            from ez_if.api_batch_csp_dlvr
            where dlvr_type_cd = '02'
            and result_cd = '200') cdrt
            on g.csp_cd = cdrt.csp_cd::text
            and coalesce (g.csp_obnd_loc_no, g.csp_cd) = cdrt.csp_dlvr_id
            and cdrt.dlvr_price = (coalesce (g.dlv_exp, 0) * 2)
            /* LG쪽에 배송위치 정보 중 교환 */
            join (select csp_cd, csp_dlvr_id, dlvr_type_cd, dlvr_cost_type_cd, dlvr_price, lg_dlvr_no
            from ez_if.api_batch_csp_dlvr
            where dlvr_type_cd = '03'
            and result_cd = '200') cdex
            on g.csp_cd = cdex.csp_cd::text
            and coalesce (g.csp_obnd_loc_no, g.csp_cd) = cdex.csp_dlvr_id
            and cdex.dlvr_price = (coalesce (g.dlv_exp, 0) * 2)
            left join (select count (1) opt_cnt
            , pgob.gds_cd
            from ez_pd.pd_gds_opt_b pgob
            where pgob.use_yn = 'Y'
            and pgob.mndr_choc_yn = 'Y'
            group by pgob.gds_cd) pgo
            on pgo.gds_cd = g.gds_cd
        where octet_length (g.gds_nm) <= 100
          and octet_length (g.mnfc_co_nm) <= 50
          and g.gds_sell_st_cd = '1002'
          and g.dlv_exp_pymt_mthd_cd in ('Y'
            , 'N'
            , 'B')/* 배송가격 적용 여부 */
          and g.taxn_knd_cd in ('Y'
            , 'N') /*  세금여부 yn만 있는데, taxn_knd_cd is '과세종류코드' 만 있음.*/
          and case
            when g.real_sell_prc = 0 then 0
            else (g.real_sell_prc - g.gds_pchs_prc) / g.real_sell_prc
        end
        >= 10 /* 마진률 없어서 계산하는 방식으로 변경 (실제 판매가격-매입가격) / 실제판매가격*/
          and case safe_stck_mng_yn
                  when 'Y' then coalesce(stck_qty, 0) - coalesce(safe_stck_qty, 0)
                  else stck_qty
        end
        > 0 /* 안전재고 생겨서 그걸로 비교해야되어 이런식으로 처리*/
          and g.gds_typ_cd = '10' /*유형상품만*/
          and g.gds_typ_dtl_cd in ('1001', '1020') /*( 일반택배, 검수배송)  기존 배송 형태에서 유형상품의 상품유형상세코드로 처리*/
          and coalesce(g.jeju_add_dlv_exp, 0) = 0 /*제주지역 배송비 0인것만*/
          and coalesce(g.ismt_add_dlv_exp, 0) = 0 /*산간지역 배송비 0인것만*/
          and coalesce(pgo.opt_cnt, 0) <= 5
            /*
            api 배치 상품 테이블
            api 배치 상품 테이블에 상품번호는 존재하면서 target_goods_cd가 널이 아닌게 존재하지 않으면
            이미 보냈는데 타겟 코드가 있는건이 존재하면 그러니까 한번 보냈고 타겟상품번호까지 받은건 다시안보냄
            해당 테이블을 지우거나 하는 정보가 있는지 확인 필요
            */
          and not exists (select 'x'
                          from ez_if.api_batch_goods abg
                          where g.gds_cd = abg.goods_cd::text
                            and abg.target_div = 'LGCNS'
                            and abg.target_goods_cd is not null)
        ]]>
    </select>

    <!-- 상품마스터 정보로 옵션 조회  -->
    <!-- selectProductOptionByGoodsIds -->
    <select id="selectLgProductOptionByGdsCds"
            resultType="com.ezwelesp.batch.lgportal.product.domain.vo.LgProductOptionCombVo">
        with temp_table as (select
        /* 상품조합별 정보 그룹핑될예정 조합 10개*/
        a.gds_cd
        , a.opt_gds_comb_seq
        , a.csp_opt_gds_no
        , a.opt_add_prc
        , a.stck_qty
        , a.safe_stck_mng_yn
        , a.safe_stck_qty
        , a.sttx_pblc_yn
        , a.use_yn
        , a.img_path
        , a.gds_dtl_img_path
        , a.sort_ordg
        /* 조합별 모든 값 조합 10개 옵션종류 5개 = 총 50행의 값*/
        , c.opt_gds_comb_cmp_nm
        /* 조합이름(색상,사이즈 등등) 각50개 행의 옵션종류가 무엇인지 매칭 */
        , b.opt_gds_seq
        , b.opt_gds_nm
        , ROW_NUMBER() OVER (PARTITION BY a.gds_cd,a.opt_gds_comb_seq ORDER BY b.opt_gds_seq) AS opt_rownum
        from ez_pd.pd_gds_opt_comb_b a
        join ez_pd.pd_gds_opt_b b
        on b.gds_cd = a.gds_cd
        and b.use_yn = 'Y'
        and b.mndr_choc_yn = 'Y'
        join ez_pd.pd_gds_opt_comb_cmp_d c
        on c.gds_cd = a.gds_cd
        and c.opt_gds_comb_seq = a.opt_gds_comb_seq
        and c.opt_gds_seq = b.opt_gds_seq
        where a.use_yn = 'Y'
        <choose>
            <when test="gdsCdList != null and gdsCdList.size > 0">
                and a.gds_cd in
                <foreach collection="gdsCdList" item="gdsCd" open="(" separator="," close=")">
                    #{gdsCd}
                </foreach>
            </when>
            <otherwise>
                and 1 = 0
            </otherwise>
        </choose>
        )
        , temp_table_comb AS (
        select tt.gds_cd
        , tt.opt_gds_comb_seq
        , tt.csp_opt_gds_no
        , tt.opt_add_prc
        , tt.stck_qty
        , tt.safe_stck_mng_yn
        , tt.safe_stck_qty
        , tt.sttx_pblc_yn
        , tt.use_yn
        , tt.img_path
        , tt.gds_dtl_img_path
        , tt.sort_ordg
        , max (case when tt.opt_rownum = 1 then opt_gds_nm end) as opt_nm1
        , max (case when tt.opt_rownum = 1 then opt_gds_comb_cmp_nm end) as opt_val1
        , max (case when tt.opt_rownum = 1 then opt_gds_seq || '-' || opt_gds_comb_seq end) as opt_gds_seq1
        , max (case when tt.opt_rownum = 2 then opt_gds_nm end) as opt_nm2
        , max (case when tt.opt_rownum = 2 then opt_gds_comb_cmp_nm end) as opt_val2
        , max (case when tt.opt_rownum = 2 then opt_gds_seq || '-' || opt_gds_comb_seq end) as opt_gds_seq2
        , max (case when tt.opt_rownum = 3 then opt_gds_nm end) as opt_nm3
        , max (case when tt.opt_rownum = 3 then opt_gds_comb_cmp_nm end) as opt_val3
        , max (case when tt.opt_rownum = 3 then opt_gds_seq || '-' || opt_gds_comb_seq end) as opt_gds_seq3
        , max (case when tt.opt_rownum = 4 then opt_gds_nm end) as opt_nm4
        , max (case when tt.opt_rownum = 4 then opt_gds_comb_cmp_nm end) as opt_val4
        , max (case when tt.opt_rownum = 4 then opt_gds_seq || '-' || opt_gds_comb_seq end) as opt_gds_seq4
        , max (case when tt.opt_rownum = 5 then opt_gds_nm end) as opt_nm5
        , max (case when tt.opt_rownum = 5 then opt_gds_comb_cmp_nm end) as opt_val5
        , max (case when tt.opt_rownum = 5 then opt_gds_seq || '-' || opt_gds_comb_seq end) as opt_gds_seq5
        from temp_table tt
        group by tt.gds_cd
        , tt.opt_gds_comb_seq
        , tt.csp_opt_gds_no
        , tt.opt_add_prc
        , tt.stck_qty
        , tt.safe_stck_mng_yn
        , tt.safe_stck_qty
        , tt.sttx_pblc_yn
        , tt.use_yn
        , tt.img_path
        , tt.gds_dtl_img_path
        , tt.sort_ordg
        order by tt.gds_cd, tt.opt_gds_comb_seq)
        SELECT gds_cd
        , opt_gds_comb_seq
        , csp_opt_gds_no
        , opt_add_prc
        , stck_qty
        , safe_stck_mng_yn
        , safe_stck_qty
        , sttx_pblc_yn
        , use_yn
        , img_path
        , gds_dtl_img_path
        , sort_ordg
        , concat_ws('^|^', opt_nm1, opt_nm2, opt_nm3, opt_nm4, opt_nm5) AS opt_nm
        , concat_ws('^|^', opt_val1, opt_val2, opt_val3, opt_val4, opt_val5) AS opt_val
        , concat_ws('_', opt_gds_seq1, opt_gds_seq2, opt_gds_seq3, opt_gds_seq4, opt_gds_seq5) AS opt_gds_seq
        FROM temp_table_comb
    </select>

    <!-- 상품마스터 정보로 고시정보 조회  -->
    <!-- selectProductAnnouncementByGoodsIds -->
    <select id="selectLgProductAnnouncementByGdsCd"
            resultType="com.ezwelesp.batch.lgportal.product.domain.vo.LgProductAnnouncementVo">
        select
        pgagr.gds_cd
        , coalesce(pgab.ttl, '') as ttl
        , pgagr.gds_anmt_cntn
        , pgab.exps_ordg
        from ez_pd.pd_gds_anmt_gds_r pgagr
        join ez_pd.pd_gds_anmt_b pgab
        on pgagr.gds_anmt_cls_cd = pgab.gds_anmt_cls_cd
        and pgagr.lyot_seq =pgab.lyot_seq
        <choose>
            <when test="gdsCdList != null and gdsCdList.size > 0">
                where pgagr.gds_cd in
                <foreach collection="gdsCdList" item="gdsCd" open="(" separator="," close=")">
                    #{gdsCd}
                </foreach>
            </when>
            <otherwise>
                where 1 = 0
            </otherwise>
        </choose>
        order by gds_cd,exps_ordg
    </select>


    <!-- 상품정보 수정 정보 조회 -->
    <!-- selectEditProductTarget -->
    <select id="selectLgEditProductTarget"
            resultType="com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo">
        select abs.api_seq
             , abs.provider_no
             , abs.certkey
             , g.gds_cd
             , abs.product_no
             , g.gds_nm
             , coalesce(g.gds_simp_desc, g.gds_nm)  as gds_simp_desc
             , coalesce(g.gds_mdl_nm, '기타')         as gds_mdl_nm
             , g.mnfc_co_nm
             , g.orgn_nm
             , g.taxn_knd_cd
             , g.t1_buy_poss_max_qty_use_yn
             , coalesce(g.t1_buy_poss_max_qty, 0)   as t1_buy_poss_max_qty
             , g.psn1_buy_poss_max_qty_use_yn
             , coalesce(g.psn1_buy_poss_max_qty, 0) as psn1_buy_poss_max_qty
             , case
                   when egi.img_path is null then ''
                   else egi.img_path end            as img_path /*'http://org-img.ezwelfare.net/welfare_shop'||img_path*/
             , abs.send_key
             , abs.send_yn
             , abs.value_arr
             , abs.board_no
        from ez_if.api_batch_send abs
                 join ez_if.api_batch_goods abg on abg.target_goods_cd = abs.product_no::text
                 join ez_pd.pd_gds_c g
        on g.gds_cd = abg.goods_cd::text
            left join (select gi.gds_cd, max (gi.img_path) img_path
            from ez_pd.pd_gds_img_d gi
            where gi.gds_img_seq = 1
            group by gi.gds_cd) egi on g.gds_cd = egi.gds_cd
        where abs.send_yn = 'N'
          and abs.send_key in (1001
            , 1002
            , 1003
            , 1006
            , 1007
            , 1015)
          and abs.product_no is not null
          and abg.target_div = 'LGCNS'
          and abs.reg_dt <![CDATA[ >= ]]> to_char(now():: DATE - INTERVAL '1 day'
            , 'yyyymmddhh24miss')
    </select>

    <select id="selectLgEditProductDeliveryTargetByGdsCd">
        select abcd.csp_cd,
               abcd.csp_dlvr_id,
               abcd.dlvr_type_cd,
               abcd.dlvr_cost_type_cd,
               abcd.lg_dlvr_no
        from ez_if.api_batch_csp_dlvr abcd
                 join ez_pd.pd_gds_c g
                      on abcd.csp_cd = g.csp_cd and abcd.csp_dlvr_id = coalesce(g.csp_obnd_loc_no, g.csp_cd)
        where abcd.send_yn = 'Y'
          and abcd.result_cd = '200'
          and g.gds_cd = #{gdsCd}
          and abcd.dlvr_price = case
                                    when dlvr_type_cd = '01' then g.dlv_exp
                                    when dlvr_type_cd = '02' then coalesce(g.rtp_dlv_exp, 0) * 2
                                    when dlvr_type_cd = '03' then coalesce(g.exch_dlv_exp, 0) * 2 end
          and abcd.dlvr_cost_type_cd = case
                                           when 0 <![CDATA[ < ]]> (case
                                                         when dlvr_type_cd = '01' then g.dlv_exp
                                                         when dlvr_type_cd = '02' then coalesce(g.rtp_dlv_exp, 0)
                                                         when dlvr_type_cd = '03' then coalesce(g.exch_dlv_exp, 0)
                                               end
                                               ) then '02'
                                           else '01' end
    </select>


    <!-- 상품 가격 수정조회 -->
    <!-- selectLgEditProductPriceTarget -->
    <select id="selectLgEditProductPriceTarget"
            resultType="com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo">
        select abs.api_seq
             , abs.provider_no
             , abs.certkey
             , abs.product_no
             , abs.send_yn
             , abs.send_key
             , g.nrml_sell_prc
             , g.real_sell_prc
        from ez_if.api_batch_send abs
                 join ez_if.api_batch_goods abg on abg.target_goods_cd = abs.product_no::text
                 join ez_pd.pd_gds_c g
        on g.gds_cd = abg.goods_cd::text
        where abs.send_yn = 'N'
          and abs.send_key = 1005
          and abs.product_no is not null
          and abg.target_div = 'LGCNS'
          and abs.reg_dt <![CDATA[ >= ]]> to_char(now():: DATE - INTERVAL '7 day'
            , 'yyyymmddhh24miss')
    </select>


    <!-- 상품 품절 판매여부 타겟 조회 -->
    <!-- selectLgEditProductSoldoutTarget -->
    <select id="selectLgEditProductSoldOutTarget"
            resultType="com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo">
        select abs.api_seq
             , abs.provider_no
             , abs.certkey
             , abs.product_no
             , abs.send_yn
             , abs.send_key
        from ez_if.api_batch_send abs
                 join ez_if.api_batch_goods abg on abg.target_goods_cd = abs.product_no::text
        where abs.send_yn = 'N'
          and abs.send_key in (1012
            , 1013)
          and abg.target_goods_cd is not null
          and abg.target_div = 'LGCNS'
          and abs.reg_dt <![CDATA[ >= ]]> to_char(now():: DATE - INTERVAL '7 day'
            , 'yyyymmddhh24miss');
    </select>

    <!-- 상품 SKU 변경 타겟 조회 -->
    <!-- selectLgEditProductSKUTarget -->
    <select id="selectLgEditProductSKUTarget"
            resultType="com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo">
        select abs.api_seq
             , abs.product_no
             , abg.goods_cd
             , g.gds_opt_use_yn
             , coalesce(g.gds_opt_typ_cd, '1001') as gds_opt_typ_cd
             , coalesce(pgo.opt_cnt, 0)           as opt_cnt
        from ez_if.api_batch_send abs
                 join ez_if.api_batch_goods abg on abg.target_goods_cd = abs.product_no::text
                 join ez_pd.pd_gds_c g
        on g.gds_cd = abg.goods_cd::text
            /* 해당상품의 옵션개수가 몇개인지 확인하기 위해서 */
            left join (select count (1) opt_cnt,
            pgob.gds_cd
            from ez_pd.pd_gds_opt_b pgob
            where pgob.use_yn = 'Y'
            and pgob.mndr_choc_yn = 'Y'
            group by pgob.gds_cd) pgo
            on pgo.gds_cd = g.gds_cd
        where abs.send_yn = 'N'
          and abs.send_key = 1014
          and abg.target_goods_cd is not null
          and abg.target_div = 'LGCNS'
          and abs.reg_dt <![CDATA[ >= ]]> to_char(now():: DATE - INTERVAL '1 day'
            , 'yyyymmddhh24miss');
    </select>

    <!-- 상품 콘텐츠 조회-->
    <!-- selectLgProductContentByGdsCd -->
    <select id="selectLgProductContentByGdsCd">
        select gds_cd
             , gds_gd_div_cd
             , ttl
             , gdoc_cntn
             , use_yn
        from ez_pd.pd_gds_gd_d pggd
        where pggd.use_yn = 'Y'
          and pggd.gds_cd = #{gdsCd}

    </select>

    <select id="selectLgProductQnaCnt">
        select lgp_bbs_no
        ,count(lgp_bbs_no)
        from ez_cm.bb_gds_inq_b
        where lgp_bbs_no is not null
        and lgp_bbs_no in
        <foreach collection="inquiryNoList" item="inquiryNo" open="(" separator="," close=")">
            #{inquiryNo}
        </foreach>
        group by lgp_bbs_no
    </select>

    <select id="selectLgQnaProductInfo">
        select
        goods_cd,
        productNo,
        csp_cd
        from ez_if.api_batch_goods
        where target_goods_cd in
        <foreach collection="productNoList" item="productNo" open="(" separator="," close=")">
            #{productNo}
        </foreach>
    </select>

    <select id="productQnAAutoAnswerTarget">
        SELECT A.gds_inq_seq     -- 질문 일련번호
             , A.gds_cd          -- 상품번호
             , A.lgp_bbs_no      --API QA 시퀀스'
             , b.target_goods_cd -- lg 상품번호
        FROM ez_cm.bb_gds_inq_b A
                 join ez_if.api_batch_goods b on a.gds_cd = b.goods_cd::text
        WHERE A.user_key = '1000000000' --사용자 고유번호
          AND A.cmnt_yn = 'N'           -- qa 유형 질문인지 아닌지
          AND A.del_yn = 'Y'            -- 삭제유형
          AND A.answ_cmpt_yn = 'N'      -- 답변유형
          AND A.frst_reg_dtm BETWEEN to_char(CURRENT_DATE - 10
            , 'yyyymmddhh24miss')
          AND to_char(CURRENT_DATE - 3
            , 'yyyymmddhh24miss')
          AND NOT EXISTS (SELECT 'X'
            FROM ez_if.api_batch_send
            WHERE BOARD_NO = A.lgp_bbs_no:: NUMERIC
          AND SEND_KEY = '1015'         -- 답변등록이 안된건
            )
        ORDER BY frst_reg_dtm ASC
    </select>
</mapper>
