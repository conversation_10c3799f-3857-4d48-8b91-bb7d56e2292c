package com.ezwelesp.batch.hims.order.entity.external;

import lombok.Data;

import java.io.Serializable;

/**
 * 상품옵션기본 pd_gds_opt_b
 */
@Data
public class PdGdsOptBEntity implements Serializable {


    /**
     * 상품코드
     */
    private String gdsCd;

    /**
     * 옵션상품순번
     */
    private Long optGdsSeq;

    /**
     * 옵션상품명
     */
    private String optGdsNm;

    /**
     * 필수선택여부
     */
    private String mndrChocYn;

    /**
     * 사용여부
     */
    private String useYn;

    /**
     * 최초등록일시
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID
     */
    private String lastModPgmId;

    private static final long serialVersionUID = 1L;
}
