package com.ezwelesp.batch.hpas.util;

import java.text.SimpleDateFormat;
import java.util.Date;

public class HpasUtils {
    public static String dateFormat(String date) {
        return date.substring(0, 4) + "년 " + date.substring(4, 6) + "월 " + date.substring(6, 8) + "일";
    }

    public static String getCurrentDtm() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date());
    }

    public static String getCurrentDt() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(new Date());
    }
}
