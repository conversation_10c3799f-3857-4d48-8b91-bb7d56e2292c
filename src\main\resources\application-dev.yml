spring:
  datasource:
    hikari:
      primary:
        command:
          driver-class-name: org.postgresql.Driver
          jdbc-url: ******************************************************************************************************************************************
          username: ezwel_bat_app
          password: ezwel_bat_app#was34
          max-lifetime: 175
          connection-timeout: 30000
          maximum-pool-size: 10
        query:
          driver-class-name: org.postgresql.Driver
          jdbc-url: *********************************************************************************************************************************************
          username: ezwel_bat_sel
          password: ezwel_bat_sel#was34
          max-lifetime: 175
          connection-timeout: 30000
          maximum-pool-size: 10

# AWS Account Credentials (AWS 접근 키)
cloud:
  aws:
    credentials:
      use-default-aws-credentials-chain: true
    region:
      static: ap-northeast-2 #(S3 버킷 지역)
    stack:
      auto: false
    kms:
      key-arn: arn:aws:kms:ap-northeast-2:************:alias/enc-dev

# ezwel config
ezwel:
  apim:
    host: https://apim-dev.ezwel.com

# lg portal interface
lgportal:
  chunk-size: 1000
  server:
    host: https://comapistg.lglifecare.com
    port: 443
    timeout: 60
  api-key: apiTest419
  cert-key: u6;ShVow;kgR5=$

kyobo:
  chunk-size: 1000
  server:
    host: https://openapi.ndev.kyobobook.co.kr/corpm/api/v1/kko/ordr
    port: 443
    timeout: 10
  auth-session-key: eyJhbGciOiJSUzUxMiJ9.******************************************************************************************************************************************************.VCtmo70jXekByCLYN4i4zTUTf8VTLd7TfHQsmctkIZH2leKFjzePTHpu_dwlaca11igS_WxQZ8FnI5uw17hIvaI5XhEcoc2nnszeY7O5OHFhWDleQO0Mn11R01ENOZvDJAgRTMY6APnMTcsJ0PN3QgLhovY7E4Ffv4KtUkNmeMS26XHHtWJL5MJCZfXSPtiVGV5VYCgdpgZdgIKPZWnuI-1ZubE_b64G_GHe6mMmgCSEzgbhAFG06iHcBk8jEqxnIV-KY9Ze7X3wxfHpjNLPa4V_jMDbJoD9d35cwUvdMrLDCSNPGKUH4I17d7iqaRfkkccYfRM47QuCKiynnOGy-Q

hyundai-futurenet:
  host: *************
  port: 12351

goodsflow:
  server:
    zkm:
      host: https://test3.goodsflow.com
      timeout: 60
      api-key: 28bd4794-3528-4966-9dca-3758f61cfa17
    return:
      host: https://test.goodsflow.com
      timeout: 60
      api-key: 1ff603e3-953d-404e-abfd-d66eeffd615c

naverpay:
  api:
    history: https://dev-pub.apis.naver.com/%s/naverpay/payments/v2.2/list/history
    settlement: https://dev-pub.apis.naver.com/naverpaysettle-payment/naverpaysettle/v1/settlements/by-case
    timeout: 25000

pinpay:
  api:
    history: https://devpinpayapi.bluewalnut.co.kr/pay/transactions
    timeout: 5000
    mchId: PINpayTest
    limit: 100