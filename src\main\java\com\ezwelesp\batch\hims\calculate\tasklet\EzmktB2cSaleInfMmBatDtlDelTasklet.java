package com.ezwelesp.batch.hims.calculate.tasklet;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import com.ezwelesp.batch.hims.calculate.mapper.command.EzmktB2cSaleInfMmBatCommandMapper;
import com.ezwelesp.batch.hims.calculate.util.CalculateBatchUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class EzmktB2cSaleInfMmBatDtlDelTasklet implements Tasklet {
    private final EzmktB2cSaleInfMmBatCommandMapper ezmktB2cSaleInfMmBatCommandMapper;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        
        // 정산년월 가져오기
        String pStlYm = Optional.ofNullable(chunkContext.getStepContext().getJobParameters().get("pStlYm"))
                                .map(Object::toString)
                                .filter(s -> !s.isBlank())
                                .orElse(null);
        
        Map <String, Object> parameter = new HashMap<>();
        if (pStlYm == null) {
            parameter.put("stlYm", CalculateBatchUtil.getCurrentBasicBeforeMonth());
            parameter.put("stlYmd", CalculateBatchUtil.getCurrentBasicBeforeMonthLastDay());
        } else {
            parameter.put("stlYm", pStlYm);
            parameter.put("stlYmd", CalculateBatchUtil.getLastDayOfMonth(pStlYm));
        }
        
        ezmktB2cSaleInfMmBatCommandMapper.deleteCaMktB2cStlD(parameter);
        
        return RepeatStatus.FINISHED;
    }
}