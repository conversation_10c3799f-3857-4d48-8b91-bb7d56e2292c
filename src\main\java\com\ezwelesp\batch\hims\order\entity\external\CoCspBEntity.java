package com.ezwelesp.batch.hims.order.entity.external;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 협력사기본 co_csp_b
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CoCspBEntity implements Serializable {
    /**
     * 협력사코드
     */
    private String cspCd;

    /**
     * 통합거래처코드
     */
    private String untdCustCd;

    /**
     * 협력사명
     */
    private String cspNm;

    /**
     * 채널코드
     */
    private String chCd;

    /**
     * 협력사상태코드
     */
    private String cspStCd;

    /**
     * 협력사전자계약번호
     */
    private String cspEltrContNo;

    /**
     * 협력사계약상태코드
     */
    private String cspContStCd;

    /**
     * 계약시작일자
     */
    private String contStrtDt;

    /**
     * 계약종료일자
     */
    private String contEndDt;

    /**
     * 최저가정책승인계약번호
     */
    private String lwprPlcyApvContNo;

    /**
     * 상담문의게시판사용여부
     */
    private String cnslInqBbsUseYn;

    /**
     * 상담문의게시판미사용사유설명
     */
    private String cnslInqBbsUnusRsnDesc;

    /**
     * 제휴사연동유형코드
     */
    private String aspIntlTypCd;

    /**
     * 제휴사연동번호
     */
    private Integer aspIntlNo;

    /**
     * E쿠폰가맹점관리여부
     */
    private String ecpnFrcsMngYn;

    /**
     * B2B특판대상여부
     */
    private String b2bSsaleObjYn;

    /**
     * 전화번호변경문자메시지재발송가능여부
     */
    private String telnoChgTmsgRsndPossYn;

    /**
     * API사용여부
     */
    private String apiUseYn;

    /**
     * 구매자취소가능여부
     */
    private String buyrCnclPossYn;

    /**
     * 배송조회방법코드
     */
    private String dlvQryMthdCd;

    /**
     * 배송위치변경방법코드
     */
    private String dlvLocChgMthdCd;

    /**
     * 수량부분취소가능여부
     */
    private String qtyInpaPossYn;

    /**
     * 선물하기사용여부
     */
    private String gvgftUseYn;

    /**
     * 굿스플로연동여부
     */
    private String goodsfIntlYn;

    /**
     * 토요일배송가능여부
     */
    private String satDlvPossYn;

    /**
     * 해외배송여부
     */
    private String abrdDlvYn;

    /**
     * 티켓발송대상여부
     */
    private String tcktSndObjYn;

    /**
     * 교환반품배송비용부담방법코드
     */
    private String exchRtpDlvExpBudnMthdCd;

    /**
     * 정기구독사용여부
     */
    private String stoUseYn;

    /**
     * 다중배송가능여부
     */
    private String mtiDlvPossYn;

    /**
     * 주문취소주체코드
     */
    private String ordCnclMagnCd;

    /**
     * 교환반품규정내용
     */
    private String exchRtpReglCntn;

    /**
     * 개인정보처리계약유형코드
     */
    private String piPrcsContTypCd;

    /**
     * PG가맹점번호
     */
    private String pgFrcsNo;

    /**
     * 현금영수증PG연동결과코드
     */
    private String csrcPgIntlRsltCd;

    /**
     * 적립금부담주체코드
     */
    private String mlgBudnMagnCd;

    /**
     * 최초로그인약관동의화면노출여부
     */
    private String frstLginTncAgrScrnExpsYn;

    /**
     * 인지세발행가능여부
     */
    private String sttxPblcPossYn;

    /**
     * 협력사검색어목록값
     */
    private String cspScwdLval;

    /**
     * 협력사소개문구내용
     */
    private String cspIntroPhrsCntn;

    /**
     * 협력사섬네일이미지경로
     */
    private String cspTnailImgPath;

    /**
     * CS가능시작시분
     */
    private String csPossStrtHm;

    /**
     * 취소규정내용
     */
    private String cnclReglCntn;

    /**
     * CS가능종료시분
     */
    private String csPossEndHm;

    /**
     * CS점심시간시작시분
     */
    private String csLnchtmStrtHm;

    /**
     * CS점심시간종료시분
     */
    private String csLnchtmEndHm;

    /**
     * 수수료적용범위설정여부
     */
    private String cmsAplyScopStupYn;

    /**
     * 수수료최소수익률
     */
    private double cmsMinDfrtRt;

    /**
     * 수수료최대수익률
     */
    private double cmsMaxDfrtRt;

    /**
     * 정산템플릿순번
     */
    private Integer stlTmplSeq;

    /**
     * 담당부서코드
     */
    private String chrgDeptCd;

    /**
     * 협력사거래구분코드
     */
    private String cspTrdDivCd;

    /**
     * 세금계산서수신종류코드
     */
    private String txinRcvKndCd;

    /**
     * 수수료계산서현대이지웰발행대상여부
     */
    private String cmsBillEzwlPblcObjYn;

    /**
     * 무형상품여부
     */
    private String intgGdsYn;

    /**
     * 수수료정산정보사용여부
     */
    private String cmsStlInfUseYn;

    /**
     * 수작업정산여부
     */
    private String hwrkStlYn;

    /**
     * 과세종류코드
     */
    private String taxnKndCd;

    /**
     * 정산대상여부
     */
    private String stlObjYn;

    /**
     * 옵션금액수수료적용여부
     */
    private String optAmtCmsAplyYn;

    /**
     * E쿠폰정산수수료종류코드
     */
    private String ecpnStlCmsKndCd;

    /**
     * E쿠폰수수료기준코드
     */
    private String ecpnCmsBsicCd;

    /**
     * 협력사수수료종류코드
     */
    private String cspCmsKndCd;

    /**
     * 협력사정산기준일자구분코드
     */
    private String cspStlBsicDtDivCd;

    /**
     * 협력사정산주기코드
     */
    private String cspStlCyclCd;

    /**
     * 정산완료일수기등록여부
     */
    private String stlCmptDdHndwRegYn;

    /**
     * 협력사현금영수증발행주체코드
     */
    private String cspCsrcPblcMagnCd;

    /**
     * 현금영수증사업자등록번호대상코드
     */
    private String csrcBzrnObjCd;

    /**
     * 현금영수증발행대상여부
     */
    private String csrcPblcObjYn;

    /**
     * 카드결제무이자할부여부
     */
    private String crdPymtWintInstYn;

    /**
     * 카드결제무이자할부시작일자
     */
    private String crdPymtWintInstStrtDt;

    /**
     * 카드결제무이자할부종료일자
     */
    private String crdPymtWintInstEndDt;

    /**
     * 정산시작년월
     */
    private String stlStrtYm;

    /**
     * 수수료율
     */
    private double cmsRt;

    /**
     * 낙전수입주체코드
     */
    private String lstincMagnCd;

    /**
     * 검진기관지역코드
     */
    private String chkupIstnAreaCd;

    /**
     * 검진기관지역상세명
     */
    private String chkupIstnAreaDtlNm;

    /**
     * 검진가능시점코드
     */
    private String chkupPossPntmCd;

    /**
     * 검진기관이미지경로
     */
    private String chkupIstnImgPath;

    /**
     * 검진기관이미지경로2
     */
    private String chkupIstnImgPath2;

    /**
     * 검진기관이미지경로3
     */
    private String chkupIstnImgPath3;

    /**
     * 검진기관이미지경로4
     */
    private String chkupIstnImgPath4;

    /**
     * 입점승인일시
     */
    private String beinApvDtm;

    /**
     * 테스트협력사여부
     */
    private String testCspYn;

    /**
     * 외부테스트협력사여부
     */
    private String osdTestCspYn;

    /**
     * 관리자메모
     */
    private String mgrMemo;

    /**
     * 최초등록일시
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID
     */
    private String lastModPgmId;

    /**
     * 상품바코드필요여부
     */
    private String gdsBcdNeedYn;

    private static final long serialVersionUID = 1L;
}
