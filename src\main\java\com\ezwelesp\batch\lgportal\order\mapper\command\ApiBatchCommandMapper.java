package com.ezwelesp.batch.lgportal.order.mapper.command;

import com.ezwelesp.batch.lgportal.entity.*;


/**
 * The interface user query mapper.
 */
public interface ApiBatchCommandMapper {
    int insertApiChgReturn(ApiBatchChgReturnEntity apiBatchChgReturnEntity);

    int updateApiChgReturn(ApiBatchChgReturnEntity apiBatchChgReturnEntity);

    int insertApiBatchCspDlvr(ApiBatchCspDlvrEntity apiBatchCspDlvrEntity);

    int insertApiBatchDlvr(ApiBatchDlvrEntity apiBatchDlvrEntity);

    int updateApiBatchDlvr(ApiBatchDlvrEntity apiBatchDlvrEntity);

    int insertApiBatchLog(ApiBatchLogEntity apiBatchLogEntity);

    int insertApiBatchSend(ApiBatchSendEntity apiBatchSendEntity);

    int insertApiBatchSendNoSku(ApiBatchSendEntity apiBatchSendEntity);

    int updateApiBatchSend(ApiBatchSendEntity apiBatchSendEntity);
}
