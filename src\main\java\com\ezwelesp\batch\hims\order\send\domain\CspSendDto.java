package com.ezwelesp.batch.hims.order.send.domain;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@ToString
public class CspSendDto {
    private String cspCd;
    private String cspGdsCd;
    private String ordrNm;
    private Long optGdsCombSeq;
    private String apiCallCount; // 주문당 쿠폰 수량
    private String clntCd;
    private String rcvrNm;
    private String rcvrMblTelno;
    private String ordNo;
    private long ordSndGdsDtlSeq;
    private String gdsCd;
    private String intgGdsInfmSndTypCntn; // 무형상품알림발송유형내용

}
