package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 고속버스API로그(ez_or.or_exbs_api_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrExbsApiGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 제휴사주문번호(asp_ord_no) not null
     */
    private String aspOrdNo;

    /**
     * 고속버스API로그순번(exbs_api_log_seq) not null
     */
    private Long exbsApiLogSeq;

    /**
     * 고속버스API서비스명(exbs_api_srv_nm) not null
     */
    private String exbsApiSrvNm;

    /**
     * 고속버스API연동결과코드(exbs_api_intl_rslt_cd)
     */
    private String exbsApiIntlRsltCd;

    /**
     * 고속버스API연동상세결과코드(exbs_api_intl_dtl_rslt_cd)
     */
    private String exbsApiIntlDtlRsltCd;

    /**
     * 결과메시지내용(rslt_msg_cntn)
     */
    private String rsltMsgCntn;

    /**
     * API요청일시(api_req_dtm)
     */
    private String apiReqDtm;

    /**
     * 요청파라미터내용(req_para_cntn)
     */
    private String reqParaCntn;

    /**
     * API응답일시(api_resp_dtm)
     */
    private String apiRespDtm;

    /**
     * 응답파라미터내용(resp_para_cntn)
     */
    private String respParaCntn;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
