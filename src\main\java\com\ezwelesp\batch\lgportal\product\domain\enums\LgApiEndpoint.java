package com.ezwelesp.batch.lgportal.product.domain.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum LgApiEndpoint {

    // 상품 관련 API
    LG_API_ADD_PRODUCT("/api/vendor/prdProductCreate", "상품 등록"),
    LG_API_DELETE_PRODUCT("/api/vendor/prdProductDelete", "상품 삭제"),
    LG_API_ADD_PRODUCT_GOSI_INFO("/api/vendor/prdProductGosiInfoSave", "상품 정보고시 등록/수정"),
    LG_API_UPDATE_BASIC_PRODUCT("/api/vendor/prdProductBasicUpdate", "상품 기본정보 수정"),
    LG_API_UPDATE_PRODUCT_PRICE("/api/vendor/prdProductPriceUpdate", "상품 가격정보 수정"),
    LG_API_UPDATE_PRODUCT_IMAGE("/api/vendor/prdProductImagesUpdate", "상품 이미지 수정"),
    LG_API_UPDATE_PRODUCT_CONTENTS("/api/vendor/prdProductContentsUpdate", "상품 컨텐츠 수정"),
    LG_API_UPDATE_PRODUCT_DELIVERY("/api/vendor/prdProductDeliveryInfoUpdate", "상품 배송 및 반품 정보 수정"),
    LG_API_UPDATE_PRODUCT_SOLDOUT("/api/vendor/prdProductSoldOut", "상품 품절 처리"),
    LG_API_UPDATE_PRODUCT_SALE("/api/vendor/prdProductUnSoldOut", "상품 판매 처리"),

    // SKU 관련 API
    LG_API_SKU_STOCK_RESET("/api/vendor/prdProductSKUReset", "SKU 재고 초기화/미사용 처리"),
    LG_API_ADD_SKU("/api/vendor/prdProductSKUItemCreate", "SKU 등록"),
    LG_API_UPDATE_SKU("/api/vendor/prdProductSKUItemUpdate", "SKU 수정"),

    // QnA 관련 API
    LG_API_GET_QNA_LIST("/api/vendor/cssProductInquiryList", "상품 문의 목록 조회"),
    LG_API_SAVE_QNA_ANSWER("/api/vendor/cssProductAnswerSave", "상품 문의 답변 저장");

    private final String url; // API URL
    private final String description; // 설명

}
