package com.ezwelesp.batch.aasample.test.chunk.reader;

import java.util.Map;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.aasample.test.domain.Test;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@StepScope
public class ChunkParallelItemReader extends MyBatisPagingItemReader<Test> {
    // 1회 read 시 가져올 row 개수
    private final int PAGE_SIZE = 10;

    public ChunkParallelItemReader(@Qualifier("primaryQuerySqlSessionFactory") SqlSessionFactory primaryQuerySqlSessionFactory, @Value("#{jobParameters['ntcStat']}") String ntcStat) {
    	log.debug("ChunkParallelItemReader=============================");
        this.setName("ChunkParallelReader"); // reader명
        this.setSqlSessionFactory(primaryQuerySqlSessionFactory);
        this.setQueryId("com.ezwelesp.batch.aasample.test.mapper.query.UserQueryMapper.selectNtcByStatus");
        this.setParameterValues(Map.of("ntcStat", ntcStat));
        this.setPageSize(PAGE_SIZE);
        this.setSaveState(false);
    }

}
