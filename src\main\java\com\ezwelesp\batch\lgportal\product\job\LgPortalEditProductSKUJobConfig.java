package com.ezwelesp.batch.lgportal.product.job;


import com.ezwelesp.batch.lgportal.product.tasklet.LgEditProductSKUTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;



/**
 * LG_API 상품 SKU 수정등록
 */
//Scheduled(cron = "00 */10 * * * *") 매 10분마다
@Configuration
public class LgPortalEditProductSKUJobConfig {
    private final LgEditProductSKUTasklet lgEditProductSKUTasklet;

    public LgPortalEditProductSKUJobConfig(
            @Qualifier("lgEditProductSKUTasklet") LgEditProductSKUTasklet lgEditProductSKUTasklet
    ) {
        this.lgEditProductSKUTasklet = lgEditProductSKUTasklet;
    }

    @Bean("BA_HIPO00034")
    public Job lgEditProductSKUJob(
            JobRepository jobRepository
            , @Qualifier("BA_HIPO00034_STEP") Step lgEditProductSKUStep) {
        return new JobBuilder("BA_HIPO00034", jobRepository)
                .start(lgEditProductSKUStep)
                .build();
    }

    @Bean("BA_HIPO00034_STEP")
    public Step lgEditProductSKUStep(
            JobRepository jobRepository
            , DataSourceTransactionManager transactionManager
    ) {
        return new StepBuilder("BA_HIPO00034_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(lgEditProductSKUTasklet, transactionManager)
                .build();
    }
}
