package com.ezwelesp.batch.hims.order.send.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SendGoodsBaseDto {
    private String rcvrNm;
    private String rcvrMblTelno;
    private String ordNo;
    private String clntCd;
    private long ordSndSeq;
    private long ordGdsSeq;
    private long ordSndGdsDtlSeq;
    private String cspCd;
    private String rdnoSndMagnCd; // 발송주체코드
    private String tmsgSndKndCd; // 문자메시지발송종류코드
    private String intgGdsRdnoNo; // 무형상품난수번호
    private String gdsCd;
    private String gdsNm;
    private String gdsImg;
    private String ordGdsOptCntn;
    private String intgGdsInfmSndTypCntn; // 무형상품알림발송유형내용
    private String bcdExpsYn; // 바코드노출여부
    private String bcdTypCd; // 바코드유형코드
    private String cspGdsCd;
    private String ordrNm;
    private Long optGdsCombSeq;
    private String apiCallCount; // 주문당 쿠폰 수량


}
