package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 접종예약상세이력(ez_or.or_inocl_rsv_d_h)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrInoclRsvDHEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 접종예약상세이력순번(inocl_rsv_dtl_his_seq) not null
     */
    private Long inoclRsvDtlHisSeq;

    /**
     * 접종예약번호(inocl_rsv_no) not null
     */
    private Long inoclRsvNo;

    /**
     * 접종코드(inocl_cd)
     */
    private String inoclCd;

    /**
     * 검진기관코드(chkup_istn_cd)
     */
    private String chkupIstnCd;

    /**
     * 사용자고유번호(user_key)
     */
    private String userKey;

    /**
     * 접종예약상태코드(inocl_rsv_st_cd)
     */
    private String inoclRsvStCd;

    /**
     * 접종예약차수(inocl_rsv_nos)
     */
    private Long inoclRsvNos;

    /**
     * 접종예약일시(inocl_rsv_dtm)
     */
    private String inoclRsvDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
