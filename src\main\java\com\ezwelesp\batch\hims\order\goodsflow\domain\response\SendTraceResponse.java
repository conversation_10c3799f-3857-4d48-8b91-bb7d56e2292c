package com.ezwelesp.batch.hims.order.goodsflow.domain.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 배송추적 요청 response (SendTraceRequest API)
 */
@Getter
@Setter
@ToString
public class SendTraceResponse {
    private boolean success;
    private Object context;
    private Error error;

    @Getter
    @Setter
    @ToString
    public static class Error {
        private List<Details> details;
        private int status;
        private String message;
        private String detailMessage;
    }

    @Getter
    @Setter
    @ToString
    public static class Details {
        private String transUniqueCode;
        private String message;
    }
}
