package com.ezwelesp.batch.lgportal.order.config;

import com.ezwelesp.framework.exception.ServiceException;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.Set;

/**
  * LG 포털 연동 시 사용되느 상수 정의
  *
  * <AUTHOR>
  * @since 2025.03.04
  * @see com.ezwelesp.batch.lgportal.order.config
  */
public class LgConstants {
    public static final String EMPTY_STR = "";

    public static final String TARGET_DELIVERY_COMPLETE_URL = "/api/vendor/ordDeliveryManualComplete"; // 배송완료처리
    public static final String TARGET_SET_DELIVERY_ACCEPT = "/api/vendor/ordDeliveryAccept"; // 배송접수처리
    public static final String TARGET_SET_ADD_INVOICE_NO = "/api/vendor/ordDeliverySetTrackingNumber"; // 송장번호 등록
    public static final String TARGET_GET_DELIVERYSLIP = "/api/vendor/ordDeliveryList"; // 배송정보 조회
    public static final String TARGET_ADD_VENDOR = "/api/vendor/prdProductAddrSave"; // 출고/반품/교환지 저장
    public static final String TARGET_VENDOR_LIST = "/api/vendor/prdProductAddrList"; // 출고/반품/교환지 목록 조회
    public static final String TARGET_SOLDOUT_CANCEL = "/api/vendor/ordOrderCancel"; // 주문취소처리
    public static final String TARGET_EXCEPTION_LIST = "/api/vendor/ordExceptionList"; // 교환/반품/취소관리 목록 조회
    public static final String TARGET_SET_EXCEPTION_FINISH = "/api/vendor/ordExceptionComplete"; // 교환/반품 완료처리
    public static final String TARGET_SET_EXCEPTION_CANCEL = "/api/vendor/ordExceptionReqReject"; // 교환/반품 취소/거부처리
    public static final String TARGET_SET_EXCEPTION_ACCEPT = "/api/vendor/ordExceptionAccept"; // 교환/반품 접수처리

    public static final String LGPORTAL_PROVIDER_NO = "419";
    public static final String LGPORTAL_SEND_KEY_SOLD_OUT = "1014";

    public static class JobName {
        public static final String JOB_ORDER_LIST = "BA_HIOR00134";                 // 주문조회
        public static final String JOB_REG_INVOICE_NO = "BA_HIOR00135";             // 송장번호 등록
        public static final String JOB_DELIVERY_COMPLETE = "BA_HIOR00136";          // 배송완료처리
        public static final String JOB_REG_DELIVERY_LOC = "BA_HIOR00137";           // 출고/반품/교환지 저장
        public static final String JOB_REG_EXCH_RTP = "BA_HIOR00138";               // 교환,반품 목록 조회 및 클레임 생성
        public static final String JOB_COMPLETE_EXCH_RTP = "BA_HIOR00139";          // 교환/반품 완료처리
        public static final String JOB_WTDR_RFS_EXCH_RTP = "BA_HIOR00140";          // 교환/반품 취소(철회)/거부 처리
        public static final String JOB_REG_CANCEL = "BA_HIOR00143";                 // 취소 목록 조회 및 클레임 생성
        public static final String JOB_ORDER_CANCEL_SOLDOUT = "orderCancelOstkJob";     // 주문취소(품절)(사용안함)
    }

    // '주문취소' 클레임 생성 요청 URL
    public static final String CLAIM_API_CANCEL = "/order/api/v1/alliance-order/claim/cancel/apply";
    // '반품'신청 클레임 생성 요청 URL
    public static final String CLAIM_API_RETURN = "/order/api/v1/alliance-order/claim/return/";
    // '교환'신청 클레임 생성 요청 URl
    public static final String CLAIM_API_EXCHANGE = "/order/api/v1/alliance-order/claim/exchange/apply";

    @Getter
    public enum GoodsOptionType {
        SINGLE("1001", "단일형"),
        COMBINATION("1002", "조합형");

        private final String code;
        private final String name;

        GoodsOptionType(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    @Getter
    public enum OrdStCd {

        WAIT("ORD103D", "WAIT"),
        ORD_CMPT("ORD1032", "ORD_CMPT");

        private final String lgCode;
        private final String ezwelCode;

        OrdStCd(String lgCode, String ezwelCode) {
            this.lgCode = lgCode;
            this.ezwelCode = ezwelCode;
        }

        public static String findEzwelCode(String lgCode) {
            OrdStCd ordStCd = Arrays.stream(OrdStCd.values())
                    .filter(result -> result.lgCode.equals(lgCode))
                    .findFirst()
                    .orElse(WAIT);

            return ordStCd.ezwelCode;
        }
    }

    @Getter
    public enum DlvStCd {

        RDY("ACC1022", "RDY");  // '배송준비중'

        private final String lgCode;
        private final String ezwelCode;

        DlvStCd(String lgCode, String ezwelCode) {
            this.lgCode = lgCode;
            this.ezwelCode = ezwelCode;
        }

        public static String findEzwelCode(String lgCode) {
            DlvStCd dlvStCd = Arrays.stream(DlvStCd.values())
                    .filter(result -> result.lgCode.equals(lgCode))
                    .findFirst()
                    .orElse(RDY);

            return dlvStCd.ezwelCode;
        }
    }

    @Getter
    public enum SendKey {
        SOLD_OUT(1012),       // 1012 : 상품 품절 처리 // 품절처리 배치
        SKU_SOLD_OUT(1014),   // 1014 : SKU 품절, 상품에서는 사용 X이나 asis db에 있어서.
        EXCH_RTP_ACCEPT(1017),    // 교환,반품 접수 처리
        CLAIM_WTDR(1019),   // 교환, 반품 철회
        CLAIM_RFS(1020),    // 교환, 반품 거부
        ;

        private final int sendKey;

        SendKey(int sendKey) {
            this.sendKey = sendKey;
        }
    }

    @Getter
    public enum ClaimTypeCd {

        CANCEL_PERSON("ORD1011", "개인취소"),
        CANCEL_MANAGER("ORD1012", "운영자취소"),
        ACCEPT_RTP("ORD1013", "접수반품"),
        ACCEPT_EXCH("ORD1014", "접수교환"),
        CANCEL_PART_PERSON("ORD1015", "개인부분취소"),
        CANCEL_PART_MANAGER("ORD1016", "운영자부분취소"),
        CANCEL_PART_STO("ORD1017", "정기구독취소")
        ;

        private final String code;
        private final String name;

        ClaimTypeCd(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    @Getter
    public enum ClaimStatusCd {
        APPLY("ORD1021", "신청"),
        ACCEPT("ORD1022", "접수"),
        COMPLETION("ORD1023", "완료")
        ;

        private final String code;
        private final String name;

        ClaimStatusCd(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    @Getter
    public enum ClaimReasonCode {
        CHANGE_OF_MIND("1001", "단순변심"),
        PRICE_DISSATISFACTION("1002", "가격불만"),
        BUY_SWITCH("1003", "타상품 구매 전환"),
        PAYMENT_WAY_CHANGE("1004", "결제방식변경"),
        GOODS_OUT_OF_STOCK("1005", "상품품절"),
        DELIVERY_DELAY("1006", "배송지연"),
        ETC("1007", "기타"),
        CHOICE_CHANGE("2001", "사이즈 색상 등 선택사항 변경"),
        GOODS_FAULT("2002", "상품 파손 및 불량"),
        GOODS_MISS_DELIVERY("2003", "상품 오배송"),
        GOODS_INFORMATION_ERROR("2004", "상품 상세정보와 다름"),

        ;

        private final String code;
        private final String name;

        ClaimReasonCode(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    @Getter
    public enum ClaimReasonsMainAgentCode {
        USER("USR", "사용자"),
        CSP("CSP", "협력사"),
        RAINBOW("RBW", "레인보우"),
        ;

        private final String code;
        private final String name;

        ClaimReasonsMainAgentCode(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    @Getter
    public enum DeliveryGoodsStatusCodeEnum {
        GENERAL("GNRL", "일반배송"),
        OUT_OF_STOCK("OSTK_CNCL", "배송취소(결품)"),
        CANCEL("CNCL", "배송취소(취소)"),
        ;

        private final String code;
        private final String name;

        DeliveryGoodsStatusCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static String getName(String code) {
            DeliveryGoodsStatusCodeEnum codeEnum = Arrays.stream(values())
                    .filter(result -> result.getCode().equals(code))
                    .findFirst()
                    .orElse(null);

            if (ObjectUtils.isEmpty(codeEnum)) {
                throw new ServiceException();
            }

            return codeEnum.getName();
        }
    }

    @Getter
    public enum ClaimWithdrawGoodsStatusCodeEnum {
        CANCEL("CNCL", "회수취소"),
        HOLD("PSTP", "회수보류"),
        REFUSE("RFS", "회수거부"),
        WITHDRAW("WTDW", "회수배송"),
        ;

        private final String code;
        private final String name;

        ClaimWithdrawGoodsStatusCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static String getName(String code) {
            ClaimWithdrawGoodsStatusCodeEnum codeEnum = Arrays.stream(values())
                    .filter(result -> result.getCode().equals(code))
                    .findFirst()
                    .orElse(null);

            if (ObjectUtils.isEmpty(codeEnum)) {
                throw new ServiceException();
            }

            return codeEnum.getName();
        }
    }

    @Getter
    public enum PickUpMethodCodeEnum {
        VISIT("R", "방문수거"),
        DIRECT("D", "직접발송"),
        REQUEST_RETURN("H", "회수요청"),
        ;

        private final String code;
        private final String name;

        PickUpMethodCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    @Getter
    public enum ClaimKindCodeEnum {
        CANCEL("CNCL", "취소"),
        RETURN("RTP", "반품"),
        EXCHANGE("EXCH", "교환"),
        ;

        private final String code;
        private final String name;

        ClaimKindCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    @Getter
    public enum ClaimAddExpenseProcessMethodCodeEnum {
        PAYMENT("PYMT", "추가결제"),
        REFUND("RFND", "환불차감"),
        ARPAY("ARPAY", "착불 (배송비)"),
        ;

        private final String code;
        private final String name;

        ClaimAddExpenseProcessMethodCodeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static ClaimAddExpenseProcessMethodCodeEnum getCode(String code) {
            return Arrays.stream(values())
                    .filter(result -> result.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }
    }
}
