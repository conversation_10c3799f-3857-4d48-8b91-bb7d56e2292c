package com.ezwelesp.batch.aasample.test.chunk.processor;

import java.util.List;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ezwelesp.batch.aasample.test.domain.Test;
import com.ezwelesp.batch.aasample.test.mapper.command.UserDetailCommandMapper;
import com.ezwelesp.batch.aasample.test.mapper.query.UserDetailQueryMapper;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@StepScope
public class ChunkParallelItemProcessor implements ItemProcessor<Test, Test> {

    @Autowired
    private UserDetailQueryMapper userDetailQueryMapper;
    @Autowired
    private UserDetailCommandMapper userDetailCommandMapper;

    @Override
    public Test process(Test item) throws Exception {
    	log.debug("processor=======================");
        Long ntcId = item.getNtcId();
        item.setNtcStat("C");
        List<Test> ntcs = userDetailQueryMapper.selectNtcByNtcId(ntcId);
        String activeStatus = "F";
        for (Test ntc : ntcs) {
            Long ntcId2 = ntc.getNtcId();

            String ntcTy = "T1";
            String ntcCnts = "테스트 내용 입니다.";
            userDetailCommandMapper.updateTyByNtcId(ntcTy, ntcId);
            userDetailCommandMapper.updateCntsByNtcId(ntcCnts, ntcId);
        }

        return item;
    }

}
