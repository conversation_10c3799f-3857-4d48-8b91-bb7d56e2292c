<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgcompare.pinpay.mapper.query.PinpayQueryMapper">

	<select id="selectPinpayAuthKey" parameterType="String" resultType="String">
		select pg_frcs_pymt_stup_cval1 as np_mid
		  from ez_cm.cm_pg_frcs_pymt_b
		 where pg_frcs_no = 'PINPAY'
	</select>

</mapper>