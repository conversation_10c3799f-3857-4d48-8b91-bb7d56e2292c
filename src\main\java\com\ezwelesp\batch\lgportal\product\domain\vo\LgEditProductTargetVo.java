package com.ezwelesp.batch.lgportal.product.domain.vo;

import com.ezwelesp.batch.lgportal.product.domain.enums.lgPortalSendKey;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgEditProductTargetVo")
@Getter
@SuperBuilder
public class LgEditProductTargetVo {

    private Integer apiSeq;                     // api시퀀스
    private Integer providerNo;                 // 공급사번호(API 신청시 할당받은 공급사번호)
    private String certKey;                     // 인증키(API 신청시 할당받은 인증키)'
    private String gdsCd;                       // 이지웰상품번호
    private Integer productNo;                  // lg상품번호
    private String gdsNm;                       // 상품이름
    private String gdsSimpDesc;                 // 상품 설명
    private String gdsMdlNm;                    // 모델명
    private String mnfcCoNm;                    // 제조사이름
    private String orgnNm;                      // 원산지이름
    private String taxnKndCd;                   // 과세방식(Y:과세, N:비과세)
    private String t1BuyPossMaxQtyUseYn;        // 1회 구매 제한 수량 사용여부
    private Integer t1BuyPossMaxQty;            // 1회 구매 제한 수량
    private String psn1BuyPossMaxQtyUseYn;      // 아이디당 1회 구매 제한 수량 사용여부
    private Integer psn1BuyPossMaxQty;          // 아이디당 1회 구매 제한 수량
    private String imgPath;                     // 이미지 경로
    private Integer sendKey;                    // If 구분 키값
    private String sendYn;                      // 전송 여부
    private String valueArr;                    // 1:1 문의 답변
    private Integer boardNo;                    // 1:1 문의 번호


    private Long nrmlSellPrc;                   // 일반 판매 가격
    private Long realSellPrc;                   // 실제 판매 가격


    private String gdsOptUseYn;                 // 옵션 사용 여부
    private String gdsOptTypCd;                 // 옵션 코드(단일형 : 1001, 조합형 :  1002)
    private Integer optCnt;                     // 옵션수량

    public Integer getBrandNo() {
        return 13;
        // todo 나중에  브랜드코드(브랜드없음) (개발:13, 운영:39) 로 운영하고 test 분개
    }

    public lgPortalSendKey getSendKeyEnum() {
        return lgPortalSendKey.fromCode(this.sendKey);
    }
}

