package com.ezwelesp.batch.lgportal.product.service;


import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductAnnouncementDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductApiResponseDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgProductSKURequestDto;
import com.ezwelesp.batch.lgportal.product.domain.dto.LgUpdateProductDeliveryRequestDto;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductDeliveryTargetVo;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgEditProductTargetVo;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgProductAnnouncementVo;
import com.ezwelesp.batch.lgportal.product.domain.vo.LgProductOptionCombVo;
import lombok.NonNull;
import lombok.val;

import java.io.File;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class LgProductUtils {

    private LgProductUtils() {
        throw new UnsupportedOperationException("LgProductUtils class");
    }

    // api 실패시 반환
    public static boolean isApiFailure(@NonNull LgProductApiResponseDto apiResponse) {
        return !"200".equals(apiResponse.getResult());
    }

    // api 실패시 반환 for String
    public static boolean isApiFailure(@NonNull String result) {
        return !"200".equals(result);
    }

    // api 성공시 반환
    public static boolean isApiSuccess(LgProductApiResponseDto apiResponse) {
        return "200".equals(apiResponse.getResult());
    }

    // 옵션 이름 반환 DB에서 가공
    public static String getSKUAttrs(
            @NonNull List<LgProductOptionCombVo> optionList
    ) {
        return optionList.isEmpty() ? "" : optionList.get(0).getOptNm();
    }

    // 옵션 개수에 따른 코드 하드코딩
    public static String getSKUTypeCode(
            @NonNull List<LgProductOptionCombVo> optionList
    ) {
        // 옵션이 없을 경우
        if (optionList.isEmpty()) {
            return "PRD1091";
        }
        // 옵션 1개 / 그 이상일때.
        return optionList.size() == 1 ? "PRD1092" : "PRD1093";
    }

    // 옵션 내역을 SK 에서 사용하는 형태로 변환
    public static List<LgProductSKURequestDto> mapToSKUList(
            @NonNull String optUseYn,
            @NonNull Integer stockQty,
            @NonNull List<LgProductOptionCombVo> optionList
    ) {
        return "Y".equals(optUseYn) && !optionList.isEmpty()
                ? optionList.stream()
                .map(LgProductUtils::setProductSKU)
                .toList()// 옵션 사용 및 옵션 내역 존재시
                : Collections.singletonList(
                        setProductSKU(defaultProductGoodsOptionComb(stockQty))// 옵션 없을 경우 기본 셋팅
                );
    }

    // 옵션정보 없을때 기본값 셋팅
    private static LgProductOptionCombVo defaultProductGoodsOptionComb(
            @NonNull Integer stockQty
    ) {
        return LgProductOptionCombVo.builder()
                .optGdsSeq("1-1")
                .optVal("기본옵션")
                .stckQty(stockQty)
                .optAddPrc(0L)
                .sortOrdg(1)
                .useYn("Y")
                .build();
    }

    private static LgProductSKURequestDto setProductSKU(
            @NonNull LgProductOptionCombVo option
    ) {
        return LgProductSKURequestDto.builder()
                .sKUMappingCode(option.getOptGdsSeq())
                .sKUVal(option.getOptVal())
                .stockCnt(option.getStckQty())
                .controlSalesPrice(option.getOptAddPrc())
                .sKUCode(option.getOptGdsSeq())
                .sortSeq(option.getSortOrdg())
                .exposeYN(option.getUseYn())
                .build();
    }

    // 고시정보 상품번호별로 한번 그룹핑 및 정렬
    public static LgProductAnnouncementDto mapToProductAnnouncement(
            List<LgProductAnnouncementVo> productAnnouncements
    ) {
        val productAnnouncementList = productAnnouncements.stream()
                .sorted(Comparator.comparingInt(LgProductAnnouncementVo::getExpsOrdg))
                .map(announcementVo
                        -> new LgProductAnnouncementDto.ProductAnnouncement(
                        announcementVo.getTtl(), announcementVo.getGdsAnmtCntn()
                )).toList();

        return LgProductAnnouncementDto.builder()
                .productMappingCode(productAnnouncements.get(0).getGdsCd())
                .items(productAnnouncementList)
                .build();
    }

    // 이미지 유효성 확인
    public static boolean isValidProductWithImage(
            @NonNull String imgPath
    ) {
        if (imgPath.isEmpty()) {
            return false;
        }
        // todo 파일 경로 확인
        val fileDir = "/data/nas_image/ezwel-image/welfare_shop";
        val mainImg = new File(fileDir + imgPath);

        return mainImg.exists();
    }

    // 상품의 배송, 환불, 교환 정보 수정 맵핑
    public static LgUpdateProductDeliveryRequestDto mapToProductDeliveryToRequest(
            @NonNull List<LgEditProductDeliveryTargetVo> lgEditProductDeliveryTargetVo
            , @NonNull LgEditProductTargetVo lgEditProductTargetVo
    ) {
        val lgEditProductDeliveryTargetMap = lgEditProductDeliveryTargetVo.stream()
                .collect(Collectors.toMap(
                        LgEditProductDeliveryTargetVo::getDlvrTypeCd, // 키: dlvrTypeCd
                        LgEditProductDeliveryTargetVo::getLgDlvrNo    // 값: lgDlvrNo
                ));

        return LgUpdateProductDeliveryRequestDto.builder()
                .productNo(lgEditProductTargetVo.getProductNo())
                .shippingLoc(lgEditProductDeliveryTargetMap.get("01"))
                .returnLoc(lgEditProductDeliveryTargetMap.get("02"))
                .exchangeLoc(lgEditProductDeliveryTargetMap.get("03"))
                .build();
    }

    // 어느 위치에서 실패했는지 내역 추가 위해서 추가.
    public static LgProductApiResponseDto createFailureResponse(
            LgProductApiResponseDto apiResponse,
            String logMessage
    ) {
        return LgProductApiResponseDto.builder()
                .result(apiResponse.getResult())
                .resultMessage(logMessage + ": " + apiResponse.getResultMessage())
                .productNo(null)
                .gdsCd(apiResponse.getGdsCd())
                .apiSeq(apiResponse.getApiSeq())
                .build();
    }

}

