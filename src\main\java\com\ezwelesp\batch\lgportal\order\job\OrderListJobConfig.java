package com.ezwelesp.batch.lgportal.order.job;

import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.tasklet.OrderListTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class OrderListJobConfig {
    private final String ORDER_LIST_STEP = LgConstants.JobName.JOB_ORDER_LIST + "_STEP";

    private final OrderListTasklet orderListTasklet;
    private final CommonJobListener commonJobListener;

    @Bean(LgConstants.JobName.JOB_ORDER_LIST)
    public Job orderListJob(JobRepository jobRepository, @Qualifier(ORDER_LIST_STEP) Step orderListStep) {
        return new JobBuilder(LgConstants.JobName.JOB_ORDER_LIST, jobRepository)
                .listener(commonJobListener)
                .start(orderListStep)
                .build();
    }

    @Bean(name = ORDER_LIST_STEP)
    public Step orderListStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder(ORDER_LIST_STEP, jobRepository)
                .allowStartIfComplete(true)
                .tasklet(orderListTasklet, transactionManager)
                .build();
    }
}
