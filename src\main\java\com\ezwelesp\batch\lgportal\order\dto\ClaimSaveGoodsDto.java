package com.ezwelesp.batch.lgportal.order.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * 클레임 상품 정보
 *
 * <AUTHOR>
 * @see com.ezwelesp.batch.lgportal.order.dto.ClaimSaveGoodsDto
 * @since 2025.02.25
 */
@Getter
@Setter
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class ClaimSaveGoodsDto implements Serializable {

    // 주문번호
    private String ordNo;

    // 주문상품순번
    private long ordGdsSeq;

    // 클레임요청수량
    private int clmGdsQty;

    // 클레임귀책사유주체코드
    private String clmAtbrMagnCd;

    // 클레임사유코드
    private String clmRsnCd;

    // 클레임사유내용
    private String clmRsnCntn;

    // 배송번호
    private String dlvNo;

    // 배송상품순번
    private long dlvGdsSeq;

    // 주문발송순번
    private long ordSndSeq;

    // 주문발송상품순번
    private long ordSndGdsDtlSeq;

    // 교환상품번호
    private String exchangeGdsCd;

    // 옵션상품조합명
    private String optGdsCombNm;

    // 옵션상품조합순번
    private String optGdsCombSeq;

    // 옵션추가가격
    private String optAddPrc;

    // 교환주문상품순번
    private Long exchOrdGdsSeq;

    // 클레임상품순번
    private Long clmGdsSeq;

    // LG포털API배송번호
    private String lgpApiDlvNo;

    // LG포털API배송상세번호
    private String lgpApiDlvDtlNo;

    public ClaimSaveGoodsDto(ClaimReturnGoodsDto returnGoods) {
        this.ordGdsSeq = returnGoods.getOrdGdsSeq();
        this.clmGdsQty = returnGoods.getClmGdsQty();
        this.clmAtbrMagnCd = returnGoods.getClmAtbrMagnCd();
        this.clmRsnCd = returnGoods.getClmRsnCd();
        this.clmRsnCntn = returnGoods.getClmRsnCntn();
    }
}
