package com.ezwelesp.batch.hims.order.reseller.service.impl;

import com.ezwelesp.batch.hims.order.config.SuspectedResellerConstants;
import com.ezwelesp.batch.hims.order.entity.external.ScMntMonitorJobsEntity;
import com.ezwelesp.batch.hims.order.entity.external.ScMntMonitorResEntity;
import com.ezwelesp.batch.hims.order.reseller.mapper.command.MonitorJobCommandMapper;
import com.ezwelesp.batch.hims.order.reseller.mapper.query.MonitorJobQueryMapper;
import com.ezwelesp.batch.hims.order.reseller.service.MonitorJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @see com.ezwelesp.batch.hims.order.reseller.service.impl
 * @since 2025.05.19
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MonitorJobServiceImpl implements MonitorJobService {
    private final MonitorJobQueryMapper monitorJobQueryMapper;
    private final MonitorJobCommandMapper monitorJobCommandMapper;

    @Override
    public String saveScMntMonitorRes(String jobStartDt, String jobId, int listSize, String resEtc) {
        try {
            ScMntMonitorJobsEntity monitorJob = monitorJobQueryMapper.selectScMntMonitorJobsByJobId(jobId);

            if (monitorJob == null) {
                log.debug("jobId is null.({})", jobId);
                return SuspectedResellerConstants.RESULT_FAIL;
            }
            else {
                ScMntMonitorResEntity res = ScMntMonitorResEntity.builder()
                        .jobId(monitorJob.getJobId())
                        .jobStartDt(jobStartDt)
                        .alramYn(monitorJob.getAlramYn())
                        .alramCntMax(monitorJob.getAlramCntMax())
                        .alramUser(SuspectedResellerConstants.EMPTY_STRING)
                        .criticalValue(monitorJob.getCriticalValue())
                        .criticalValueUnit(monitorJob.getCriticalValueUnit())
                        .resStatus(SuspectedResellerConstants.AlarmReqStatusEnum.REQ_STBY.getCode())
                        .resValue(BigDecimal.valueOf(listSize))
                        .sendStatus(SuspectedResellerConstants.AlarmSendStatusEnum.SEND_STBY.getCode())
                        .resEtc(resEtc)
                        .resEtc2(listSize > 0 ? SuspectedResellerConstants.RES_ETC2_DEFAULT : SuspectedResellerConstants.EMPTY_STRING)
                        .resIp("")  // TODO
                        .build();
                monitorJobCommandMapper.insertScMntMonitorRes(res);
            }
        } catch (Exception e) {
            return SuspectedResellerConstants.RESULT_FAIL;
        }

        return SuspectedResellerConstants.RESULT_SUCCESS;
    }
}
