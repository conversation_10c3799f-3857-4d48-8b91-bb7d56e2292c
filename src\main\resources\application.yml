spring:
  batch:
    job:
      name: ${job.name:NONE}
      enabled: true
    jdbc:
      initialize-schema: never

  main:
    web-application-type: none

# MyBatis 설정
mybatis:
  # 지정한 패키지 별칭 찾기
  primary-type-aliases-package: com.ezwelesp.batch.**.domain
  # 모든 mapper를 찾기 위해 mapper.xml의 스캔을 설정합니다
  primary-command-mapper-locations: classpath:mybatis/**/command/*Mapper.xml
  primary-query-mapper-locations: classpath:mybatis/**/query/*Mapper.xml
  # 전역 설정 파일 불러오기
  config-location: classpath:mybatis/mybatis-config.xml

# ezwel config
ezwel:
  project:
    type: batch
    id: batch  
  
# 웹클라이언트 기본 주소
rest:
  client:
    base-url: ${ezwel.apim.host}