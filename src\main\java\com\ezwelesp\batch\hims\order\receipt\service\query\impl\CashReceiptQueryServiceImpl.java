package com.ezwelesp.batch.hims.order.receipt.service.query.impl;

import com.ezwelesp.batch.hims.order.receipt.domain.*;
import com.ezwelesp.batch.hims.order.receipt.mapper.query.CashReceiptQueryMapper;
import com.ezwelesp.batch.hims.order.receipt.service.query.CashReceiptQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CashReceiptQueryServiceImpl implements CashReceiptQueryService {

    private final CashReceiptQueryMapper cashReceiptQueryMapper;


    @Override
    public List<OrderClaimDto> getOrderCashReceiptTarget() {
        return cashReceiptQueryMapper.selectOrderCashReceiptTarget();
    }

    @Override
    public List<OrderClaimDto> getClaimCashReceiptTarget() {
        return cashReceiptQueryMapper.selectClaimCashReceiptTarget();
    }

    @Override
    public List<OrderInfoDto> getOrderInfo(String ordNo) {
        return cashReceiptQueryMapper.selectOrderInfo(ordNo);
    }

    @Override
    public int getCountCashReceipt(CashReceiptParamDto param) {
        return cashReceiptQueryMapper.selectCountCashReceipt(param);
    }

    @Override
    public List<CashPublicationGatherDto> getCashReceiptPublicationGather(CashPublicationGatherDto dto) {
        return cashReceiptQueryMapper.selectCashReceiptPublicationGather(dto);
    }

    public int getCashReceiptPublicationGatherCount(CashPublicationGatherDto dto) {
        return cashReceiptQueryMapper.selectCashReceiptPublicationGatherCount(dto);
    }

    @Override
    public ContentServiceProviderDto getUnitedContentServiceProviderInfo(String cspCd) {
        return cashReceiptQueryMapper.selectUnitedContentServiceProviderInfo(cspCd);
    }



}
