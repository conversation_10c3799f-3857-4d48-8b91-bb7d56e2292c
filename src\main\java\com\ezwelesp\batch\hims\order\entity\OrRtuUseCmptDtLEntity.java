package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 이용권사용완료일자내역(ez_or.or_rtu_use_cmpt_dt_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrRtuUseCmptDtLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 주문상품순번(ord_gds_seq) not null
     */
    private Long ordGdsSeq;

    /**
     * 이용권완료기준코드(rtu_cmpt_bsic_cd)
     */
    private String rtuCmptBsicCd;

    /**
     * 이용권사용완료일자등록일시(rtu_use_cmpt_dt_reg_dtm)
     */
    private String rtuUseCmptDtRegDtm;

    /**
     * 이용권사용완료일자등록관리자ID(rtu_use_cmpt_dt_reg_mgr_id)
     */
    private String rtuUseCmptDtRegMgrId;

    /**
     * 수수료수기등록일시(cms_hndw_reg_dtm)
     */
    private String cmsHndwRegDtm;

    /**
     * 수수료수기등록관리자ID(cms_hndw_reg_mgr_id)
     */
    private String cmsHndwRegMgrId;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
