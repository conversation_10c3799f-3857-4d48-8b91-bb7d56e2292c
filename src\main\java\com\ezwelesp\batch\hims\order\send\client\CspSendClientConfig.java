package com.ezwelesp.batch.hims.order.send.client;

import com.ezwelesp.framework.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

@Slf4j
@Configuration
public class CspSendClientConfig {

    @Bean("cspSendHttpClient")
    public CloseableHttpClient cspSendHttpClient() {
        return HttpClients.createDefault();
    }

//    @Bean("cspSendUnsafeHttpClient")
//    public CloseableHttpClient cspSendUnsafeHttpClient() {
//        TrustManager[] tm = new TrustManager[] {
//                new X509TrustManager() {
//                    @Override
//                    public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {}
//
//                    @Override
//                    public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {}
//
//                    @Override
//                    public X509Certificate[] getAcceptedIssuers() {
//                        return new X509Certificate[0];
//                    }
//                }
//        };
//
//        SSLContext sslContext;
//        try {
//            sslContext = SSLContext.getInstance("SSL");
//            sslContext.init(null, tm, null);
//        } catch (Exception e) {
//            log.error("CspSendClient getUnsafeHttpClient error: {}", e.getMessage());
//            throw new ServiceException();
//        }
//
//        return HttpClients.custom()
//                .setSSLContext(sslContext)
//                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
//                .build();
//    }
}
