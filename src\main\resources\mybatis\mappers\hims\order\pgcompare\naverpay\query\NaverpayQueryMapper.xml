<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgcompare.naverpay.mapper.query.NaverpayQueryMapper">

	<select id="selectNaverPayMidInfo" parameterType="String" resultType="Map">
		select A.pg_frcs_pymt_stup_cval1 as np_mid
			 , A.pg_frcs_pymt_stup_cval2 as np_chain_id
			 , A.pg_frcs_pymt_stup_cval3 as np_partner_id
			 , A.pg_frcs_pymt_stup_cval4 as np_client_id
			 , A.pg_frcs_pymt_stup_cval5 as np_client_secret
			 , B.api_key
		  from ez_cm.cm_pg_frcs_pymt_b A
		  left outer join(
			   select 'NAVER_PARTNER_ID' as pg_frcs_no, '6b0604ded4f60f527e983018e000d215eaacbf6bac4f1cceb20a6e487b50e9a9' as api_key
			   union all select 'NAVER_PARTNER_TRAVEL' as pg_frcs_no, 'a04406d8209ede0ed89b6d01a2f4a705229653c736845eb86fd5a62dee69766e' as api_key
			   union all select 'NAVER_PARTNER_LIMIT' 	as pg_frcs_no, '5d28ee28147a6d0d4262229f7e82c77c45edbd1ff6a28a6e23b98ef3db0da173' as api_key
			   union all select 'NAVER_PARTNER_DEDUCT' as pg_frcs_no, '5cad6ba3fe9e1dff30860bdaeb8b8ed6a1264366b0de354d4dba0953f7c09d4d' as api_key
			   union all select 'NAVER_PARTNER_MK_B2E' as pg_frcs_no, '9ea9bba4870c1e67d4b0db0b8795502c0de425fe7865956440252520c6e9e90c' as api_key
			   union all select 'NAVER_PARTNER_MK_B2C' as pg_frcs_no, 'c154ba539931a78fe2b464f0872e2d344dbb42c5f0ae5016b0af2bd71771eaca' as api_key
		     ) B
		    on A.pg_frcs_no = B.pg_frcs_no
		 where A.pg_knd_cd = 'B'
		   and A.pg_frcs_no = #{mid}
	</select>

	
	
</mapper>