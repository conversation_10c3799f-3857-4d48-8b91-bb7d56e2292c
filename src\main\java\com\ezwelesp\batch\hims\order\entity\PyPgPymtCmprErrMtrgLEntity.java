package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * PG결제대사오류모니터링내역(ez_or.py_pg_pymt_cmpr_err_mtrg_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyPgPymtCmprErrMtrgLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * PG결제대사오류모니터링내역순번(pg_pymt_cmpr_err_mtrg_lst_seq) not null
     */
    private Long pgPymtCmprErrMtrgLstSeq;

    /**
     * PG결제오류코드(pg_pymt_err_cd)
     */
    private String pgPymtErrCd;

    /**
     * 주문번호(ord_no)
     */
    private String ordNo;

    /**
     * 주문상태코드(ord_st_cd)
     */
    private String ordStCd;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 주문채널명(ord_ch_nm)
     */
    private String ordChNm;

    /**
     * 결제금액(pymt_amt) not null
     */
    private BigDecimal pymtAmt;

    /**
     * PG결제승인금액(pg_pymt_apv_amt) not null
     */
    private BigDecimal pgPymtApvAmt;

    /**
     * PG종류코드(pg_knd_cd)
     */
    private String pgKndCd;

    /**
     * 결제수단명(pymt_mns_nm)
     */
    private String pymtMnsNm;

    /**
     * PG가맹점번호(pg_frcs_no)
     */
    private String pgFrcsNo;

    /**
     * PG승인일시(pg_apv_dtm)
     */
    private String pgApvDtm;

    /**
     * PG승인번호(pg_apv_no)
     */
    private String pgApvNo;

    /**
     * PG카드사승인번호(pg_crdc_apv_no)
     */
    private String pgCrdcApvNo;

    /**
     * PG가맹점주문번호(pg_frcs_ord_no)
     */
    private String pgFrcsOrdNo;

    /**
     * 접속단말기유형명(acss_te_typ_nm)
     */
    private String acssTeTypNm;

    /**
     * 관리자확인완료여부(mgr_cnft_cmpt_yn) not null
     */
    private String mgrCnftCmptYn;

    /**
     * 관리자메모(mgr_memo)
     */
    private String mgrMemo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
