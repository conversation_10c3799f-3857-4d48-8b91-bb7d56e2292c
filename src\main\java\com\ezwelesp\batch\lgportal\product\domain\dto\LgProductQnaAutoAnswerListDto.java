package com.ezwelesp.batch.lgportal.product.domain.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgProductQnaAutoAnswerListDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgProductQnaAutoAnswerListDto {

    @Builder.Default
    private Integer providerNo = 419;            // 공급사번호
    @Builder.Default
    private String certkey = "Testtest";             // todo 인증키 인데 공통코드로 빼야될듯?
    private Integer productNo;             // 상품번호
    private String titleArr;            // 품질보증기준 제목
    private String valueArr;            // 품질보증기준 내용
    private Integer boardNo;               // API QA 시퀀스

    @Builder.Default
    private Integer sendKey = 1015;            // 전송 키값
    private String result;        // 에러 메시지
    private String resultMessage;        // 에러 메시지
}
