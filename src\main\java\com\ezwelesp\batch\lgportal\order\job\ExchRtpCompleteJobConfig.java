package com.ezwelesp.batch.lgportal.order.job;

import com.ezwelesp.batch.lgportal.entity.ApiBatchChgReturnEntity;
import com.ezwelesp.batch.lgportal.order.chunk.reader.ExchRtpCompleteItemReader;
import com.ezwelesp.batch.lgportal.order.chunk.reader.RegDeliveryLocItemReader;
import com.ezwelesp.batch.lgportal.order.chunk.writer.ExchRtpCompleteItemWriter;
import com.ezwelesp.batch.lgportal.order.chunk.writer.RegDeliveryLocItemWriter;
import com.ezwelesp.batch.lgportal.order.config.LgConstants;
import com.ezwelesp.batch.lgportal.order.dto.CspDlvrLocDto;
import com.ezwelesp.batch.listener.CommonJobListener;
import com.ezwelesp.batch.listener.CommonStepListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@Configuration
@RequiredArgsConstructor
public class ExchRtpCompleteJobConfig {

    private final String COMPLETE_EXCH_RTP_STEP = LgConstants.JobName.JOB_COMPLETE_EXCH_RTP + "_STEP";
    private final CommonJobListener commonJobListener;

    @Value("${lgportal.chunk-size}")
    private int chunkSize;


    @Bean(LgConstants.JobName.JOB_COMPLETE_EXCH_RTP)
    public Job regInvoiceNoJob(JobRepository jobRepository, @Qualifier(COMPLETE_EXCH_RTP_STEP) Step regInvoiceNoStep) {
        return new JobBuilder(LgConstants.JobName.JOB_COMPLETE_EXCH_RTP, jobRepository)
                .listener(commonJobListener)
                .start(regInvoiceNoStep)
                .build();
    }

    @Bean(name = COMPLETE_EXCH_RTP_STEP)
    public Step exchRtpCompleteStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager,
            ExchRtpCompleteItemReader exchRtpCompleteItemReader,
            ExchRtpCompleteItemWriter exchRtpCompleteItemWriter, CommonStepListener commonStepListener) {

        return new StepBuilder(COMPLETE_EXCH_RTP_STEP, jobRepository)
                .allowStartIfComplete(true)
                .<ApiBatchChgReturnEntity, ApiBatchChgReturnEntity>chunk(chunkSize, transactionManager)
                .transactionManager(transactionManager)
                .reader(exchRtpCompleteItemReader)
                .writer(exchRtpCompleteItemWriter)
                .listener(commonStepListener)
                .build();

    }
}
