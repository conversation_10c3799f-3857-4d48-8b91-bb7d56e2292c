package com.ezwelesp.batch.hpas.alarm.job;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import com.ezwelesp.batch.hpas.alarm.tasklet.ReturnExchangeMailMainTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class ReturnExchangeMailMainJobConfig {
    private final CommonJobListener commonJobListener;
    private final ReturnExchangeMailMainTasklet returnExchangeMailMainTasklet;

    @Bean("returnExchangeMailMainJob")
    public Job returnExchangeMailMainJob(JobRepository jobRepository,
            @Qualifier("returnExchangeMailMainStep") Step returnExchangeMailMainStep) {
        return new JobBuilder("returnExchangeMailMainJob", jobRepository)
                .listener(commonJobListener)
                .start(returnExchangeMailMainStep)
                .build();
    }

    @Bean("returnExchangeMailMainStep")
    public Step returnExchangeMailMainStep(JobRepository jobRepository,
            DataSourceTransactionManager transactionManager) {
        return new StepBuilder("returnExchangeMailMainStep", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(returnExchangeMailMainTasklet, transactionManager)
                .build();

    }
}
