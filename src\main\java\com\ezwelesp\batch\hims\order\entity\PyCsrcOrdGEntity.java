package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 현금영수증주문로그(ez_or.py_csrc_ord_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class PyCsrcOrdGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 현금영수증주문로그순번(csrc_ord_log_seq) not null
     */
    private Long csrcOrdLogSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 제휴사주문번호(asp_ord_no)
     */
    private String aspOrdNo;

    /**
     * 급여처리대상여부(wgs_prcs_obj_yn) not null
     */
    private String wgsPrcsObjYn;

    /**
     * 선택적복지포인트현금영수증발행여부(wfp_csrc_pblc_yn) not null
     */
    private String wfpCsrcPblcYn;

    /**
     * 선택적복지포인트종류목록값(wfp_knd_lval)
     */
    private String wfpKndLval;

    /**
     * 현금영수증발행대상상품여부(csrc_pblc_obj_gds_yn) not null
     */
    private String csrcPblcObjGdsYn;

    /**
     * 협력사현금영수증발행여부(csp_csrc_pblc_yn) not null
     */
    private String cspCsrcPblcYn;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 특별포인트현금영수증발행여부(spp_csrc_pblc_yn) not null
     */
    private String sppCsrcPblcYn;

    /**
     * 현금영수증발행여부(csrc_pblc_yn) not null
     */
    private String csrcPblcYn;

    /**
     * 현금영수증발행방법코드(csrc_pblc_mthd_cd)
     */
    private String csrcPblcMthdCd;

    /**
     * 사업자등록번호(bzrn)
     */
    private String bzrn;

    /**
     * 사업자명(bzco_nm)
     */
    private String bzcoNm;

    /**
     * 암호화주민등록번호(enc_rrn)
     */
    private String encRrn;

    /**
     * 모바일전화번호(mbl_telno)
     */
    private String mblTelno;

    /**
     * 현금영수증공급가격(csrc_spl_prc) not null
     */
    private BigDecimal csrcSplPrc;

    /**
     * 현금영수증부가가치세금액(csrc_vat_amt) not null
     */
    private BigDecimal csrcVatAmt;

    /**
     * 현금영수증발행용도코드(csrc_pblc_usg_cd)
     */
    private String csrcPblcUsgCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
