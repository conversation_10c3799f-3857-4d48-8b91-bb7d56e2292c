package com.ezwelesp.batch.lgportal.product.domain.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.Alias;

@Alias("LgUpdateProductContentsRequestDto")
@Getter
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LgUpdateProductContentsRequestDto {

    private String productMappingCode;
    private Integer productNo;
    private String typeCode;
    private String contents;
}
