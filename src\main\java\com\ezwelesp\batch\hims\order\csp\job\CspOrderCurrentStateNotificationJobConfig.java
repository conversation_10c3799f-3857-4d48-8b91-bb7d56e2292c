package com.ezwelesp.batch.hims.order.csp.job;

import com.ezwelesp.batch.hims.order.csp.tasklet.CspOrderCurrentStateNotificationTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 복지샵 배송마감,배송지연,교/반 지연 안내 알림톡 발송 [BA_HIOR00110]
 */
@Configuration
@RequiredArgsConstructor
public class CspOrderCurrentStateNotificationJobConfig {
    private final CommonJobListener commonJobListener;
    private final CspOrderCurrentStateNotificationTasklet cspOrderCurrentStateNotificationTasklet;

    @Bean("BA_HIOR00110")
    public Job cspOrderCurrentStateNotificationJob(JobRepository jobRepository, @Qualifier("BA_HIOR00110_STEP") Step cspOrderCurrentStateNotificationStep) {
        return new JobBuilder("BA_HIOR00110", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(cspOrderCurrentStateNotificationStep)
                .build();
    }

    @Bean("BA_HIOR00110_STEP")
    public Step cspOrderCurrentStateNotificationStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00110_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(cspOrderCurrentStateNotificationTasklet, transactionManager)
                .build();
    }
}
