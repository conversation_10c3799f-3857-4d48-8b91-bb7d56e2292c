<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.client.contract.mapper.query.ContractAutoExtendQueryMapper">

    <select id="selectContractPeriodExpired"
            resultType="com.ezwelesp.batch.hims.client.contract.domain.CtSlsContBAutoExtendDto">
        select
            cscb.sls_cont_no as slsContNo
             , cscb.clnt_cd as clntCd
        from ez_ct.ct_sls_cont_b cscb
        where 1=1
        and cscb.cont_end_dt = to_char(current_date - interval '1 day','yyyyMMdd')
   	    and cscb.cont_term_auto_ext_yn = 'Y'
    </select>
</mapper>
