package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 배송지연소명처리로그(ez_or.cl_dlv_dlay_expl_prcs_g)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClDlvDlayExplPrcsGEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 배송지연소명처리로그순번(dlv_dlay_expl_prcs_log_seq) not null
     */
    private Long dlvDlayExplPrcsLogSeq;

    /**
     * 배송번호(dlv_no) not null
     */
    private String dlvNo;

    /**
     * 배송지연소명코드(dlv_dlay_expl_cd)
     */
    private String dlvDlayExplCd;

    /**
     * 배송지연소명상태코드(dlv_dlay_expl_st_cd)
     */
    private String dlvDlayExplStCd;

    /**
     * 배송지연소명대상종류코드(dlv_dlay_expl_obj_knd_cd)
     */
    private String dlvDlayExplObjKndCd;

    /**
     * 배송지연소명처리상태코드(dlv_dlay_expl_prcs_st_cd)
     */
    private String dlvDlayExplPrcsStCd;

    /**
     * 배송지연소명처리관리자ID(dlv_dlay_expl_prcs_mgr_id)
     */
    private String dlvDlayExplPrcsMgrId;

    /**
     * 이전데이터등록일시(bef_data_reg_dtm) not null
     */
    private String befDataRegDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

    /**
     * 배송상품순번(dlv_gds_seq) not null
     */
    private Long dlvGdsSeq;

}
