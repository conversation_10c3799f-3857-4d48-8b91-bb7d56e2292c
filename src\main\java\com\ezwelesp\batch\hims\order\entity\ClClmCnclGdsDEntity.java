package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * 클레임취소상품상세(ez_or.cl_clm_cncl_gds_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class ClClmCnclGdsDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 클레임번호(clm_no) not null
     */
    private String clmNo;

    /**
     * 클레임상품순번(clm_gds_seq) not null
     */
    private Long clmGdsSeq;

    /**
     * 클레임취소요청차수(clm_cncl_req_nos) not null
     */
    private Long clmCnclReqNos;

    /**
     * 취소수량(cncl_qty) not null
     */
    private Integer cnclQty;

    /**
     * 관리자메모(mgr_memo)
     */
    private String mgrMemo;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
