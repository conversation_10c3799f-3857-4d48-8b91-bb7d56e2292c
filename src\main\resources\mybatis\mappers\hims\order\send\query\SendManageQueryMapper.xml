<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.send.mapper.query.SendManageQueryMapper">

    <select id="selectCoEcpnCspApi" resultType="com.ezwelesp.batch.hims.order.entity.external.CoEcpnCspApiDEntity">
        select csp_cd
              , use_api_nm
              , ecpn_api_url
              , ecpn_api_port
              , seed_enc_innt_no
              , gds_img_api_url
              , gds_img_api_port
              , ecpn_snd_url
              , ecpn_rsnd_url
              , ecpn_issu_cncl_url
              , ecpn_term_chg_url
              , ecpn_issu_gd_url
              , ecpn_term_ext_poss_yn
              , ecpn_snd_cnft_obj_yn
              , snd_rslt_rcnf_url
          from ez_co.co_ecpn_csp_api_d
         where csp_cd = #{cspCd}
    </select>

    <select id="selectSendCallCenterInfo" resultType="com.ezwelesp.batch.hims.order.send.domain.SendCallCenterInfoDto">
        select ccb.clnt_cls_cd
             , ccb.cst_cntr_telno_typ_cd
             , chb.cst_cntr_telno_aply_bsic_cd
             , chb.clct_telno
        from ez_ct.ct_clnt_b ccb
                 join ez_ct.ct_hezo_b chb on chb.hezo_no = ccb.hezo_no
        where ccb.clnt_cd = #{clntCd}
    </select>
</mapper>
