package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 주문제도마감상세(ez_or.or_ord_rgm_clsg_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrOrdRgmClsgDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 선택적복지포인트마감일자(wfp_clsg_dt)
     */
    private String wfpClsgDt;

    /**
     * 선택적복지포인트소멸금액(wfp_extn_amt) not null
     */
    private BigDecimal wfpExtnAmt;

    /**
     * 특별포인트마감일자(spp_clsg_dt)
     */
    private String sppClsgDt;

    /**
     * 특별포인트소멸금액(spp_extn_amt) not null
     */
    private BigDecimal sppExtnAmt;

    /**
     * 제도마감취소사유코드(rgm_clsg_cncl_rsn_cd)
     */
    private String rgmClsgCnclRsnCd;

    /**
     * 원본주문번호(orgl_ord_no)
     */
    private String orglOrdNo;

    /**
     * 원본주문일시(orgl_ord_dtm)
     */
    private String orglOrdDtm;

    /**
     * 직전주문번호(nrly_ord_no)
     */
    private String nrlyOrdNo;

    /**
     * 직전주문일시(nrly_ord_dtm)
     */
    private String nrlyOrdDtm;

    /**
     * 선택적복지포인트마감이후주문여부(wfp_clsg_aft_ord_yn) not null
     */
    private String wfpClsgAftOrdYn;

    /**
     * 특별포인트마감이후주문여부(spp_clsg_aft_ord_yn) not null
     */
    private String sppClsgAftOrdYn;

    /**
     * 적립금지급여부(mlg_pay_yn) not null
     */
    private String mlgPayYn;

    /**
     * 적립금지급금액(mlg_pay_amt) not null
     */
    private BigDecimal mlgPayAmt;

    /**
     * 적립금지급관리자ID(mlg_pay_mgr_id)
     */
    private String mlgPayMgrId;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
