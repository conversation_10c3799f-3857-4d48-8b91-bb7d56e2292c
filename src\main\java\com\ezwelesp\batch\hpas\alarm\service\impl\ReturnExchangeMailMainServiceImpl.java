package com.ezwelesp.batch.hpas.alarm.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import org.springframework.stereotype.Service;
import com.ezwelesp.batch.hpas.alarm.dto.ReturnExchangeMailMainDto;
import com.ezwelesp.batch.hpas.alarm.mapper.query.ReturnExchangeMailMainQueryMapper;
import com.ezwelesp.batch.hpas.alarm.service.ReturnExchangeMailMainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class ReturnExchangeMailMainServiceImpl implements ReturnExchangeMailMainService {
    private final ReturnExchangeMailMainQueryMapper returnExchangeMailMainQueryMapper;

    @Override
    public int procReturnExchangeMailMain() {
        int count = 0;

        try {
            List<String> cspCdList = new ArrayList<String>();
            List<String> mailList = new ArrayList<String>();

            // 교환 미처리 데이터 조회
            List<ReturnExchangeMailMainDto> exchangeList = new ArrayList<ReturnExchangeMailMainDto>();
            exchangeList = returnExchangeMailMainQueryMapper.selectReturnExchangeMailMainExchList();

            for (ReturnExchangeMailMainDto exchangeInfo : exchangeList) {
                if (exchangeInfo.getCspCd() != null) {
                    cspCdList.add(exchangeInfo.getCspCd());
                }
                if (exchangeInfo.getEmail() != null) {
                    mailList.add(exchangeInfo.getEmail());
                }
            }

            // 반품 미처리 데이터 조회
            List<ReturnExchangeMailMainDto> returnList = new ArrayList<ReturnExchangeMailMainDto>();
            returnList = returnExchangeMailMainQueryMapper.selectReturnExchangeMailMainRtpList();

            for (ReturnExchangeMailMainDto returnInfo : returnList) {
                if (returnInfo.getCspCd() != null) {
                    cspCdList.add(returnInfo.getCspCd());
                }
                if (returnInfo.getEmail() != null) {
                    mailList.add(returnInfo.getEmail());
                }
            }

            // 배송 미처리 데이터 조회
            List<ReturnExchangeMailMainDto> penddingOrderList = new ArrayList<ReturnExchangeMailMainDto>();
            penddingOrderList = returnExchangeMailMainQueryMapper.selectReturnExchangeMailMainOrdList();

            for (ReturnExchangeMailMainDto penddingOrderInfo : penddingOrderList) {
                if (penddingOrderInfo.getCspCd() != null) {
                    cspCdList.add(penddingOrderInfo.getCspCd());
                }
                if (penddingOrderInfo.getEmail() != null) {
                    mailList.add(penddingOrderInfo.getEmail());
                }
            }

            //중복 제거
            HashSet<String> setCspCdList = new HashSet<String>(cspCdList);
            ArrayList<String> uniqCspCdList = new ArrayList<String>(setCspCdList);

            if (uniqCspCdList.size() > 0) {
                for (int i = 0; i < uniqCspCdList.size(); i++) {
                    String cspCd = uniqCspCdList.get(i);
                    String mailContent = "";

                    int exCount = 0;
                    int reCount = 0;
                    int peCount = 0;

                    // 교환 미처리 데이터 메일내용 세팅
                    for (ReturnExchangeMailMainDto exchangeInfo : exchangeList) {
                        if (exchangeInfo.getCspCd().equals(cspCd)) {
                            if (exchangeInfo.getEmail() == null) {
                                //mailBean.setEmail("");
                            }
                            else {
                                //mailBean.setEmail(exchangeInfo.getEmail());
                                //mailBean.setCspNm(exchangeInfo.getCspNm());
                            }
                            exCount++;
                            exchangeInfo.setCount(exCount);

                            // 메일내용 셋팅
                            //mailContent = mailContent + this.getCommentTargetContent(exchangeInfo, "E");
                        }
                    }

                    // 반품 미처리 데이터 메일내용 세팅
                    for (ReturnExchangeMailMainDto returnInfo : returnList) {
                        if (returnInfo.getCspCd().equals(cspCd)) {
                            if (returnInfo.getEmail() == null) {
                                //mailBean.setEmail("");
                            }
                            else {
                                //mailBean.setEmail(returnInfo.getEmail());
                                //mailBean.setCspNm(returnInfo.getCspNm());
                            }
                            reCount++;
                            returnInfo.setCount(reCount);

                            // 메일내용 셋팅
                            //mailContent = mailContent + this.getCommentTargetContent(returnInfo, "R");
                        }
                    }

                    // 배송 미처리 데이터 메일내용 세팅
                    for (ReturnExchangeMailMainDto penddingOrderInfo : penddingOrderList) {
                        if (penddingOrderInfo.getCspCd().equals(cspCd)) {
                            if (penddingOrderInfo.getEmail() == null) {
                                //mailBean.setEmail("");
                            }
                            else {
                                //mailBean.setEmail(penddingOrderInfo.getEmail());
                                //mailBean.setCspNm(penddingOrderInfo.getCspNm());
                            }
                            peCount++;
                            penddingOrderInfo.setCount(peCount);

                            // 메일내용 셋팅
                            //mailContent = mailContent + this.getCommentTargetContent(penddingOrderInfo, "O");
                        }
                    }

                    count = exCount + reCount + peCount;
                }

                this.sendEmailEntryCspMainLogin_mgr();
            }

        } catch (Exception e) {
            log.error("###########################################");
            log.error("## [협력사 교환/반품 미처리 안내메일 발송] 처리 중 에러 ###");
            log.error("###########################################");
            log.error(e.toString());
        }

        return count;
    }

    public void sendEmailEntryCspMainLogin_mgr() {
        // 템플릿 : MailTemplate.html
        // 송신이메일 : <EMAIL>
        // 송신자명 : 현대이지웰
        // 반송이메일 : <EMAIL>
        log.info("#############################################");
        log.info("### [협력사 교환/반품 미처리 안내메일 발송] 은 공통기능 제공받아 개발 예정");
        log.info("#############################################");
    }
}
