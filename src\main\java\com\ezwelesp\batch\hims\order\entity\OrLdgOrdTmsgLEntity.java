package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;

/**
 * [2차]숙박주문문자메시지내역(ez_or.or_ldg_ord_tmsg_l)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrLdgOrdTmsgLEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 숙박주문문자메시지내역순번(ldg_ord_tmsg_lst_seq) not null
     */
    private Long ldgOrdTmsgLstSeq;

    /**
     * 주문번호(ord_no) not null
     */
    private String ordNo;

    /**
     * 숙박주문지원차수(ldg_ord_afe_nos) not null
     */
    private Long ldgOrdAfeNos;

    /**
     * 숙박주문이력순번(ldg_ord_his_seq)
     */
    private Long ldgOrdHisSeq;

    /**
     * 문자메시지발송전화번호(tmsg_snd_telno)
     */
    private String tmsgSndTelno;

    /**
     * 수신자모바일전화번호(rcvr_mbl_telno)
     */
    private String rcvrMblTelno;

    /**
     * 메시지내용(msg_cntn)
     */
    private String msgCntn;

    /**
     * 문자메시지템플릿코드(tmsg_tmpl_cd)
     */
    private String tmsgTmplCd;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
