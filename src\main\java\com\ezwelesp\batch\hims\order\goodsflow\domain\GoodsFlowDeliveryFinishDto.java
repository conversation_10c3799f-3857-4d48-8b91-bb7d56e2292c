package com.ezwelesp.batch.hims.order.goodsflow.domain;

import lombok.Data;

@Data
public class GoodsFlowDeliveryFinishDto {
    private String transUniqueCode;
    private int seq;
    private String logisticsCode;
    private String invoiceNo;
    private String dlvStatType;
    private String exceptionCode;
    private String exceptionName;
    private String errorCode;
    private String errorName;
    private String procYn;
    private String ezwelProcDate;
    private String defCode1;
    private String defCode2;
}
