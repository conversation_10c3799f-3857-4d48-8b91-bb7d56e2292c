<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ezwelesp.batch.hims.order.pgconfirm.dongbaek.mapper.query.PgTransactionErrorQueryMapper">

	<select id="selectPgTransactionCuserDiffList" resultType="HashMap">
		with ORDER_TMP as (
			select ord.ord_no
			     , ord.asp_ord_no
			     , ord.ord_dtm
			     , ord.ord_st_cd
			     , pymt.pg_apv_no
			     , pymt.pg_frcs_no
			     , pymt.pymt_amt
			     , ord.cncl_dtm
			     , pymt.pymt_dtm
			     , ord.clnt_cd
			     , ch.ch_nm
			     , ordGds.gds_nm
			     , pymt.pg_knd_cd
			     , pymt.pymt_mns_cd
			     , ordGds.csp_cd
			     , pymt.pymt_dtm as pg_auth_dt
			     , pg_apv_no as org_pg_apv_no
			  from ez_or.or_ord_b ord
			 inner join ez_or.or_ord_gds_d ordGds
			    on ord.ord_no = ordGds.ord_no
			 inner join ez_or.py_pymt_b pymt
			    on ordGds.ord_no = pymt.ord_no 
			  left outer join ez_cm.cm_ch_b ch 
			    on ordGds.ch_cd = ch.ch_cd
			 where pymt.pg_knd_cd = 'D'
			   and pymt.pymt_mns_cd = '2000'
			   and ord.ord_dtm <![CDATA[<=]]> to_char(now() + interval '-1' day, 'YYYYMMDD') || '235959'
			   and pymt.pg_apv_no in (
			   	   select pymt.pg_apv_no
			   	     from ez_or.or_ord_b ord
					inner join ez_or.or_ord_gds_d ordGds
					   on ord.ord_no = ordGds.ord_no
					inner join ez_or.py_pymt_b pymt
					   on ordGds.ord_no = pymt.ord_no 
					where pymt.pymt_mns_cd = '2000'
					  and pymt.pg_knd_cd = 'D'
					   and (((ord.ord_dtm between to_char(now() + interval '-1' day, 'YYYYMMDD') || '000000' and to_char(now() + interval '-1' day, 'YYYYMMDD') || '235959')
		                    and ord.ord_st_cd in ('OBND','ORD_CMPT')			-- 1001/1002 : 주문처리중/주문완료
		                ) or (( ord.cncl_dtm between to_char(now() + interval '-1' day, 'YYYYMMDD') || '000000' and to_char(now() + interval '-1' day, 'YYYYMMDD') || '235959')
		                    and ord.ord_st_cd = 'DLV' -- 1003 : 주문취소
		                ))
			     )
		)
		, ORDER_DATA as(
		  select A.ord_no
		       , A.asp_ord_no
		       , A.ord_dtm
		       , (case when A.ord_st_cd = 'DLV' then -- 1003
		       		(case when A.cncl_dtm > to_char(now() + interval '-1' day, 'YYYYMMDD') || '235959' then 'OBND' --1002
		       		 else (
		       		 	  case when A.ord_st_cd = 'DLV' and ( select count(*) 
		       		 	                                        from order_tmp 
		       		 	                                       where org_pg_apv_no = A.org_pg_apv_no
		       		 	                                         and pg_auth_dt = A.pg_auth_dt
		       		 	                                         and pymt_mns_cd = A.pymt_mns_cd
		       		 	                                         and pymt_dtm > A.pymt_dtm
		  		 	                                       ) = 0 then 'DLV_CMPT'
		  		 	      else A.ord_st_cd 
		  		 	      end)
		       		  end)
		         else A.ord_st_cd
		         end) as ord_st_cd
		       , A.pg_apv_no
		       , A.pg_frcs_no
		       , A.pymt_amt
		       , A.cncl_dtm
		       , A.clnt_cd 
		       , A.ch_nm
		       , A.gds_nm
		       , A.pg_knd_cd
		       , A.pymt_mns_cd
		       , A.pg_auth_dt
		       , A.org_pg_apv_no
		       , row_number() over (partition by A.org_pg_apv_no order by a.ord_dtm asc) num	
		    from ORDER_TMP A
		), PG_TARGET_TMP as (
			select proc_cd
			     , auth_no
			     , auth_amt
			     , auth_dt || (case when proc_cd = '2' then '00100'
			       else to_char(row_number() over (partition by org_auth_no order by proc_cd asc, auth_trx_id asc), 'FM000099')
			       end) as auth_dt
			     , org_auth_dt
			     , org_auth_no
			     , auth_trx_id
			   from (
				    select req_no 
				         , req_dt
				         , store_no
				         , proc_cd
				         , auth_no
				         , auth_amt
				         , auth_dt
				         , auth_trx_id
				         , coalesce(org_auth_dd, auth_dt) as org_auth_dt
				         , coalesce(org_auth_no, auth_no) as org_auth_no
				      from ez_if.api_pg_dongbaek
				     where (org_auth_no, org_auth_dd) in (select org_pg_apv_no, pg_auth_dt from ORDER_DATA)
				        or (auth_no, auth_dt) in (select org_pg_apv_no, pg_auth_dt from ORDER_DATA)
				  )
		), PG_DATA as (
		   select T.*
		        , case when proc_cd > '1' and real_amt > '0' then 'P' else proc_cd end as status -- P:부분취소결제
		     from (
		     	  select (
		     	         select sum(case when proc_cd = '1' then auth_amt else auth_amt * -1 end)
		     	           from (select TT.*
		     	                   from PG_TARGET_TMP TT)
		     	          where org_auth_no = A.org_auth_no
		     	            and auth_dt <![CDATA[<=]]> A.auth_dt
		     	       ) as real_amt
		     	       , A.*
		     	    from PG_TARGET_TMP A
		     	) T order by auth_dt
		)
		
		select '1000' as err_type
		     , A.ord_no
		     , A.clnt_cd
		     , A.ord_st_cd
		     , A.asp_ord_no
		     , A.pymt_amt
		     , coalesce (B.auth_amt, '-1') as pg_amt
		     , 'D' as pg_type
		     , A.pymt_mns_cd
		     , A.pg_frcs_no
		     , coalesce(A.pg_auth_dt, A.ord_dtm) as pg_date
		     , coalesce(A.org_pg_apv_no, B.org_auth_no) as cancel_tid
		     , B.proc_cd as card_confirm_num
		     , coalesce(A.pg_apv_no, B.auth_no) as shop_order_num
		     , B.auth_trx_id as device_type
		  from ORDER_DATA A
		  left outer join PG_DATA B
		    on A.org_pg_apv_no = b.org_auth_no
		   and A.pg_auth_dt = B.org_auth_dt
		   and A.pymt_amt = B.real_amt
		 where B.org_auth_no is null
		 union
		select '1005' as err_type
		     , A.ord_no
		     , A.clnt_cd
		     , A.ord_st_cd
		     , A.asp_ord_no
		     , A.pymt_amt
		     , coalesce (B.auth_amt, '-1') as pg_amt
		     , 'D' as pg_type
		     , A.pymt_mns_cd
		     , A.pg_frcs_no
		     , coalesce(A.pg_auth_dt, A.ord_dtm) as pg_date
		     , coalesce(A.org_pg_apv_no, B.org_auth_no) as cancel_tid
		     , B.proc_cd as card_confirm_num
		     , coalesce(A.pg_apv_no, B.auth_no) as shop_order_num
		     , B.auth_trx_id as device_type
		  from ORDER_DATA A
		  left outer join PG_DATA B
		    on A.org_pg_apv_no = b.org_auth_no
		   and A.pg_auth_dt = B.org_auth_dt
		   and A.pymt_amt = B.real_amt
		 where B.org_auth_no is null  
		   and A.ord_st_cd = 'DLV_CMPT'
	</select>

</mapper>