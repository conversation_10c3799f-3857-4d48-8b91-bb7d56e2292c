package com.ezwelesp.batch.hims.order.goodsflow.job;

import com.ezwelesp.batch.hims.order.goodsflow.tasklet.GoodsFlowAfterTraceResultTasklet;
import com.ezwelesp.batch.listener.CommonJobListener;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * BA_HIOR00082
 * as-is: GoodsFlow DB연동 받아오기
 */
@Configuration
@RequiredArgsConstructor
public class GoodsFlowAfterTraceResultJobConfig {
    private final CommonJobListener commonJobListener;
    private final GoodsFlowAfterTraceResultTasklet goodsFlowAfterTraceResultTasklet;

    @Bean("BA_HIOR00082")
    public Job goodsFlowAfterTraceResultJob(JobRepository jobRepository, @Qualifier("BA_HIOR00082_STEP") Step goodsFlowAfterTraceResultStep) {
        return new JobBuilder("BA_HIOR00082", jobRepository)
                .listener(commonJobListener)
                .incrementer(new RunIdIncrementer())
                .start(goodsFlowAfterTraceResultStep)
                .build();
    }

    @Bean("BA_HIOR00082_STEP")
    public Step goodsFlowAfterTraceResultStep(JobRepository jobRepository, DataSourceTransactionManager transactionManager) {
        return new StepBuilder("BA_HIOR00082_STEP", jobRepository)
                .allowStartIfComplete(true)
                .tasklet(goodsFlowAfterTraceResultTasklet, transactionManager)
                .build();
    }
}
