package com.ezwelesp.batch.hims.client.contract.mapper.query;

import com.ezwelesp.batch.hims.client.contract.domain.CtSlsContBAutoExtendDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> 임종갑 플그림
 * @see : com.ezwelesp.batch.hims.client.contract.mapper.query
 * @since : 2025-06-16
 */
@Mapper
public interface ContractAutoExtendQueryMapper {

    /**
     * 영업원장 계약 종료 예정 조회
     *
     * <AUTHOR>
     * @since 임종갑 플그림
     *
     */
    List<CtSlsContBAutoExtendDto> selectContractPeriodExpired();
}
