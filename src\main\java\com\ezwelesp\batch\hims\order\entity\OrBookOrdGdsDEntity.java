package com.ezwelesp.batch.hims.order.entity;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 도서주문상품상세(ez_or.or_book_ord_gds_d)
 */
@Jacksonized
@Getter
@SuperBuilder
public class OrBookOrdGdsDEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 제휴사주문번호(asp_ord_no) not null
     */
    private String aspOrdNo;

    /**
     * 도서주문상품순번(book_ord_gds_seq) not null
     */
    private Long bookOrdGdsSeq;

    /**
     * 원본도서주문상품순번(orgl_book_ord_gds_seq)
     */
    private Long orglBookOrdGdsSeq;

    /**
     * 사용자고유번호(user_key) not null
     */
    private String userKey;

    /**
     * 도서상품코드(book_gds_cd) not null
     */
    private String bookGdsCd;

    /**
     * 도서주문상품상태코드(book_ord_gds_st_cd) not null
     */
    private String bookOrdGdsStCd;

    /**
     * 주문상품수량(ord_gds_qty) not null
     */
    private Integer ordGdsQty;

    /**
     * 상품명(gds_nm) not null
     */
    private String gdsNm;

    /**
     * 상품매입가격(gds_pchs_prc) not null
     */
    private BigDecimal gdsPchsPrc;

    /**
     * 상품판매가격(gds_sell_prc) not null
     */
    private BigDecimal gdsSellPrc;

    /**
     * 취소상품수량(cncl_gds_qty)
     */
    private Integer cnclGdsQty;

    /**
     * 취소일시(cncl_dtm)
     */
    private String cnclDtm;

    /**
     * 도서상품API연동결과코드(book_gds_api_intl_rslt_cd)
     */
    private String bookGdsApiIntlRsltCd;

    /**
     * 도서상품API연동일시(book_gds_api_intl_dtm)
     */
    private String bookGdsApiIntlDtm;

    /**
     * 최초등록일시(frst_reg_dtm) not null
     */
    private String frstRegDtm;

    /**
     * 최초등록사용자ID(frst_reg_usr_id) not null
     */
    private String frstRegUsrId;

    /**
     * 최초등록프로그램ID(frst_reg_pgm_id) not null
     */
    private String frstRegPgmId;

    /**
     * 최종수정일시(last_mod_dtm) not null
     */
    private String lastModDtm;

    /**
     * 최종수정사용자ID(last_mod_usr_id) not null
     */
    private String lastModUsrId;

    /**
     * 최종수정프로그램ID(last_mod_pgm_id) not null
     */
    private String lastModPgmId;

}
